{"version": 3, "file": "event-grid-storage-trigger.js", "sourceRoot": "", "sources": ["../../src/functions/event-grid-storage-trigger.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,gDAA0E;AAC1E,mDAAgD;AAChD,0DAAiD;AACjD,sFAAiF;AAEjF,8DAA8D;AAC9D,IAAK,SAWJ;AAXD,WAAK,SAAS;IACZ,oDAAuC,CAAA;IACvC,sDAAyC,CAAA;IACzC,gDAAmC,CAAA;IACnC,kDAAqC,CAAA;IACrC,sDAAyC,CAAA;IACzC,gDAAmC,CAAA;IACnC,oDAAuC,CAAA;IACvC,wDAA2C,CAAA;IAC3C,uDAA0C,CAAA;IAC1C,oDAAuC,CAAA;AACzC,CAAC,EAXI,SAAS,KAAT,SAAS,QAWb;AAED;;GAEG;AACH,KAAK,UAAU,YAAY,CAAC,SAAoB,EAAE,OAAe,EAAE,IAAS;IAC1E,IAAI,CAAC;QACH,MAAM,6CAAoB,CAAC,YAAY,CAAC;YACtC,SAAS;YACT,OAAO;YACP,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,8CAA8C,EAAE;YAC3D,SAAS;YACT,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CAAC,KAAqB,EAAE,OAA0B;IACtF,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;QAClD,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,OAAO,EAAE,KAAK,CAAC,EAAE;QACjB,YAAY,EAAE,OAAO,CAAC,YAAY;KACnC,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,mBAAmB,CAAC,KAAK,CAAC,CAAC;QACjC,eAAM,CAAC,IAAI,CAAC,sCAAsC,EAAE;YAClD,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC,CAAC,sCAAsC;IACrD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAC,KAAqB;IACtD,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE;QACtC,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,OAAO,EAAE,KAAK,CAAC,EAAE;KAClB,CAAC,CAAC;IAEH,QAAQ,KAAK,CAAC,SAAS,EAAE,CAAC;QACxB,KAAK,+BAA+B;YAClC,MAAM,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM;QACR,KAAK,+BAA+B;YAClC,MAAM,sBAAsB,CAAC,KAAK,CAAC,CAAC;YACpC,MAAM;QACR;YACE,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IAChF,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CAAC,KAAqB;IACzD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,IAAI,EAAE,CAAC;IACpC,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,IAAI,SAAS,CAAC;IAEvD,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,iEAAiE;QACjE,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,MAAM,YAAY,CAChB,SAAS,CAAC,iBAAiB,EAC3B,aAAa,QAAQ,EAAE,EACvB;gBACE,QAAQ;gBACR,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,cAAc,EAAE,IAAI;gBACpB,WAAW,EAAE,iBAAiB;aAC/B,CACF,CAAC;YAEF,wDAAwD;YACxD,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EACpD,8CAA8C,EAC9C,CAAC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE,CAAC,CACzC,CAAC;gBAEF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;oBAC5B,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE;wBAC/B,GAAG,GAAG;wBACN,MAAM,EAAE,UAAU;wBAClB,OAAO,EAAE,OAAO;wBAChB,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBACrC,CAAC,CAAC;gBACL,CAAC;YACH,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;oBACnD,QAAQ;oBACR,KAAK,EAAE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;iBACpE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,yBAAyB;QACzB,IAAI,OAAO,CAAC,QAAQ,CAAC,aAAa,CAAC,EAAE,CAAC;YACpC,MAAM,YAAY,CAChB,SAAS,CAAC,kBAAkB,EAC5B,aAAa,QAAQ,EAAE,EACvB;gBACE,QAAQ;gBACR,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,iBAAiB;aAC/B,CACF,CAAC;QACJ,CAAC;QAED,oBAAoB;QACpB,IAAI,OAAO,CAAC,QAAQ,CAAC,cAAc,CAAC,EAAE,CAAC;YACrC,eAAM,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;QAC1D,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,QAAQ;YACR,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CAAC,KAAqB;IACzD,MAAM,OAAO,GAAG,KAAK,CAAC,OAAO,CAAC;IAC9B,MAAM,QAAQ,GAAG,OAAO,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;IAE1C,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,QAAQ,EAAE,OAAO,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,qCAAqC;QACrC,MAAM,SAAS,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EACpD,wEAAwE,EACxE;YACE,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;YACtC,EAAE,IAAI,EAAE,WAAW,EAAE,KAAK,EAAE,QAAQ,EAAE;SACvC,CACF,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,SAAS,EAAE,CAAC;YAC5B,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE;gBAC/B,GAAG,GAAG;gBACN,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;QACL,CAAC;QAED,yBAAyB;QACzB,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzB,MAAM,YAAY,CAChB,SAAS,CAAC,kBAAkB,EAC5B,aAAa,QAAQ,UAAU,EAC/B;gBACE,QAAQ;gBACR,OAAO;gBACP,WAAW,EAAE,SAAS,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,EAAE,CAAC;gBACrC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,iBAAiB;aAC/B,CACF,CAAC;QACJ,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,QAAQ;YACR,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,kCAAkC;AAClC,eAAG,CAAC,SAAS,CAAC,yBAAyB,EAAE;IACvC,OAAO,EAAE,uBAAuB;CACjC,CAAC,CAAC"}