/**
 * Real-time Messaging Function
 * Handles real-time chat, messaging, and communication features
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';
import { redis } from '../shared/services/redis';
import { signalREnhanced } from '../shared/services/signalr';

// Message types enum
enum MessageType {
  TEXT = 'TEXT',
  FILE = 'FILE',
  IMAGE = 'IMAGE',
  SYSTEM = 'SYSTEM',
  NOTIFICATION = 'NOTIFICATION',
  MENTION = 'MENTION',
  REACTION = 'REACTION'
}

// Channel types enum
enum ChannelType {
  DIRECT = 'DIRECT',
  GROUP = 'GROUP',
  PROJECT = 'PROJECT',
  ORGANIZATION = 'ORGANIZATION',
  DOCUMENT = 'DOCUMENT'
}

// Note: SignalR interfaces and state management are now handled by the enhanced shared service

// Validation schemas
const sendMessageSchema = Joi.object({
  channelId: Joi.string().uuid().required(),
  content: Joi.string().required().max(4000),
  type: Joi.string().valid(...Object.values(MessageType)).default(MessageType.TEXT),
  parentMessageId: Joi.string().uuid().optional(),
  mentions: Joi.array().items(Joi.string().uuid()).optional(),
  attachments: Joi.array().items(
    Joi.object({
      id: Joi.string().uuid().required(),
      name: Joi.string().required(),
      type: Joi.string().required(),
      size: Joi.number().integer().min(0).required(),
      url: Joi.string().uri().required()
    })
  ).optional(),
  metadata: Joi.object().optional()
});

const createChannelSchema = Joi.object({
  name: Joi.string().required().min(2).max(100),
  description: Joi.string().max(500).optional(),
  type: Joi.string().valid(...Object.values(ChannelType)).required(),
  participants: Joi.array().items(Joi.string().uuid()).min(1).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  documentId: Joi.string().uuid().optional(),
  isPrivate: Joi.boolean().default(false),
  settings: Joi.object({
    allowFileSharing: Joi.boolean().default(true),
    allowMentions: Joi.boolean().default(true),
    retentionDays: Joi.number().integer().min(1).max(365).optional()
  }).optional()
});

const listMessagesSchema = Joi.object({
  channelId: Joi.string().uuid().required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(50),
  before: Joi.string().uuid().optional(),
  after: Joi.string().uuid().optional(),
  search: Joi.string().max(100).optional()
});

/**
 * Send message handler
 */
export async function sendMessage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Send message started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = sendMessageSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { channelId, content, type, parentMessageId, mentions, attachments, metadata } = value;

    // Get channel and verify access
    const channel = await db.readItem('channels', channelId, channelId);
    if (!channel) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Channel not found" }
      }, request);
    }

    // Check if user is a participant
    const isParticipant = (channel as any).participants.includes(user.id);
    if (!isParticipant) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to channel" }
      }, request);
    }

    // Create message
    const messageId = uuidv4();
    const message = {
      id: messageId,
      channelId,
      content,
      type,
      parentMessageId,
      mentions: mentions || [],
      attachments: attachments || [],
      metadata: metadata || {},
      senderId: user.id,
      sentAt: new Date().toISOString(),
      editedAt: null,
      deletedAt: null,
      reactions: [],
      threadCount: 0,
      organizationId: (channel as any).organizationId,
      projectId: (channel as any).projectId,
      tenantId: user.tenantId
    };

    await db.createItem('messages', message);

    // Update channel last activity
    const updatedChannel = {
      ...(channel as any),
      id: channelId,
      lastMessageId: messageId,
      lastMessageAt: message.sentAt,
      messageCount: ((channel as any).messageCount || 0) + 1,
      updatedAt: new Date().toISOString()
    };
    await db.updateItem('channels', updatedChannel);

    // Update parent message thread count if this is a reply
    if (parentMessageId) {
      const parentMessage = await db.readItem('messages', parentMessageId, parentMessageId);
      if (parentMessage) {
        const updatedParent = {
          ...(parentMessage as any),
          id: parentMessageId,
          threadCount: ((parentMessage as any).threadCount || 0) + 1
        };
        await db.updateItem('messages', updatedParent);
      }
    }

    // Send notifications to mentioned users
    if (mentions && mentions.length > 0) {
      for (const mentionedUserId of mentions) {
        await db.createItem('notifications', {
          id: uuidv4(),
          recipientId: mentionedUserId,
          senderId: user.id,
          type: 'MESSAGE_MENTION',
          title: `You were mentioned in ${(channel as any).name}`,
          message: `${(user as any).name || user.email} mentioned you: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`,
          priority: 'MEDIUM',
          actionUrl: `/channels/${channelId}/messages/${messageId}`,
          actionText: 'View Message',
          channelId,
          messageId,
          organizationId: (channel as any).organizationId,
          projectId: (channel as any).projectId,
          isRead: false,
          createdAt: new Date().toISOString(),
          tenantId: user.tenantId
        });
      }
    }

    // Broadcast message to channel participants (simplified)
    await broadcastMessage(channel as any, message, user);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "message_sent",
      userId: user.id,
      organizationId: (channel as any).organizationId,
      projectId: (channel as any).projectId,
      channelId,
      messageId,
      timestamp: new Date().toISOString(),
      details: {
        channelName: (channel as any).name,
        messageType: type,
        hasAttachments: (attachments || []).length > 0,
        mentionCount: (mentions || []).length,
        isReply: !!parentMessageId
      },
      tenantId: user.tenantId
    });

    logger.info("Message sent successfully", {
      correlationId,
      messageId,
      channelId,
      userId: user.id,
      type,
      mentionCount: (mentions || []).length
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: messageId,
        channelId,
        content,
        type,
        senderId: user.id,
        sentAt: message.sentAt,
        mentions: mentions || [],
        attachments: attachments || [],
        message: "Message sent successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Send message failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Create channel handler
 */
export async function createChannel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create channel started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createChannelSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const channelData = value;

    // Check organization access
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, channelData.organizationId, 'active']);

    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Ensure creator is in participants list
    const participants = Array.from(new Set([...channelData.participants, user.id]));

    // Create channel
    const channelId = uuidv4();
    const channel = {
      id: channelId,
      name: channelData.name,
      description: channelData.description || "",
      type: channelData.type,
      participants,
      organizationId: channelData.organizationId,
      projectId: channelData.projectId,
      documentId: channelData.documentId,
      isPrivate: channelData.isPrivate,
      settings: channelData.settings || {
        allowFileSharing: true,
        allowMentions: true
      },
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      lastMessageId: null,
      lastMessageAt: null,
      messageCount: 0,
      isActive: true,
      tenantId: user.tenantId
    };

    await db.createItem('channels', channel);

    // Create channel memberships
    for (const participantId of participants) {
      await db.createItem('channel-members', {
        id: uuidv4(),
        channelId,
        userId: participantId,
        role: participantId === user.id ? 'ADMIN' : 'MEMBER',
        joinedAt: new Date().toISOString(),
        lastReadAt: new Date().toISOString(),
        notificationSettings: {
          muted: false,
          mentions: true,
          allMessages: true
        },
        organizationId: channelData.organizationId,
        tenantId: user.tenantId
      });
    }

    // Send welcome message
    const welcomeMessage = {
      id: uuidv4(),
      channelId,
      content: `Welcome to ${channel.name}! This channel was created by ${(user as any).name || user.email}.`,
      type: MessageType.SYSTEM,
      senderId: 'system',
      sentAt: new Date().toISOString(),
      organizationId: channelData.organizationId,
      projectId: channelData.projectId,
      tenantId: user.tenantId
    };
    await db.createItem('messages', welcomeMessage);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "channel_created",
      userId: user.id,
      organizationId: channelData.organizationId,
      projectId: channelData.projectId,
      channelId,
      timestamp: new Date().toISOString(),
      details: {
        channelName: channel.name,
        channelType: channel.type,
        participantCount: participants.length,
        isPrivate: channel.isPrivate
      },
      tenantId: user.tenantId
    });

    logger.info("Channel created successfully", {
      correlationId,
      channelId,
      userId: user.id,
      organizationId: channelData.organizationId,
      type: channelData.type,
      participantCount: participants.length
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: channelId,
        name: channel.name,
        type: channel.type,
        participants,
        organizationId: channelData.organizationId,
        projectId: channelData.projectId,
        isPrivate: channel.isPrivate,
        message: "Channel created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create channel failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List messages handler
 */
export async function listMessages(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("List messages started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listMessagesSchema.validate(queryParams);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { channelId, page, limit, before, after, search } = value;

    // Get channel and verify access
    const channel = await db.readItem('channels', channelId, channelId);
    if (!channel) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Channel not found" }
      }, request);
    }

    // Check if user is a participant
    const isParticipant = (channel as any).participants.includes(user.id);
    if (!isParticipant) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to channel" }
      }, request);
    }

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.channelId = @channelId AND c.deletedAt IS NULL';
    const parameters: any[] = [channelId];

    if (before) {
      queryText += ' AND c.sentAt < (SELECT VALUE m.sentAt FROM messages m WHERE m.id = @before)[0]';
      parameters.push(before);
    }

    if (after) {
      queryText += ' AND c.sentAt > (SELECT VALUE m.sentAt FROM messages m WHERE m.id = @after)[0]';
      parameters.push(after);
    }

    if (search) {
      queryText += ' AND CONTAINS(LOWER(c.content), LOWER(@search))';
      parameters.push(search);
    }

    // Add ordering
    queryText += ' ORDER BY c.sentAt DESC';

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const messages = await db.queryItems('messages', paginatedQuery, parameters);

    // Enrich messages with sender information
    const enrichedMessages = await Promise.all(
      messages.map(async (message: any) => {
        let senderName = 'System';
        let senderAvatar = null;

        if (message.senderId && message.senderId !== 'system') {
          try {
            const sender = await db.readItem('users', message.senderId, message.senderId);
            if (sender) {
              senderName = (sender as any).name || (sender as any).email;
              senderAvatar = (sender as any).avatar;
            }
          } catch (error) {
            // Sender might not exist
          }
        }

        return {
          ...message,
          senderName,
          senderAvatar
        };
      })
    );

    // Update user's last read timestamp
    try {
      const membershipQuery = 'SELECT * FROM c WHERE c.channelId = @channelId AND c.userId = @userId';
      const memberships = await db.queryItems('channel-members', membershipQuery, [channelId, user.id]);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        const updatedMembership = {
          ...membership,
          id: membership.id,
          lastReadAt: new Date().toISOString()
        };
        await db.updateItem('channel-members', updatedMembership);
      }
    } catch (error) {
      // Non-critical error
    }

    logger.info("Messages listed successfully", {
      correlationId,
      channelId,
      userId: user.id,
      count: messages.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        channelId,
        messages: enrichedMessages.reverse(), // Return in chronological order
        hasMore: messages.length === limit,
        page,
        limit
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List messages failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Enhanced broadcast message to channel participants
 */
async function broadcastMessage(channel: any, message: any, sender: any): Promise<void> {
  const startTime = Date.now();

  try {
    logger.info("Broadcasting message to channel participants", {
      channelId: channel.id,
      messageId: message.id,
      participantCount: channel.participants.length
    });

    // Broadcast to SignalR group using enhanced shared service
    const groupName = `channel-${channel.id}`;
    await signalREnhanced.sendToGroup(groupName, {
      target: 'ReceiveMessage',
      arguments: [{
        id: message.id,
        channelId: channel.id,
        content: message.content,
        type: message.type,
        senderId: message.senderId,
        senderName: sender.name || sender.email,
        sentAt: message.sentAt,
        mentions: message.mentions || [],
        attachments: message.attachments || [],
        parentMessageId: message.parentMessageId,
        metadata: message.metadata || {}
      }]
    });

    // Send push notifications to offline participants
    for (const participantId of channel.participants) {
      if (participantId !== sender.id) {
        await sendPushNotification(participantId, {
          title: `New message in ${channel.name}`,
          body: `${sender.name || sender.email}: ${message.content.substring(0, 100)}`,
          data: {
            channelId: channel.id,
            messageId: message.id,
            type: 'new_message'
          }
        });
      }
    }

    // Publish metrics event using Event Grid
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'messaging/broadcast',
      {
        service: 'real-time-messaging',
        eventType: 'message_broadcast',
        channelId: channel.id,
        messageId: message.id,
        participantCount: channel.participants.length,
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      }
    );

  } catch (error) {
    logger.error("Error broadcasting message", {
      channelId: channel.id,
      messageId: message.id,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Register functions
app.http('message-send', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'messages',
  handler: sendMessage
});

app.http('channel-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'channels',
  handler: createChannel
});

app.http('message-list', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'channels/{channelId}/messages',
  handler: listMessages
});

/**
 * Enhanced SignalR negotiate function using shared service
 * Provides SignalR connection info for clients with enhanced features
 */
async function signalRNegotiate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const userId = authResult.user?.id;
    if (!userId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'User ID required' }
      }, request);
    }

    // Generate SignalR connection info
    const hubName = process.env.SIGNALR_HUB_NAME || 'hepztech';
    const connectionString = process.env.SIGNALR_CONNECTION_STRING;

    if (!connectionString) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'SignalR not configured' }
      }, request);
    }

    // Extract endpoint and access key from connection string
    const endpointMatch = connectionString.match(/Endpoint=([^;]+)/);
    const keyMatch = connectionString.match(/AccessKey=([^;]+)/);

    if (!endpointMatch || !keyMatch) {
      return addCorsHeaders({
        status: 500,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Invalid SignalR connection string' }
      }, request);
    }

    const endpoint = endpointMatch[1];
    const accessKey = keyMatch[1];

    // Generate access token for user
    const token = generateSignalRAccessToken(userId, hubName, accessKey);
    const connectionId = `conn-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;

    const connectionInfo = {
      url: `${endpoint}/client/?hub=${hubName}`,
      accessToken: token,
      connectionId
    };

    // Register connection with enhanced shared service
    await signalREnhanced.registerConnection(connectionId, userId, {
      endpoint,
      hubName,
      userAgent: request.headers.get('user-agent') || 'unknown',
      ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
    });

    // Publish connection event
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'signalr/connection',
      {
        service: 'signalr',
        eventType: 'connection_negotiated',
        connectionId,
        userId,
        endpoint,
        timestamp: new Date().toISOString()
      }
    );

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: connectionInfo
    }, request);

  } catch (error) {
    logger.error('SignalR negotiate failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to negotiate SignalR connection' }
    }, request);
  }
}

/**
 * SignalR broadcast function
 * Broadcasts messages to SignalR clients
 */
async function signalRBroadcast(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const { target, message, userId, groupName, connectionId } = body;

    if (!target || !message) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Target and message are required' }
      }, request);
    }

    // Prepare SignalR message
    const signalRMessage = {
      target,
      arguments: [message]
    };

    // Determine broadcast scope using enhanced shared service
    let broadcastResult;
    if (connectionId) {
      // Send to specific connection (not directly supported, send to user instead)
      const connection = signalREnhanced.getConnection(connectionId);
      if (connection) {
        broadcastResult = await signalREnhanced.sendToUser(connection.userId, signalRMessage);
      } else {
        broadcastResult = { success: false, error: 'Connection not found' };
      }
    } else if (userId) {
      // Send to specific user
      broadcastResult = await signalREnhanced.sendToUser(userId, signalRMessage);
    } else if (groupName) {
      // Send to group
      broadcastResult = await signalREnhanced.sendToGroup(groupName, signalRMessage);
    } else {
      // Broadcast to all
      broadcastResult = await signalREnhanced.broadcast(signalRMessage);
    }

    // Log broadcast
    await db.createItem('signalr-broadcasts', {
      id: `broadcast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      target,
      message,
      userId,
      groupName,
      connectionId,
      sentBy: authResult.user?.id,
      sentAt: new Date().toISOString(),
      result: broadcastResult
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: 'Broadcast sent successfully',
        result: broadcastResult
      }
    }, request);

  } catch (error) {
    logger.error('SignalR broadcast failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to broadcast message' }
    }, request);
  }
}

/**
 * SignalR group management function
 * Manages SignalR groups (join/leave)
 */
async function signalRGroupManagement(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  try {
    // Authenticate request
    const authResult = await authenticateRequest(request);
    if (!authResult.success) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized' }
      }, request);
    }

    const body = await request.json() as any;
    const { action, groupName, connectionId, userId } = body;

    if (!action || !groupName || (!connectionId && !userId)) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Action, groupName, and connectionId or userId are required' }
      }, request);
    }

    // Perform group action using enhanced shared service
    let result;
    switch (action.toLowerCase()) {
      case 'join':
        if (connectionId) {
          result = await signalREnhanced.addToGroup(connectionId, groupName);
        } else {
          // For user-based group management, we need to find their connections
          const userConnections = signalREnhanced.getUserConnections(userId);
          if (userConnections.length > 0) {
            // Add all user connections to the group
            const results = await Promise.all(
              userConnections.map(conn => signalREnhanced.addToGroup(conn.connectionId, groupName))
            );
            result = { success: results.every(r => r), action: 'added', groupName, userId };
          } else {
            result = { success: false, error: 'No active connections for user' };
          }
        }
        break;
      case 'leave':
        if (connectionId) {
          result = await signalREnhanced.removeFromGroup(connectionId, groupName);
        } else {
          // For user-based group management, remove all user connections from group
          const userConnections = signalREnhanced.getUserConnections(userId);
          if (userConnections.length > 0) {
            const results = await Promise.all(
              userConnections.map(conn => signalREnhanced.removeFromGroup(conn.connectionId, groupName))
            );
            result = { success: results.every(r => r), action: 'removed', groupName, userId };
          } else {
            result = { success: false, error: 'No active connections for user' };
          }
        }
        break;
      default:
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: 'Invalid action. Use "join" or "leave"' }
        }, request);
    }

    // Log group management
    await db.createItem('signalr-group-management', {
      id: `group-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
      action,
      groupName,
      connectionId,
      userId,
      managedBy: authResult.user?.id,
      managedAt: new Date().toISOString(),
      result
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        message: `Successfully ${action}ed ${connectionId ? 'connection' : 'user'} ${action === 'join' ? 'to' : 'from'} group`,
        result
      }
    }, request);

  } catch (error) {
    logger.error('SignalR group management failed', {
      error: error instanceof Error ? error.message : String(error)
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Failed to manage group membership' }
    }, request);
  }
}

// Helper functions for SignalR operations
function generateSignalRAccessToken(userId: string, hubName: string, accessKey: string): string {
  try {
    // Production JWT token generation for Azure SignalR Service
    const jwt = require('jsonwebtoken');

    const now = Math.floor(Date.now() / 1000);
    const payload = {
      aud: `https://${process.env.SIGNALR_HUB_NAME}.service.signalr.net/client/?hub=${hubName}`,
      iss: null,
      exp: now + 3600, // 1 hour expiration
      iat: now,
      nbf: now,
      sub: userId,
      role: ['webpubsub.sendToGroup', 'webpubsub.joinLeaveGroup']
    };

    // Sign JWT with SignalR access key
    const token = jwt.sign(payload, accessKey, {
      algorithm: 'HS256',
      header: {
        typ: 'JWT',
        alg: 'HS256'
      }
    });

    logger.info('SignalR access token generated', {
      userId,
      hubName,
      expiresAt: new Date((now + 3600) * 1000).toISOString()
    });

    return token;
  } catch (error) {
    logger.error('Failed to generate SignalR access token', {
      error: error instanceof Error ? error.message : String(error),
      userId,
      hubName
    });
    throw new Error('Failed to generate SignalR access token');
  }
}

/**
 * Enhanced SignalR helper functions
 */

// Note: Connection registration is now handled by the enhanced shared SignalR service

async function sendPushNotification(userId: string, notification: any): Promise<void> {
  try {
    // Store notification for later delivery
    await db.createItem('push-notifications', {
      id: `push-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      userId,
      title: notification.title,
      body: notification.body,
      data: notification.data,
      createdAt: new Date().toISOString(),
      delivered: false
    });

    logger.debug('Push notification queued', { userId, title: notification.title });
  } catch (error) {
    logger.error('Failed to queue push notification', {
      userId,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function checkUserOnlineStatus(userId: string): Promise<boolean> {
  try {
    // Check if user has any active SignalR connections using enhanced service
    const userConnections = signalREnhanced.getUserConnections(userId);
    return userConnections.length > 0;
  } catch (error) {
    logger.error('Failed to check user online status', {
      userId,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Get SignalR metrics from enhanced shared service
 */
export function getSignalRMetrics() {
  return signalREnhanced.getMetrics();
}

// Register SignalR functions
app.http('signalr-negotiate', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'signalr/negotiate',
  handler: signalRNegotiate
});

app.http('signalr-broadcast', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'signalr/broadcast',
  handler: signalRBroadcast
});

app.http('signalr-groups', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'signalr/groups',
  handler: signalRGroupManagement
});
