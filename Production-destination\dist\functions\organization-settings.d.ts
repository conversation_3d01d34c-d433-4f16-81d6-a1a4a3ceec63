/**
 * Organization Settings Function
 * Handles organization settings, configuration, and policy management
 * Migrated from old-arch/src/organization-service/settings/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get organization settings handler
 */
export declare function getOrganizationSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update organization settings handler
 */
export declare function updateOrganizationSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
