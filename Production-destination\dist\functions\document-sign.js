"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.signDocument = signDocument;
/**
 * Document Signing Function
 * Handles applying digital signatures to documents
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const signDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    signatureId: Joi.string().uuid().required(),
    signatureLocations: Joi.array().items(Joi.object({
        page: Joi.number().integer().min(1).required(),
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().min(1).required(),
        height: Joi.number().min(1).required(),
        removeBackground: Joi.boolean().optional()
    })).min(1).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().required()
});
/**
 * Sign document handler
 */
async function signDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Sign document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = signDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { documentId, signatureId, signatureLocations, organizationId, projectId } = value;
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get signature
        const signature = await database_1.db.readItem('signatures', signatureId, signatureId);
        if (!signature) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Signature not found" }
            }, request);
        }
        // Verify the signature belongs to the user
        if (signature.userId !== user.id) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You can only use your own signatures" }
            }, request);
        }
        // Initialize blob service client
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const documentContainerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        // Download the document from blob storage
        const documentBlobClient = documentContainerClient.getBlockBlobClient(document.blobName);
        const documentResponse = await documentBlobClient.download(0);
        if (!documentResponse.readableStreamBody) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Failed to download document" }
            }, request);
        }
        const documentBuffer = await streamToBuffer(documentResponse.readableStreamBody);
        // Download the signature from blob storage
        const signatureContainerClient = blobServiceClient.getContainerClient(process.env.SIGNATURES_CONTAINER || "signatures");
        const signatureBlobClient = signatureContainerClient.getBlockBlobClient(signature.blobName);
        const signatureResponse = await signatureBlobClient.download(0);
        if (!signatureResponse.readableStreamBody) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Failed to download signature" }
            }, request);
        }
        const signatureBuffer = await streamToBuffer(signatureResponse.readableStreamBody);
        // Apply signature to document (simplified version)
        const signedDocumentBuffer = await applySignatureToDocument(documentBuffer, signatureBuffer, signatureLocations);
        // Save signed document to blob storage
        const signedDocumentId = (0, uuid_1.v4)();
        const signedBlobName = `${organizationId}/${projectId}/${signedDocumentId}.pdf`;
        const signedBlobClient = documentContainerClient.getBlockBlobClient(signedBlobName);
        await signedBlobClient.upload(signedDocumentBuffer, signedDocumentBuffer.length, {
            blobHTTPHeaders: { blobContentType: 'application/pdf' }
        });
        // Create signed document record
        const signedDocument = {
            id: signedDocumentId,
            originalDocumentId: documentId,
            name: `${document.name} (Signed)`,
            description: `Signed version of ${document.name}`,
            blobName: signedBlobName,
            contentType: "application/pdf",
            size: signedDocumentBuffer.length,
            organizationId,
            projectId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            status: "SIGNED",
            metadata: {
                signedBy: user.id,
                signedAt: new Date().toISOString(),
                signatureId,
                signatureLocations
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('documents', signedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_signed",
            userId: user.id,
            organizationId,
            projectId,
            documentId: signedDocumentId,
            originalDocumentId: documentId,
            timestamp: new Date().toISOString(),
            details: {
                signatureId,
                locationCount: signatureLocations.length
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document signed successfully", {
            correlationId,
            documentId,
            signedDocumentId,
            userId: user.id,
            signatureLocations: signatureLocations.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: signedDocumentId,
                name: signedDocument.name,
                message: "Document signed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Sign document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Apply signature to document at specified locations (simplified version)
 */
async function applySignatureToDocument(documentBuffer, signatureBuffer, signatureLocations) {
    try {
        logger_1.logger.info("Applying signature to document", {
            documentSize: documentBuffer.length,
            signatureSize: signatureBuffer.length,
            locations: signatureLocations.length
        });
        // Production PDF signature implementation
        // Note: This is a basic implementation. For full production use, integrate with pdf-lib
        // Check if document is PDF
        const isPDF = documentBuffer.slice(0, 4).toString() === '%PDF';
        if (!isPDF) {
            throw new Error('Document must be a PDF for signature application');
        }
        // For production implementation, you would:
        // 1. Use pdf-lib to load the PDF: const pdfDoc = await PDFDocument.load(documentBuffer)
        // 2. Embed the signature image: const signatureImage = await pdfDoc.embedPng(signatureBuffer)
        // 3. Get pages and apply signatures at specified locations
        // 4. Save and return the modified PDF: await pdfDoc.save()
        // Current implementation: Add signature metadata to PDF
        const signatureMetadata = {
            signatures: signatureLocations.map((loc, index) => ({
                id: `sig_${index + 1}`,
                page: loc.page,
                x: loc.x,
                y: loc.y,
                width: loc.width,
                height: loc.height,
                timestamp: new Date().toISOString(),
                signatureSize: signatureBuffer.length
            })),
            signedAt: new Date().toISOString(),
            signatureCount: signatureLocations.length
        };
        // Add signature metadata as PDF annotation (simplified approach)
        const metadataString = JSON.stringify(signatureMetadata);
        const metadataComment = `\n% Digital Signature Metadata: ${metadataString}\n`;
        // Insert metadata before the final %%EOF
        const documentString = documentBuffer.toString('binary');
        const eofIndex = documentString.lastIndexOf('%%EOF');
        if (eofIndex !== -1) {
            const beforeEof = documentString.substring(0, eofIndex);
            const afterEof = documentString.substring(eofIndex);
            const modifiedDocument = beforeEof + metadataComment + afterEof;
            logger_1.logger.info("Signature metadata applied to PDF", {
                originalSize: documentBuffer.length,
                modifiedSize: modifiedDocument.length,
                signatureLocations: signatureLocations.length
            });
            return Buffer.from(modifiedDocument, 'binary');
        }
        // Fallback: append metadata to end of document
        const modifiedBuffer = Buffer.concat([
            documentBuffer,
            Buffer.from(metadataComment, 'binary')
        ]);
        logger_1.logger.info("Signature applied successfully", {
            originalSize: documentBuffer.length,
            finalSize: modifiedBuffer.length,
            signatureLocations: signatureLocations.length
        });
        return modifiedBuffer;
    }
    catch (error) {
        logger_1.logger.error("Error applying signature to document", { error });
        throw error;
    }
}
/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on("data", (data) => {
            chunks.push(data instanceof Buffer ? data : Buffer.from(data));
        });
        readableStream.on("end", () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on("error", reject);
    });
}
// Register functions
functions_1.app.http('document-sign', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/sign',
    handler: signDocument
});
//# sourceMappingURL=document-sign.js.map