"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.inviteOrganizationMember = inviteOrganizationMember;
/**
 * Organization Members Invite Function
 * Handles inviting new members to organizations
 * Migrated from old-arch/src/organization-service/members/invite/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Organization roles
var OrganizationRole;
(function (OrganizationRole) {
    OrganizationRole["OWNER"] = "OWNER";
    OrganizationRole["ADMIN"] = "ADMIN";
    OrganizationRole["MEMBER"] = "MEMBER";
    OrganizationRole["VIEWER"] = "VIEWER";
    OrganizationRole["GUEST"] = "GUEST";
})(OrganizationRole || (OrganizationRole = {}));
// Validation schema
const inviteMemberSchema = Joi.object({
    email: Joi.string().email().required(),
    role: Joi.string().valid(...Object.values(OrganizationRole)).default(OrganizationRole.MEMBER),
    permissions: Joi.array().items(Joi.string()).optional(),
    message: Joi.string().max(500).optional(),
    expiresInDays: Joi.number().min(1).max(30).default(7)
});
/**
 * Invite organization member handler
 */
async function inviteOrganizationMember(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const organizationId = request.params.organizationId;
    logger_1.logger.info("Invite organization member started", { correlationId, organizationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!organizationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization ID is required" }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = inviteMemberSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const inviteRequest = value;
        // Get organization
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user has permission to invite members
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        const userMembership = memberships[0];
        const canInvite = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';
        if (!canInvite) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to invite members" }
            }, request);
        }
        // Check if user is already a member or has pending invitation
        const existingMemberQuery = 'SELECT * FROM c WHERE c.email = @email AND c.organizationId = @orgId';
        const existingMembers = await database_1.db.queryItems('organization-members', existingMemberQuery, [inviteRequest.email, organizationId]);
        if (existingMembers.length > 0) {
            const existingMember = existingMembers[0];
            if (existingMember.status === 'ACTIVE') {
                return (0, cors_1.addCorsHeaders)({
                    status: 409,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "User is already a member of this organization" }
                }, request);
            }
        }
        const existingInviteQuery = 'SELECT * FROM c WHERE c.email = @email AND c.organizationId = @orgId AND c.status = @status';
        const existingInvites = await database_1.db.queryItems('organization-invitations', existingInviteQuery, [inviteRequest.email, organizationId, 'PENDING']);
        if (existingInvites.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User already has a pending invitation to this organization" }
            }, request);
        }
        // Check organization member limits
        const orgData = organization;
        if (orgData.billing?.limits?.maxActiveUsers && orgData.billing.limits.maxActiveUsers > 0) {
            const currentMemberCount = await getCurrentMemberCount(organizationId);
            if (currentMemberCount >= orgData.billing.limits.maxActiveUsers) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: "Member limit reached for this organization tier",
                        limit: orgData.billing.limits.maxActiveUsers,
                        current: currentMemberCount
                    }
                }, request);
            }
        }
        // Create invitation
        const invitationId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const expiresAt = new Date(Date.now() + inviteRequest.expiresInDays * 24 * 60 * 60 * 1000).toISOString();
        const invitation = {
            id: invitationId,
            organizationId,
            email: inviteRequest.email,
            role: inviteRequest.role,
            permissions: inviteRequest.permissions || [],
            invitedBy: user.id,
            invitedAt: now,
            expiresAt,
            status: 'PENDING',
            message: inviteRequest.message,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('organization-invitations', invitation);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "organization_member_invited",
            userId: user.id,
            organizationId,
            timestamp: now,
            details: {
                invitedEmail: inviteRequest.email,
                role: inviteRequest.role,
                invitationId,
                organizationName: orgData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'OrganizationMemberInvited',
            aggregateId: organizationId,
            aggregateType: 'Organization',
            version: 1,
            data: {
                invitation,
                invitedBy: user.id,
                organizationName: orgData.name
            },
            userId: user.id,
            organizationId,
            tenantId: user.tenantId
        });
        // Send invitation notification (email would be sent here in production)
        const inviteUrl = `${process.env.FRONTEND_BASE_URL || 'https://app.docucontext.com'}/invitations/${invitationId}`;
        // For now, we'll create an in-app notification for the inviter
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'ORGANIZATION_INVITATION_SENT',
            title: 'Invitation sent successfully',
            message: `Invitation sent to ${inviteRequest.email} to join ${orgData.name} as ${inviteRequest.role}.`,
            priority: 'normal',
            metadata: {
                invitationId,
                invitedEmail: inviteRequest.email,
                organizationId,
                organizationName: orgData.name,
                role: inviteRequest.role,
                inviteUrl
            },
            organizationId
        });
        logger_1.logger.info("Organization member invitation created successfully", {
            correlationId,
            organizationId,
            invitationId,
            invitedEmail: inviteRequest.email,
            role: inviteRequest.role,
            invitedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                invitationId,
                email: inviteRequest.email,
                role: inviteRequest.role,
                organizationId,
                organizationName: orgData.name,
                expiresAt,
                inviteUrl,
                message: "Invitation sent successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Invite organization member failed", {
            correlationId,
            organizationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get current member count for organization
 */
async function getCurrentMemberCount(organizationId) {
    try {
        const memberQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
        const result = await database_1.db.queryItems('organization-members', memberQuery, [organizationId, 'ACTIVE']);
        return Number(result[0]) || 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to get member count', { error, organizationId });
        return 0;
    }
}
// Register functions
functions_1.app.http('organization-members-invite', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/{organizationId}/members/invite',
    handler: inviteOrganizationMember
});
//# sourceMappingURL=organization-members-invite.js.map