/**
 * Metrics Collection Function
 * Handles comprehensive metrics collection and aggregation
 * Migrated from old-arch/src/monitoring-service/metrics/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Collect metrics handler
 */
export declare function collectMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Query metrics handler
 */
export declare function queryMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get metrics summary handler
 */
export declare function getMetricsSummary(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
