"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.archiveDocument = archiveDocument;
exports.restoreDocument = restoreDocument;
/**
 * Document Archiving Function
 * Handles document archiving, retention, and lifecycle management
 * Migrated from old-arch/src/document-service/archiving/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Archiving types and enums
var ArchiveStatus;
(function (ArchiveStatus) {
    ArchiveStatus["ACTIVE"] = "ACTIVE";
    ArchiveStatus["ARCHIVED"] = "ARCHIVED";
    ArchiveStatus["PENDING_DELETION"] = "PENDING_DELETION";
    ArchiveStatus["DELETED"] = "DELETED";
})(ArchiveStatus || (ArchiveStatus = {}));
var RetentionPolicy;
(function (RetentionPolicy) {
    RetentionPolicy["DAYS_30"] = "DAYS_30";
    RetentionPolicy["DAYS_90"] = "DAYS_90";
    RetentionPolicy["DAYS_365"] = "DAYS_365";
    RetentionPolicy["YEARS_7"] = "YEARS_7";
    RetentionPolicy["PERMANENT"] = "PERMANENT";
    RetentionPolicy["CUSTOM"] = "CUSTOM";
})(RetentionPolicy || (RetentionPolicy = {}));
// Validation schemas
const archiveDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    retentionPolicy: Joi.string().valid(...Object.values(RetentionPolicy)).default(RetentionPolicy.YEARS_7),
    customRetentionDays: Joi.number().min(1).max(3650).optional(),
    reason: Joi.string().max(500).optional(),
    preserveMetadata: Joi.boolean().default(true),
    compressContent: Joi.boolean().default(true)
});
const restoreDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    reason: Joi.string().max(500).optional(),
    restoreToProject: Joi.string().uuid().optional()
});
/**
 * Archive document handler
 */
async function archiveDocument(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Archive document started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        const { error, value } = archiveDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const archiveRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', archiveRequest.documentId, archiveRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Check if document is already archived
        if (documentData.status === 'ARCHIVED') {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document is already archived" }
            }, request);
        }
        // Calculate expiration date
        const expiresAt = calculateExpirationDate(archiveRequest.retentionPolicy || RetentionPolicy.YEARS_7, archiveRequest.customRetentionDays);
        // Create archive record
        const archiveId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const documentArchive = {
            id: archiveId,
            documentId: archiveRequest.documentId,
            originalDocument: documentData,
            archiveStatus: ArchiveStatus.ARCHIVED,
            retentionPolicy: archiveRequest.retentionPolicy || RetentionPolicy.YEARS_7,
            archivedAt: now,
            expiresAt,
            reason: archiveRequest.reason,
            metadata: {
                originalSize: documentData.size || 0,
                preserveMetadata: archiveRequest.preserveMetadata !== false,
                compressContent: archiveRequest.compressContent !== false,
                checksumOriginal: generateChecksum(documentData)
            },
            archivedBy: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId || user.id
        };
        // Process archiving
        const archiveResult = await processDocumentArchiving(documentArchive);
        documentArchive.metadata.compressedSize = archiveResult.compressedSize;
        documentArchive.metadata.checksumArchived = archiveResult.checksum;
        await database_1.db.createItem('document-archives', documentArchive);
        // Update document status
        const updatedDocument = {
            ...documentData,
            id: documentData.id,
            status: 'ARCHIVED',
            archivedAt: now,
            archivedBy: user.id,
            archiveId,
            updatedAt: now
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_archived",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: archiveRequest.documentId,
            timestamp: now,
            details: {
                archiveId,
                retentionPolicy: documentArchive.retentionPolicy,
                expiresAt,
                reason: archiveRequest.reason,
                originalSize: documentArchive.metadata.originalSize,
                compressedSize: documentArchive.metadata.compressedSize
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentArchived',
            aggregateId: archiveRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: updatedDocument,
                archive: documentArchive,
                archivedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document archived successfully", {
            correlationId,
            documentId: archiveRequest.documentId,
            archiveId,
            retentionPolicy: documentArchive.retentionPolicy,
            expiresAt,
            archivedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                archiveId,
                documentId: archiveRequest.documentId,
                status: ArchiveStatus.ARCHIVED,
                retentionPolicy: documentArchive.retentionPolicy,
                archivedAt: now,
                expiresAt,
                compressionRatio: documentArchive.metadata.compressedSize ?
                    (documentArchive.metadata.originalSize / documentArchive.metadata.compressedSize).toFixed(2) : null,
                message: "Document archived successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Archive document failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Restore document handler
 */
async function restoreDocument(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Restore document started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        const { error, value } = restoreDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const restoreRequest = value;
        // Get archived document
        const document = await database_1.db.readItem('documents', restoreRequest.documentId, restoreRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        if (documentData.status !== 'ARCHIVED') {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document is not archived" }
            }, request);
        }
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Get archive record
        const archive = await database_1.db.readItem('document-archives', documentData.archiveId, documentData.archiveId);
        if (!archive) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Archive record not found" }
            }, request);
        }
        const archiveData = archive;
        // Process restoration
        const restoreResult = await processDocumentRestoration(archiveData);
        // Update document status
        const now = new Date().toISOString();
        const restoredDocument = {
            ...documentData,
            id: documentData.id,
            status: 'ACTIVE',
            restoredAt: now,
            restoredBy: user.id,
            projectId: restoreRequest.restoreToProject || documentData.projectId,
            updatedAt: now
        };
        await database_1.db.updateItem('documents', restoredDocument);
        // Update archive status
        const updatedArchive = {
            ...archiveData,
            id: archiveData.id,
            archiveStatus: ArchiveStatus.ACTIVE,
            restoredAt: now,
            restoredBy: user.id
        };
        await database_1.db.updateItem('document-archives', updatedArchive);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_restored",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: restoredDocument.projectId,
            documentId: restoreRequest.documentId,
            timestamp: now,
            details: {
                archiveId: documentData.archiveId,
                reason: restoreRequest.reason,
                restoredToProject: restoreRequest.restoreToProject,
                originalArchivedAt: archiveData.archivedAt
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentRestored',
            aggregateId: restoreRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: restoredDocument,
                archive: updatedArchive,
                restoredBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document restored successfully", {
            correlationId,
            documentId: restoreRequest.documentId,
            archiveId: documentData.archiveId,
            restoredBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: restoreRequest.documentId,
                status: 'ACTIVE',
                restoredAt: now,
                projectId: restoredDocument.projectId,
                message: "Document restored successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Restore document failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        if (document.createdBy === userId)
            return true;
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
function calculateExpirationDate(retentionPolicy, customDays) {
    const now = new Date();
    let expirationDate;
    switch (retentionPolicy) {
        case RetentionPolicy.DAYS_30:
            expirationDate = new Date(now.getTime() + 30 * 24 * 60 * 60 * 1000);
            break;
        case RetentionPolicy.DAYS_90:
            expirationDate = new Date(now.getTime() + 90 * 24 * 60 * 60 * 1000);
            break;
        case RetentionPolicy.DAYS_365:
            expirationDate = new Date(now.getTime() + 365 * 24 * 60 * 60 * 1000);
            break;
        case RetentionPolicy.YEARS_7:
            expirationDate = new Date(now.getTime() + 7 * 365 * 24 * 60 * 60 * 1000);
            break;
        case RetentionPolicy.CUSTOM:
            if (!customDays)
                throw new Error('Custom retention days required for CUSTOM policy');
            expirationDate = new Date(now.getTime() + customDays * 24 * 60 * 60 * 1000);
            break;
        case RetentionPolicy.PERMANENT:
            return undefined;
        default:
            expirationDate = new Date(now.getTime() + 7 * 365 * 24 * 60 * 60 * 1000);
    }
    return expirationDate.toISOString();
}
function generateChecksum(document) {
    // Simplified checksum generation
    const crypto = require('crypto');
    const content = JSON.stringify(document);
    return crypto.createHash('sha256').update(content).digest('hex');
}
async function processDocumentArchiving(archive) {
    try {
        // Simulate compression and archiving process
        const originalSize = archive.metadata.originalSize;
        const compressionRatio = archive.metadata.compressContent ? 0.7 : 1.0;
        const compressedSize = Math.floor(originalSize * compressionRatio);
        // Generate checksum for archived content
        const checksum = generateChecksum(archive.originalDocument);
        logger_1.logger.info('Document archiving processed', {
            archiveId: archive.id,
            originalSize,
            compressedSize,
            compressionRatio: (originalSize / compressedSize).toFixed(2)
        });
        return {
            compressedSize,
            checksum,
            success: true
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to process document archiving', { error, archiveId: archive.id });
        throw error;
    }
}
async function processDocumentRestoration(archive) {
    try {
        // Simulate restoration process
        logger_1.logger.info('Document restoration processed', {
            archiveId: archive.id,
            documentId: archive.documentId
        });
        return {
            success: true,
            restoredSize: archive.metadata.originalSize
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to process document restoration', { error, archiveId: archive.id });
        throw error;
    }
}
// Register functions
functions_1.app.http('document-archive', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/archive',
    handler: archiveDocument
});
functions_1.app.http('document-restore', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/restore',
    handler: restoreDocument
});
//# sourceMappingURL=document-archiving.js.map