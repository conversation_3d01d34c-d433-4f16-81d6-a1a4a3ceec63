/**
 * Event Service
 * Handles domain events and event publishing/subscribing
 */
export interface DomainEvent {
    id: string;
    type: string;
    aggregateId: string;
    aggregateType: string;
    version: number;
    data: any;
    metadata?: any;
    timestamp: string;
    userId?: string;
    organizationId?: string;
    tenantId?: string;
}
export interface EventHandler {
    eventType: string;
    handle(event: DomainEvent): Promise<void>;
}
export interface EventSubscription {
    id: string;
    eventType: string;
    handler: EventHandler;
    isActive: boolean;
}
export declare class EventService {
    private handlers;
    private subscriptions;
    /**
     * Publish a domain event
     */
    publishEvent(event: Omit<DomainEvent, 'id' | 'timestamp'>): Promise<string>;
    /**
     * Subscribe to events
     */
    subscribe(eventType: string, handler: EventHandler): string;
    /**
     * Unsubscribe from events
     */
    unsubscribe(subscriptionId: string): boolean;
    /**
     * Store event in event store
     */
    private storeEvent;
    /**
     * Process event by calling all registered handlers
     */
    private processEvent;
    /**
     * Get events for an aggregate
     */
    getEventsForAggregate(aggregateId: string, aggregateType: string): Promise<DomainEvent[]>;
    /**
     * Get events by type
     */
    getEventsByType(eventType: string, limit?: number): Promise<DomainEvent[]>;
    /**
     * Replay events for an aggregate
     */
    replayEvents(aggregateId: string, aggregateType: string, fromVersion?: number): Promise<void>;
}
export declare class DocumentEventService {
    private eventService;
    constructor(eventService: EventService);
    documentCreated(documentId: string, document: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
    documentUpdated(documentId: string, changes: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
    documentDeleted(documentId: string, userId: string, organizationId: string, tenantId: string): Promise<void>;
    documentShared(documentId: string, shareDetails: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
    documentSigned(documentId: string, signatureDetails: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
}
export declare class WorkflowEventService {
    private eventService;
    constructor(eventService: EventService);
    workflowCreated(workflowId: string, workflow: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
    workflowStarted(workflowId: string, executionId: string, userId: string, organizationId: string, tenantId: string): Promise<void>;
    workflowCompleted(workflowId: string, executionId: string, result: any, userId: string, organizationId: string, tenantId: string): Promise<void>;
}
export declare const eventService: EventService;
export declare const documentEventService: DocumentEventService;
export declare const workflowEventService: WorkflowEventService;
