/**
 * Compliance Management Function
 * Handles compliance requirements, auditing, and regulatory compliance
 * Migrated from old-arch/src/compliance-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create compliance assessment handler
 */
export declare function createComplianceAssessment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update compliance status handler
 */
export declare function updateComplianceStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
