/**
 * Permission Management Function
 * Handles advanced permission operations like granting, revoking, and checking permissions
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Grant permission handler
 */
export declare function grantPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Check permission handler
 */
export declare function checkPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Batch check permissions handler
 */
export declare function batchCheckPermissions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
