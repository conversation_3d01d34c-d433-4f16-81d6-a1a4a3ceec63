"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTenant = createTenant;
exports.getTenant = getTenant;
/**
 * Tenant Management Function
 * Handles multi-tenant operations and tenant isolation
 * Migrated from old-arch/src/tenant-service/management/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Tenant types and enums
var TenantStatus;
(function (TenantStatus) {
    TenantStatus["ACTIVE"] = "ACTIVE";
    TenantStatus["INACTIVE"] = "INACTIVE";
    TenantStatus["SUSPENDED"] = "SUSPENDED";
    TenantStatus["MIGRATING"] = "MIGRATING";
    TenantStatus["ARCHIVED"] = "ARCHIVED";
})(TenantStatus || (TenantStatus = {}));
var TenantTier;
(function (TenantTier) {
    TenantTier["FREE"] = "FREE";
    TenantTier["PROFESSIONAL"] = "PROFESSIONAL";
    TenantTier["ENTERPRISE"] = "ENTERPRISE";
    TenantTier["CUSTOM"] = "CUSTOM";
})(TenantTier || (TenantTier = {}));
// Validation schemas
const createTenantSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    tier: Joi.string().valid(...Object.values(TenantTier)).default(TenantTier.FREE),
    organizationId: Joi.string().uuid().required(),
    settings: Joi.object({
        dataRegion: Joi.string().valid('us-east', 'us-west', 'eu-west', 'asia-pacific').default('us-east'),
        encryptionEnabled: Joi.boolean().default(true),
        backupEnabled: Joi.boolean().default(true),
        auditLoggingEnabled: Joi.boolean().default(true),
        customDomain: Joi.string().domain().optional(),
        ssoEnabled: Joi.boolean().default(false),
        apiAccessEnabled: Joi.boolean().default(true)
    }).optional(),
    limits: Joi.object({
        maxUsers: Joi.number().min(1).optional(),
        maxDocuments: Joi.number().min(1).optional(),
        maxStorage: Joi.number().min(1).optional(), // GB
        maxApiCalls: Joi.number().min(1).optional(),
        maxProjects: Joi.number().min(1).optional()
    }).optional()
});
const updateTenantSchema = Joi.object({
    tenantId: Joi.string().uuid().required(),
    name: Joi.string().min(2).max(100).optional(),
    description: Joi.string().max(500).optional(),
    status: Joi.string().valid(...Object.values(TenantStatus)).optional(),
    tier: Joi.string().valid(...Object.values(TenantTier)).optional(),
    settings: Joi.object({
        dataRegion: Joi.string().valid('us-east', 'us-west', 'eu-west', 'asia-pacific').optional(),
        encryptionEnabled: Joi.boolean().optional(),
        backupEnabled: Joi.boolean().optional(),
        auditLoggingEnabled: Joi.boolean().optional(),
        customDomain: Joi.string().domain().optional(),
        ssoEnabled: Joi.boolean().optional(),
        apiAccessEnabled: Joi.boolean().optional()
    }).optional(),
    limits: Joi.object({
        maxUsers: Joi.number().min(1).optional(),
        maxDocuments: Joi.number().min(1).optional(),
        maxStorage: Joi.number().min(1).optional(),
        maxApiCalls: Joi.number().min(1).optional(),
        maxProjects: Joi.number().min(1).optional()
    }).optional()
});
/**
 * Create tenant handler
 */
async function createTenant(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create tenant started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkTenantAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to tenant management" }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = createTenantSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const tenantRequest = value;
        // Check organization exists
        const organization = await database_1.db.readItem('organizations', tenantRequest.organizationId, tenantRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check tenant creation limits
        const canCreate = await checkTenantLimits();
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Create tenant
        const tenantId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const tenant = {
            id: tenantId,
            name: tenantRequest.name,
            description: tenantRequest.description,
            status: TenantStatus.ACTIVE,
            tier: tenantRequest.tier || TenantTier.FREE,
            organizationId: tenantRequest.organizationId,
            settings: {
                dataRegion: 'us-east',
                encryptionEnabled: true,
                backupEnabled: true,
                auditLoggingEnabled: true,
                ssoEnabled: false,
                apiAccessEnabled: true,
                ...tenantRequest.settings
            },
            limits: getTierLimits(tenantRequest.tier || TenantTier.FREE, tenantRequest.limits),
            usage: {
                currentUsers: 0,
                currentDocuments: 0,
                currentStorage: 0,
                currentApiCalls: 0,
                currentProjects: 0,
                lastUpdated: now
            },
            isolation: {
                databasePartition: `tenant_${tenantId}`,
                storageContainer: `tenant-${tenantId}`,
                cacheNamespace: `tenant:${tenantId}`,
                logStream: `tenant-${tenantId}`
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now
        };
        await database_1.db.createItem('tenants', tenant);
        // Initialize tenant infrastructure
        await initializeTenantInfrastructure(tenant);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "tenant_created",
            userId: user.id,
            organizationId: tenantRequest.organizationId,
            timestamp: now,
            details: {
                tenantId,
                tenantName: tenantRequest.name,
                tier: tenant.tier,
                dataRegion: tenant.settings.dataRegion
            },
            tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'TenantCreated',
            aggregateId: tenantId,
            aggregateType: 'Tenant',
            version: 1,
            data: {
                tenant,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: tenantRequest.organizationId,
            tenantId
        });
        logger_1.logger.info("Tenant created successfully", {
            correlationId,
            tenantId,
            tenantName: tenantRequest.name,
            tier: tenant.tier,
            organizationId: tenantRequest.organizationId,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: tenantId,
                name: tenantRequest.name,
                status: TenantStatus.ACTIVE,
                tier: tenant.tier,
                organizationId: tenantRequest.organizationId,
                settings: tenant.settings,
                limits: tenant.limits,
                isolation: tenant.isolation,
                createdAt: now,
                message: "Tenant created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create tenant failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get tenant handler
 */
async function getTenant(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const tenantId = request.params.tenantId;
    logger_1.logger.info("Get tenant started", { correlationId, tenantId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!tenantId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Tenant ID is required" }
            }, request);
        }
        // Check tenant access
        const hasAccess = await checkTenantAccess(user, tenantId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to tenant" }
            }, request);
        }
        // Get tenant
        const tenant = await database_1.db.readItem('tenants', tenantId, tenantId);
        if (!tenant) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Tenant not found" }
            }, request);
        }
        const tenantData = tenant;
        // Update usage statistics
        const updatedUsage = await updateTenantUsage(tenantData);
        logger_1.logger.info("Tenant retrieved successfully", {
            correlationId,
            tenantId,
            tenantName: tenantData.name,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                tenant: {
                    ...tenantData,
                    usage: updatedUsage
                },
                retrievedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get tenant failed", {
            correlationId,
            tenantId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkTenantAccess(user, tenantId) {
    try {
        // Check if user has admin or tenant management role
        if (user.roles?.includes('admin') || user.roles?.includes('tenant_admin')) {
            return true;
        }
        // If specific tenant, check if user has access to that tenant
        if (tenantId) {
            return user.tenantId === tenantId;
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check tenant access', { error, userId: user.id, tenantId });
        return false;
    }
}
async function checkTenantLimits() {
    try {
        // Check total tenant count (simplified)
        const tenantCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.status = @active';
        const tenantCountResult = await database_1.db.queryItems('tenants', tenantCountQuery, [TenantStatus.ACTIVE]);
        const tenantCount = Number(tenantCountResult[0]) || 0;
        const maxTenants = 1000; // Configurable limit
        if (tenantCount >= maxTenants) {
            return {
                allowed: false,
                reason: `Maximum tenant limit reached (${maxTenants})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check tenant limits', { error });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function getTierLimits(tier, customLimits) {
    const baseLimits = {
        [TenantTier.FREE]: {
            maxUsers: 3,
            maxDocuments: 100,
            maxStorage: 1, // GB
            maxApiCalls: 1000,
            maxProjects: 3
        },
        [TenantTier.PROFESSIONAL]: {
            maxUsers: 25,
            maxDocuments: 10000,
            maxStorage: 100,
            maxApiCalls: 50000,
            maxProjects: 50
        },
        [TenantTier.ENTERPRISE]: {
            maxUsers: -1, // Unlimited
            maxDocuments: -1,
            maxStorage: -1,
            maxApiCalls: -1,
            maxProjects: -1
        }
    };
    const limits = baseLimits[tier] || baseLimits[TenantTier.FREE];
    return { ...limits, ...customLimits };
}
async function initializeTenantInfrastructure(tenant) {
    try {
        // Initialize tenant-specific infrastructure
        logger_1.logger.info('Initializing tenant infrastructure', {
            tenantId: tenant.id,
            dataRegion: tenant.settings.dataRegion,
            storageContainer: tenant.isolation.storageContainer
        });
        // In production, this would:
        // 1. Create tenant-specific database partitions
        // 2. Set up storage containers
        // 3. Configure cache namespaces
        // 4. Initialize logging streams
        // 5. Set up monitoring and alerting
    }
    catch (error) {
        logger_1.logger.error('Failed to initialize tenant infrastructure', { error, tenantId: tenant.id });
    }
}
async function updateTenantUsage(tenant) {
    try {
        // Get current usage statistics for the tenant
        const usageStats = await getCurrentTenantUsage(tenant.id);
        const updatedUsage = {
            currentUsers: usageStats.userCount || 0,
            currentDocuments: usageStats.documentCount || 0,
            currentStorage: usageStats.storageUsed || 0,
            currentApiCalls: usageStats.apiCallsThisMonth || 0,
            currentProjects: usageStats.projectCount || 0,
            lastUpdated: new Date().toISOString()
        };
        // Update tenant with new usage
        const updatedTenant = {
            ...tenant,
            id: tenant.id,
            usage: updatedUsage,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('tenants', updatedTenant);
        return updatedUsage;
    }
    catch (error) {
        logger_1.logger.error('Failed to update tenant usage', { error, tenantId: tenant.id });
        return tenant.usage;
    }
}
async function getCurrentTenantUsage(tenantId) {
    try {
        // Get usage statistics from various collections
        const userCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.tenantId = @tenantId';
        const userCount = await database_1.db.queryItems('users', userCountQuery, [tenantId]);
        const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.tenantId = @tenantId';
        const documentCount = await database_1.db.queryItems('documents', documentCountQuery, [tenantId]);
        const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.tenantId = @tenantId';
        const projectCount = await database_1.db.queryItems('projects', projectCountQuery, [tenantId]);
        return {
            userCount: Number(userCount[0]) || 0,
            documentCount: Number(documentCount[0]) || 0,
            projectCount: Number(projectCount[0]) || 0,
            storageUsed: 0, // Simplified - would calculate actual storage usage
            apiCallsThisMonth: 0 // Simplified - would get from API usage tracking
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to get current tenant usage', { error, tenantId });
        return {
            userCount: 0,
            documentCount: 0,
            projectCount: 0,
            storageUsed: 0,
            apiCallsThisMonth: 0
        };
    }
}
// Register functions
functions_1.app.http('tenant-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'tenants',
    handler: createTenant
});
functions_1.app.http('tenant-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'tenants/{tenantId}',
    handler: getTenant
});
//# sourceMappingURL=tenant-management.js.map