/**
 * Feature Flags Function
 * Handles feature flag management and evaluation
 * Migrated from old-arch/src/admin-service/feature-flags/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create feature flag handler
 */
export declare function createFeatureFlag(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Evaluate feature flag handler
 */
export declare function evaluateFeatureFlag(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
