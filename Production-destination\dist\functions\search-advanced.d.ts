/**
 * Advanced Search Function
 * Handles advanced search capabilities with filtering, faceting, and analytics
 * Migrated from old-arch/src/search-service/advanced/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Advanced search handler
 */
export declare function advancedSearch(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
