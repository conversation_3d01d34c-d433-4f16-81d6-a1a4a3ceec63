/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */
import { HttpRequest } from '@azure/functions';
export interface UserContext {
    id: string;
    email: string;
    name?: string;
    tenantId?: string;
    organizationId?: string;
    roles?: string[];
    permissions?: string[];
}
export interface AuthResult {
    success: boolean;
    user?: UserContext;
    error?: string;
}
/**
 * Extract and validate JW<PERSON> token from request
 */
export declare function extractToken(request: HttpRequest): string | null;
/**
 * Validate JWT token and extract user context
 */
export declare function validateToken(token: string): Promise<AuthResult>;
/**
 * Authenticate request and extract user context
 */
export declare function authenticateRequest(request: HttpRequest): Promise<AuthResult>;
/**
 * Check if user has required role
 */
export declare function hasRole(user: UserContext, requiredRole: string): boolean;
/**
 * Check if user has required permission
 */
export declare function hasPermission(user: User<PERSON>ontex<PERSON>, requiredPermission: string): boolean;
/**
 * Create authentication middleware
 */
export declare function requireAuth(handler: (request: HttpRequest, context: any, user: UserContext) => Promise<any>): (request: HttpRequest, context: any) => Promise<any>;
