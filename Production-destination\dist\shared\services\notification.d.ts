/**
 * Notification Service
 * Handles sending notifications via various channels (in-app, email, push)
 */
export interface NotificationRequest {
    userId: string;
    type: string;
    title: string;
    message: string;
    resourceId?: string;
    resourceType?: string;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    metadata?: any;
    channels?: NotificationChannelType[];
    organizationId?: string;
    projectId?: string;
}
export declare enum NotificationChannelType {
    IN_APP = "in_app",
    EMAIL = "email",
    PUSH = "push",
    WEBHOOK = "webhook",
    SLACK = "slack"
}
export interface NotificationChannel {
    type: 'inApp' | 'email' | 'push' | 'webhook';
    enabled: boolean;
    configuration?: any;
}
export interface InAppNotificationRequest {
    userId: string;
    type: string;
    title: string;
    message: string;
    resourceId?: string;
    resourceType?: string;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    metadata?: any;
}
export interface EmailNotificationRequest {
    to: string;
    subject: string;
    body: string;
    template?: string;
    templateData?: any;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
}
export interface PushNotificationRequest {
    userId: string;
    title: string;
    body: string;
    data?: any;
    badge?: number;
    sound?: string;
}
export declare class NotificationService {
    /**
     * Send a notification through multiple channels
     */
    sendNotification(request: NotificationRequest): Promise<{
        success: boolean;
        notificationId?: string;
        channels: {
            type: string;
            success: boolean;
            error?: string;
        }[];
    }>;
    /**
     * Send in-app notification
     */
    sendInAppNotification(request: InAppNotificationRequest): Promise<{
        success: boolean;
        notificationId: string;
    }>;
    /**
     * Send email notification (simplified implementation)
     */
    sendEmailNotification(request: EmailNotificationRequest): Promise<{
        success: boolean;
        messageId?: string;
    }>;
    /**
     * Send push notification (simplified implementation)
     */
    sendPushNotification(request: PushNotificationRequest): Promise<{
        success: boolean;
        messageId?: string;
    }>;
    /**
     * Send webhook notification
     */
    sendWebhookNotification(request: NotificationRequest, webhookConfig: any): Promise<{
        success: boolean;
        responseStatus?: number;
    }>;
    /**
     * Get user notification preferences
     */
    getUserNotificationPreferences(userId: string): Promise<{
        inApp: boolean;
        email: boolean;
        push: boolean;
        channels: NotificationChannel[];
    }>;
    /**
     * Mark notification as read
     */
    markNotificationAsRead(notificationId: string, userId: string): Promise<boolean>;
    /**
     * Get user notifications with pagination
     */
    getUserNotifications(userId: string, options?: {
        page?: number;
        limit?: number;
        status?: 'unread' | 'read' | 'all';
        type?: string;
    }): Promise<{
        notifications: any[];
        total: number;
        unreadCount: number;
        page: number;
        limit: number;
        hasMore: boolean;
    }>;
}
export declare const notificationService: NotificationService;
