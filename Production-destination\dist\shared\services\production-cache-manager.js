"use strict";
/**
 * Production Cache Manager
 * Implements production-standard cache invalidation without relying on Redis pattern operations
 * Uses application-level tracking for better performance and reliability
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.productionCacheManager = exports.ProductionCacheManager = void 0;
const redis_1 = require("./redis");
const logger_1 = require("../utils/logger");
class ProductionCacheManager {
    constructor() {
        this.keyRegistry = new Map();
        this.tagToKeys = new Map();
        this.invalidationRules = new Map();
        this.setupDefaultRules();
    }
    static getInstance() {
        if (!ProductionCacheManager.instance) {
            ProductionCacheManager.instance = new ProductionCacheManager();
        }
        return ProductionCacheManager.instance;
    }
    /**
     * Setup default cache invalidation rules
     */
    setupDefaultRules() {
        // Document-related cache invalidation
        this.addInvalidationRule('document-update', {
            pattern: 'document:*',
            tags: ['document', 'content', 'collaboration'],
            relatedKeys: ['session:*', 'bi_report:*']
        });
        // User-related cache invalidation
        this.addInvalidationRule('user-update', {
            pattern: 'user:*',
            tags: ['user', 'activity', 'session'],
            relatedKeys: ['device:*', 'user_advanced_roles:*']
        });
        // Session-related cache invalidation
        this.addInvalidationRule('session-update', {
            pattern: 'session:*',
            tags: ['session', 'collaboration'],
            relatedKeys: ['document:*:content']
        });
        // Configuration cache invalidation
        this.addInvalidationRule('config-update', {
            pattern: 'config:*',
            tags: ['configuration', 'system'],
            relatedKeys: ['feature_flag:*']
        });
    }
    /**
     * Add cache invalidation rule
     */
    addInvalidationRule(ruleId, rule) {
        this.invalidationRules.set(ruleId, rule);
        logger_1.logger.debug('Cache invalidation rule added', { ruleId, rule });
    }
    /**
     * Register a cache key with tags for tracking
     */
    async registerKey(key, tags, ttl) {
        const cacheKey = {
            key,
            tags,
            ttl,
            createdAt: new Date()
        };
        this.keyRegistry.set(key, cacheKey);
        // Update tag-to-keys mapping
        for (const tag of tags) {
            if (!this.tagToKeys.has(tag)) {
                this.tagToKeys.set(tag, new Set());
            }
            this.tagToKeys.get(tag).add(key);
        }
        logger_1.logger.debug('Cache key registered', { key, tags, ttl });
    }
    /**
     * Set cache value with automatic registration
     */
    async set(key, value, ttl = 3600, tags = []) {
        try {
            // Register the key for tracking
            await this.registerKey(key, tags, ttl);
            // Set the value in Redis
            const success = await redis_1.redis.setJson(key, value, ttl);
            if (success) {
                logger_1.logger.debug('Cache value set and registered', { key, tags, ttl });
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error('Failed to set cache value', {
                error: error instanceof Error ? error.message : String(error),
                key
            });
            return false;
        }
    }
    /**
     * Get cache value
     */
    async get(key) {
        try {
            return await redis_1.redis.getJson(key);
        }
        catch (error) {
            logger_1.logger.error('Failed to get cache value', {
                error: error instanceof Error ? error.message : String(error),
                key
            });
            return null;
        }
    }
    /**
     * Delete cache key and unregister
     */
    async delete(key) {
        try {
            // Remove from Redis
            const success = await redis_1.redis.delete(key);
            // Unregister from tracking
            this.unregisterKey(key);
            return success;
        }
        catch (error) {
            logger_1.logger.error('Failed to delete cache key', {
                error: error instanceof Error ? error.message : String(error),
                key
            });
            return false;
        }
    }
    /**
     * Invalidate cache by tags (production-safe approach)
     */
    async invalidateByTags(tags) {
        try {
            const keysToDelete = new Set();
            // Find all keys associated with the given tags
            for (const tag of tags) {
                const tagKeys = this.tagToKeys.get(tag);
                if (tagKeys) {
                    tagKeys.forEach(key => keysToDelete.add(key));
                }
            }
            // Delete the keys
            const deletionPromises = Array.from(keysToDelete).map(key => this.delete(key));
            await Promise.all(deletionPromises);
            logger_1.logger.info('Cache invalidated by tags', {
                tags,
                keysDeleted: keysToDelete.size
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate cache by tags', {
                error: error instanceof Error ? error.message : String(error),
                tags
            });
        }
    }
    /**
     * Invalidate cache by rule ID
     */
    async invalidateByRule(ruleId, context = {}) {
        try {
            const rule = this.invalidationRules.get(ruleId);
            if (!rule) {
                logger_1.logger.warn('Cache invalidation rule not found', { ruleId });
                return;
            }
            // Invalidate by tags
            await this.invalidateByTags(rule.tags);
            // Handle related keys with context substitution
            for (const relatedPattern of rule.relatedKeys) {
                await this.invalidateRelatedKeys(relatedPattern, context);
            }
            logger_1.logger.info('Cache invalidated by rule', { ruleId, context });
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate cache by rule', {
                error: error instanceof Error ? error.message : String(error),
                ruleId,
                context
            });
        }
    }
    /**
     * Invalidate related keys with context substitution
     */
    async invalidateRelatedKeys(pattern, context) {
        try {
            // Find keys that match the pattern with context substitution
            const keysToDelete = [];
            for (const [key, cacheKey] of this.keyRegistry) {
                if (this.matchesPattern(key, pattern, context)) {
                    keysToDelete.push(key);
                }
            }
            // Delete the matching keys
            const deletionPromises = keysToDelete.map(key => this.delete(key));
            await Promise.all(deletionPromises);
            logger_1.logger.debug('Related keys invalidated', {
                pattern,
                context,
                keysDeleted: keysToDelete.length
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate related keys', {
                error: error instanceof Error ? error.message : String(error),
                pattern,
                context
            });
        }
    }
    /**
     * Check if a key matches a pattern with context substitution
     */
    matchesPattern(key, pattern, context) {
        try {
            // Simple pattern matching with context substitution
            let processedPattern = pattern;
            // Replace context variables in pattern
            for (const [contextKey, contextValue] of Object.entries(context)) {
                processedPattern = processedPattern.replace(`{${contextKey}}`, String(contextValue));
            }
            // Convert pattern to regex
            const regexPattern = processedPattern
                .replace(/\*/g, '.*')
                .replace(/\?/g, '.');
            const regex = new RegExp(`^${regexPattern}$`);
            return regex.test(key);
        }
        catch (error) {
            logger_1.logger.error('Pattern matching failed', { key, pattern, context, error });
            return false;
        }
    }
    /**
     * Unregister a key from tracking
     */
    unregisterKey(key) {
        const cacheKey = this.keyRegistry.get(key);
        if (cacheKey) {
            // Remove from tag mappings
            for (const tag of cacheKey.tags) {
                const tagKeys = this.tagToKeys.get(tag);
                if (tagKeys) {
                    tagKeys.delete(key);
                    if (tagKeys.size === 0) {
                        this.tagToKeys.delete(tag);
                    }
                }
            }
            // Remove from registry
            this.keyRegistry.delete(key);
        }
    }
    /**
     * Clean up expired keys from registry
     */
    async cleanupExpiredKeys() {
        try {
            const now = new Date();
            const expiredKeys = [];
            for (const [key, cacheKey] of this.keyRegistry) {
                if (cacheKey.ttl) {
                    const expiryTime = new Date(cacheKey.createdAt.getTime() + (cacheKey.ttl * 1000));
                    if (now > expiryTime) {
                        expiredKeys.push(key);
                    }
                }
            }
            // Remove expired keys from registry
            for (const key of expiredKeys) {
                this.unregisterKey(key);
            }
            logger_1.logger.debug('Expired keys cleaned up', { count: expiredKeys.length });
        }
        catch (error) {
            logger_1.logger.error('Failed to cleanup expired keys', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    /**
     * Get cache statistics
     */
    getStatistics() {
        const keysByTag = {};
        for (const [tag, keys] of this.tagToKeys) {
            keysByTag[tag] = keys.size;
        }
        return {
            totalKeys: this.keyRegistry.size,
            totalTags: this.tagToKeys.size,
            totalRules: this.invalidationRules.size,
            keysByTag
        };
    }
}
exports.ProductionCacheManager = ProductionCacheManager;
// Export singleton instance
exports.productionCacheManager = ProductionCacheManager.getInstance();
exports.default = exports.productionCacheManager;
//# sourceMappingURL=production-cache-manager.js.map