"use strict";
/**
 * Workflow Models and Interfaces
 * Defines the structure for workflow-related data models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ConditionOperator = exports.WorkflowTriggerType = exports.WorkflowStepStatus = exports.WorkflowStepType = exports.WorkflowPriority = exports.WorkflowStatus = void 0;
var WorkflowStatus;
(function (WorkflowStatus) {
    WorkflowStatus["DRAFT"] = "DRAFT";
    WorkflowStatus["ACTIVE"] = "ACTIVE";
    WorkflowStatus["RUNNING"] = "RUNNING";
    WorkflowStatus["PAUSED"] = "PAUSED";
    WorkflowStatus["COMPLETED"] = "COMPLETED";
    WorkflowStatus["CANCELLED"] = "CANCELLED";
    WorkflowStatus["ERROR"] = "ERROR";
    WorkflowStatus["ARCHIVED"] = "ARCHIVED";
})(WorkflowStatus || (exports.WorkflowStatus = WorkflowStatus = {}));
var WorkflowPriority;
(function (WorkflowPriority) {
    WorkflowPriority["LOW"] = "LOW";
    WorkflowPriority["NORMAL"] = "NORMAL";
    WorkflowPriority["HIGH"] = "HIGH";
    WorkflowPriority["URGENT"] = "URGENT";
})(WorkflowPriority || (exports.WorkflowPriority = WorkflowPriority = {}));
var WorkflowStepType;
(function (WorkflowStepType) {
    WorkflowStepType["MANUAL"] = "MANUAL";
    WorkflowStepType["AUTOMATED"] = "AUTOMATED";
    WorkflowStepType["APPROVAL"] = "APPROVAL";
    WorkflowStepType["REVIEW"] = "REVIEW";
    WorkflowStepType["NOTIFICATION"] = "NOTIFICATION";
    WorkflowStepType["CONDITION"] = "CONDITION";
    WorkflowStepType["PARALLEL"] = "PARALLEL";
    WorkflowStepType["SEQUENTIAL"] = "SEQUENTIAL";
    WorkflowStepType["LOOP"] = "LOOP";
    WorkflowStepType["WEBHOOK"] = "WEBHOOK";
    WorkflowStepType["API_CALL"] = "API_CALL";
    WorkflowStepType["DOCUMENT_PROCESSING"] = "DOCUMENT_PROCESSING";
    WorkflowStepType["AI_ANALYSIS"] = "AI_ANALYSIS";
})(WorkflowStepType || (exports.WorkflowStepType = WorkflowStepType = {}));
var WorkflowStepStatus;
(function (WorkflowStepStatus) {
    WorkflowStepStatus["PENDING"] = "PENDING";
    WorkflowStepStatus["RUNNING"] = "RUNNING";
    WorkflowStepStatus["COMPLETED"] = "COMPLETED";
    WorkflowStepStatus["FAILED"] = "FAILED";
    WorkflowStepStatus["SKIPPED"] = "SKIPPED";
    WorkflowStepStatus["CANCELLED"] = "CANCELLED";
    WorkflowStepStatus["WAITING_FOR_APPROVAL"] = "WAITING_FOR_APPROVAL";
})(WorkflowStepStatus || (exports.WorkflowStepStatus = WorkflowStepStatus = {}));
var WorkflowTriggerType;
(function (WorkflowTriggerType) {
    WorkflowTriggerType["MANUAL"] = "MANUAL";
    WorkflowTriggerType["SCHEDULED"] = "SCHEDULED";
    WorkflowTriggerType["EVENT"] = "EVENT";
    WorkflowTriggerType["WEBHOOK"] = "WEBHOOK";
    WorkflowTriggerType["DOCUMENT_UPLOAD"] = "DOCUMENT_UPLOAD";
    WorkflowTriggerType["DOCUMENT_PROCESSED"] = "DOCUMENT_PROCESSED";
    WorkflowTriggerType["USER_ACTION"] = "USER_ACTION";
    WorkflowTriggerType["API_CALL"] = "API_CALL";
})(WorkflowTriggerType || (exports.WorkflowTriggerType = WorkflowTriggerType = {}));
var ConditionOperator;
(function (ConditionOperator) {
    ConditionOperator["EQUALS"] = "EQUALS";
    ConditionOperator["NOT_EQUALS"] = "NOT_EQUALS";
    ConditionOperator["GREATER_THAN"] = "GREATER_THAN";
    ConditionOperator["LESS_THAN"] = "LESS_THAN";
    ConditionOperator["GREATER_THAN_OR_EQUAL"] = "GREATER_THAN_OR_EQUAL";
    ConditionOperator["LESS_THAN_OR_EQUAL"] = "LESS_THAN_OR_EQUAL";
    ConditionOperator["CONTAINS"] = "CONTAINS";
    ConditionOperator["NOT_CONTAINS"] = "NOT_CONTAINS";
    ConditionOperator["STARTS_WITH"] = "STARTS_WITH";
    ConditionOperator["ENDS_WITH"] = "ENDS_WITH";
    ConditionOperator["IN"] = "IN";
    ConditionOperator["NOT_IN"] = "NOT_IN";
    ConditionOperator["IS_NULL"] = "IS_NULL";
    ConditionOperator["IS_NOT_NULL"] = "IS_NOT_NULL";
})(ConditionOperator || (exports.ConditionOperator = ConditionOperator = {}));
//# sourceMappingURL=workflow.js.map