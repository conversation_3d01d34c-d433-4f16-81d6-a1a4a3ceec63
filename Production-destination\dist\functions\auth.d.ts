/**
 * Authentication Functions
 * Handles user login, token validation, and user sync operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * User login handler
 */
export declare function userLogin(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get current user info handler
 */
export declare function getCurrentUser(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
