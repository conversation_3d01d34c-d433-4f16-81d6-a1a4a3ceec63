"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.performComprehensiveAnalysis = performComprehensiveAnalysis;
/**
 * Document Intelligence Function
 * Handles comprehensive document analysis with advanced AI features
 * Migrated from old-arch/src/ai-service/models/document-intelligence.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Document intelligence types and enums
var AnalysisFeature;
(function (AnalysisFeature) {
    AnalysisFeature["KEY_VALUE_PAIRS"] = "keyValuePairs";
    AnalysisFeature["TABLES"] = "tables";
    AnalysisFeature["ENTITIES"] = "entities";
    AnalysisFeature["STYLES"] = "styles";
    AnalysisFeature["LANGUAGES"] = "languages";
    AnalysisFeature["BARCODES"] = "barcodes";
    AnalysisFeature["FORMULAS"] = "formulas";
    AnalysisFeature["FONTS"] = "fonts";
    AnalysisFeature["PARAGRAPHS"] = "paragraphs";
    AnalysisFeature["SECTIONS"] = "sections";
    AnalysisFeature["FIGURES"] = "figures";
    AnalysisFeature["SIGNATURES"] = "signatures";
    AnalysisFeature["LAYOUT"] = "layout";
    AnalysisFeature["METADATA"] = "metadata";
})(AnalysisFeature || (AnalysisFeature = {}));
var DocumentType;
(function (DocumentType) {
    DocumentType["INVOICE"] = "invoice";
    DocumentType["RECEIPT"] = "receipt";
    DocumentType["CONTRACT"] = "contract";
    DocumentType["BUSINESS_CARD"] = "businessCard";
    DocumentType["ID_DOCUMENT"] = "idDocument";
    DocumentType["TAX_DOCUMENT"] = "tax";
    DocumentType["HEALTH_INSURANCE"] = "healthInsurance";
    DocumentType["FINANCIAL_STATEMENT"] = "financialStatement";
    DocumentType["LEGAL_DOCUMENT"] = "legalDocument";
    DocumentType["GENERAL"] = "general";
})(DocumentType || (DocumentType = {}));
var ConfidenceLevel;
(function (ConfidenceLevel) {
    ConfidenceLevel["LOW"] = "LOW";
    ConfidenceLevel["MEDIUM"] = "MEDIUM";
    ConfidenceLevel["HIGH"] = "HIGH";
    ConfidenceLevel["VERY_HIGH"] = "VERY_HIGH";
})(ConfidenceLevel || (ConfidenceLevel = {}));
// Validation schemas
const comprehensiveAnalysisSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    documentType: Joi.string().valid(...Object.values(DocumentType)).default(DocumentType.GENERAL),
    features: Joi.array().items(Joi.string().valid(...Object.values(AnalysisFeature))).default([
        AnalysisFeature.KEY_VALUE_PAIRS,
        AnalysisFeature.TABLES,
        AnalysisFeature.ENTITIES,
        AnalysisFeature.LAYOUT
    ]),
    options: Joi.object({
        extractTables: Joi.boolean().default(true),
        extractKeyValuePairs: Joi.boolean().default(true),
        extractEntities: Joi.boolean().default(true),
        extractBarcodes: Joi.boolean().default(false),
        extractFormulas: Joi.boolean().default(false),
        extractSignatures: Joi.boolean().default(false),
        analyzeLayout: Joi.boolean().default(true),
        detectLanguages: Joi.boolean().default(true),
        extractMetadata: Joi.boolean().default(true),
        enableBusinessIntelligence: Joi.boolean().default(false),
        customModels: Joi.array().items(Joi.string()).optional()
    }).optional(),
    businessRules: Joi.object({
        requiredFields: Joi.array().items(Joi.string()).optional(),
        validationRules: Joi.array().items(Joi.object()).optional(),
        complianceChecks: Joi.array().items(Joi.string()).optional()
    }).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional()
});
/**
 * Comprehensive document analysis handler
 */
async function performComprehensiveAnalysis(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Comprehensive document analysis started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        const { error, value } = comprehensiveAnalysisSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const analysisRequest = value;
        // Check document access
        const document = await database_1.db.readItem('documents', analysisRequest.documentId, analysisRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(analysisRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Perform comprehensive analysis
        const analysisResult = await executeComprehensiveAnalysis(analysisRequest, documentData, user.id);
        // Store analysis result
        await storeAnalysisResult(analysisResult);
        const duration = Date.now() - startTime;
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_intelligence_analysis",
            userId: user.id,
            organizationId: analysisRequest.organizationId,
            projectId: analysisRequest.projectId,
            documentId: analysisRequest.documentId,
            timestamp: new Date().toISOString(),
            details: {
                analysisId: analysisResult.id,
                documentType: analysisRequest.documentType,
                featuresAnalyzed: analysisResult.analysisFeatures,
                confidenceScore: analysisResult.performance.confidenceScore,
                processingTime: duration
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentIntelligenceCompleted',
            aggregateId: analysisRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                analysisResult: {
                    ...analysisResult,
                    results: {
                        ...analysisResult.results,
                        keyValuePairs: analysisResult.results.keyValuePairs.slice(0, 10) // Limit data in event
                    }
                },
                analyzedBy: user.id
            },
            userId: user.id,
            organizationId: analysisRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Comprehensive document analysis completed", {
            correlationId,
            analysisId: analysisResult.id,
            documentId: analysisRequest.documentId,
            documentType: analysisRequest.documentType,
            featuresAnalyzed: analysisResult.analysisFeatures.length,
            confidenceScore: analysisResult.performance.confidenceScore,
            duration,
            analyzedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                analysisResult,
                processingTime: duration,
                message: "Comprehensive document analysis completed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Comprehensive document analysis failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function executeComprehensiveAnalysis(request, document, userId) {
    const analysisId = (0, uuid_1.v4)();
    const startTime = Date.now();
    try {
        // Simulate comprehensive document analysis
        const analysisResult = {
            id: analysisId,
            documentId: request.documentId,
            documentType: request.documentType || DocumentType.GENERAL,
            analysisFeatures: request.features || [
                AnalysisFeature.KEY_VALUE_PAIRS,
                AnalysisFeature.TABLES,
                AnalysisFeature.ENTITIES,
                AnalysisFeature.LAYOUT
            ],
            results: {
                keyValuePairs: await extractKeyValuePairs(document, request.options),
                tables: await extractTables(document, request.options),
                entities: await extractEntities(document, request.options),
                languages: await detectLanguages(document, request.options),
                barcodes: await extractBarcodes(document, request.options),
                formulas: await extractFormulas(document, request.options),
                signatures: await extractSignatures(document, request.options),
                layout: await analyzeLayout(document, request.options),
                metadata: await extractMetadata(document, request.options)
            },
            businessIntelligence: request.options?.enableBusinessIntelligence ?
                await generateBusinessIntelligence(document, request) : undefined,
            performance: {
                processingTime: Date.now() - startTime,
                modelVersion: '2024.1.0',
                confidenceScore: Math.random() * 0.3 + 0.7, // 0.7 to 1.0
                qualityScore: Math.random() * 0.2 + 0.8 // 0.8 to 1.0
            },
            createdAt: new Date().toISOString(),
            organizationId: request.organizationId,
            tenantId: request.organizationId
        };
        return analysisResult;
    }
    catch (error) {
        logger_1.logger.error('Failed to execute comprehensive analysis', { error, analysisId, documentId: request.documentId });
        throw error;
    }
}
async function extractKeyValuePairs(document, options) {
    // Mock key-value pair extraction
    const mockPairs = [
        { key: 'Invoice Number', value: 'INV-2024-001', confidence: 0.95 },
        { key: 'Date', value: '2024-01-15', confidence: 0.92 },
        { key: 'Total Amount', value: '$1,250.00', confidence: 0.98 },
        { key: 'Customer Name', value: 'Acme Corporation', confidence: 0.89 },
        { key: 'Due Date', value: '2024-02-15', confidence: 0.91 }
    ];
    return mockPairs.map(pair => ({
        ...pair,
        boundingBox: {
            x: Math.random() * 500,
            y: Math.random() * 700,
            width: Math.random() * 200 + 100,
            height: 20
        }
    }));
}
async function extractTables(document, options) {
    // Mock table extraction
    return [
        {
            id: (0, uuid_1.v4)(),
            rows: 5,
            columns: 4,
            cells: [
                { rowIndex: 0, columnIndex: 0, text: 'Item', confidence: 0.95, isHeader: true },
                { rowIndex: 0, columnIndex: 1, text: 'Quantity', confidence: 0.93, isHeader: true },
                { rowIndex: 0, columnIndex: 2, text: 'Price', confidence: 0.94, isHeader: true },
                { rowIndex: 0, columnIndex: 3, text: 'Total', confidence: 0.96, isHeader: true },
                { rowIndex: 1, columnIndex: 0, text: 'Widget A', confidence: 0.91 },
                { rowIndex: 1, columnIndex: 1, text: '10', confidence: 0.98 },
                { rowIndex: 1, columnIndex: 2, text: '$25.00', confidence: 0.94 },
                { rowIndex: 1, columnIndex: 3, text: '$250.00', confidence: 0.97 }
            ],
            confidence: 0.94
        }
    ];
}
async function extractEntities(document, options) {
    // Mock entity extraction
    return [
        { type: 'PERSON', text: 'John Smith', confidence: 0.92, category: 'Person', boundingBox: {} },
        { type: 'ORGANIZATION', text: 'Acme Corporation', confidence: 0.89, category: 'Organization', boundingBox: {} },
        { type: 'DATE', text: '2024-01-15', confidence: 0.95, category: 'DateTime', boundingBox: {} },
        { type: 'MONEY', text: '$1,250.00', confidence: 0.97, category: 'Quantity', subcategory: 'Currency', boundingBox: {} },
        { type: 'EMAIL', text: '<EMAIL>', confidence: 0.93, category: 'PersonType', subcategory: 'Email', boundingBox: {} }
    ];
}
async function detectLanguages(document, options) {
    return [
        { language: 'en', confidence: 0.98, regions: [{ pageNumber: 1, boundingBox: {} }] },
        { language: 'es', confidence: 0.15, regions: [{ pageNumber: 1, boundingBox: {} }] }
    ];
}
async function extractBarcodes(document, options) {
    if (!options?.extractBarcodes)
        return [];
    return [
        { type: 'QR', value: 'https://example.com/invoice/123', confidence: 0.96, boundingBox: {} },
        { type: 'Code128', value: '1234567890', confidence: 0.94, boundingBox: {} }
    ];
}
async function extractFormulas(document, options) {
    if (!options?.extractFormulas)
        return [];
    return [
        { formula: 'SUM(B2:B10)', result: '1250.00', confidence: 0.91, boundingBox: {} },
        { formula: 'B2*C2', result: '250.00', confidence: 0.89, boundingBox: {} }
    ];
}
async function extractSignatures(document, options) {
    if (!options?.extractSignatures)
        return [];
    return [
        { type: 'handwritten', confidence: 0.87, boundingBox: {}, verified: false },
        { type: 'digital', confidence: 0.95, boundingBox: {}, verified: true }
    ];
}
async function analyzeLayout(document, options) {
    return {
        pages: [
            {
                pageNumber: 1,
                width: 612,
                height: 792,
                angle: 0,
                unit: 'pixel',
                lines: [],
                words: [],
                paragraphs: [],
                sections: []
            }
        ]
    };
}
async function extractMetadata(document, options) {
    return {
        pageCount: 1,
        documentSize: document.size || 0,
        creationDate: document.createdAt,
        modificationDate: document.updatedAt,
        author: document.createdBy,
        title: document.name,
        subject: document.description,
        keywords: document.tags || []
    };
}
async function generateBusinessIntelligence(document, request) {
    return {
        insights: [
            {
                type: 'data_quality',
                message: 'Document contains high-quality structured data suitable for automation',
                confidence: 0.92,
                impact: 'high',
                actionable: true
            },
            {
                type: 'compliance',
                message: 'Document meets standard invoice formatting requirements',
                confidence: 0.89,
                impact: 'medium',
                actionable: false
            }
        ],
        dataQuality: {
            completeness: 95,
            accuracy: 92,
            consistency: 88,
            validity: 94
        },
        complianceStatus: {
            overall: 'COMPLIANT',
            checks: [
                { rule: 'Required fields present', status: 'PASS', message: 'All mandatory fields detected' },
                { rule: 'Date format validation', status: 'PASS', message: 'Dates in correct format' },
                { rule: 'Amount validation', status: 'WARNING', message: 'Currency symbol inconsistent' }
            ]
        },
        recommendations: [
            {
                type: 'automation',
                priority: 'high',
                description: 'Consider automating data extraction for similar documents',
                estimatedImpact: '40% time savings'
            },
            {
                type: 'quality',
                priority: 'medium',
                description: 'Standardize currency formatting for better accuracy',
                estimatedImpact: '15% accuracy improvement'
            }
        ]
    };
}
async function storeAnalysisResult(result) {
    try {
        await database_1.db.createItem('document-intelligence-results', result);
        logger_1.logger.info('Document intelligence result stored', {
            analysisId: result.id,
            documentId: result.documentId,
            featuresAnalyzed: result.analysisFeatures.length,
            confidenceScore: result.performance.confidenceScore
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to store analysis result', { error, analysisId: result.id });
    }
}
// Register functions
functions_1.app.http('document-intelligence-comprehensive', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/intelligence/comprehensive',
    handler: performComprehensiveAnalysis
});
//# sourceMappingURL=document-intelligence.js.map