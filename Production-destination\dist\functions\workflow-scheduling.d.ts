/**
 * Workflow Scheduling Function
 * Handles workflow scheduling, triggers, and automated execution
 * Migrated from old-arch/src/workflow-service/scheduler/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create workflow schedule handler
 */
export declare function createWorkflowSchedule(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update schedule status handler
 */
export declare function updateScheduleStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
