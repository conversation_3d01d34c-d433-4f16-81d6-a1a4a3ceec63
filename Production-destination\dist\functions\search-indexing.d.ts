/**
 * Search Indexing Function
 * Handles document indexing and search operations
 * Migrated from old-arch/src/search-service/document-indexer/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Index document handler
 */
export declare function indexDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Search documents handler
 */
export declare function searchDocuments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
