"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPreferences = getUserPreferences;
exports.updateUserPreferences = updateUserPreferences;
exports.resetUserPreferences = resetUserPreferences;
/**
 * User Preferences Function
 * Handles user preference management, notification settings, and UI customization
 * Migrated from old-arch/src/user-service/preferences/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Validation schemas
const updatePreferencesSchema = Joi.object({
    theme: Joi.string().valid('light', 'dark', 'system').optional(),
    language: Joi.string().min(2).max(5).optional(),
    timezone: Joi.string().optional(),
    dateFormat: Joi.string().optional(),
    timeFormat: Joi.string().valid('12h', '24h').optional(),
    notifications: Joi.object({
        email: Joi.boolean().optional(),
        inApp: Joi.boolean().optional(),
        push: Joi.boolean().optional(),
        documentUploaded: Joi.boolean().optional(),
        documentProcessed: Joi.boolean().optional(),
        commentAdded: Joi.boolean().optional(),
        mentionedInComment: Joi.boolean().optional(),
        projectInvitation: Joi.boolean().optional(),
        organizationInvitation: Joi.boolean().optional(),
        workflowAssigned: Joi.boolean().optional(),
        workflowCompleted: Joi.boolean().optional(),
        systemUpdates: Joi.boolean().optional(),
        securityAlerts: Joi.boolean().optional(),
        marketingEmails: Joi.boolean().optional()
    }).optional(),
    privacy: Joi.object({
        profileVisibility: Joi.string().valid('public', 'organization', 'private').optional(),
        showOnlineStatus: Joi.boolean().optional(),
        allowDirectMessages: Joi.boolean().optional(),
        shareAnalytics: Joi.boolean().optional()
    }).optional(),
    accessibility: Joi.object({
        highContrast: Joi.boolean().optional(),
        largeText: Joi.boolean().optional(),
        reducedMotion: Joi.boolean().optional(),
        screenReader: Joi.boolean().optional(),
        keyboardNavigation: Joi.boolean().optional()
    }).optional(),
    dashboard: Joi.object({
        defaultView: Joi.string().valid('projects', 'documents', 'recent', 'analytics').optional(),
        showWelcomeMessage: Joi.boolean().optional(),
        pinnedProjects: Joi.array().items(Joi.string().uuid()).max(10).optional(),
        pinnedDocuments: Joi.array().items(Joi.string().uuid()).max(10).optional(),
        widgetLayout: Joi.array().items(Joi.object({
            id: Joi.string().required(),
            position: Joi.object({
                x: Joi.number().required(),
                y: Joi.number().required(),
                width: Joi.number().required(),
                height: Joi.number().required()
            }).required(),
            visible: Joi.boolean().default(true)
        })).optional()
    }).optional(),
    documentViewer: Joi.object({
        defaultZoom: Joi.number().min(0.1).max(5).optional(),
        showAnnotations: Joi.boolean().optional(),
        showComments: Joi.boolean().optional(),
        highlightEntities: Joi.boolean().optional(),
        autoSave: Joi.boolean().optional(),
        showLineNumbers: Joi.boolean().optional()
    }).optional(),
    search: Joi.object({
        defaultScope: Joi.string().valid('all', 'organization', 'projects', 'documents').optional(),
        saveSearchHistory: Joi.boolean().optional(),
        maxSearchResults: Joi.number().min(10).max(100).optional(),
        enableSmartSuggestions: Joi.boolean().optional(),
        includeArchivedContent: Joi.boolean().optional()
    }).optional(),
    workflow: Joi.object({
        defaultAssigneeNotification: Joi.boolean().optional(),
        autoAcceptAssignments: Joi.boolean().optional(),
        showWorkflowProgress: Joi.boolean().optional(),
        enableWorkflowReminders: Joi.boolean().optional()
    }).optional()
});
/**
 * Get user preferences handler
 */
async function getUserPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get user preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get user profile with preferences
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        const userData = userProfile;
        const preferences = userData.preferences || getDefaultPreferences();
        logger_1.logger.info("User preferences retrieved successfully", {
            correlationId,
            userId: user.id,
            theme: preferences.theme,
            language: preferences.language
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: preferences
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update user preferences handler
 */
async function updateUserPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update user preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updatePreferencesSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const preferenceUpdates = value;
        // Get current user profile
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        const userData = userProfile;
        const currentPreferences = userData.preferences || getDefaultPreferences();
        // Merge preferences with deep merge for nested objects
        const updatedPreferences = deepMerge(currentPreferences, preferenceUpdates);
        // Update user profile with new preferences
        const updatedUser = {
            ...userData,
            preferences: updatedPreferences,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('users', updatedUser);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "user_preferences_updated",
            userId: user.id,
            timestamp: new Date().toISOString(),
            details: {
                updatedFields: Object.keys(preferenceUpdates),
                theme: updatedPreferences.theme,
                language: updatedPreferences.language,
                notificationChanges: preferenceUpdates.notifications ? Object.keys(preferenceUpdates.notifications) : []
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'UserPreferencesUpdated',
            aggregateId: user.id,
            aggregateType: 'User',
            version: 1,
            data: {
                userId: user.id,
                updatedFields: Object.keys(preferenceUpdates),
                previousPreferences: currentPreferences,
                newPreferences: updatedPreferences
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        logger_1.logger.info("User preferences updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(preferenceUpdates),
            theme: updatedPreferences.theme,
            language: updatedPreferences.language
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                preferences: updatedPreferences,
                updatedFields: Object.keys(preferenceUpdates),
                message: "Preferences updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update user preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Reset user preferences to defaults handler
 */
async function resetUserPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Reset user preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get current user profile
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        const userData = userProfile;
        const defaultPreferences = getDefaultPreferences();
        // Update user profile with default preferences
        const updatedUser = {
            ...userData,
            preferences: defaultPreferences,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('users', updatedUser);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "user_preferences_reset",
            userId: user.id,
            timestamp: new Date().toISOString(),
            details: {
                resetToDefaults: true
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'UserPreferencesReset',
            aggregateId: user.id,
            aggregateType: 'User',
            version: 1,
            data: {
                userId: user.id,
                resetToDefaults: true,
                defaultPreferences
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        logger_1.logger.info("User preferences reset successfully", {
            correlationId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                preferences: defaultPreferences,
                message: "Preferences reset to defaults successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Reset user preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get default user preferences
 */
function getDefaultPreferences() {
    return {
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        timeFormat: '12h',
        notifications: {
            email: true,
            inApp: true,
            push: true,
            documentUploaded: true,
            documentProcessed: true,
            commentAdded: true,
            mentionedInComment: true,
            projectInvitation: true,
            organizationInvitation: true,
            workflowAssigned: true,
            workflowCompleted: true,
            systemUpdates: true,
            securityAlerts: true,
            marketingEmails: false
        },
        privacy: {
            profileVisibility: 'organization',
            showOnlineStatus: true,
            allowDirectMessages: true,
            shareAnalytics: true
        },
        accessibility: {
            highContrast: false,
            largeText: false,
            reducedMotion: false,
            screenReader: false,
            keyboardNavigation: false
        },
        dashboard: {
            defaultView: 'recent',
            showWelcomeMessage: true,
            pinnedProjects: [],
            pinnedDocuments: [],
            widgetLayout: [
                { id: 'recent-documents', position: { x: 0, y: 0, width: 6, height: 4 }, visible: true },
                { id: 'project-overview', position: { x: 6, y: 0, width: 6, height: 4 }, visible: true },
                { id: 'activity-feed', position: { x: 0, y: 4, width: 12, height: 6 }, visible: true }
            ]
        },
        documentViewer: {
            defaultZoom: 1,
            showAnnotations: true,
            showComments: true,
            highlightEntities: true,
            autoSave: true,
            showLineNumbers: false
        },
        search: {
            defaultScope: 'all',
            saveSearchHistory: true,
            maxSearchResults: 50,
            enableSmartSuggestions: true,
            includeArchivedContent: false
        },
        workflow: {
            defaultAssigneeNotification: true,
            autoAcceptAssignments: false,
            showWorkflowProgress: true,
            enableWorkflowReminders: true
        }
    };
}
/**
 * Deep merge objects
 */
function deepMerge(target, source) {
    const result = { ...target };
    for (const key in source) {
        if (source[key] !== null && typeof source[key] === 'object' && !Array.isArray(source[key])) {
            result[key] = deepMerge(target[key] || {}, source[key]);
        }
        else {
            result[key] = source[key];
        }
    }
    return result;
}
// Register functions
functions_1.app.http('user-preferences-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/preferences',
    handler: getUserPreferences
});
functions_1.app.http('user-preferences-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/preferences/update',
    handler: updateUserPreferences
});
functions_1.app.http('user-preferences-reset', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/preferences/reset',
    handler: resetUserPreferences
});
//# sourceMappingURL=user-preferences.js.map