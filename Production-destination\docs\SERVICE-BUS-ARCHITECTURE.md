# 🚌 Service Bus Architecture & Queue Mapping

## 📋 Overview

This document outlines the proper Service Bus architecture and queue/topic mapping for the HEPZ platform. All functions should use the shared `serviceBusEnhanced` service instead of creating their own Service Bus clients.

## 🏗️ Architecture Principles

### ✅ **CORRECT APPROACH**
- Use the shared `serviceBusEnhanced` service from `src/shared/services/service-bus.ts`
- Use pre-configured queues and topics
- Leverage enhanced features (circuit breaker, retry policies, deduplication)
- Consistent error handling and monitoring

### ❌ **INCORRECT APPROACH** 
- Creating individual `ServiceBusClient` instances in functions
- Using non-existent queue names
- Bypassing the shared service infrastructure
- Inconsistent error handling

## 🗺️ Queue & Topic Mapping

### **Topics** (Pub/Sub Pattern)
| Topic Name | Subscription | Purpose | Used By |
|------------|-------------|---------|---------|
| `analytics-events` | `analytics-aggregator` | Analytics data processing | Performance monitoring, reporting |
| `document-collaboration` | `collaboration-processor` | Real-time document collaboration | Document editing, user presence |
| `monitoring-events` | `system-monitor` | System health and alerts | Health monitoring, alerting |

### **Queues** (Point-to-Point Pattern)
| Queue Name | Purpose | Used By | Message Types |
|------------|---------|---------|---------------|
| `workflow-orchestration` | Workflow step execution | Workflow engine | Workflow steps, state transitions |
| `ai-operations` | AI processing tasks | AI orchestration, document analysis | AI analysis requests, model training |
| `scheduled-emails` | Email delivery scheduling | Email service | Scheduled emails, notifications |
| `document-processing` | Document processing tasks | Document upload, transformation | Document analysis, conversion |
| `notification-delivery` | Push notification delivery | Notification service | Push notifications, alerts |

## 🔧 Usage Examples

### **Correct Implementation**

```typescript
import { serviceBusEnhanced } from '../shared/services/service-bus';

async function queueAIOperation(operation: any): Promise<void> {
  // Ensure Service Bus is initialized
  await serviceBusEnhanced.initialize();

  const message = {
    body: operation,
    messageId: `ai-op-${operation.id}-${Date.now()}`,
    subject: `AI Operation: ${operation.type}`,
    correlationId: operation.id,
    applicationProperties: {
      operationType: operation.type,
      priority: operation.priority,
      source: 'ai-orchestration-hub'
    },
    timeToLive: 3600000 // 1 hour
  };

  const success = await serviceBusEnhanced.sendToQueue('ai-operations', message);
  if (!success) {
    throw new Error('Failed to queue AI operation');
  }
}
```

### **Incorrect Implementation** ❌

```typescript
// DON'T DO THIS
const { ServiceBusClient } = require('@azure/service-bus');

async function queueAIOperation(operation: any): Promise<void> {
  const connectionString = process.env.AZURE_SERVICE_BUS_CONNECTION_STRING;
  const serviceBusClient = new ServiceBusClient(connectionString);
  const sender = serviceBusClient.createSender('ai-operations');
  
  await sender.sendMessages({ body: operation });
  await sender.close();
  await serviceBusClient.close();
}
```

## 🚀 Enhanced Features

The shared `serviceBusEnhanced` service provides:

- **Circuit Breaker**: Automatic failure detection and recovery
- **Retry Policies**: Configurable retry with exponential backoff
- **Message Deduplication**: Prevents duplicate message processing
- **Performance Monitoring**: Built-in metrics and monitoring
- **Connection Pooling**: Efficient connection management
- **Dead Letter Handling**: Automatic dead letter queue processing

## 📊 Monitoring & Metrics

Access Service Bus metrics:

```typescript
import { serviceBusEnhanced } from '../shared/services/service-bus';

const metrics = serviceBusEnhanced.getMetrics();
console.log('Messages sent:', metrics.messagesSent);
console.log('Messages received:', metrics.messagesReceived);
console.log('Errors:', metrics.errors);
console.log('Average processing time:', metrics.averageProcessingTime);
```

## 🔄 Migration Checklist

When migrating functions to use the shared service:

1. ✅ Remove individual `ServiceBusClient` imports
2. ✅ Import `serviceBusEnhanced` from shared services
3. ✅ Use configured queue/topic names
4. ✅ Call `serviceBusEnhanced.initialize()` before use
5. ✅ Use `sendToQueue()` or `sendToTopic()` methods
6. ✅ Handle success/failure responses
7. ✅ Update error handling to use shared patterns
8. ✅ Remove manual connection management

## 🛠️ Configuration

Ensure these queues and topics are created in Azure:

```powershell
# Run the configuration script
./scripts/configure-azure-services.ps1
```

This creates all necessary Service Bus entities with proper configuration.

## 🧪 Testing

Test the Service Bus configuration:

```bash
# Test all queues and topics
node scripts/test-azure-services.js

# Send test messages
node scripts/send-proper-test-messages.js
```

## 📝 Best Practices

1. **Always use the shared service** - Never create individual Service Bus clients
2. **Initialize once** - Call `serviceBusEnhanced.initialize()` at application startup
3. **Handle failures gracefully** - Check return values and implement fallbacks
4. **Use appropriate queues** - Map operations to the correct queue/topic
5. **Set proper TTL** - Configure message time-to-live based on urgency
6. **Include metadata** - Use `applicationProperties` for routing and filtering
7. **Monitor metrics** - Regularly check service health and performance
