"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTeam = createTeam;
/**
 * Organization Teams Create Function
 * Handles creation and management of teams within organizations
 * Migrated from old-arch/src/organization-service/teams/create/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Team types and roles
var TeamType;
(function (TeamType) {
    TeamType["DEPARTMENT"] = "DEPARTMENT";
    TeamType["PROJECT"] = "PROJECT";
    TeamType["FUNCTIONAL"] = "FUNCTIONAL";
    TeamType["CROSS_FUNCTIONAL"] = "CROSS_FUNCTIONAL";
})(TeamType || (TeamType = {}));
var TeamRole;
(function (TeamRole) {
    TeamRole["LEAD"] = "LEAD";
    TeamRole["MEMBER"] = "MEMBER";
    TeamRole["CONTRIBUTOR"] = "CONTRIBUTOR";
    TeamRole["OBSERVER"] = "OBSERVER";
})(TeamRole || (TeamRole = {}));
var TeamVisibility;
(function (TeamVisibility) {
    TeamVisibility["PUBLIC"] = "PUBLIC";
    TeamVisibility["PRIVATE"] = "PRIVATE";
    TeamVisibility["RESTRICTED"] = "RESTRICTED";
})(TeamVisibility || (TeamVisibility = {}));
// Validation schema
const createTeamSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(TeamType)).default(TeamType.FUNCTIONAL),
    visibility: Joi.string().valid(...Object.values(TeamVisibility)).default(TeamVisibility.PRIVATE),
    organizationId: Joi.string().uuid().required(),
    parentTeamId: Joi.string().uuid().optional(),
    initialMembers: Joi.array().items(Joi.object({
        userId: Joi.string().uuid().required(),
        role: Joi.string().valid(...Object.values(TeamRole)).default(TeamRole.MEMBER),
        permissions: Joi.array().items(Joi.string()).optional()
    })).max(50).default([]),
    settings: Joi.object({
        allowSelfJoin: Joi.boolean().default(false),
        requireApprovalToJoin: Joi.boolean().default(true),
        maxMembers: Joi.number().min(1).max(1000).optional(),
        enableDiscussions: Joi.boolean().default(true),
        enableFileSharing: Joi.boolean().default(true),
        enableTaskManagement: Joi.boolean().default(false),
        defaultProjectRole: Joi.string().valid('OWNER', 'MANAGER', 'CONTRIBUTOR', 'VIEWER').default('CONTRIBUTOR')
    }).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
});
/**
 * Create team handler
 */
async function createTeam(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create team started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createTeamSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const teamRequest = value;
        // Verify organization access
        const organization = await database_1.db.readItem('organizations', teamRequest.organizationId, teamRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user is a member of the organization with team creation permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, teamRequest.organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        const userMembership = memberships[0];
        const canCreateTeam = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';
        if (!canCreateTeam) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to create teams" }
            }, request);
        }
        // Verify parent team if specified
        if (teamRequest.parentTeamId) {
            const parentTeam = await database_1.db.readItem('teams', teamRequest.parentTeamId, teamRequest.parentTeamId);
            if (!parentTeam) {
                return (0, cors_1.addCorsHeaders)({
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Parent team not found" }
                }, request);
            }
            if (parentTeam.organizationId !== teamRequest.organizationId) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Parent team does not belong to the specified organization" }
                }, request);
            }
        }
        // Check team limits for organization tier
        const orgData = organization;
        if (await isTeamCountLimitReached(teamRequest.organizationId, orgData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Team limit reached for this organization tier",
                    tier: orgData.tier
                }
            }, request);
        }
        // Validate initial members
        const validationResult = await validateInitialMembers(teamRequest.initialMembers, teamRequest.organizationId);
        if (!validationResult.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Invalid initial members",
                    details: validationResult.errors
                }
            }, request);
        }
        // Create team
        const teamId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const team = {
            id: teamId,
            name: teamRequest.name,
            description: teamRequest.description || "",
            type: teamRequest.type,
            visibility: teamRequest.visibility,
            organizationId: teamRequest.organizationId,
            parentTeamId: teamRequest.parentTeamId,
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            settings: {
                allowSelfJoin: teamRequest.settings?.allowSelfJoin || false,
                requireApprovalToJoin: teamRequest.settings?.requireApprovalToJoin || true,
                maxMembers: teamRequest.settings?.maxMembers,
                enableDiscussions: teamRequest.settings?.enableDiscussions || true,
                enableFileSharing: teamRequest.settings?.enableFileSharing || true,
                enableTaskManagement: teamRequest.settings?.enableTaskManagement || false,
                defaultProjectRole: teamRequest.settings?.defaultProjectRole || 'CONTRIBUTOR'
            },
            tags: teamRequest.tags,
            metadata: {
                memberCount: teamRequest.initialMembers.length + 1, // +1 for creator
                projectCount: 0,
                documentCount: 0,
                activityCount: 0
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('teams', team);
        // Create team membership for creator (as LEAD)
        const creatorMembershipId = (0, uuid_1.v4)();
        const creatorMembership = {
            id: creatorMembershipId,
            teamId,
            userId: user.id,
            role: TeamRole.LEAD,
            permissions: ['*'], // Lead has all permissions
            joinedAt: now,
            addedBy: user.id,
            status: 'ACTIVE',
            tenantId: user.tenantId
        };
        await database_1.db.createItem('team-members', creatorMembership);
        // Create memberships for initial members
        const membershipIds = [creatorMembershipId];
        for (const member of teamRequest.initialMembers) {
            const membershipId = (0, uuid_1.v4)();
            const membership = {
                id: membershipId,
                teamId,
                userId: member.userId,
                role: member.role,
                permissions: member.permissions || [],
                joinedAt: now,
                addedBy: user.id,
                status: 'ACTIVE',
                tenantId: user.tenantId
            };
            await database_1.db.createItem('team-members', membership);
            membershipIds.push(membershipId);
        }
        // Update organization's team count
        const updatedOrg = {
            ...orgData,
            metadata: {
                ...orgData.metadata,
                teamCount: (orgData.metadata?.teamCount || 0) + 1
            },
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('organizations', updatedOrg);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "team_created",
            userId: user.id,
            organizationId: teamRequest.organizationId,
            teamId,
            timestamp: now,
            details: {
                teamName: team.name,
                teamType: team.type,
                visibility: team.visibility,
                memberCount: team.metadata.memberCount,
                organizationName: orgData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'TeamCreated',
            aggregateId: teamId,
            aggregateType: 'Team',
            version: 1,
            data: {
                team,
                creatorMembership,
                initialMembers: teamRequest.initialMembers,
                createdBy: user.id,
                organizationName: orgData.name
            },
            userId: user.id,
            organizationId: teamRequest.organizationId,
            tenantId: user.tenantId
        });
        // Send notifications to initial members
        for (const member of teamRequest.initialMembers) {
            await notification_1.notificationService.sendNotification({
                userId: member.userId,
                type: 'TEAM_MEMBER_ADDED',
                title: 'Added to team',
                message: `You have been added to the team "${team.name}" in ${orgData.name} as ${member.role}.`,
                priority: 'normal',
                metadata: {
                    teamId,
                    teamName: team.name,
                    role: member.role,
                    organizationId: teamRequest.organizationId,
                    organizationName: orgData.name,
                    addedBy: user.id
                },
                organizationId: teamRequest.organizationId
            });
        }
        // Send notification to creator
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'TEAM_CREATED',
            title: 'Team created successfully!',
            message: `Your team "${team.name}" has been created with ${team.metadata.memberCount} members.`,
            priority: 'normal',
            metadata: {
                teamId,
                teamName: team.name,
                memberCount: team.metadata.memberCount,
                organizationId: teamRequest.organizationId,
                organizationName: orgData.name
            },
            organizationId: teamRequest.organizationId
        });
        logger_1.logger.info("Team created successfully", {
            correlationId,
            teamId,
            teamName: team.name,
            organizationId: teamRequest.organizationId,
            memberCount: team.metadata.memberCount,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: teamId,
                name: team.name,
                description: team.description,
                type: team.type,
                visibility: team.visibility,
                organizationId: teamRequest.organizationId,
                organizationName: orgData.name,
                memberCount: team.metadata.memberCount,
                membershipIds,
                message: "Team created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create team failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check if team count limit is reached for organization
 */
async function isTeamCountLimitReached(organizationId, tier) {
    try {
        const teamCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const result = await database_1.db.queryItems('teams', teamCountQuery, [organizationId]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 3,
            'PROFESSIONAL': 15,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check team count limit', { error, organizationId });
        return false;
    }
}
/**
 * Validate initial members
 */
async function validateInitialMembers(initialMembers, organizationId) {
    const errors = [];
    for (const member of initialMembers) {
        // Check if user exists and is member of organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [member.userId, organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            errors.push(`User ${member.userId} is not a member of the organization`);
        }
    }
    // Check for duplicate users
    const userIds = initialMembers.map(m => m.userId);
    const uniqueUserIds = new Set(userIds);
    if (userIds.length !== uniqueUserIds.size) {
        errors.push('Duplicate users in initial members list');
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
// Register functions
functions_1.app.http('organization-teams-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/{organizationId}/teams',
    handler: createTeam
});
//# sourceMappingURL=organization-teams-create.js.map