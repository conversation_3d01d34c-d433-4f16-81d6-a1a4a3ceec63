/**
 * Document Upload Function
 * Handles document upload operations with validation and processing
 * Migrated from old-arch/src/document-service/upload/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Upload document handler
 */
export declare function uploadDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Complete upload handler
 */
export declare function completeUpload(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
