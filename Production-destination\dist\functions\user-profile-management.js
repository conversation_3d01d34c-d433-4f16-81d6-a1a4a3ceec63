"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserProfile = getUserProfile;
exports.updateUserProfile = updateUserProfile;
exports.uploadAvatar = uploadAvatar;
/**
 * User Profile Management Function
 * Handles user profile updates, avatar management, and profile settings
 * Migrated from old-arch/src/user-service/profile/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Validation schemas
const updateProfileSchema = Joi.object({
    firstName: Joi.string().min(1).max(50).optional(),
    lastName: Joi.string().min(1).max(50).optional(),
    displayName: Joi.string().min(1).max(100).optional(),
    jobTitle: Joi.string().max(100).optional(),
    department: Joi.string().max(100).optional(),
    phoneNumber: Joi.string().max(20).optional(),
    bio: Joi.string().max(500).optional(),
    location: Joi.string().max(100).optional(),
    website: Joi.string().uri().optional(),
    socialLinks: Joi.object({
        linkedin: Joi.string().uri().optional(),
        twitter: Joi.string().uri().optional(),
        github: Joi.string().uri().optional()
    }).optional(),
    skills: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    interests: Joi.array().items(Joi.string().max(50)).max(20).optional()
});
const uploadAvatarSchema = Joi.object({
    fileName: Joi.string().required(),
    contentType: Joi.string().required(),
    fileSize: Joi.number().max(5 * 1024 * 1024).required() // 5MB max
});
/**
 * Get user profile handler
 */
async function getUserProfile(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const targetUserId = request.params.userId;
    logger_1.logger.info("Get user profile started", { correlationId, targetUserId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const userId = targetUserId || user.id;
        // Check if user can view this profile
        const canViewProfile = userId === user.id || user.roles?.includes('admin');
        if (!canViewProfile) {
            // Check if users are in the same organization
            const userOrgs = await getUserOrganizations(user.id);
            const targetUserOrgs = await getUserOrganizations(userId);
            const hasSharedOrg = userOrgs.some(org => targetUserOrgs.includes(org));
            if (!hasSharedOrg) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Access denied to user profile" }
                }, request);
            }
        }
        // Get user profile
        const userProfile = await database_1.db.readItem('users', userId, userId);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        const profileData = userProfile;
        // Return profile data (excluding sensitive information for other users)
        const isOwnProfile = userId === user.id;
        const responseData = {
            id: profileData.id,
            email: isOwnProfile ? profileData.email : undefined,
            firstName: profileData.firstName,
            lastName: profileData.lastName,
            displayName: profileData.displayName,
            jobTitle: profileData.jobTitle,
            department: profileData.department,
            phoneNumber: isOwnProfile ? profileData.phoneNumber : undefined,
            bio: profileData.bio,
            location: profileData.location,
            website: profileData.website,
            socialLinks: profileData.socialLinks,
            skills: profileData.skills || [],
            interests: profileData.interests || [],
            avatarUrl: profileData.avatarUrl,
            createdAt: profileData.createdAt,
            updatedAt: profileData.updatedAt,
            lastLoginAt: isOwnProfile ? profileData.lastLoginAt : undefined,
            isOnline: profileData.isOnline || false,
            lastSeenAt: profileData.lastSeenAt,
            organizationIds: isOwnProfile ? profileData.organizationIds : undefined,
            roles: isOwnProfile ? profileData.roles : undefined
        };
        logger_1.logger.info("User profile retrieved successfully", {
            correlationId,
            userId,
            isOwnProfile,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: responseData
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user profile failed", {
            correlationId,
            targetUserId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update user profile handler
 */
async function updateUserProfile(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update user profile started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateProfileSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const profileUpdates = value;
        // Get current user profile
        const currentProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!currentProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User profile not found" }
            }, request);
        }
        const currentData = currentProfile;
        const now = new Date().toISOString();
        // Update display name if first/last name changed
        let displayName = profileUpdates.displayName;
        if ((profileUpdates.firstName || profileUpdates.lastName) && !displayName) {
            const firstName = profileUpdates.firstName || currentData.firstName;
            const lastName = profileUpdates.lastName || currentData.lastName;
            displayName = `${firstName} ${lastName}`.trim();
        }
        // Create updated profile
        const updatedProfile = {
            ...currentData,
            ...profileUpdates,
            displayName: displayName || currentData.displayName,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('users', updatedProfile);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "user_profile_updated",
            userId: user.id,
            timestamp: now,
            details: {
                updatedFields: Object.keys(profileUpdates),
                displayNameChanged: displayName !== currentData.displayName
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'UserProfileUpdated',
            aggregateId: user.id,
            aggregateType: 'User',
            version: 1,
            data: {
                userId: user.id,
                updatedFields: Object.keys(profileUpdates),
                previousData: {
                    displayName: currentData.displayName,
                    jobTitle: currentData.jobTitle,
                    department: currentData.department
                },
                newData: {
                    displayName: updatedProfile.displayName,
                    jobTitle: updatedProfile.jobTitle,
                    department: updatedProfile.department
                }
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        // Send notification for significant changes
        if (profileUpdates.displayName || profileUpdates.jobTitle || profileUpdates.department) {
            await notification_1.notificationService.sendNotification({
                userId: user.id,
                type: 'PROFILE_UPDATED',
                title: 'Profile updated successfully',
                message: 'Your profile information has been updated.',
                priority: 'normal',
                metadata: {
                    updatedFields: Object.keys(profileUpdates)
                }
            });
        }
        logger_1.logger.info("User profile updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(profileUpdates)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: updatedProfile.id,
                firstName: updatedProfile.firstName,
                lastName: updatedProfile.lastName,
                displayName: updatedProfile.displayName,
                jobTitle: updatedProfile.jobTitle,
                department: updatedProfile.department,
                phoneNumber: updatedProfile.phoneNumber,
                bio: updatedProfile.bio,
                location: updatedProfile.location,
                website: updatedProfile.website,
                socialLinks: updatedProfile.socialLinks,
                skills: updatedProfile.skills,
                interests: updatedProfile.interests,
                updatedAt: updatedProfile.updatedAt,
                message: "Profile updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update user profile failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Upload avatar handler
 */
async function uploadAvatar(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Upload avatar started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = uploadAvatarSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const uploadRequest = value;
        // Validate file type
        const allowedTypes = ['image/jpeg', 'image/png', 'image/gif', 'image/webp'];
        if (!allowedTypes.includes(uploadRequest.contentType)) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid file type. Only JPEG, PNG, GIF, and WebP are allowed." }
            }, request);
        }
        // Generate avatar blob name
        const fileExtension = uploadRequest.fileName.split('.').pop();
        const avatarBlobName = `avatars/${user.id}/${(0, uuid_1.v4)()}.${fileExtension}`;
        // Generate upload URL
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.AVATAR_CONTAINER || "avatars");
        const blobClient = containerClient.getBlockBlobClient(avatarBlobName);
        // Generate SAS URL for upload
        const permissions = new storage_blob_1.BlobSASPermissions();
        permissions.write = true;
        permissions.create = true;
        const uploadUrl = await blobClient.generateSasUrl({
            permissions,
            expiresOn: new Date(Date.now() + 15 * 60 * 1000) // 15 minutes
        });
        // Generate public URL for the avatar
        const avatarUrl = blobClient.url;
        logger_1.logger.info("Avatar upload URL generated successfully", {
            correlationId,
            userId: user.id,
            avatarBlobName,
            fileSize: uploadRequest.fileSize
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                uploadUrl,
                avatarUrl,
                blobName: avatarBlobName,
                expiresAt: new Date(Date.now() + 15 * 60 * 1000).toISOString(),
                message: "Upload URL generated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Upload avatar failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function getUserOrganizations(userId) {
    try {
        const membershipQuery = 'SELECT c.organizationId FROM c WHERE c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [userId, 'ACTIVE']);
        return memberships.map((m) => m.organizationId);
    }
    catch (error) {
        logger_1.logger.error('Failed to get user organizations', { error, userId });
        return [];
    }
}
// Register functions
functions_1.app.http('user-profile-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/{userId?}/profile',
    handler: getUserProfile
});
functions_1.app.http('user-profile-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/profile/update',
    handler: updateUserProfile
});
functions_1.app.http('user-avatar-upload', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/avatar/upload',
    handler: uploadAvatar
});
//# sourceMappingURL=user-profile-management.js.map