/**
 * Enhanced Redis Service for caching, session management, and distributed operations
 * Updated to follow Azure Service Connector best practices (2025)
 * Provides production-ready Redis operations with advanced features:
 * - Azure Managed Identity authentication
 * - Token-based authentication with automatic refresh
 * - TLS/SSL connections (port 6380)
 * - Connection pooling and clustering support
 * - Distributed locking mechanisms
 * - Pub/Sub messaging
 * - Advanced caching patterns
 * - Performance monitoring and metrics
 */
export interface RedisLockOptions {
    ttl?: number;
    retryDelay?: number;
    retryCount?: number;
}
export interface RedisPubSubMessage {
    channel: string;
    message: string;
    timestamp: Date;
}
export interface RedisMetrics {
    operations: number;
    errors: number;
    connectionTime: number;
    lastOperation: Date;
    cacheHitRate: number;
}
export interface RedisClusterNode {
    host: string;
    port: number;
}
export interface AzureRedisConfig {
    host: string;
    port: number;
    ssl: boolean;
    clientId?: string;
}
export interface AccessToken {
    token: string;
    expiresOn: Date;
}
export declare class RedisService {
    private static instance;
    private client;
    private clusterClient;
    private pubSubClient;
    private isConnected;
    private isClusterMode;
    private connectionAttempts;
    private maxRetries;
    private metrics;
    private subscribers;
    private lockPrefix;
    private sessionPrefix;
    private azureConfig;
    private credential;
    private currentToken;
    private tokenRefreshTimer;
    private constructor();
    static getInstance(): RedisService;
    /**
     * Initialize Redis connection with enhanced features
     */
    initialize(): Promise<void>;
    /**
     * Setup event handlers for single node
     */
    private setupSingleNodeEventHandlers;
    /**
     * Setup event handlers for cluster
     */
    private setupClusterEventHandlers;
    /**
     * Initialize pub/sub client
     */
    private initializePubSub;
    /**
     * Publish metrics events to Event Grid
     */
    private publishMetricsEvent;
    /**
     * Get the appropriate Redis client
     */
    private getClient;
    /**
     * Update operation metrics
     */
    private updateMetrics;
    /**
     * Check if Redis is available
     */
    isAvailable(): boolean;
    /**
     * Get Redis metrics
     */
    getMetrics(): RedisMetrics;
    /**
     * Distributed lock acquisition
     */
    acquireLock(resource: string, identifier: string, options?: RedisLockOptions): Promise<boolean>;
    /**
     * Distributed lock release
     */
    releaseLock(resource: string, identifier: string): Promise<boolean>;
    /**
     * Execute function with distributed lock
     */
    withLock<T>(resource: string, fn: () => Promise<T>, options?: RedisLockOptions): Promise<T | null>;
    /**
     * Publish message to Redis channel
     */
    publish(channel: string, message: string): Promise<boolean>;
    /**
     * Subscribe to Redis channel
     */
    subscribe(channel: string, callback: (message: RedisPubSubMessage) => void): Promise<boolean>;
    /**
     * Unsubscribe from Redis channel
     */
    unsubscribe(channel: string, callback?: (message: RedisPubSubMessage) => void): Promise<boolean>;
    /**
     * Session management - set session data
     */
    setSession(sessionId: string, data: any, ttlSeconds?: number): Promise<boolean>;
    /**
     * Session management - get session data with database fallback
     */
    getSession<T extends Record<string, any>>(sessionId: string, fallbackToDb?: boolean): Promise<T | null>;
    /**
     * Session management - delete session
     */
    deleteSession(sessionId: string): Promise<boolean>;
    /**
     * Document content management with database fallback
     */
    getDocumentContent(documentId: string, fallbackToDb?: boolean): Promise<string | null>;
    /**
     * Set document content with cache invalidation using production cache manager
     */
    setDocumentContent(documentId: string, content: string, ttlSeconds?: number): Promise<boolean>;
    /**
     * User activity tracking with database fallback
     */
    getUserActivities(userId: string, limit?: number, fallbackToDb?: boolean): Promise<any[]>;
    /**
     * Device registration with database fallback
     */
    getDeviceRegistration(userId: string, platform: string, fallbackToDb?: boolean): Promise<any | null>;
    /**
     * Cache invalidation methods
     */
    invalidateDocumentCaches(documentId: string): Promise<void>;
    invalidateUserCaches(userId: string): Promise<void>;
    /**
     * Delete keys by pattern using production-standard approach
     * Uses a safe, non-blocking method that works with both single instances and clusters
     */
    private deleteByPattern;
    /**
     * Get keys from Redis cluster using a safe approach
     */
    private getKeysFromCluster;
    /**
     * Extract common prefixes from a pattern for cluster key lookup
     */
    private extractPrefixFromPattern;
    /**
     * Delete a batch of keys safely
     */
    private deleteBatch;
    /**
     * List range operation
     */
    lrange(key: string, start: number, stop: number): Promise<string[]>;
    /**
     * Get value from cache with enhanced metrics
     */
    get(key: string): Promise<string | null>;
    /**
     * Get JSON value from cache
     */
    getJson<T>(key: string): Promise<T | null>;
    /**
     * Set value in cache with enhanced metrics
     */
    set(key: string, value: string, ttlSeconds?: number): Promise<boolean>;
    /**
     * Set JSON value in cache
     */
    setJson<T>(key: string, value: T, ttlSeconds?: number): Promise<boolean>;
    /**
     * Delete key from cache
     */
    delete(key: string): Promise<boolean>;
    /**
     * Check if key exists
     */
    exists(key: string): Promise<boolean>;
    /**
     * Set expiration for key
     */
    expire(key: string, ttlSeconds: number): Promise<boolean>;
    /**
     * Increment counter
     */
    increment(key: string, amount?: number): Promise<number | null>;
    /**
     * Add to set
     */
    addToSet(key: string, ...members: string[]): Promise<number | null>;
    /**
     * Get set members
     */
    getSetMembers(key: string): Promise<string[]>;
    /**
     * Push to list
     */
    pushToList(key: string, ...values: string[]): Promise<number | null>;
    /**
     * Get list range
     */
    getListRange(key: string, start?: number, end?: number): Promise<string[]>;
    /**
     * Set with expiration (SETEX)
     */
    setex(key: string, seconds: number, value: string): Promise<boolean>;
    /**
     * Delete multiple keys
     */
    del(...keys: string[]): Promise<number>;
    /**
     * Add to set (SADD)
     */
    sadd(key: string, ...members: string[]): Promise<number>;
    /**
     * Hash set (HSET)
     */
    hset(key: string, field: string | Record<string, string>, value?: string): Promise<number>;
    /**
     * Hash increment by (HINCRBY)
     */
    hincrby(key: string, field: string, increment: number): Promise<number>;
    /**
     * Hash get all (HGETALL)
     */
    hgetall(key: string): Promise<Record<string, string>>;
    /**
     * List push (LPUSH)
     */
    lpush(key: string, ...elements: string[]): Promise<number>;
    /**
     * List trim (LTRIM)
     */
    ltrim(key: string, start: number, stop: number): Promise<string>;
    /**
     * Get keys matching pattern (KEYS)
     */
    keys(pattern: string): Promise<string[]>;
    /**
     * Flush all data (FLUSHALL)
     */
    flushall(): Promise<string>;
    /**
     * Ping Redis server
     */
    ping(): Promise<string>;
    /**
     * Get Redis server info
     */
    info(section?: string): Promise<string>;
    /**
     * Get key type
     */
    type(key: string): Promise<string>;
    /**
     * Get TTL for key
     */
    ttl(key: string): Promise<number>;
    /**
     * Hash get (HGET)
     */
    hget(key: string, field: string): Promise<string | null>;
    /**
     * Hash increment by float (HINCRBYFLOAT)
     */
    hincrbyfloat(key: string, field: string, increment: number): Promise<number>;
    /**
     * Close all Redis connections
     */
    close(): Promise<void>;
    /**
     * Configure Azure Managed Redis with Managed Identity authentication
     */
    private configureAzureRedis;
    /**
     * Initialize Azure Redis with Managed Identity authentication
     * Supports both single node and cluster configurations
     */
    private initializeAzureRedis;
    /**
     * Extract username from Azure token
     */
    private extractUsernameFromToken;
    /**
     * Refresh Azure authentication token
     */
    private refreshToken;
    /**
     * Setup automatic token refresh
     */
    private setupTokenRefresh;
}
export declare const redis: RedisService;
