📚 Actual API Endpoints (for documentation):
=============================================

### Analytics
- `/analytics/advanced` - advanced-analytics-get
- `/analytics/dashboard` - analytics-dashboard
- `/analytics` - analytics-get
- `/analytics/bi/reports` - bi-report-generate
- `/analytics/bi/dashboard` - bi-dashboard-get
- `/analytics/predictions` - prediction-generate

### Comments
- `/comments` - comment-create
- `/comments/reactions` - comment-reaction-add

### Management
- `/management/advanced-roles` - advanced-role-create
- `/management/rate-limits/create` - rate-limit-create
- `/management/backups/create` - backup-create
- `/management/backups/{backupId}/status` - backup-status
- `/management/cache/operations` - cache-operation
- `/management/cache/statistics` - cache-statistics
- `/management/cache/clear` - cache-clear
- `/management/data-migrations/create` - migration-create
- `/management/data-migrations/{migrationId}/status` - migration-status
- `/management/feature-flags/create` - feature-flag-create
- `/management/health-checks/create` - health-check-create
- `/management/analytics/models/train` - model-train
- `/management/configuration` - config-update

### Permissions
- `/permissions/check-advanced` - advanced-permission-check
- `/permissions/grant` - permission-grant
- `/permissions/check` - permission-check
- `/permissions/batch-check` - permission-batch-check

### Ai
- `/ai/batch-jobs` - batch-job-create
- `/ai/batch-jobs/{batchJobId}/status` - batch-job-status
- `/ai/models` - ai-model-create
- `/ai/models/{modelId}/train` - ai-model-train
- `/ai/models/{modelId}/deploy` - ai-model-deploy
- `/ai/operations` - ai-operation-create
- `/ai/operations/{operationId}` - ai-operation-status
- `/ai/forms/process` - ai-form-process

### Documents
- `/documents/{id}/ai-analysis` - ai-document-analysis
- `/documents/{documentId}/classify` - document-classify
- `/documents/approvals` - document-approval-create
- `/documents/approvals/review` - document-review
- `/documents/{documentId}/archive` - document-archive
- `/documents/{documentId}/restore` - document-restore
- `/documents/{id}/share` - document-share
- `/documents/{id}/comments` - document-comment-add
- `/documents/{documentId}/comments` - document-comment-list
- `/documents/{documentId}/comments` - document-comments
- `/documents/{id}/complete-content` - document-complete-content
- `/documents/{id}/enhance` - document-enhance
- `/documents/{documentId}/intelligence/comprehensive` - document-intelligence-comprehensive
- `/documents/{documentId}/metadata` - document-metadata-update
- `/documents/metadata/extract` - document-metadata-extract
- `/documents/process` - document-processing
- `/documents/{id}` - document-retrieve
- `/documents` - document-list
- `/documents/{documentId}/share` - document-share-create
- `/documents/{documentId}/shares` - document-share-get
- `/documents/sign` - document-sign
- `/documents/{id}/specialized-processing` - document-specialized-processing
- `/documents/transform` - document-transform
- `/documents/upload` - document-upload
- `/documents/{documentId}/upload/complete` - document-upload-complete
- `/documents/{documentId}/versions` - document-version-create
- `/documents/{documentId}/versions/{versionId}/restore` - document-version-restore
- `/documents/{id}/versions` - document-versions

### Search
- `/search/intelligent` - ai-intelligent-search
- `/search/advanced` - search-advanced
- `/search/index` - search-index-document
- `/search/documents` - search-documents
- `/search` - search

### Api-keys
- `/api-keys` - api-keys
- `/api-keys/{apiKeyId}` - api-key-revoke
- `/api-keys/create` - api-key-create
- `/api-keys/validate` - api-key-validate

### Rate-limit
- `/rate-limit/check` - rate-limit-check

### Audit
- `/audit/logs` - audit-logs-list
- `/audit/logs/export` - audit-logs-export
- `/audit/statistics` - audit-statistics
- `/audit/logs/create` - audit-log-create
- `/audit/logs/get` - audit-logs-get

### Auth
- `/auth/login` - auth-login
- `/auth/me` - auth-me
- `/auth/logout` - auth-logout
- `/auth/refresh` - auth-refresh
- `/auth/register` - auth-register

### Classification
- `/classification/categories` - classification-category-create

### Storage
- `/storage/configure` - storage-configure
- `/storage/sync/document` - storage-sync-document
- `/storage/sync/bulk` - storage-bulk-sync

### Compliance
- `/compliance/assessments` - compliance-assessment-create
- `/compliance/status` - compliance-status-update

### Reports
- `/reports/custom` - custom-report-create
- `/reports/custom/{reportId}/status` - custom-report-status

### Dashboards
- `/dashboards` - dashboard-create
- `/dashboards/{dashboardId}` - dashboard-get

### Security
- `/security/encrypt` - data-encrypt
- `/security/decrypt` - data-decrypt
- `/security/incidents` - security-incident-create
- `/security/threats/analyze` - security-threats-analyze

### Exports
- `/exports` - data-export-create
- `/exports/{exportId}/status` - data-export-status

### Templates
- `/templates` - template-create
- `/templates/generate` - template-generate
- `/templates/manage` - templates
- `/templates/{templateId}/apply` - template-apply

### Document-templates
- `/document-templates/generate` - document-template-generate

### Emails
- `/emails/automation/send` - email-automation-send
- `/emails/automation/templates` - email-automation-template-create
- `/emails` - email-list
- `/emails/send` - email-send
- `/emails/templates` - email-template-create

### Integrations
- `/integrations/enterprise` - enterprise-integration-create
- `/integrations/enterprise/sync` - enterprise-integration-sync
- `/integrations/api-connections` - api-connection-create
- `/integrations/api-connections/test` - api-connection-test
- `/integrations` - integration-create

### Eventgrid
- `/eventgrid/webhook` - event-grid-webhook
- `/eventgrid/publish` - event-grid-publish

### Feature-flags
- `/feature-flags/evaluate` - feature-flag-evaluate

### Files
- `/files/process` - file-process
- `/files/processing/{jobId}/status` - file-processing-status

### System
- `/system/health-status` - health-system
- `/system/health` - system-health
- `/system/metrics` - system-metrics

### Metrics
- `/metrics` - metric-record
- `/metrics/collect` - metrics-collect
- `/metrics/query` - metrics-query
- `/metrics/summary` - metrics-summary

### Health
- `/health` - health

### Logs
- `/logs` - log-create
- `/logs/query` - log-query
- `/logs/statistics` - log-statistics

### Mobile
- `/mobile/devices/register` - mobile-device-register
- `/mobile/sync` - mobile-sync
- `/mobile/offline-data` - mobile-offline-data

### Notifications
- `/notifications/push/send` - push-notification-send
- `/notifications/push/register` - push-notification-register
- `/notifications/push/unregister` - push-notification-unregister
- `/notifications/push/stats` - push-notification-stats
- `/notifications` - notification-list
- `/notifications/mark-read` - notification-mark-read
- `/notifications/{notificationId}` - notification-get
- `/notifications/preferences` - notification-preferences-get
- `/notifications/preferences/update` - notification-preferences-update
- `/notifications/preferences/reset` - notification-preferences-reset
- `/notifications/send` - notification-send
- `/notifications/track` - notification-tracking-track
- `/notifications/analytics` - notification-analytics
- `/notifications/push` - push-notifications-send
- `/notifications/devices` - device-register

### Organizations
- `/organizations/analytics` - organization-analytics
- `/organizations/{organizationId}/billing` - organization-billing-get
- `/organizations/{organizationId}/billing/subscription` - organization-billing-update
- `/organizations` - organizations
- `/organizations` - organization-list
- `/organizations/{organizationId}` - organization-manage
- `/organizations/{organizationId}/members/invite` - organization-members-invite
- `/organizations/{organizationId}/settings` - organization-settings-get
- `/organizations/settings` - organization-settings-update
- `/organizations/{organizationId}/teams` - organization-teams-create
- `/organizations/{organizationId}/invite` - user-tenants-invite

### Performance
- `/performance/metrics` - performance-metrics
- `/performance/alert-rules` - performance-alert-rule-create

### Sample
- `/sample` - Production-sample

### Projects
- `/projects/analytics` - project-analytics
- `/projects` - projects
- `/projects` - project-list
- `/projects/{projectId}` - project-manage
- `/projects/{projectId}/members` - project-members-get
- `/projects/members` - project-members-add
- `/projects/members/update` - project-members-update
- `/projects/{projectId}/settings` - project-settings-get
- `/projects/settings` - project-settings-update

### Collaboration
- `/collaboration/sessions` - collaboration-session-create
- `/collaboration/sessions/join` - collaboration-session-join

### Messages
- `/messages` - message-send

### Channels
- `/channels` - channel-create
- `/channels/{channelId}/messages` - message-list

### Signalr
- `/signalr/negotiate` - signalr-negotiate
- `/signalr/broadcast` - signalr-broadcast
- `/signalr/groups` - signalr-groups

### Subscriptions
- `/subscriptions` - subscription-create
- `/subscriptions/{organizationId}` - subscription-get

### Config
- `/config` - config-get

### Tenants
- `/tenants` - tenant-create
- `/tenants/{tenantId}` - tenant-get

### Users
- `/users/activity/track` - user-activity-track
- `/users/activity/analytics` - user-activity-analytics
- `/users/profile` - user-profile
- `/users/profile/preferences` - user-profile-preferences-update
- `/users/{userId?}/permissions` - user-permissions
- `/users/personalization` - user-personalization-get
- `/users/personalization/update` - user-personalization-update
- `/users/preferences` - user-preferences-get
- `/users/preferences/update` - user-preferences-update
- `/users/preferences/reset` - user-preferences-reset
- `/users/{userId?}/profile` - user-profile-get
- `/users/profile/update` - user-profile-update
- `/users/avatar/upload` - user-avatar-upload
- `/users/{userId?}/tenants` - user-tenants-get
- `/users/tenants/switch` - user-tenants-switch

### Webhooks
- `/webhooks/deliver` - webhook-delivery
- `/webhooks` - webhooks
- `/webhooks/test` - webhook-test

### Automations
- `/automations` - automation-create
- `/automations/execute` - automation-execute

### Workflows
- `/workflows/{workflowId}/actions` - workflow-action
- `/workflows/bulk-actions` - workflow-bulk-action
- `/workflows/executions` - workflow-execution-start
- `/workflows/{id}/start` - workflow-start
- `/workflows/{id}/steps/{stepId}/complete` - workflow-execution-complete-step
- `/workflows` - workflows
- `/workflows/{id}` - workflow-get
- `/workflows/analytics` - workflow-monitoring
- `/workflows/schedules` - workflow-schedule-create
- `/workflows/schedules/status` - workflow-schedule-status-update

### Workflow-templates
- `/workflow-templates/create` - workflow-template-create
- `/workflow-templates` - workflow-templates
- `/workflow-templates/{id}` - workflow-templates-get
