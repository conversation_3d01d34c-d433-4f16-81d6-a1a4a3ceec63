"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.shareDocument = shareDocument;
exports.addComment = addComment;
exports.listComments = listComments;
/**
 * Document Collaboration Function
 * Handles document sharing, commenting, and collaborative features
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const bcrypt = __importStar(require("bcrypt"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Share permission levels enum
var SharePermission;
(function (SharePermission) {
    SharePermission["VIEW"] = "VIEW";
    SharePermission["COMMENT"] = "COMMENT";
    SharePermission["EDIT"] = "EDIT";
    SharePermission["ADMIN"] = "ADMIN";
})(SharePermission || (SharePermission = {}));
// Comment types enum
var CommentType;
(function (CommentType) {
    CommentType["GENERAL"] = "GENERAL";
    CommentType["SUGGESTION"] = "SUGGESTION";
    CommentType["APPROVAL"] = "APPROVAL";
    CommentType["QUESTION"] = "QUESTION";
})(CommentType || (CommentType = {}));
// Validation schemas
const shareDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    shareWith: Joi.array().items(Joi.object({
        userId: Joi.string().uuid().optional(),
        email: Joi.string().email().optional(),
        permission: Joi.string().valid(...Object.values(SharePermission)).required()
    }).or('userId', 'email')).min(1).required(),
    message: Joi.string().max(500).optional(),
    expiresAt: Joi.date().iso().optional(),
    allowDownload: Joi.boolean().default(true),
    allowPrint: Joi.boolean().default(true),
    requirePassword: Joi.boolean().default(false),
    password: Joi.string().min(6).optional()
});
const addCommentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    content: Joi.string().required().max(2000),
    type: Joi.string().valid(...Object.values(CommentType)).default(CommentType.GENERAL),
    parentCommentId: Joi.string().uuid().optional(),
    position: Joi.object({
        page: Joi.number().integer().min(1).optional(),
        x: Joi.number().optional(),
        y: Joi.number().optional(),
        width: Joi.number().optional(),
        height: Joi.number().optional()
    }).optional(),
    mentions: Joi.array().items(Joi.string().uuid()).optional()
});
const listCommentsSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    type: Joi.string().valid(...Object.values(CommentType)).optional(),
    resolved: Joi.boolean().optional()
});
/**
 * Share document handler
 */
async function shareDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Share document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = shareDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { documentId, shareWith, message, expiresAt, allowDownload, allowPrint, requirePassword, password } = value;
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check if user has permission to share
        const canShare = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!canShare) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to share document" }
            }, request);
        }
        const shareResults = [];
        const shareId = (0, uuid_1.v4)();
        // Process each share recipient
        for (const recipient of shareWith) {
            let targetUserId = recipient.userId;
            // If email provided, try to find user by email
            if (!targetUserId && recipient.email) {
                const userQuery = 'SELECT * FROM c WHERE c.email = @email';
                const users = await database_1.db.queryItems('users', userQuery, [recipient.email]);
                if (users.length > 0) {
                    targetUserId = users[0].id;
                }
            }
            // Create share record
            const shareRecord = {
                id: (0, uuid_1.v4)(),
                shareId,
                documentId,
                sharedBy: user.id,
                sharedWith: targetUserId,
                email: recipient.email,
                permission: recipient.permission,
                message,
                expiresAt,
                allowDownload,
                allowPrint,
                requirePassword,
                passwordHash: password ? await hashPassword(password) : undefined,
                isActive: true,
                createdAt: new Date().toISOString(),
                accessCount: 0,
                lastAccessedAt: null,
                organizationId: document.organizationId,
                projectId: document.projectId,
                tenantId: user.tenantId
            };
            await database_1.db.createItem('document-shares', shareRecord);
            // Send notification if user exists
            if (targetUserId) {
                await database_1.db.createItem('notifications', {
                    id: (0, uuid_1.v4)(),
                    recipientId: targetUserId,
                    senderId: user.id,
                    type: 'DOCUMENT_SHARED',
                    title: `Document shared: ${document.name}`,
                    message: message || `${user.name || user.email} shared a document with you`,
                    priority: 'MEDIUM',
                    actionUrl: `/documents/${documentId}`,
                    actionText: 'View Document',
                    documentId,
                    organizationId: document.organizationId,
                    projectId: document.projectId,
                    isRead: false,
                    createdAt: new Date().toISOString(),
                    tenantId: user.tenantId
                });
            }
            shareResults.push({
                email: recipient.email,
                userId: targetUserId,
                permission: recipient.permission,
                shareId: shareRecord.id,
                notificationSent: !!targetUserId
            });
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_shared",
            userId: user.id,
            organizationId: document.organizationId,
            projectId: document.projectId,
            documentId,
            timestamp: new Date().toISOString(),
            details: {
                documentName: document.name,
                recipientCount: shareWith.length,
                permissions: shareWith.map((s) => s.permission),
                hasExpiration: !!expiresAt
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document shared successfully", {
            correlationId,
            documentId,
            sharedBy: user.id,
            recipientCount: shareWith.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                shareId,
                documentId,
                documentName: document.name,
                shares: shareResults,
                expiresAt,
                message: "Document shared successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Share document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Add comment handler
 */
async function addComment(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Add comment started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = addCommentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { documentId, content, type, parentCommentId, position, mentions } = value;
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check if user has permission to comment
        const canComment = await checkDocumentAccess(user.id, documentId, SharePermission.COMMENT);
        if (!canComment) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to comment on document" }
            }, request);
        }
        // Create comment
        const commentId = (0, uuid_1.v4)();
        const comment = {
            id: commentId,
            documentId,
            content,
            type,
            parentCommentId,
            position,
            mentions: mentions || [],
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            isResolved: false,
            resolvedBy: null,
            resolvedAt: null,
            likesCount: 0,
            repliesCount: 0,
            organizationId: document.organizationId,
            projectId: document.projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-comments', comment);
        // Update parent comment reply count if this is a reply
        if (parentCommentId) {
            const parentComment = await database_1.db.readItem('document-comments', parentCommentId, parentCommentId);
            if (parentComment) {
                const updatedParent = {
                    ...parentComment,
                    id: parentCommentId,
                    repliesCount: (parentComment.repliesCount || 0) + 1,
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('document-comments', updatedParent);
            }
        }
        // Send notifications to mentioned users
        if (mentions && mentions.length > 0) {
            for (const mentionedUserId of mentions) {
                await database_1.db.createItem('notifications', {
                    id: (0, uuid_1.v4)(),
                    recipientId: mentionedUserId,
                    senderId: user.id,
                    type: 'DOCUMENT_COMMENTED',
                    title: `You were mentioned in a comment`,
                    message: `${user.name || user.email} mentioned you in a comment on ${document.name}`,
                    priority: 'MEDIUM',
                    actionUrl: `/documents/${documentId}#comment-${commentId}`,
                    actionText: 'View Comment',
                    documentId,
                    organizationId: document.organizationId,
                    projectId: document.projectId,
                    isRead: false,
                    createdAt: new Date().toISOString(),
                    tenantId: user.tenantId
                });
            }
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_commented",
            userId: user.id,
            organizationId: document.organizationId,
            projectId: document.projectId,
            documentId,
            commentId,
            timestamp: new Date().toISOString(),
            details: {
                documentName: document.name,
                commentType: type,
                isReply: !!parentCommentId,
                mentionsCount: mentions?.length || 0
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Comment added successfully", {
            correlationId,
            commentId,
            documentId,
            userId: user.id,
            type,
            isReply: !!parentCommentId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: commentId,
                documentId,
                content,
                type,
                createdBy: user.id,
                createdAt: comment.createdAt,
                message: "Comment added successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Add comment failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List comments handler
 */
async function listComments(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("List comments started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listCommentsSchema.validate({ ...queryParams, documentId });
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, type, resolved } = value;
        // Check if user has access to document
        const canView = await checkDocumentAccess(user.id, documentId, SharePermission.VIEW);
        if (!canView) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Build query
        let queryText = 'SELECT * FROM c WHERE c.documentId = @documentId';
        const parameters = [documentId];
        if (type) {
            queryText += ' AND c.type = @type';
            parameters.push(type);
        }
        if (resolved !== undefined) {
            queryText += ' AND c.isResolved = @resolved';
            parameters.push(resolved);
        }
        // Order by creation date
        queryText += ' ORDER BY c.createdAt DESC';
        // Get total count
        const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
        const countResult = await database_1.db.queryItems('document-comments', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const comments = await database_1.db.queryItems('document-comments', paginatedQuery, parameters);
        // Enrich comments with user information
        const enrichedComments = await Promise.all(comments.map(async (comment) => {
            let authorName = 'Unknown User';
            try {
                const author = await database_1.db.readItem('users', comment.createdBy, comment.createdBy);
                if (author) {
                    authorName = author.name || author.email;
                }
            }
            catch (error) {
                // Author might not exist
            }
            return {
                ...comment,
                authorName
            };
        }));
        logger_1.logger.info("Comments listed successfully", {
            correlationId,
            documentId,
            userId: user.id,
            count: comments.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                items: enrichedComments,
                total,
                page,
                limit,
                hasMore: page * limit < total
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List comments failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check document access
 */
async function checkDocumentAccess(userId, documentId, requiredPermission) {
    try {
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document)
            return false;
        // Check if user is owner
        if (document.createdBy === userId)
            return true;
        // Check organization membership
        if (document.organizationId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [userId, document.organizationId, 'active']);
            if (memberships.length > 0) {
                const membership = memberships[0];
                if (membership.role === 'ADMIN')
                    return true;
                if (requiredPermission === SharePermission.VIEW && membership.role === 'MEMBER')
                    return true;
            }
        }
        // Check direct shares
        const shareQuery = 'SELECT * FROM c WHERE c.documentId = @documentId AND c.sharedWith = @userId AND c.isActive = true AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
        const shares = await database_1.db.queryItems('document-shares', shareQuery, [documentId, userId, new Date().toISOString()]);
        if (shares.length > 0) {
            const share = shares[0];
            const permissionLevels = [SharePermission.VIEW, SharePermission.COMMENT, SharePermission.EDIT, SharePermission.ADMIN];
            const userLevel = permissionLevels.indexOf(share.permission);
            const requiredLevel = permissionLevels.indexOf(requiredPermission);
            return userLevel >= requiredLevel;
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error("Error checking document access", { userId, documentId, requiredPermission, error });
        return false;
    }
}
/**
 * Production password hashing using bcrypt
 */
async function hashPassword(password) {
    try {
        const saltRounds = 12; // High security salt rounds
        const hashedPassword = await bcrypt.hash(password, saltRounds);
        logger_1.logger.info('Password hashed successfully');
        return hashedPassword;
    }
    catch (error) {
        logger_1.logger.error('Password hashing failed', { error: error instanceof Error ? error.message : String(error) });
        throw new Error('Password hashing failed');
    }
}
/**
 * Verify password against hash
 */
async function verifyPassword(password, hash) {
    try {
        const isValid = await bcrypt.compare(password, hash);
        logger_1.logger.info('Password verification completed', { isValid });
        return isValid;
    }
    catch (error) {
        logger_1.logger.error('Password verification failed', { error: error instanceof Error ? error.message : String(error) });
        return false;
    }
}
// Register functions
functions_1.app.http('document-share', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}/share',
    handler: shareDocument
});
// Note: Comment functionality is handled by document-comments.ts
// app.http('document-comment-add', {
//   methods: ['POST', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'documents/{id}/comments',
//   handler: addComment
// });
// app.http('document-comment-list', {
//   methods: ['GET', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'documents/{documentId}/comments',
//   handler: listComments
// });
//# sourceMappingURL=document-collaboration.js.map