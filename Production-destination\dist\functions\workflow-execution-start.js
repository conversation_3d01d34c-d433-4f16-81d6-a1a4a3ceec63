"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.startWorkflowExecution = startWorkflowExecution;
/**
 * Workflow Execution Start Function
 * Handles starting workflow executions with proper validation and orchestration
 * Migrated from old-arch/src/workflow-service/execution/start/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Workflow execution status
var WorkflowExecutionStatus;
(function (WorkflowExecutionStatus) {
    WorkflowExecutionStatus["PENDING"] = "PENDING";
    WorkflowExecutionStatus["RUNNING"] = "RUNNING";
    WorkflowExecutionStatus["PAUSED"] = "PAUSED";
    WorkflowExecutionStatus["COMPLETED"] = "COMPLETED";
    WorkflowExecutionStatus["FAILED"] = "FAILED";
    WorkflowExecutionStatus["CANCELLED"] = "CANCELLED";
})(WorkflowExecutionStatus || (WorkflowExecutionStatus = {}));
var WorkflowStepStatus;
(function (WorkflowStepStatus) {
    WorkflowStepStatus["PENDING"] = "PENDING";
    WorkflowStepStatus["RUNNING"] = "RUNNING";
    WorkflowStepStatus["COMPLETED"] = "COMPLETED";
    WorkflowStepStatus["FAILED"] = "FAILED";
    WorkflowStepStatus["SKIPPED"] = "SKIPPED";
    WorkflowStepStatus["WAITING_APPROVAL"] = "WAITING_APPROVAL";
})(WorkflowStepStatus || (WorkflowStepStatus = {}));
var WorkflowPriority;
(function (WorkflowPriority) {
    WorkflowPriority["LOW"] = "LOW";
    WorkflowPriority["NORMAL"] = "NORMAL";
    WorkflowPriority["HIGH"] = "HIGH";
    WorkflowPriority["URGENT"] = "URGENT";
})(WorkflowPriority || (WorkflowPriority = {}));
// Validation schema
const startWorkflowSchema = Joi.object({
    workflowId: Joi.string().uuid().required(),
    documentIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
    priority: Joi.string().valid(...Object.values(WorkflowPriority)).default(WorkflowPriority.NORMAL),
    variables: Joi.object().default({}),
    assignees: Joi.object().pattern(Joi.string(), // stepId
    Joi.array().items(Joi.string().uuid()) // userIds
    ).optional(),
    dueDate: Joi.string().isoDate().optional(),
    notes: Joi.string().max(1000).optional(),
    options: Joi.object({
        autoStart: Joi.boolean().default(true),
        skipValidation: Joi.boolean().default(false),
        notifyAssignees: Joi.boolean().default(true),
        enableParallelExecution: Joi.boolean().optional()
    }).optional()
});
/**
 * Start workflow execution handler
 */
async function startWorkflowExecution(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Start workflow execution started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = startWorkflowSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const workflowRequest = value;
        // Get workflow definition
        const workflow = await database_1.db.readItem('workflows', workflowRequest.workflowId, workflowRequest.workflowId);
        if (!workflow) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Workflow not found" }
            }, request);
        }
        const workflowData = workflow;
        // Check workflow access permissions
        const hasAccess = await checkWorkflowAccess(workflowData, user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to workflow" }
            }, request);
        }
        // Validate documents
        const documentValidation = await validateDocuments(workflowRequest.documentIds, user);
        if (!documentValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Invalid documents",
                    details: documentValidation.errors
                }
            }, request);
        }
        // Check workflow execution limits
        if (await isWorkflowExecutionLimitReached(workflowData.organizationId, workflowData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Workflow execution limit reached for this organization tier"
                }
            }, request);
        }
        // Validate assignees if provided
        if (workflowRequest.assignees && !workflowRequest.options?.skipValidation) {
            const assigneeValidation = await validateAssignees(workflowRequest.assignees, workflowData.organizationId);
            if (!assigneeValidation.valid) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: "Invalid assignees",
                        details: assigneeValidation.errors
                    }
                }, request);
            }
        }
        // Create workflow execution
        const executionId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Prepare execution steps
        const executionSteps = await prepareExecutionSteps(workflowData.definition.steps, workflowRequest.assignees, workflowRequest.variables);
        const workflowExecution = {
            id: executionId,
            workflowId: workflowRequest.workflowId,
            workflowName: workflowData.name,
            workflowVersion: workflowData.version,
            status: workflowRequest.options?.autoStart ? WorkflowExecutionStatus.RUNNING : WorkflowExecutionStatus.PENDING,
            priority: workflowRequest.priority,
            documentIds: workflowRequest.documentIds,
            variables: workflowRequest.variables,
            assignees: workflowRequest.assignees || {},
            dueDate: workflowRequest.dueDate,
            notes: workflowRequest.notes,
            organizationId: workflowData.organizationId,
            projectId: workflowData.projectId,
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            startedAt: workflowRequest.options?.autoStart ? now : undefined,
            steps: executionSteps,
            currentStepIndex: workflowRequest.options?.autoStart ? 0 : -1,
            metadata: {
                totalSteps: executionSteps.length,
                completedSteps: 0,
                failedSteps: 0,
                estimatedDuration: calculateEstimatedDuration(workflowData.definition),
                actualDuration: 0
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('workflow-executions', workflowExecution);
        // Update workflow usage statistics
        const updatedWorkflow = {
            ...workflowData,
            metadata: {
                ...workflowData.metadata,
                executionCount: (workflowData.metadata?.executionCount || 0) + 1,
                lastExecutedAt: now
            },
            updatedAt: now
        };
        await database_1.db.updateItem('workflows', updatedWorkflow);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "workflow_execution_started",
            userId: user.id,
            organizationId: workflowData.organizationId,
            projectId: workflowData.projectId,
            workflowId: workflowRequest.workflowId,
            executionId,
            timestamp: now,
            details: {
                workflowName: workflowData.name,
                priority: workflowRequest.priority,
                documentCount: workflowRequest.documentIds.length,
                stepCount: executionSteps.length,
                autoStarted: workflowRequest.options?.autoStart || false
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'WorkflowExecutionStarted',
            aggregateId: executionId,
            aggregateType: 'WorkflowExecution',
            version: 1,
            data: {
                execution: workflowExecution,
                workflow: workflowData,
                startedBy: user.id
            },
            userId: user.id,
            organizationId: workflowData.organizationId,
            tenantId: user.tenantId
        });
        // Send notifications to assignees if enabled
        if (workflowRequest.options?.notifyAssignees !== false) {
            await notifyWorkflowAssignees(workflowExecution, workflowData);
        }
        // Send notification to creator
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'WORKFLOW_EXECUTION_STARTED',
            title: 'Workflow execution started',
            message: `Workflow "${workflowData.name}" has been started with ${workflowRequest.documentIds.length} documents.`,
            priority: workflowRequest.priority === WorkflowPriority.URGENT ? 'high' : 'normal',
            metadata: {
                executionId,
                workflowId: workflowRequest.workflowId,
                workflowName: workflowData.name,
                documentCount: workflowRequest.documentIds.length,
                priority: workflowRequest.priority,
                organizationId: workflowData.organizationId
            },
            organizationId: workflowData.organizationId,
            projectId: workflowData.projectId
        });
        // Prepare response
        const currentStep = workflowRequest.options?.autoStart && executionSteps.length > 0 ? {
            id: executionSteps[0].id,
            name: executionSteps[0].name,
            assignees: executionSteps[0].assignees,
            status: executionSteps[0].status
        } : undefined;
        const response = {
            executionId,
            workflowId: workflowRequest.workflowId,
            workflowName: workflowData.name,
            status: workflowExecution.status,
            priority: workflowRequest.priority,
            documentIds: workflowRequest.documentIds,
            currentStep,
            estimatedCompletionTime: workflowExecution.dueDate || calculateEstimatedCompletionTime(workflowData.definition),
            createdAt: now,
            startedAt: workflowExecution.startedAt,
            success: true
        };
        logger_1.logger.info("Workflow execution started successfully", {
            correlationId,
            executionId,
            workflowId: workflowRequest.workflowId,
            workflowName: workflowData.name,
            priority: workflowRequest.priority,
            documentCount: workflowRequest.documentIds.length,
            autoStarted: workflowRequest.options?.autoStart || false,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Start workflow execution failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check workflow access permissions
 */
async function checkWorkflowAccess(workflow, user) {
    // User is the creator
    if (workflow.createdBy === user.id) {
        return true;
    }
    // Check if user is member of the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, workflow.organizationId, 'ACTIVE']);
    if (memberships.length === 0) {
        return false;
    }
    // Check workflow permissions
    if (workflow.permissions) {
        return workflow.permissions.some((p) => p.entityType === 'user' && p.entityId === user.id && p.permission === 'execute');
    }
    // Default: organization members can execute workflows
    return true;
}
/**
 * Validate documents
 */
async function validateDocuments(documentIds, user) {
    const errors = [];
    for (const documentId of documentIds) {
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            errors.push(`Document ${documentId} not found`);
            continue;
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            errors.push(`Access denied to document ${documentId}`);
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
/**
 * Validate assignees
 */
async function validateAssignees(assignees, organizationId) {
    const errors = [];
    for (const [stepId, userIds] of Object.entries(assignees)) {
        for (const userId of userIds) {
            // Check if user is member of organization
            const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [userId, organizationId, 'ACTIVE']);
            if (memberships.length === 0) {
                errors.push(`User ${userId} is not a member of the organization`);
            }
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
/**
 * Check if workflow execution limit is reached
 */
async function isWorkflowExecutionLimitReached(organizationId, tier) {
    try {
        // Get current month's execution count
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const executionQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
        const result = await database_1.db.queryItems('workflow-executions', executionQuery, [organizationId, startOfMonth.toISOString()]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 50,
            'PROFESSIONAL': 500,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check workflow execution limit', { error, organizationId });
        return false;
    }
}
/**
 * Prepare execution steps
 */
async function prepareExecutionSteps(workflowSteps, assignees, variables) {
    return workflowSteps.map((step, index) => ({
        id: step.id,
        name: step.name,
        description: step.description,
        type: step.type,
        action: step.action,
        assignees: assignees?.[step.id] || step.assignedTo || [],
        dependencies: step.dependencies || [],
        conditions: step.conditions || [],
        status: index === 0 ? WorkflowStepStatus.PENDING : WorkflowStepStatus.PENDING,
        startedAt: null,
        completedAt: null,
        result: null,
        error: null,
        variables: { ...variables },
        metadata: {
            retryCount: 0,
            maxRetries: step.maxRetries || 3,
            timeout: step.timeout || 3600 // 1 hour default
        }
    }));
}
/**
 * Calculate estimated duration
 */
function calculateEstimatedDuration(workflowDefinition) {
    // Simplified calculation - in production, use historical data
    const stepCount = workflowDefinition.steps?.length || 1;
    const avgStepDuration = 30; // 30 minutes per step average
    return stepCount * avgStepDuration;
}
/**
 * Calculate estimated completion time
 */
function calculateEstimatedCompletionTime(workflowDefinition) {
    const estimatedMinutes = calculateEstimatedDuration(workflowDefinition);
    const completionTime = new Date(Date.now() + estimatedMinutes * 60 * 1000);
    return completionTime.toISOString();
}
/**
 * Notify workflow assignees
 */
async function notifyWorkflowAssignees(execution, workflow) {
    try {
        const currentStep = execution.steps[0]; // First step
        if (currentStep && currentStep.assignees.length > 0) {
            for (const assigneeId of currentStep.assignees) {
                await notification_1.notificationService.sendNotification({
                    userId: assigneeId,
                    type: 'WORKFLOW_STEP_ASSIGNED',
                    title: 'New workflow assignment',
                    message: `You have been assigned to step "${currentStep.name}" in workflow "${workflow.name}".`,
                    priority: execution.priority === WorkflowPriority.URGENT ? 'high' : 'normal',
                    metadata: {
                        executionId: execution.id,
                        workflowId: execution.workflowId,
                        workflowName: workflow.name,
                        stepId: currentStep.id,
                        stepName: currentStep.name,
                        priority: execution.priority,
                        organizationId: workflow.organizationId
                    },
                    organizationId: workflow.organizationId,
                    projectId: workflow.projectId
                });
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to notify workflow assignees', { error, executionId: execution.id });
    }
}
// Register functions
functions_1.app.http('workflow-execution-start', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflows/executions',
    handler: startWorkflowExecution
});
//# sourceMappingURL=workflow-execution-start.js.map