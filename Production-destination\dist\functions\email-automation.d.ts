/**
 * Email Automation Function
 * Handles automated email sending, templates, and campaigns
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Send email handler
 */
export declare function sendEmail(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create email template handler
 */
export declare function createEmailTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List emails handler
 */
export declare function listEmails(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
