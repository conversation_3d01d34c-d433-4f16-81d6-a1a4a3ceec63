"use strict";
/**
 * Environment configuration for Azure Functions
 * Loads environment variables and validates required settings
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.config = void 0;
const dotenv = __importStar(require("dotenv"));
// Load environment variables from .env file in development
if (process.env.NODE_ENV !== 'production') {
    dotenv.config();
}
// Environment validation
const requiredEnvVars = [
    'COSMOS_DB_ENDPOINT',
    'COSMOS_DB_KEY',
    'COSMOS_DB_DATABASE',
    'AZURE_STORAGE_CONNECTION_STRING'
];
// Check for required environment variables
const missingEnvVars = requiredEnvVars.filter(envVar => !process.env[envVar]);
if (missingEnvVars.length > 0) {
    console.warn(`Warning: Missing environment variables: ${missingEnvVars.join(', ')}`);
    console.warn('Some functions may not work properly without these variables.');
}
// Export environment configuration
exports.config = {
    // Database
    cosmosDb: {
        endpoint: process.env.COSMOS_DB_ENDPOINT || '',
        key: process.env.COSMOS_DB_KEY || '',
        database: process.env.COSMOS_DB_DATABASE || 'hepz-db'
    },
    // Storage
    storage: {
        connectionString: process.env.AZURE_STORAGE_CONNECTION_STRING || ''
    },
    // AI Services
    ai: {
        azureOpenAI: {
            endpoint: process.env.AZURE_OPENAI_ENDPOINT || '',
            key: process.env.AZURE_OPENAI_KEY || '',
            deploymentName: process.env.AZURE_OPENAI_DEPLOYMENT_NAME || 'gpt-4'
        },
        documentIntelligence: {
            endpoint: process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT || '',
            key: process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY || ''
        },
        search: {
            endpoint: process.env.AZURE_SEARCH_ENDPOINT || '',
            key: process.env.AZURE_SEARCH_KEY || '',
            indexName: process.env.AZURE_SEARCH_INDEX_NAME || 'documents'
        }
    },
    // Service Bus
    serviceBus: {
        connectionString: process.env.AZURE_SERVICE_BUS_CONNECTION_STRING || ''
    },
    // Email
    email: {
        postmarkServerToken: process.env.POSTMARK_SERVER_TOKEN || ''
    },
    // Redis
    redis: {
        connectionString: process.env.REDIS_CONNECTION_STRING || ''
    },
    // Application
    app: {
        environment: process.env.NODE_ENV || 'development',
        version: process.env.VERSION || '1.0.0',
        logLevel: process.env.LOG_LEVEL || 'info'
    }
};
exports.default = exports.config;
//# sourceMappingURL=env.js.map