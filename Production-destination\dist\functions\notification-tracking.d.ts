/**
 * Notification Tracking Function
 * Handles notification engagement tracking, analytics, and delivery monitoring
 * Migrated from old-arch/src/notification-service/analytics/track/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Track notification engagement handler
 */
export declare function trackNotificationEngagement(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get notification analytics handler
 */
export declare function getNotificationAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
