"use strict";
/**
 * Authentication utilities for Azure Functions
 * Handles JWT token validation and user context extraction
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.extractToken = extractToken;
exports.validateToken = validateToken;
exports.authenticateRequest = authenticateRequest;
exports.hasRole = hasRole;
exports.hasPermission = hasPermission;
exports.requireAuth = requireAuth;
const jwt = __importStar(require("jsonwebtoken"));
const jwks = __importStar(require("jwks-rsa"));
const logger_1 = require("./logger");
/**
 * Extract and validate JWT token from request
 */
function extractToken(request) {
    const authHeader = request.headers.get('authorization');
    if (!authHeader) {
        return null;
    }
    if (authHeader.startsWith('Bearer ')) {
        return authHeader.substring(7);
    }
    return null;
}
/**
 * Validate JWT token and extract user context
 */
async function validateToken(token) {
    try {
        // Production JWT validation with Azure AD B2C
        const tenantId = process.env.AZURE_AD_B2C_TENANT_ID;
        const clientId = process.env.AZURE_AD_B2C_CLIENT_ID;
        if (!tenantId || !clientId) {
            logger_1.logger.error('Azure AD B2C configuration missing');
            return {
                success: false,
                error: 'Authentication service not configured'
            };
        }
        // Production: Verify JWT signature using Azure AD B2C public keys
        let payload;
        try {
            // Create JWKS client for Azure AD B2C
            const jwksClient = jwks({
                jwksUri: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${tenantId}/discovery/v2.0/keys`,
                cache: true,
                cacheMaxEntries: 5,
                cacheMaxAge: 600000, // 10 minutes
                timeout: 30000,
                jwksRequestsPerMinute: 5,
                jwksRequestsPerMinute: 5
            });
            // Decode token header to get key ID
            const decoded = jwt.decode(token, { complete: true });
            if (!decoded || !decoded.header || !decoded.header.kid) {
                return {
                    success: false,
                    error: 'Invalid token structure'
                };
            }
            // Get signing key from JWKS
            const key = await jwksClient.getSigningKey(decoded.header.kid);
            const signingKey = key.getPublicKey();
            // Verify token signature and extract payload
            payload = jwt.verify(token, signingKey, {
                audience: clientId,
                issuer: `https://${process.env.AZURE_AD_B2C_AUTHORITY_DOMAIN}/${tenantId}/v2.0/`,
                algorithms: ['RS256']
            });
            logger_1.logger.info('JWT signature verified successfully', {
                kid: decoded.header.kid,
                alg: decoded.header.alg
            });
        }
        catch (verifyError) {
            logger_1.logger.error('JWT signature verification failed', {
                error: verifyError instanceof Error ? verifyError.message : String(verifyError)
            });
            return {
                success: false,
                error: 'Token signature verification failed'
            };
        }
        // Token is already validated by jwt.verify() above, including expiration, audience, and issuer
        // Extract user information from token
        const user = {
            id: payload.sub || payload.oid || payload.userId || payload.id,
            email: payload.email || payload.emails?.[0] || payload.preferred_username,
            name: payload.name || `${payload.given_name || ''} ${payload.family_name || ''}`.trim(),
            tenantId: payload.tid || tenantId,
            organizationId: payload.organizationId || payload.orgId,
            roles: payload.roles || [],
            permissions: payload.permissions || []
        };
        if (!user.id || !user.email) {
            return {
                success: false,
                error: 'Token missing required user information'
            };
        }
        logger_1.logger.info('Token validated successfully', {
            userId: user.id,
            email: user.email,
            roles: user.roles?.length || 0,
            permissions: user.permissions?.length || 0
        });
        return {
            success: true,
            user
        };
    }
    catch (error) {
        logger_1.logger.error('Token validation failed', { error: error instanceof Error ? error.message : String(error) });
        return {
            success: false,
            error: 'Token validation failed'
        };
    }
}
/**
 * Authenticate request and extract user context
 */
async function authenticateRequest(request) {
    const token = extractToken(request);
    if (!token) {
        return {
            success: false,
            error: 'No authentication token provided'
        };
    }
    return await validateToken(token);
}
/**
 * Check if user has required role
 */
function hasRole(user, requiredRole) {
    return user.roles?.includes(requiredRole) || false;
}
/**
 * Check if user has required permission
 */
function hasPermission(user, requiredPermission) {
    return user.permissions?.includes(requiredPermission) || false;
}
/**
 * Create authentication middleware
 */
function requireAuth(handler) {
    return async (request, context) => {
        const authResult = await authenticateRequest(request);
        if (!authResult.success || !authResult.user) {
            return {
                status: 401,
                headers: {
                    'Content-Type': 'application/json'
                },
                jsonBody: {
                    error: 'Unauthorized',
                    message: authResult.error || 'Authentication required'
                }
            };
        }
        return await handler(request, context, authResult.user);
    };
}
//# sourceMappingURL=auth.js.map