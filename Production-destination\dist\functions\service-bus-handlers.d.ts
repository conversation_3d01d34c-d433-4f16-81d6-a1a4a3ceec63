/**
 * Service Bus Handlers for Azure Functions
 * Handles Service Bus queue and topic message processing
 */
interface ServiceBusMetrics {
    messagesSent: number;
    messagesReceived: number;
    messagesDeadLettered: number;
    errors: number;
    averageProcessingTime: number;
    activeConnections: number;
}
/**
 * Enhanced Service Bus utilities
 */
/**
 * Send message with enhanced features using shared serviceBusEnhanced service
 */
export declare function sendEnhancedMessage(destination: string, message: any, options?: {
    messageId?: string;
    sessionId?: string;
    timeToLive?: number;
    scheduledEnqueueTime?: Date;
    correlationId?: string;
    isQueue?: boolean;
}): Promise<boolean>;
/**
 * Send batch of messages using shared serviceBusEnhanced service
 */
export declare function sendBatchMessages(destination: string, messages: any[], isQueue?: boolean): Promise<boolean>;
/**
 * Process dead letter messages using shared service
 * Note: This is a simplified implementation - the shared service handles dead letter processing internally
 */
export declare function processDeadLetterQueue(queueOrTopicName: string, subscriptionName?: string): Promise<void>;
/**
 * Get service metrics
 */
export declare function getServiceBusMetrics(): ServiceBusMetrics;
export {};
