"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.updateDocumentMetadata = updateDocumentMetadata;
exports.extractDocumentMetadata = extractDocumentMetadata;
/**
 * Document Metadata Management Function
 * Handles advanced document metadata management, tagging, and classification
 * Migrated from old-arch/src/document-service/metadata/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Metadata types and enums
var MetadataType;
(function (MetadataType) {
    MetadataType["SYSTEM"] = "SYSTEM";
    MetadataType["USER"] = "USER";
    MetadataType["EXTRACTED"] = "EXTRACTED";
    MetadataType["COMPUTED"] = "COMPUTED";
})(MetadataType || (MetadataType = {}));
var DataType;
(function (DataType) {
    DataType["STRING"] = "STRING";
    DataType["NUMBER"] = "NUMBER";
    DataType["DATE"] = "DATE";
    DataType["BOOLEAN"] = "BOOLEAN";
    DataType["ARRAY"] = "ARRAY";
    DataType["OBJECT"] = "OBJECT";
    DataType["URL"] = "URL";
    DataType["EMAIL"] = "EMAIL";
    DataType["PHONE"] = "PHONE";
})(DataType || (DataType = {}));
// Validation schemas
const updateDocumentMetadataSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    metadata: Joi.object({
        title: Joi.string().max(200).optional(),
        description: Joi.string().max(1000).optional(),
        author: Joi.string().max(100).optional(),
        subject: Joi.string().max(200).optional(),
        keywords: Joi.array().items(Joi.string().max(50)).max(20).optional(),
        category: Joi.string().max(50).optional(),
        classification: Joi.string().valid('public', 'internal', 'confidential', 'restricted').optional(),
        language: Joi.string().length(2).optional(),
        department: Joi.string().max(100).optional(),
        project: Joi.string().max(100).optional(),
        version: Joi.string().max(20).optional(),
        status: Joi.string().max(50).optional(),
        priority: Joi.string().valid('low', 'normal', 'high', 'urgent').optional(),
        expiryDate: Joi.string().isoDate().optional(),
        reviewDate: Joi.string().isoDate().optional(),
        retentionPeriod: Joi.number().min(1).max(3650).optional(), // Days
        customFields: Joi.object().pattern(Joi.string(), Joi.object({
            value: Joi.any().required(),
            type: Joi.string().valid(...Object.values(DataType)).required(),
            label: Joi.string().optional(),
            description: Joi.string().optional()
        })).optional()
    }).required(),
    tags: Joi.array().items(Joi.string().max(30)).max(50).optional(),
    replaceExisting: Joi.boolean().default(false)
});
const extractMetadataSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    extractionType: Joi.string().valid('basic', 'advanced', 'ai_powered').default('basic'),
    includeContent: Joi.boolean().default(true),
    includeStructure: Joi.boolean().default(true),
    includeEntities: Joi.boolean().default(false),
    customExtractors: Joi.array().items(Joi.string()).optional()
});
/**
 * Update document metadata handler
 */
async function updateDocumentMetadata(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update document metadata started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateDocumentMetadataSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const metadataRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', metadataRequest.documentId, metadataRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Get current metadata
        let currentMetadata = await database_1.db.readItem('document-metadata', metadataRequest.documentId, metadataRequest.documentId);
        if (!currentMetadata) {
            currentMetadata = createDefaultMetadata(documentData);
        }
        const metadataData = currentMetadata;
        const now = new Date().toISOString();
        // Update metadata
        const updatedMetadata = {
            ...metadataData,
            id: metadataRequest.documentId,
            documentId: metadataRequest.documentId,
            userMetadata: metadataRequest.replaceExisting
                ? metadataRequest.metadata
                : { ...metadataData.userMetadata, ...metadataRequest.metadata },
            tags: metadataRequest.tags !== undefined
                ? metadataRequest.tags
                : metadataData.tags || [],
            lastUpdated: now,
            updatedBy: user.id,
            tenantId: user.tenantId
        };
        await database_1.db.upsertItem('document-metadata', updatedMetadata);
        // Update document with metadata summary
        const updatedDocument = {
            ...documentData,
            id: metadataRequest.documentId,
            metadata: {
                ...documentData.metadata,
                title: metadataRequest.metadata.title || documentData.metadata?.title,
                description: metadataRequest.metadata.description || documentData.metadata?.description,
                category: metadataRequest.metadata.category || documentData.metadata?.category,
                classification: metadataRequest.metadata.classification || documentData.metadata?.classification,
                tags: metadataRequest.tags || documentData.metadata?.tags || []
            },
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_metadata_updated",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: metadataRequest.documentId,
            timestamp: now,
            details: {
                documentName: documentData.name,
                updatedFields: Object.keys(metadataRequest.metadata),
                tagsUpdated: metadataRequest.tags !== undefined,
                tagCount: metadataRequest.tags?.length || 0,
                replaceExisting: metadataRequest.replaceExisting
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentMetadataUpdated',
            aggregateId: metadataRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentData,
                metadata: updatedMetadata,
                updatedBy: user.id,
                updatedFields: Object.keys(metadataRequest.metadata)
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document metadata updated successfully", {
            correlationId,
            documentId: metadataRequest.documentId,
            documentName: documentData.name,
            updatedFields: Object.keys(metadataRequest.metadata),
            tagCount: metadataRequest.tags?.length || 0,
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: metadataRequest.documentId,
                documentName: documentData.name,
                metadata: updatedMetadata,
                updatedFields: Object.keys(metadataRequest.metadata),
                updatedAt: now,
                message: "Document metadata updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update document metadata failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Extract document metadata handler
 */
async function extractDocumentMetadata(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Extract document metadata started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = extractMetadataSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const extractRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', extractRequest.documentId, extractRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Extract metadata based on type
        const extractedData = await performMetadataExtraction(documentData, extractRequest.extractionType || 'basic', extractRequest);
        // Get or create metadata record
        let metadataRecord = await database_1.db.readItem('document-metadata', extractRequest.documentId, extractRequest.documentId);
        if (!metadataRecord) {
            metadataRecord = createDefaultMetadata(documentData);
        }
        const metadataData = metadataRecord;
        const now = new Date().toISOString();
        // Update with extracted metadata
        const updatedMetadata = {
            ...metadataData,
            id: extractRequest.documentId,
            documentId: extractRequest.documentId,
            extractedMetadata: {
                ...metadataData.extractedMetadata,
                ...extractedData.extracted
            },
            computedMetadata: {
                ...metadataData.computedMetadata,
                ...extractedData.computed
            },
            lastUpdated: now,
            updatedBy: user.id,
            tenantId: user.tenantId
        };
        await database_1.db.upsertItem('document-metadata', updatedMetadata);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_metadata_extracted",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: extractRequest.documentId,
            timestamp: now,
            details: {
                documentName: documentData.name,
                extractionType: extractRequest.extractionType,
                entitiesFound: extractedData.extracted.entities?.length || 0,
                topicsFound: extractedData.extracted.topics?.length || 0,
                processingTime: extractedData.processingTime
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentMetadataExtracted',
            aggregateId: extractRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentData,
                extractionType: extractRequest.extractionType,
                extractedData,
                extractedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document metadata extracted successfully", {
            correlationId,
            documentId: extractRequest.documentId,
            documentName: documentData.name,
            extractionType: extractRequest.extractionType,
            entitiesFound: extractedData.extracted.entities?.length || 0,
            processingTime: extractedData.processingTime,
            extractedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: extractRequest.documentId,
                documentName: documentData.name,
                extractionType: extractRequest.extractionType,
                extractedMetadata: extractedData.extracted,
                computedMetadata: extractedData.computed,
                processingTime: extractedData.processingTime,
                extractedAt: now,
                message: "Document metadata extracted successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Extract document metadata failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
function createDefaultMetadata(document) {
    const now = new Date().toISOString();
    return {
        id: document.id,
        documentId: document.id,
        systemMetadata: {
            fileName: document.name,
            fileSize: document.size || 0,
            contentType: document.contentType || 'application/octet-stream',
            checksum: document.checksum || '',
            createdAt: document.createdAt,
            modifiedAt: document.updatedAt,
            uploadedBy: document.createdBy,
            version: document.version || '1.0'
        },
        userMetadata: {},
        extractedMetadata: {},
        computedMetadata: {},
        tags: [],
        lastUpdated: now,
        updatedBy: document.createdBy,
        tenantId: document.tenantId
    };
}
async function performMetadataExtraction(document, extractionType, options) {
    const startTime = Date.now();
    // Simplified metadata extraction - in production, use actual AI/ML services
    const mockExtracted = {
        contentSummary: `This document contains information about ${document.name}. It appears to be a ${document.contentType} file with various content sections.`,
        detectedLanguage: 'en',
        entities: [
            { name: 'Acme Corporation', type: 'organization', confidence: 0.95, mentions: 3 },
            { name: 'John Smith', type: 'person', confidence: 0.88, mentions: 2 },
            { name: 'New York', type: 'location', confidence: 0.92, mentions: 1 }
        ],
        topics: [
            { name: 'Business Agreement', confidence: 0.85, keywords: ['contract', 'terms', 'agreement'] },
            { name: 'Financial Terms', confidence: 0.78, keywords: ['payment', 'invoice', 'amount'] }
        ],
        structure: {
            sections: 5,
            tables: 2,
            images: 1,
            links: 3
        }
    };
    const mockComputed = {
        searchableText: `${document.name} searchable content extracted from document`,
        indexedKeywords: ['document', 'content', 'metadata', 'extraction'],
        similarityHash: 'abc123def456',
        qualityScore: 0.85,
        readabilityScore: 0.72,
        sentimentScore: 0.1 // Neutral
    };
    const processingTime = Date.now() - startTime;
    return {
        extracted: mockExtracted,
        computed: mockComputed,
        processingTime
    };
}
// Register functions
functions_1.app.http('document-metadata-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/metadata',
    handler: updateDocumentMetadata
});
functions_1.app.http('document-metadata-extract', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/metadata/extract',
    handler: extractDocumentMetadata
});
//# sourceMappingURL=document-metadata-management.js.map