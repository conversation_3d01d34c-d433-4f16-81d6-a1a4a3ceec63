"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharePermission = exports.DocumentSharingStatus = void 0;
exports.createDocumentShare = createDocumentShare;
exports.getDocumentShares = getDocumentShares;
/**
 * Document Sharing Function
 * Handles document sharing operations (create, read, update, revoke)
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Enums
var DocumentSharingStatus;
(function (DocumentSharingStatus) {
    DocumentSharingStatus["ACTIVE"] = "ACTIVE";
    DocumentSharingStatus["REVOKED"] = "REVOKED";
    DocumentSharingStatus["EXPIRED"] = "EXPIRED";
})(DocumentSharingStatus || (exports.DocumentSharingStatus = DocumentSharingStatus = {}));
var SharePermission;
(function (SharePermission) {
    SharePermission["VIEW"] = "VIEW";
    SharePermission["COMMENT"] = "COMMENT";
    SharePermission["EDIT"] = "EDIT";
    SharePermission["DOWNLOAD"] = "DOWNLOAD";
})(SharePermission || (exports.SharePermission = SharePermission = {}));
// Validation schemas
const createShareSchema = Joi.object({
    sharedWith: Joi.string().uuid().required(),
    permissions: Joi.array().items(Joi.string().valid(...Object.values(SharePermission))).required(),
    expiresAt: Joi.date().iso().optional(),
    projectId: Joi.string().uuid().required(),
    organizationId: Joi.string().uuid().required(),
    metadata: Joi.object().optional()
});
const updateShareSchema = Joi.object({
    permissions: Joi.array().items(Joi.string().valid(...Object.values(SharePermission))).optional(),
    expiresAt: Joi.date().iso().optional(),
    status: Joi.string().valid(...Object.values(DocumentSharingStatus)).optional()
});
const getSharesSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    status: Joi.string().valid(...Object.values(DocumentSharingStatus)).optional()
});
/**
 * Create document share handler
 */
async function createDocumentShare(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Create document share started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createShareSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const shareData = value;
        // Verify document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Verify target user exists
        const targetUser = await database_1.db.readItem('users', shareData.sharedWith, shareData.sharedWith);
        if (!targetUser) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Target user not found" }
            }, request);
        }
        // Check if sharing already exists
        const existingShareQuery = 'SELECT * FROM c WHERE c.documentId = @documentId AND c.sharedWith = @sharedWith AND c.status = @status';
        const existingShares = await database_1.db.queryItems('document-sharing', existingShareQuery, [
            documentId,
            shareData.sharedWith,
            DocumentSharingStatus.ACTIVE
        ]);
        if (existingShares.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document is already shared with this user" }
            }, request);
        }
        // Create sharing
        const sharingId = (0, uuid_1.v4)();
        const sharing = {
            id: sharingId,
            documentId,
            sharedBy: user.id,
            sharedWith: shareData.sharedWith,
            sharedAt: new Date().toISOString(),
            expiresAt: shareData.expiresAt,
            permissions: shareData.permissions,
            organizationId: shareData.organizationId,
            projectId: shareData.projectId,
            status: DocumentSharingStatus.ACTIVE,
            metadata: shareData.metadata,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-sharing', sharing);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_shared",
            userId: user.id,
            organizationId: shareData.organizationId,
            projectId: shareData.projectId,
            documentId,
            sharingId,
            timestamp: new Date().toISOString(),
            details: {
                sharedWith: shareData.sharedWith,
                permissions: shareData.permissions,
                expiresAt: shareData.expiresAt
            }
        });
        logger_1.logger.info("Document share created successfully", {
            correlationId,
            sharingId,
            documentId,
            sharedWith: shareData.sharedWith,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: sharing
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create document share failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get document shares handler
 */
async function getDocumentShares(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Get document shares started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getSharesSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, status } = value;
        // Verify document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Build query
        let query = 'SELECT * FROM c WHERE c.documentId = @documentId';
        const parameters = [documentId];
        // Add status filter if provided
        if (status) {
            query += ' AND c.status = @status';
            parameters.push(status);
        }
        // Add tenant isolation
        if (user.tenantId) {
            query += ' AND (c.organizationId = @tenantId OR c.sharedBy = @userId)';
            parameters.push(user.tenantId, user.id);
        }
        query += ' ORDER BY c.sharedAt DESC';
        // Execute query with pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;
        const shares = await database_1.db.queryItems('document-sharing', paginatedQuery, parameters);
        // Get total count
        const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
        const countResult = await database_1.db.queryItems('document-sharing', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        logger_1.logger.info("Document shares retrieved successfully", {
            correlationId,
            documentId,
            userId: user.id,
            count: shares.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                shares,
                pagination: {
                    page,
                    limit,
                    total,
                    hasMore: (page * limit) < total
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get document shares failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('document-share-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/share',
    handler: createDocumentShare
});
functions_1.app.http('document-share-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/shares',
    handler: getDocumentShares
});
//# sourceMappingURL=document-share.js.map