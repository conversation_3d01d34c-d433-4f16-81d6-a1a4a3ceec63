"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAnalytics = getAnalytics;
exports.getDashboard = getDashboard;
/**
 * Advanced Analytics Function
 * Handles comprehensive analytics, reporting, and business intelligence
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Analytics metric types enum
var MetricType;
(function (MetricType) {
    MetricType["DOCUMENT_USAGE"] = "DOCUMENT_USAGE";
    MetricType["USER_ACTIVITY"] = "USER_ACTIVITY";
    MetricType["WORKFLOW_PERFORMANCE"] = "WORKFLOW_PERFORMANCE";
    MetricType["STORAGE_UTILIZATION"] = "STORAGE_UTILIZATION";
    MetricType["API_USAGE"] = "API_USAGE";
    MetricType["COLLABORATION_METRICS"] = "COLLABORATION_METRICS";
    MetricType["SECURITY_METRICS"] = "SECURITY_METRICS";
    MetricType["PERFORMANCE_METRICS"] = "PERFORMANCE_METRICS";
})(MetricType || (MetricType = {}));
// Time period enum
var TimePeriod;
(function (TimePeriod) {
    TimePeriod["LAST_24_HOURS"] = "LAST_24_HOURS";
    TimePeriod["LAST_7_DAYS"] = "LAST_7_DAYS";
    TimePeriod["LAST_30_DAYS"] = "LAST_30_DAYS";
    TimePeriod["LAST_90_DAYS"] = "LAST_90_DAYS";
    TimePeriod["LAST_YEAR"] = "LAST_YEAR";
    TimePeriod["CUSTOM"] = "CUSTOM";
})(TimePeriod || (TimePeriod = {}));
// Validation schemas
const getAnalyticsSchema = Joi.object({
    metricType: Joi.string().valid(...Object.values(MetricType)).required(),
    timePeriod: Joi.string().valid(...Object.values(TimePeriod)).default(TimePeriod.LAST_30_DAYS),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    groupBy: Joi.string().valid('day', 'week', 'month', 'user', 'project', 'document_type').optional(),
    filters: Joi.object({
        userId: Joi.string().uuid().optional(),
        documentType: Joi.string().optional(),
        workflowType: Joi.string().optional(),
        status: Joi.string().optional()
    }).optional()
});
const getDashboardSchema = Joi.object({
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    timePeriod: Joi.string().valid(...Object.values(TimePeriod)).default(TimePeriod.LAST_30_DAYS),
    includeComparisons: Joi.boolean().default(true)
});
/**
 * Get analytics handler
 */
async function getAnalytics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get analytics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getAnalyticsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { metricType, timePeriod, startDate, endDate, organizationId, projectId, groupBy, filters } = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Calculate date range
        const dateRange = calculateDateRange(timePeriod, startDate, endDate);
        // Get analytics data based on metric type
        const analyticsData = await getAnalyticsData(metricType, dateRange, organizationId, projectId, groupBy, filters || {});
        logger_1.logger.info("Analytics retrieved successfully", {
            correlationId,
            userId: user.id,
            metricType,
            timePeriod,
            organizationId,
            dataPoints: analyticsData.data?.length || 0
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                metricType,
                timePeriod,
                dateRange,
                organizationId,
                projectId,
                groupBy,
                filters,
                ...analyticsData,
                generatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get analytics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get dashboard data handler
 */
async function getDashboard(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get dashboard started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getDashboardSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { organizationId, projectId, timePeriod, includeComparisons } = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Calculate date range
        const dateRange = calculateDateRange(timePeriod);
        // Get comprehensive dashboard data
        const dashboardData = await getDashboardData(organizationId, dateRange, includeComparisons, projectId);
        logger_1.logger.info("Dashboard data retrieved successfully", {
            correlationId,
            userId: user.id,
            organizationId,
            projectId,
            timePeriod
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                organizationId,
                projectId,
                timePeriod,
                dateRange,
                includeComparisons,
                ...dashboardData,
                generatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get dashboard failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Calculate date range based on time period
 */
function calculateDateRange(timePeriod, startDate, endDate) {
    const now = new Date();
    let start;
    let end = now;
    switch (timePeriod) {
        case TimePeriod.LAST_24_HOURS:
            start = new Date(now.getTime() - 24 * 60 * 60 * 1000);
            break;
        case TimePeriod.LAST_7_DAYS:
            start = new Date(now.getTime() - 7 * 24 * 60 * 60 * 1000);
            break;
        case TimePeriod.LAST_30_DAYS:
            start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            break;
        case TimePeriod.LAST_90_DAYS:
            start = new Date(now.getTime() - 90 * 24 * 60 * 60 * 1000);
            break;
        case TimePeriod.LAST_YEAR:
            start = new Date(now.getTime() - 365 * 24 * 60 * 60 * 1000);
            break;
        case TimePeriod.CUSTOM:
            start = startDate ? new Date(startDate) : new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
            end = endDate ? new Date(endDate) : now;
            break;
        default:
            start = new Date(now.getTime() - 30 * 24 * 60 * 60 * 1000);
    }
    return {
        startDate: start.toISOString(),
        endDate: end.toISOString()
    };
}
/**
 * Get analytics data based on metric type
 */
async function getAnalyticsData(metricType, dateRange, organizationId, projectId, groupBy, filters = {}) {
    switch (metricType) {
        case MetricType.DOCUMENT_USAGE:
            return await getDocumentUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.USER_ACTIVITY:
            return await getUserActivityAnalytics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.WORKFLOW_PERFORMANCE:
            return await getWorkflowPerformanceAnalytics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.STORAGE_UTILIZATION:
            return await getStorageUtilizationAnalytics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.API_USAGE:
            return await getApiUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.COLLABORATION_METRICS:
            return await getCollaborationMetrics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.SECURITY_METRICS:
            return await getSecurityMetrics(dateRange, organizationId, projectId, groupBy, filters);
        case MetricType.PERFORMANCE_METRICS:
            return await getPerformanceMetrics(dateRange, organizationId, projectId, groupBy, filters);
        default:
            throw new Error(`Unsupported metric type: ${metricType}`);
    }
}
/**
 * Get document usage analytics
 */
async function getDocumentUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    try {
        // Production aggregation using Cosmos DB SQL with advanced analytics
        let documentsQuery = `
      SELECT
        c.id,
        c.name,
        c.size,
        c.contentType,
        c.createdAt,
        c.updatedAt,
        c.createdBy,
        c.projectId,
        c.organizationId,
        c.accessCount,
        c.lastAccessedAt,
        c.tags
      FROM c
      WHERE c.organizationId = @orgId
      AND c.createdAt >= @startDate
      AND c.createdAt <= @endDate
    `;
        const parameters = [
            { name: '@orgId', value: organizationId },
            { name: '@startDate', value: dateRange.startDate },
            { name: '@endDate', value: dateRange.endDate }
        ];
        if (projectId) {
            documentsQuery += ' AND c.projectId = @projectId';
            parameters.push({ name: '@projectId', value: projectId });
        }
        // Add content type filter if specified
        if (filters.contentTypes && filters.contentTypes.length > 0) {
            documentsQuery += ' AND c.contentType IN (@contentTypes)';
            parameters.push({ name: '@contentTypes', value: filters.contentTypes });
        }
        documentsQuery += ' ORDER BY c.createdAt DESC';
        const documents = await database_1.db.queryItems('documents', documentsQuery, parameters);
        // Perform advanced statistical aggregations
        const aggregatedData = await performAdvancedDocumentAggregation(documents, groupBy, dateRange);
        // Calculate advanced metrics
        const advancedMetrics = calculateAdvancedDocumentMetrics(documents, dateRange);
        // Generate usage patterns analysis
        const usagePatterns = analyzeDocumentUsagePatterns(documents, dateRange);
        logger_1.logger.info('Document usage analytics completed', {
            organizationId,
            projectId,
            totalDocuments: documents.length,
            dateRange,
            groupBy
        });
        return {
            totalDocuments: documents.length,
            aggregatedData,
            advancedMetrics,
            usagePatterns,
            summary: {
                averageSize: advancedMetrics.averageSize,
                totalSize: advancedMetrics.totalSize,
                medianSize: advancedMetrics.medianSize,
                mostActiveUsers: advancedMetrics.mostActiveUsers,
                popularFileTypes: advancedMetrics.popularFileTypes,
                accessFrequency: advancedMetrics.accessFrequency
            }
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to get document usage analytics', {
            error: error instanceof Error ? error.message : String(error),
            organizationId,
            projectId,
            dateRange
        });
        throw error;
    }
}
/**
 * Get user activity analytics
 */
async function getUserActivityAnalytics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    const activitiesQuery = `
    SELECT * FROM c
    WHERE c.organizationId = @orgId
    AND c.timestamp >= @startDate
    AND c.timestamp <= @endDate
  `;
    const activities = await database_1.db.queryItems('activities', activitiesQuery, [organizationId, dateRange.startDate, dateRange.endDate]);
    return {
        totalActivities: activities.length,
        activeUsers: getActiveUsers(activities),
        activitiesByType: groupActivitiesByType(activities),
        activitiesOverTime: groupActivitiesByTime(activities, groupBy || 'day'),
        topUsers: getTopActiveUsers(activities)
    };
}
/**
 * Get comprehensive dashboard data
 */
async function getDashboardData(organizationId, dateRange, includeComparisons = true, projectId) {
    // Get key metrics
    const [documentMetrics, userMetrics, workflowMetrics, storageMetrics] = await Promise.all([
        getDocumentUsageAnalytics(dateRange, organizationId, projectId),
        getUserActivityAnalytics(dateRange, organizationId, projectId),
        getWorkflowPerformanceAnalytics(dateRange, organizationId, projectId),
        getStorageUtilizationAnalytics(dateRange, organizationId, projectId)
    ]);
    const dashboard = {
        overview: {
            totalDocuments: documentMetrics.totalDocuments,
            activeUsers: userMetrics.activeUsers.length,
            totalActivities: userMetrics.totalActivities,
            storageUsed: storageMetrics.totalUsed || 0
        },
        charts: {
            documentTrends: documentMetrics.documentsOverTime,
            userActivity: userMetrics.activitiesOverTime,
            workflowPerformance: workflowMetrics.performanceOverTime || [],
            storageGrowth: storageMetrics.growthOverTime || []
        },
        insights: {
            topDocuments: documentMetrics.topDocuments,
            topUsers: userMetrics.topUsers,
            documentTypes: documentMetrics.documentsByType,
            activityTypes: userMetrics.activitiesByType
        }
    };
    if (includeComparisons) {
        // Add comparison data (previous period)
        dashboard.comparisons = await getComparisonData(organizationId, dateRange, projectId);
    }
    return dashboard;
}
// Helper functions for analytics calculations
function groupDocumentsByType(documents) {
    const grouped = documents.reduce((acc, doc) => {
        const type = doc.contentType || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
    }, {});
    return Object.entries(grouped).map(([type, count]) => ({ type, count }));
}
function groupDocumentsByTime(documents, groupBy) {
    // Simplified implementation
    return documents.map(doc => ({
        date: doc.createdAt.split('T')[0],
        count: 1
    }));
}
function getTopDocuments(documents) {
    return documents.slice(0, 10).map(doc => ({
        id: doc.id,
        name: doc.name,
        views: doc.viewCount || 0,
        size: doc.size || 0
    }));
}
function calculateAverageSize(documents) {
    if (documents.length === 0)
        return 0;
    const totalSize = documents.reduce((sum, doc) => sum + (doc.size || 0), 0);
    return Math.round(totalSize / documents.length);
}
function getMostActiveDay(documents) {
    // Simplified implementation
    return new Date().toISOString().split('T')[0];
}
function getActiveUsers(activities) {
    const users = new Set(activities.map(activity => activity.userId).filter(Boolean));
    return Array.from(users);
}
function groupActivitiesByType(activities) {
    const grouped = activities.reduce((acc, activity) => {
        const type = activity.type || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
    }, {});
    return Object.entries(grouped).map(([type, count]) => ({ type, count }));
}
function groupActivitiesByTime(activities, groupBy) {
    // Simplified implementation
    return activities.map(activity => ({
        date: activity.timestamp.split('T')[0],
        count: 1
    }));
}
function getTopActiveUsers(activities) {
    const userCounts = activities.reduce((acc, activity) => {
        if (activity.userId) {
            acc[activity.userId] = (acc[activity.userId] || 0) + 1;
        }
        return acc;
    }, {});
    return Object.entries(userCounts)
        .sort(([, a], [, b]) => b - a)
        .slice(0, 10)
        .map(([userId, count]) => ({ userId, activityCount: count }));
}
async function getWorkflowPerformanceAnalytics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        totalWorkflows: 0,
        completedWorkflows: 0,
        averageCompletionTime: 0,
        performanceOverTime: []
    };
}
async function getStorageUtilizationAnalytics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        totalUsed: 0,
        totalLimit: 1000000000, // 1GB
        utilizationPercentage: 0,
        growthOverTime: []
    };
}
async function getApiUsageAnalytics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        totalRequests: 0,
        successfulRequests: 0,
        errorRate: 0,
        requestsOverTime: []
    };
}
async function getCollaborationMetrics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        totalShares: 0,
        totalComments: 0,
        collaborativeDocuments: 0,
        collaborationOverTime: []
    };
}
async function getSecurityMetrics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        securityEvents: 0,
        failedLogins: 0,
        suspiciousActivity: 0,
        securityEventsOverTime: []
    };
}
async function getPerformanceMetrics(dateRange, organizationId, projectId, groupBy, filters = {}) {
    return {
        averageResponseTime: 0,
        uptime: 99.9,
        errorRate: 0.1,
        performanceOverTime: []
    };
}
async function getComparisonData(organizationId, dateRange, projectId) {
    // Calculate previous period for comparison
    const periodLength = new Date(dateRange.endDate).getTime() - new Date(dateRange.startDate).getTime();
    const previousPeriod = {
        startDate: new Date(new Date(dateRange.startDate).getTime() - periodLength).toISOString(),
        endDate: dateRange.startDate
    };
    // Get metrics for previous period
    const previousMetrics = await getDocumentUsageAnalytics(previousPeriod, organizationId, projectId);
    return {
        previousPeriod,
        documentGrowth: 0, // Calculate percentage change
        userGrowth: 0,
        activityGrowth: 0
    };
}
/**
 * Perform advanced document aggregation with statistical analysis
 */
async function performAdvancedDocumentAggregation(documents, groupBy, dateRange) {
    try {
        const aggregations = {};
        // Time-based aggregation
        if (groupBy === 'hour' || groupBy === 'day' || groupBy === 'week' || groupBy === 'month') {
            aggregations.timeSeries = aggregateByTimeInterval(documents, groupBy);
        }
        // Content type aggregation
        aggregations.byContentType = documents.reduce((acc, doc) => {
            const type = doc.contentType || 'unknown';
            if (!acc[type]) {
                acc[type] = { count: 0, totalSize: 0, avgSize: 0 };
            }
            acc[type].count++;
            acc[type].totalSize += doc.size || 0;
            acc[type].avgSize = acc[type].totalSize / acc[type].count;
            return acc;
        }, {});
        // User activity aggregation
        aggregations.byUser = documents.reduce((acc, doc) => {
            const userId = doc.createdBy || 'unknown';
            if (!acc[userId]) {
                acc[userId] = { count: 0, totalSize: 0, lastActivity: null };
            }
            acc[userId].count++;
            acc[userId].totalSize += doc.size || 0;
            if (!acc[userId].lastActivity || doc.createdAt > acc[userId].lastActivity) {
                acc[userId].lastActivity = doc.createdAt;
            }
            return acc;
        }, {});
        // Project aggregation
        aggregations.byProject = documents.reduce((acc, doc) => {
            const projectId = doc.projectId || 'unassigned';
            if (!acc[projectId]) {
                acc[projectId] = { count: 0, totalSize: 0, uniqueUsers: new Set() };
            }
            acc[projectId].count++;
            acc[projectId].totalSize += doc.size || 0;
            acc[projectId].uniqueUsers.add(doc.createdBy);
            return acc;
        }, {});
        // Convert Sets to arrays for JSON serialization
        Object.keys(aggregations.byProject).forEach(projectId => {
            aggregations.byProject[projectId].uniqueUsers = Array.from(aggregations.byProject[projectId].uniqueUsers);
            aggregations.byProject[projectId].collaborationScore = aggregations.byProject[projectId].uniqueUsers.length;
        });
        return aggregations;
    }
    catch (error) {
        logger_1.logger.error('Failed to perform advanced document aggregation', {
            error: error instanceof Error ? error.message : String(error)
        });
        return {};
    }
}
/**
 * Calculate advanced document metrics with statistical analysis
 */
function calculateAdvancedDocumentMetrics(documents, dateRange) {
    try {
        const sizes = documents.map(doc => doc.size || 0).filter(size => size > 0);
        const accessCounts = documents.map(doc => doc.accessCount || 0);
        // Statistical calculations
        const totalSize = sizes.reduce((sum, size) => sum + size, 0);
        const averageSize = sizes.length > 0 ? totalSize / sizes.length : 0;
        const medianSize = calculateMedian(sizes);
        const sizeStandardDeviation = calculateStandardDeviation(sizes);
        // User activity analysis
        const userActivity = documents.reduce((acc, doc) => {
            const userId = doc.createdBy || 'unknown';
            acc[userId] = (acc[userId] || 0) + 1;
            return acc;
        }, {});
        const mostActiveUsers = Object.entries(userActivity)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([userId, count]) => ({ userId, documentCount: count }));
        // File type analysis
        const fileTypes = documents.reduce((acc, doc) => {
            const type = doc.contentType || 'unknown';
            acc[type] = (acc[type] || 0) + 1;
            return acc;
        }, {});
        const popularFileTypes = Object.entries(fileTypes)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 10)
            .map(([type, count]) => ({ contentType: type, count }));
        // Access frequency analysis
        const totalAccess = accessCounts.reduce((sum, count) => sum + count, 0);
        const averageAccess = accessCounts.length > 0 ? totalAccess / accessCounts.length : 0;
        return {
            totalSize,
            averageSize,
            medianSize,
            sizeStandardDeviation,
            mostActiveUsers,
            popularFileTypes,
            accessFrequency: {
                total: totalAccess,
                average: averageAccess,
                median: calculateMedian(accessCounts)
            }
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to calculate advanced document metrics', {
            error: error instanceof Error ? error.message : String(error)
        });
        return {};
    }
}
/**
 * Analyze document usage patterns
 */
function analyzeDocumentUsagePatterns(documents, dateRange) {
    try {
        // Peak usage hours analysis
        const hourlyActivity = documents.reduce((acc, doc) => {
            const hour = new Date(doc.createdAt).getHours();
            acc[hour] = (acc[hour] || 0) + 1;
            return acc;
        }, {});
        const peakHours = Object.entries(hourlyActivity)
            .sort(([, a], [, b]) => b - a)
            .slice(0, 3)
            .map(([hour, count]) => ({ hour: parseInt(hour), activityCount: count }));
        // Weekly patterns
        const weeklyActivity = documents.reduce((acc, doc) => {
            const dayOfWeek = new Date(doc.createdAt).getDay();
            const dayNames = ['Sunday', 'Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday', 'Saturday'];
            const dayName = dayNames[dayOfWeek];
            acc[dayName] = (acc[dayName] || 0) + 1;
            return acc;
        }, {});
        // Collaboration patterns
        const collaborationData = analyzeCollaborationPatterns(documents);
        return {
            peakHours,
            weeklyActivity,
            collaborationPatterns: collaborationData,
            activityDistribution: {
                hourly: hourlyActivity,
                weekly: weeklyActivity
            }
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to analyze document usage patterns', {
            error: error instanceof Error ? error.message : String(error)
        });
        return {};
    }
}
/**
 * Helper function to calculate median
 */
function calculateMedian(numbers) {
    if (numbers.length === 0)
        return 0;
    const sorted = [...numbers].sort((a, b) => a - b);
    const mid = Math.floor(sorted.length / 2);
    return sorted.length % 2 === 0 ? (sorted[mid - 1] + sorted[mid]) / 2 : sorted[mid];
}
/**
 * Helper function to calculate standard deviation
 */
function calculateStandardDeviation(numbers) {
    if (numbers.length === 0)
        return 0;
    const mean = numbers.reduce((sum, num) => sum + num, 0) / numbers.length;
    const squaredDiffs = numbers.map(num => Math.pow(num - mean, 2));
    const avgSquaredDiff = squaredDiffs.reduce((sum, diff) => sum + diff, 0) / numbers.length;
    return Math.sqrt(avgSquaredDiff);
}
/**
 * Aggregate documents by time interval
 */
function aggregateByTimeInterval(documents, interval) {
    return documents.reduce((acc, doc) => {
        const date = new Date(doc.createdAt);
        let key;
        switch (interval) {
            case 'hour':
                key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')} ${String(date.getHours()).padStart(2, '0')}:00`;
                break;
            case 'day':
                key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}-${String(date.getDate()).padStart(2, '0')}`;
                break;
            case 'week':
                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());
                key = `${weekStart.getFullYear()}-W${Math.ceil((weekStart.getTime() - new Date(weekStart.getFullYear(), 0, 1).getTime()) / (7 * 24 * 60 * 60 * 1000))}`;
                break;
            case 'month':
                key = `${date.getFullYear()}-${String(date.getMonth() + 1).padStart(2, '0')}`;
                break;
            default:
                key = date.toISOString().split('T')[0];
        }
        if (!acc[key]) {
            acc[key] = { count: 0, totalSize: 0 };
        }
        acc[key].count++;
        acc[key].totalSize += doc.size || 0;
        return acc;
    }, {});
}
/**
 * Analyze collaboration patterns
 */
function analyzeCollaborationPatterns(documents) {
    const projectCollaboration = documents.reduce((acc, doc) => {
        const projectId = doc.projectId || 'unassigned';
        if (!acc[projectId]) {
            acc[projectId] = new Set();
        }
        acc[projectId].add(doc.createdBy);
        return acc;
    }, {});
    const collaborationScores = Object.entries(projectCollaboration).map(([projectId, users]) => ({
        projectId,
        uniqueUsers: users.size,
        collaborationScore: users.size > 1 ? users.size / documents.filter(d => d.projectId === projectId).length : 0
    }));
    return {
        projectCollaboration: collaborationScores,
        averageCollaborationScore: collaborationScores.reduce((sum, score) => sum + score.collaborationScore, 0) / collaborationScores.length || 0
    };
}
// Register functions
functions_1.app.http('advanced-analytics-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'analytics/advanced',
    handler: getAnalytics
});
functions_1.app.http('analytics-dashboard', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'analytics/dashboard',
    handler: getDashboard
});
//# sourceMappingURL=advanced-analytics.js.map