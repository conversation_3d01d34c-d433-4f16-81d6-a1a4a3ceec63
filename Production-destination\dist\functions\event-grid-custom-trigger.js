"use strict";
/**
 * Event Grid Custom Events Trigger - Fixed Version
 * Native Event Grid trigger for custom application events
 */
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const database_1 = require("../shared/services/database");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
// Import EventType enum directly to avoid circular dependency
var EventType;
(function (EventType) {
    EventType["DOCUMENT_UPLOADED"] = "Document.Uploaded";
    EventType["DOCUMENT_PROCESSED"] = "Document.Processed";
    EventType["DOCUMENT_SHARED"] = "Document.Shared";
    EventType["WORKFLOW_STARTED"] = "Workflow.Started";
    EventType["WORKFLOW_COMPLETED"] = "Workflow.Completed";
    EventType["USER_REGISTERED"] = "User.Registered";
    EventType["NOTIFICATION_SENT"] = "Notification.Sent";
    EventType["ANALYTICS_GENERATED"] = "Analytics.Generated";
    EventType["SYSTEM_HEALTH_CHECK"] = "System.HealthCheck";
    EventType["PERFORMANCE_ALERT"] = "Performance.Alert";
})(EventType || (EventType = {}));
/**
 * Publish event using Event Grid integration service
 */
async function publishEvent(eventType, subject, data) {
    try {
        await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType,
            subject,
            data
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to publish event from custom trigger', {
            eventType,
            subject,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle Custom Event Grid events with native trigger
 */
async function customEventGridTrigger(event, context) {
    logger_1.logger.info('Custom Event Grid trigger activated', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id,
        invocationId: context.invocationId
    });
    try {
        await processCustomEvent(event);
        logger_1.logger.info('Custom event processed successfully', {
            eventId: event.id,
            eventType: event.eventType
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing custom event', {
            eventId: event.id,
            eventType: event.eventType,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error; // Re-throw to trigger retry mechanism
    }
}
/**
 * Process custom application events
 */
async function processCustomEvent(event) {
    logger_1.logger.info('Processing custom event', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id
    });
    // Safe data extraction
    const eventData = event.data || {};
    switch (event.eventType) {
        case EventType.DOCUMENT_UPLOADED:
            await handleDocumentEvent(eventData, 'uploaded');
            break;
        case EventType.DOCUMENT_PROCESSED:
            await handleDocumentEvent(eventData, 'processed');
            break;
        case EventType.DOCUMENT_SHARED:
            await handleDocumentEvent(eventData, 'shared');
            break;
        case EventType.WORKFLOW_STARTED:
            await handleWorkflowEvent(eventData, 'started');
            break;
        case EventType.WORKFLOW_COMPLETED:
            await handleWorkflowEvent(eventData, 'completed');
            break;
        case EventType.USER_REGISTERED:
            await handleUserEvent(eventData, 'registered');
            break;
        case EventType.SYSTEM_HEALTH_CHECK:
            await handleSystemEvent(eventData, 'health_check');
            break;
        case EventType.PERFORMANCE_ALERT:
            await handleSystemEvent(eventData, 'performance_alert');
            break;
        case EventType.ANALYTICS_GENERATED:
            await handleAnalyticsEvent(eventData);
            break;
        case EventType.NOTIFICATION_SENT:
            await handleNotificationEvent(eventData);
            break;
        default:
            logger_1.logger.info('Unhandled custom event type', { eventType: event.eventType });
    }
}
/**
 * Handle document events
 */
async function handleDocumentEvent(data, action) {
    const { documentId, blobName, uploadedBy, sharedWith, sharedBy } = data;
    logger_1.logger.info(`Document ${action} event received`, { documentId, blobName, uploadedBy });
    try {
        // Update document in database if documentId exists
        if (documentId) {
            try {
                const documents = await database_1.db.queryItems('documents', 'SELECT * FROM c WHERE c.id = @documentId', [{ name: '@documentId', value: documentId }]);
                if (documents.length > 0) {
                    const document = documents[0];
                    const updateData = {
                        ...document,
                        [`${action}At`]: new Date().toISOString()
                    };
                    if (action === 'uploaded') {
                        updateData.status = 'processing';
                        updateData.blobName = blobName;
                    }
                    else if (action === 'processed') {
                        updateData.status = 'processed';
                    }
                    else if (action === 'shared') {
                        updateData.sharedWith = sharedWith;
                        updateData.sharedBy = sharedBy;
                    }
                    await database_1.db.updateItem('documents', updateData);
                }
            }
            catch (dbError) {
                logger_1.logger.warn('Could not update document in database', {
                    documentId,
                    action,
                    error: dbError instanceof Error ? dbError.message : String(dbError)
                });
            }
        }
        // Trigger follow-up events
        if (action === 'uploaded') {
            await publishEvent(EventType.ANALYTICS_GENERATED, `documents/${documentId}/upload-analytics`, {
                documentId,
                blobName,
                uploadedBy,
                timestamp: new Date().toISOString(),
                eventSource: 'custom-trigger'
            });
        }
    }
    catch (error) {
        logger_1.logger.error(`Error handling document ${action} event`, {
            documentId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle workflow events
 */
async function handleWorkflowEvent(data, action) {
    const { workflowId, startedBy, completedBy, workflowType, status } = data;
    logger_1.logger.info(`Workflow ${action} event received`, { workflowId, startedBy, completedBy });
    try {
        // Track workflow analytics
        await publishEvent(EventType.ANALYTICS_GENERATED, `workflows/${workflowId}/${action}-analytics`, {
            workflowId,
            action,
            startedBy,
            completedBy,
            workflowType,
            status,
            timestamp: new Date().toISOString(),
            eventSource: 'custom-trigger'
        });
        // Send notifications for completed workflows
        if (action === 'completed') {
            await publishEvent(EventType.NOTIFICATION_SENT, `workflows/${workflowId}/completion`, {
                workflowId,
                status,
                completedBy,
                notificationType: 'workflow_completed',
                timestamp: new Date().toISOString(),
                eventSource: 'custom-trigger'
            });
        }
    }
    catch (error) {
        logger_1.logger.error(`Error handling workflow ${action} event`, {
            workflowId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle user events
 */
async function handleUserEvent(data, action) {
    const { userId, email, registrationMethod } = data;
    logger_1.logger.info(`User ${action} event received`, { userId, email, registrationMethod });
    try {
        if (action === 'registered') {
            // Send welcome notification
            await publishEvent(EventType.NOTIFICATION_SENT, `users/${userId}/welcome`, {
                userId,
                email,
                notificationType: 'welcome',
                registrationMethod,
                timestamp: new Date().toISOString(),
                eventSource: 'custom-trigger'
            });
            // Track user analytics
            await publishEvent(EventType.ANALYTICS_GENERATED, `users/${userId}/registration-analytics`, {
                userId,
                registrationMethod,
                timestamp: new Date().toISOString(),
                eventSource: 'custom-trigger'
            });
        }
    }
    catch (error) {
        logger_1.logger.error(`Error handling user ${action} event`, {
            userId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle system events
 */
async function handleSystemEvent(data, eventType) {
    const { healthStatus, metrics, alertType, severity } = data;
    logger_1.logger.info(`System ${eventType} event received`, { healthStatus, alertType, severity });
    try {
        if (eventType === 'health_check') {
            // Store health metrics
            await database_1.db.createItem('system-health', {
                id: `health-${Date.now()}`,
                healthStatus,
                metrics,
                timestamp: new Date().toISOString()
            });
            // Generate alert if unhealthy
            if (healthStatus !== 'healthy') {
                await publishEvent(EventType.PERFORMANCE_ALERT, 'system/health-alert', {
                    alertType: 'health_check_failed',
                    severity: healthStatus === 'critical' ? 'high' : 'medium',
                    healthStatus,
                    metrics,
                    timestamp: new Date().toISOString(),
                    eventSource: 'custom-trigger'
                });
            }
        }
        else if (eventType === 'performance_alert') {
            // Store alert in database
            await database_1.db.createItem('performance-alerts', {
                id: `alert-${Date.now()}`,
                alertType,
                severity,
                metrics,
                timestamp: new Date().toISOString(),
                status: 'active'
            });
        }
    }
    catch (error) {
        logger_1.logger.error(`Error handling system ${eventType} event`, {
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle analytics events
 */
async function handleAnalyticsEvent(data) {
    const { analyticsType, generatedFor } = data;
    logger_1.logger.info('Analytics generated event received', { analyticsType, generatedFor });
    try {
        // Store analytics data
        await database_1.db.createItem('analytics-data', {
            id: `analytics-${Date.now()}`,
            analyticsType,
            data: data.data || {},
            generatedFor,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling analytics generated event', {
            analyticsType,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle notification events
 */
async function handleNotificationEvent(data) {
    const { notificationId, notificationType, recipient, status } = data;
    logger_1.logger.info('Notification sent event received', { notificationId, notificationType, recipient });
    try {
        // Track notification analytics
        await database_1.db.createItem('notification-analytics', {
            id: `notification-${Date.now()}`,
            notificationId,
            notificationType,
            recipient,
            status,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling notification sent event', {
            notificationId,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
// Register the Event Grid trigger
functions_1.app.eventGrid('customEventGridTrigger', {
    handler: customEventGridTrigger
});
//# sourceMappingURL=event-grid-custom-trigger.js.map