/**
 * Cache Management Function
 * Handles cache operations and management
 * Migrated from old-arch/src/cache-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Cache operation handler
 */
export declare function performCacheOperation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get cache statistics handler
 */
export declare function getCacheStatistics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Clear cache handler
 */
export declare function clearCache(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
