"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.encryptData = encryptData;
exports.decryptData = decryptData;
/**
 * Data Encryption Function
 * Handles data encryption, decryption, and key management
 * Migrated from old-arch/src/security-service/encryption/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Encryption types and enums
var EncryptionAlgorithm;
(function (EncryptionAlgorithm) {
    EncryptionAlgorithm["AES_256_GCM"] = "AES_256_GCM";
    EncryptionAlgorithm["AES_128_GCM"] = "AES_128_GCM";
    EncryptionAlgorithm["CHACHA20_POLY1305"] = "CHACHA20_POLY1305";
})(EncryptionAlgorithm || (EncryptionAlgorithm = {}));
var KeyType;
(function (KeyType) {
    KeyType["MASTER"] = "MASTER";
    KeyType["DATA"] = "DATA";
    KeyType["DOCUMENT"] = "DOCUMENT";
    KeyType["FIELD"] = "FIELD";
    KeyType["BACKUP"] = "BACKUP";
})(KeyType || (KeyType = {}));
var KeyStatus;
(function (KeyStatus) {
    KeyStatus["ACTIVE"] = "ACTIVE";
    KeyStatus["INACTIVE"] = "INACTIVE";
    KeyStatus["ROTATED"] = "ROTATED";
    KeyStatus["COMPROMISED"] = "COMPROMISED";
    KeyStatus["EXPIRED"] = "EXPIRED";
})(KeyStatus || (KeyStatus = {}));
// Validation schemas
const encryptDataSchema = Joi.object({
    data: Joi.string().required(),
    keyType: Joi.string().valid(...Object.values(KeyType)).default(KeyType.DATA),
    algorithm: Joi.string().valid(...Object.values(EncryptionAlgorithm)).default(EncryptionAlgorithm.AES_256_GCM),
    organizationId: Joi.string().uuid().required(),
    context: Joi.object({
        documentId: Joi.string().uuid().optional(),
        fieldName: Joi.string().optional(),
        purpose: Joi.string().optional(),
        classification: Joi.string().valid('public', 'internal', 'confidential', 'restricted').optional()
    }).optional(),
    metadata: Joi.object().optional()
});
const decryptDataSchema = Joi.object({
    encryptedData: Joi.string().required(),
    keyId: Joi.string().uuid().required(),
    organizationId: Joi.string().uuid().required(),
    context: Joi.object({
        documentId: Joi.string().uuid().optional(),
        fieldName: Joi.string().optional(),
        purpose: Joi.string().optional()
    }).optional()
});
const createEncryptionKeySchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    keyType: Joi.string().valid(...Object.values(KeyType)).required(),
    algorithm: Joi.string().valid(...Object.values(EncryptionAlgorithm)).default(EncryptionAlgorithm.AES_256_GCM),
    organizationId: Joi.string().uuid().required(),
    expirationDays: Joi.number().min(1).max(3650).default(365),
    rotationDays: Joi.number().min(1).max(365).default(90),
    metadata: Joi.object().optional()
});
/**
 * Encrypt data handler
 */
async function encryptData(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Encrypt data started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = encryptDataSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const encryptRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(encryptRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check encryption permissions
        const hasEncryptionAccess = await checkEncryptionAccess(user, encryptRequest.organizationId);
        if (!hasEncryptionAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to encryption operations" }
            }, request);
        }
        // Get or create encryption key
        const encryptionKey = await getOrCreateEncryptionKey(encryptRequest.organizationId, encryptRequest.keyType || KeyType.DATA, encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM, user.id);
        if (!encryptionKey) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Failed to obtain encryption key" }
            }, request);
        }
        // Perform encryption
        const encryptionResult = await performEncryption(encryptRequest.data, encryptionKey, encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM);
        if (!encryptionResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Encryption failed", details: encryptionResult.error }
            }, request);
        }
        const duration = Date.now() - startTime;
        // Log encryption operation
        await logEncryptionOperation({
            type: 'ENCRYPT',
            keyId: encryptionKey.id,
            organizationId: encryptRequest.organizationId,
            context: encryptRequest.context,
            metadata: {
                algorithm: encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM,
                dataSize: encryptRequest.data.length,
                duration,
                success: true
            },
            audit: {
                userId: user.id,
                ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
                userAgent: request.headers.get('user-agent') || 'unknown',
                timestamp: new Date().toISOString()
            },
            tenantId: user.tenantId || user.id
        });
        // Update key usage
        await updateKeyUsage(encryptionKey.id, 'encrypt');
        logger_1.logger.info("Data encrypted successfully", {
            correlationId,
            keyId: encryptionKey.id,
            algorithm: encryptRequest.algorithm,
            dataSize: encryptRequest.data.length,
            duration,
            encryptedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                encryptedData: encryptionResult.encryptedData,
                keyId: encryptionKey.id,
                algorithm: encryptRequest.algorithm || EncryptionAlgorithm.AES_256_GCM,
                metadata: {
                    iv: encryptionResult.iv,
                    authTag: encryptionResult.authTag,
                    keyVersion: encryptionKey.keyData.keyVersion
                },
                encryptedAt: new Date().toISOString(),
                message: "Data encrypted successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Encrypt data failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Decrypt data handler
 */
async function decryptData(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Decrypt data started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = decryptDataSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const decryptRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(decryptRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Get encryption key
        const encryptionKey = await getEncryptionKey(decryptRequest.keyId);
        if (!encryptionKey) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Encryption key not found" }
            }, request);
        }
        // Check key access
        const hasKeyAccess = await checkKeyAccess(encryptionKey, user.id);
        if (!hasKeyAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to encryption key" }
            }, request);
        }
        // Perform decryption
        const decryptionResult = await performDecryption(decryptRequest.encryptedData, encryptionKey);
        if (!decryptionResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Decryption failed", details: decryptionResult.error }
            }, request);
        }
        const duration = Date.now() - startTime;
        // Log decryption operation
        await logEncryptionOperation({
            type: 'DECRYPT',
            keyId: encryptionKey.id,
            organizationId: decryptRequest.organizationId,
            context: decryptRequest.context,
            metadata: {
                algorithm: encryptionKey.algorithm,
                dataSize: decryptRequest.encryptedData.length,
                duration,
                success: true
            },
            audit: {
                userId: user.id,
                ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
                userAgent: request.headers.get('user-agent') || 'unknown',
                timestamp: new Date().toISOString()
            },
            tenantId: user.tenantId || user.id
        });
        // Update key usage
        await updateKeyUsage(encryptionKey.id, 'decrypt');
        logger_1.logger.info("Data decrypted successfully", {
            correlationId,
            keyId: encryptionKey.id,
            algorithm: encryptionKey.algorithm,
            dataSize: decryptRequest.encryptedData.length,
            duration,
            decryptedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                data: decryptionResult.data,
                keyId: encryptionKey.id,
                algorithm: encryptionKey.algorithm,
                decryptedAt: new Date().toISOString(),
                message: "Data decrypted successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Decrypt data failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkEncryptionAccess(user, organizationId) {
    try {
        // Check if user has admin or encryption role
        if (user.roles?.includes('admin') || user.roles?.includes('encryption_admin')) {
            return true;
        }
        // Check organization-level permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN';
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check encryption access', { error, userId: user.id, organizationId });
        return false;
    }
}
async function getOrCreateEncryptionKey(organizationId, keyType, algorithm, userId) {
    try {
        // Try to find existing active key
        const keyQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.keyType = @keyType AND c.algorithm = @algorithm AND c.status = @active';
        const existingKeys = await database_1.db.queryItems('encryption-keys', keyQuery, [organizationId, keyType, algorithm, KeyStatus.ACTIVE]);
        if (existingKeys.length > 0) {
            return existingKeys[0];
        }
        // Create new key
        return await createNewEncryptionKey(organizationId, keyType, algorithm, userId);
    }
    catch (error) {
        logger_1.logger.error('Failed to get or create encryption key', { error, organizationId, keyType, algorithm });
        return null;
    }
}
async function createNewEncryptionKey(organizationId, keyType, algorithm, userId) {
    try {
        const keyId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const expirationDate = new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(); // 1 year
        const rotationDate = new Date(Date.now() + 90 * 24 * 60 * 60 * 1000).toISOString(); // 90 days
        // Generate encryption key (simplified - in production use proper key generation)
        const keyData = generateEncryptionKeyData(algorithm);
        const encryptionKey = {
            id: keyId,
            name: `${keyType}_${algorithm}_${Date.now()}`,
            keyType,
            algorithm,
            status: KeyStatus.ACTIVE,
            organizationId,
            keyData,
            usage: {
                encryptionCount: 0,
                decryptionCount: 0
            },
            lifecycle: {
                createdAt: now,
                expiresAt: expirationDate,
                rotationDue: rotationDate
            },
            access: {
                allowedUsers: [userId],
                allowedRoles: ['admin', 'encryption_admin'],
                accessLog: []
            },
            metadata: {},
            createdBy: userId,
            updatedAt: now,
            tenantId: organizationId
        };
        await database_1.db.createItem('encryption-keys', encryptionKey);
        return encryptionKey;
    }
    catch (error) {
        logger_1.logger.error('Failed to create new encryption key', { error, organizationId, keyType, algorithm });
        return null;
    }
}
function generateEncryptionKeyData(algorithm) {
    // Production-grade key generation using Node.js crypto
    const crypto = require('crypto');
    try {
        // Use appropriate key sizes for different algorithms
        const keySize = algorithm === EncryptionAlgorithm.AES_256_GCM ? 32 : 16;
        // Generate cryptographically secure random key
        const key = crypto.randomBytes(keySize);
        // Generate salt for key derivation
        const salt = crypto.randomBytes(32); // Larger salt for better security
        // Generate IV for encryption
        const iv = crypto.randomBytes(16);
        // Create key derivation using PBKDF2
        const derivedKey = crypto.pbkdf2Sync(key, salt, 100000, keySize, 'sha256');
        logger_1.logger.info('Encryption key generated successfully', {
            algorithm,
            keySize,
            saltLength: salt.length,
            ivLength: iv.length
        });
        return {
            encryptedKey: derivedKey.toString('base64'),
            keyVersion: 1,
            salt: salt.toString('base64'),
            iv: iv.toString('base64'),
            algorithm: algorithm,
            keySize: keySize,
            iterations: 100000,
            hashFunction: 'sha256'
        };
    }
    catch (error) {
        logger_1.logger.error('Key generation failed', {
            error: error instanceof Error ? error.message : String(error),
            algorithm
        });
        throw new Error('Failed to generate encryption key');
    }
}
async function performEncryption(data, key, algorithm) {
    try {
        // Production-grade encryption using AES-256-GCM
        const crypto = require('crypto');
        const keyBuffer = Buffer.from(key.keyData.encryptedKey, 'base64');
        const iv = crypto.randomBytes(16); // 128-bit IV for AES
        // Use AES-256-GCM for authenticated encryption
        const cipher = crypto.createCipherGCM('aes-256-gcm', keyBuffer, iv);
        // Add additional authenticated data (AAD)
        const aad = Buffer.from(JSON.stringify({
            keyId: key.id,
            algorithm: algorithm,
            timestamp: new Date().toISOString()
        }));
        cipher.setAAD(aad);
        let encrypted = cipher.update(data, 'utf8', 'base64');
        encrypted += cipher.final('base64');
        const authTag = cipher.getAuthTag();
        logger_1.logger.info('Data encrypted successfully', {
            algorithm,
            keyId: key.id,
            dataLength: data.length,
            encryptedLength: encrypted.length
        });
        return {
            success: true,
            encryptedData: encrypted,
            iv: iv.toString('base64'),
            authTag: authTag.toString('base64'),
            aad: aad.toString('base64'),
            algorithm: algorithm,
            keyId: key.id,
            timestamp: new Date().toISOString()
        };
    }
    catch (error) {
        logger_1.logger.error('Encryption failed', {
            error: error instanceof Error ? error.message : String(error),
            keyId: key.id,
            algorithm
        });
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Encryption failed'
        };
    }
}
async function performDecryption(encryptedData, key) {
    try {
        // Production-grade decryption using AES-256-GCM
        const crypto = require('crypto');
        const keyBuffer = Buffer.from(key.keyData.encryptedKey, 'base64');
        const iv = Buffer.from(encryptedData.iv, 'base64');
        const authTag = Buffer.from(encryptedData.authTag, 'base64');
        const aad = Buffer.from(encryptedData.aad || '', 'base64');
        // Use AES-256-GCM for authenticated decryption
        const decipher = crypto.createDecipherGCM('aes-256-gcm', keyBuffer, iv);
        // Set the authentication tag
        decipher.setAuthTag(authTag);
        // Set additional authenticated data if present
        if (encryptedData.aad) {
            decipher.setAAD(aad);
        }
        let decrypted = decipher.update(encryptedData.encryptedData, 'base64', 'utf8');
        decrypted += decipher.final('utf8');
        logger_1.logger.info('Data decrypted successfully', {
            keyId: key.id,
            algorithm: encryptedData.algorithm,
            decryptedLength: decrypted.length
        });
        return {
            success: true,
            data: decrypted,
            algorithm: encryptedData.algorithm,
            keyId: key.id
        };
    }
    catch (error) {
        logger_1.logger.error('Decryption failed', {
            error: error instanceof Error ? error.message : String(error),
            keyId: key.id
        });
        return {
            success: false,
            error: error instanceof Error ? error.message : 'Decryption failed'
        };
    }
}
async function getEncryptionKey(keyId) {
    try {
        const key = await database_1.db.readItem('encryption-keys', keyId, keyId);
        return key;
    }
    catch (error) {
        logger_1.logger.error('Failed to get encryption key', { error, keyId });
        return null;
    }
}
async function checkKeyAccess(key, userId) {
    try {
        return key.access.allowedUsers.includes(userId) || key.createdBy === userId;
    }
    catch (error) {
        logger_1.logger.error('Failed to check key access', { error, keyId: key.id, userId });
        return false;
    }
}
async function logEncryptionOperation(operation) {
    try {
        const operationRecord = {
            id: (0, uuid_1.v4)(),
            ...operation
        };
        await database_1.db.createItem('encryption-operations', operationRecord);
    }
    catch (error) {
        logger_1.logger.error('Failed to log encryption operation', { error });
    }
}
async function updateKeyUsage(keyId, operation) {
    try {
        const key = await database_1.db.readItem('encryption-keys', keyId, keyId);
        if (key) {
            const keyData = key;
            if (operation === 'encrypt') {
                keyData.usage.encryptionCount += 1;
            }
            else {
                keyData.usage.decryptionCount += 1;
            }
            keyData.usage.lastUsed = new Date().toISOString();
            keyData.updatedAt = new Date().toISOString();
            await database_1.db.updateItem('encryption-keys', keyData);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to update key usage', { error, keyId, operation });
    }
}
// Register functions
functions_1.app.http('data-encrypt', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'security/encrypt',
    handler: encryptData
});
functions_1.app.http('data-decrypt', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'security/decrypt',
    handler: decryptData
});
//# sourceMappingURL=data-encryption.js.map