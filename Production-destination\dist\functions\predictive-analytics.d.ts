/**
 * Predictive Analytics Function
 * Handles predictive analytics and machine learning insights
 * Migrated from old-arch/src/analytics-service/predictive/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Generate prediction handler
 */
export declare function generatePrediction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Train model handler
 */
export declare function trainModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
