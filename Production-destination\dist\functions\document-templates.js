"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTemplate = createTemplate;
exports.generateFromTemplate = generateFromTemplate;
/**
 * Document Templates Function
 * Handles document template management and generation
 * Migrated from old-arch/src/template-service/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Template types and enums
var TemplateType;
(function (TemplateType) {
    TemplateType["DOCUMENT"] = "DOCUMENT";
    TemplateType["FORM"] = "FORM";
    TemplateType["REPORT"] = "REPORT";
    TemplateType["CONTRACT"] = "CONTRACT";
    TemplateType["INVOICE"] = "INVOICE";
    TemplateType["LETTER"] = "LETTER";
    TemplateType["PRESENTATION"] = "PRESENTATION";
    TemplateType["CUSTOM"] = "CUSTOM";
})(TemplateType || (TemplateType = {}));
var TemplateStatus;
(function (TemplateStatus) {
    TemplateStatus["DRAFT"] = "DRAFT";
    TemplateStatus["ACTIVE"] = "ACTIVE";
    TemplateStatus["ARCHIVED"] = "ARCHIVED";
    TemplateStatus["DEPRECATED"] = "DEPRECATED";
})(TemplateStatus || (TemplateStatus = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "TEXT";
    FieldType["NUMBER"] = "NUMBER";
    FieldType["DATE"] = "DATE";
    FieldType["BOOLEAN"] = "BOOLEAN";
    FieldType["EMAIL"] = "EMAIL";
    FieldType["PHONE"] = "PHONE";
    FieldType["ADDRESS"] = "ADDRESS";
    FieldType["CURRENCY"] = "CURRENCY";
    FieldType["PERCENTAGE"] = "PERCENTAGE";
    FieldType["SELECT"] = "SELECT";
    FieldType["MULTISELECT"] = "MULTISELECT";
    FieldType["FILE"] = "FILE";
    FieldType["IMAGE"] = "IMAGE";
})(FieldType || (FieldType = {}));
// Validation schemas
const createTemplateSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(TemplateType)).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    content: Joi.string().required(),
    fields: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        label: Joi.string().required(),
        type: Joi.string().valid(...Object.values(FieldType)).required(),
        required: Joi.boolean().default(false),
        defaultValue: Joi.any().optional(),
        placeholder: Joi.string().optional(),
        validation: Joi.object({
            pattern: Joi.string().optional(),
            minLength: Joi.number().optional(),
            maxLength: Joi.number().optional(),
            min: Joi.number().optional(),
            max: Joi.number().optional()
        }).optional(),
        options: Joi.array().items(Joi.object({
            value: Joi.string().required(),
            label: Joi.string().required()
        })).optional(),
        conditional: Joi.object({
            field: Joi.string().required(),
            value: Joi.any().required(),
            operator: Joi.string().valid('equals', 'not_equals', 'contains', 'greater_than', 'less_than').default('equals')
        }).optional()
    })).optional(),
    settings: Joi.object({
        allowPublicAccess: Joi.boolean().default(false),
        requireApproval: Joi.boolean().default(false),
        enableVersioning: Joi.boolean().default(true),
        defaultFormat: Joi.string().valid('html', 'pdf', 'docx', 'txt').default('html'),
        autoSave: Joi.boolean().default(true),
        enableCollaboration: Joi.boolean().default(false)
    }).optional(),
    metadata: Joi.object().optional(),
    tags: Joi.array().items(Joi.string()).optional()
});
const generateFromTemplateSchema = Joi.object({
    templateId: Joi.string().uuid().required(),
    data: Joi.object().required(),
    outputFormat: Joi.string().valid('html', 'pdf', 'docx', 'txt').optional(),
    documentName: Joi.string().max(100).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    saveAsDocument: Joi.boolean().default(true),
    metadata: Joi.object().optional()
});
/**
 * Create template handler
 */
async function createTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const templateRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(templateRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check template creation limits
        const canCreate = await checkTemplateCreationLimits(templateRequest.organizationId);
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Create template
        const templateId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const template = {
            id: templateId,
            name: templateRequest.name,
            description: templateRequest.description,
            type: templateRequest.type,
            status: TemplateStatus.DRAFT,
            organizationId: templateRequest.organizationId,
            projectId: templateRequest.projectId,
            content: templateRequest.content,
            fields: templateRequest.fields || [],
            settings: {
                allowPublicAccess: false,
                requireApproval: false,
                enableVersioning: true,
                defaultFormat: 'html',
                autoSave: true,
                enableCollaboration: false,
                ...templateRequest.settings
            },
            metadata: templateRequest.metadata || {},
            tags: templateRequest.tags || [],
            version: '1.0.0',
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            usageCount: 0,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('templates', template);
        // Store template content in blob storage
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient("templates");
        const blobClient = containerClient.getBlobClient(`${templateId}/content.html`);
        const contentBuffer = Buffer.from(templateRequest.content, 'utf-8');
        await blobClient.getBlockBlobClient().upload(contentBuffer, contentBuffer.length);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "template_created",
            userId: user.id,
            organizationId: templateRequest.organizationId,
            projectId: templateRequest.projectId,
            timestamp: now,
            details: {
                templateId,
                templateName: templateRequest.name,
                templateType: templateRequest.type,
                fieldCount: templateRequest.fields?.length || 0,
                hasCustomFields: (templateRequest.fields?.length || 0) > 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'TemplateCreated',
            aggregateId: templateId,
            aggregateType: 'Template',
            version: 1,
            data: {
                template,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: templateRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Template created successfully", {
            correlationId,
            templateId,
            templateName: templateRequest.name,
            templateType: templateRequest.type,
            fieldCount: templateRequest.fields?.length || 0,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: templateId,
                name: templateRequest.name,
                type: templateRequest.type,
                status: TemplateStatus.DRAFT,
                version: '1.0.0',
                fieldCount: templateRequest.fields?.length || 0,
                createdAt: now,
                message: "Template created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Generate document from template handler
 */
async function generateFromTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Generate from template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = generateFromTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const generateRequest = value;
        // Get template
        const template = await database_1.db.readItem('templates', generateRequest.templateId, generateRequest.templateId);
        if (!template) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Template not found" }
            }, request);
        }
        const templateData = template;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(generateRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check if template is accessible
        if (templateData.organizationId !== generateRequest.organizationId && !templateData.settings.allowPublicAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to template" }
            }, request);
        }
        // Validate required fields
        const validationResult = validateTemplateData(templateData.fields, generateRequest.data);
        if (!validationResult.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Template data validation failed',
                    message: validationResult.errors.join(', ')
                }
            }, request);
        }
        // Generate document content
        const generatedContent = await generateDocumentContent(templateData.content, generateRequest.data, generateRequest.outputFormat || templateData.settings.defaultFormat);
        let documentId = null;
        // Save as document if requested
        if (generateRequest.saveAsDocument) {
            documentId = await createDocumentFromTemplate(templateData, generatedContent, generateRequest.documentName, generateRequest.organizationId, generateRequest.projectId, user);
        }
        // Update template usage count
        const updatedTemplate = {
            ...templateData,
            id: generateRequest.templateId,
            usageCount: (templateData.usageCount || 0) + 1,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('templates', updatedTemplate);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_generated_from_template",
            userId: user.id,
            organizationId: generateRequest.organizationId,
            projectId: generateRequest.projectId,
            timestamp: new Date().toISOString(),
            details: {
                templateId: generateRequest.templateId,
                templateName: templateData.name,
                documentId,
                outputFormat: generateRequest.outputFormat || templateData.settings.defaultFormat,
                savedAsDocument: generateRequest.saveAsDocument
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentGeneratedFromTemplate',
            aggregateId: documentId || (0, uuid_1.v4)(),
            aggregateType: 'Document',
            version: 1,
            data: {
                templateId: generateRequest.templateId,
                templateName: templateData.name,
                documentId,
                generatedBy: user.id,
                outputFormat: generateRequest.outputFormat || templateData.settings.defaultFormat
            },
            userId: user.id,
            organizationId: generateRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document generated from template successfully", {
            correlationId,
            templateId: generateRequest.templateId,
            templateName: templateData.name,
            documentId,
            outputFormat: generateRequest.outputFormat || templateData.settings.defaultFormat,
            generatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                templateId: generateRequest.templateId,
                templateName: templateData.name,
                documentId,
                content: generatedContent,
                outputFormat: generateRequest.outputFormat || templateData.settings.defaultFormat,
                generatedAt: new Date().toISOString(),
                message: "Document generated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Generate from template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkTemplateCreationLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxTemplates: 5 },
            'PROFESSIONAL': { maxTemplates: 50 },
            'ENTERPRISE': { maxTemplates: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxTemplates === -1) {
            return { allowed: true };
        }
        // Check current template count
        const templateCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != @archived';
        const countResult = await database_1.db.queryItems('templates', templateCountQuery, [organizationId, TemplateStatus.ARCHIVED]);
        const currentCount = Number(countResult[0]) || 0;
        if (currentCount >= limit.maxTemplates) {
            return {
                allowed: false,
                reason: `Template limit reached (${limit.maxTemplates})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check template creation limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function validateTemplateData(fields, data) {
    const errors = [];
    for (const field of fields) {
        if (field.required && (!data[field.name] || data[field.name] === '')) {
            errors.push(`Field '${field.label}' is required`);
            continue;
        }
        const value = data[field.name];
        if (value && field.validation) {
            // Validate based on field type and validation rules
            if (field.validation.pattern && !new RegExp(field.validation.pattern).test(value)) {
                errors.push(`Field '${field.label}' does not match required pattern`);
            }
            if (field.validation.minLength && value.length < field.validation.minLength) {
                errors.push(`Field '${field.label}' must be at least ${field.validation.minLength} characters`);
            }
            if (field.validation.maxLength && value.length > field.validation.maxLength) {
                errors.push(`Field '${field.label}' must be no more than ${field.validation.maxLength} characters`);
            }
            if (field.type === FieldType.NUMBER) {
                const numValue = Number(value);
                if (isNaN(numValue)) {
                    errors.push(`Field '${field.label}' must be a valid number`);
                }
                else {
                    if (field.validation.min !== undefined && numValue < field.validation.min) {
                        errors.push(`Field '${field.label}' must be at least ${field.validation.min}`);
                    }
                    if (field.validation.max !== undefined && numValue > field.validation.max) {
                        errors.push(`Field '${field.label}' must be no more than ${field.validation.max}`);
                    }
                }
            }
        }
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
async function generateDocumentContent(templateContent, data, outputFormat) {
    // Simple template variable replacement
    let content = templateContent;
    // Replace template variables like {{fieldName}}
    for (const [key, value] of Object.entries(data)) {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        content = content.replace(regex, String(value || ''));
    }
    // Handle conditional blocks {{#if condition}}...{{/if}}
    content = content.replace(/{{#if\s+(\w+)}}(.*?){{\/if}}/gs, (match, condition, block) => {
        return data[condition] ? block : '';
    });
    // Handle loops {{#each items}}...{{/each}}
    content = content.replace(/{{#each\s+(\w+)}}(.*?){{\/each}}/gs, (match, arrayName, block) => {
        const items = data[arrayName];
        if (Array.isArray(items)) {
            return items.map(item => {
                let itemBlock = block;
                for (const [key, value] of Object.entries(item)) {
                    const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
                    itemBlock = itemBlock.replace(regex, String(value || ''));
                }
                return itemBlock;
            }).join('');
        }
        return '';
    });
    // Format conversion would happen here based on outputFormat
    // For now, return as-is
    return content;
}
async function createDocumentFromTemplate(template, content, name, organizationId, projectId, user) {
    const documentId = (0, uuid_1.v4)();
    const document = {
        id: documentId,
        name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
        description: `Generated from template: ${template.name}`,
        templateId: template.id,
        content,
        organizationId,
        projectId,
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
        status: "DRAFT",
        tenantId: user.tenantId
    };
    await database_1.db.createItem('documents', document);
    return documentId;
}
// Register functions
functions_1.app.http('template-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'templates',
    handler: createTemplate
});
functions_1.app.http('document-template-generate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'document-templates/generate',
    handler: generateFromTemplate
});
//# sourceMappingURL=document-templates.js.map