/**
 * Security Monitoring Function
 * Handles security monitoring, threat detection, and incident response
 * Migrated from old-arch/src/security-service/monitoring/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create security incident handler
 */
export declare function createSecurityIncident(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Analyze security threats handler
 */
export declare function analyzeSecurityThreats(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
