/**
 * Service Bus Handlers for Azure Functions
 * Handles Service Bus queue and topic message processing
 */

import { InvocationContext, app } from '@azure/functions';
import { logger } from '../shared/utils/logger';
import { db } from '../shared/services/database';
import { publishEvent, EventType } from './event-grid-handlers';
import { redis } from '../shared/services/redis';
import { serviceBusEnhanced } from '../shared/services/service-bus';

// Enhanced Service Bus features (using shared service)
// Note: Individual client management removed - now using serviceBusEnhanced

// Enhanced Service Bus metrics
interface ServiceBusMetrics {
  messagesSent: number;
  messagesReceived: number;
  messagesDeadLettered: number;
  errors: number;
  averageProcessingTime: number;
  activeConnections: number;
}

let metrics: ServiceBusMetrics = {
  messagesSent: 0,
  messagesReceived: 0,
  messagesDeadLettered: 0,
  errors: 0,
  averageProcessingTime: 0,
  activeConnections: 0
};

// Circuit breaker state
interface CircuitBreakerState {
  isOpen: boolean;
  failureCount: number;
  lastFailureTime: Date;
  threshold: number;
  timeout: number;
}

let circuitBreakers: Map<string, CircuitBreakerState> = new Map();

/**
 * Initialize shared Service Bus service
 */
async function initializeServiceBus(): Promise<void> {
  await serviceBusEnhanced.initialize();
}

/**
 * AI Operations handler - processes AI operation requests from the queue
 */
async function aiOperationsHandler(message: unknown, context: InvocationContext): Promise<void> {
  const startTime = Date.now();
  metrics.messagesReceived++;

  try {
    logger.info('Processing AI operation from Service Bus', {
      messageId: context.invocationId,
      triggerMetadata: context.triggerMetadata
    });

    const aiMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const { operationId, operationType, data } = aiMessage as any;

    if (!operationId || !operationType) {
      throw new Error('Invalid AI operation message: missing operationId or operationType');
    }

    // Update operation status to processing
    await db.updateItem('ai-operations', {
      ...data,
      id: operationId,
      status: 'processing',
      processingStartedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // Process based on operation type
    let result;
    switch (operationType) {
      case 'DOCUMENT_ANALYSIS':
        result = await processDocumentAnalysis(data);
        break;
      case 'CONTENT_GENERATION':
        result = await processContentGeneration(data);
        break;
      case 'CONTENT_COMPLETION':
        result = await processContentCompletion(data);
        break;
      case 'DOCUMENT_SUMMARIZATION':
        result = await processDocumentSummarization(data);
        break;
      case 'INTELLIGENT_SEARCH':
        result = await processIntelligentSearch(data);
        break;
      case 'WORKFLOW_OPTIMIZATION':
        result = await processWorkflowOptimization(data);
        break;
      case 'BATCH_PROCESSING':
        result = await processBatchOperation(data);
        break;
      default:
        throw new Error(`Unknown AI operation type: ${operationType}`);
    }

    // Update operation status to completed
    await db.updateItem('ai-operations', {
      ...data,
      id: operationId,
      status: 'completed',
      result,
      processingCompletedAt: new Date().toISOString(),
      processingTime: Date.now() - startTime,
      updatedAt: new Date().toISOString()
    });

    // Publish completion event
    await publishEvent(
      EventType.ANALYTICS_GENERATED,
      `ai-operations/${operationId}`,
      {
        operationId,
        operationType,
        status: 'completed',
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      }
    );

    // Cache result in Redis for quick access
    await redis.setJson(`ai-op:${operationId}`, result, 3600); // 1 hour

    logger.info('AI operation processed successfully', {
      operationId,
      operationType,
      processingTime: Date.now() - startTime
    });

  } catch (error) {
    metrics.errors++;
    logger.error('AI operation handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update operation status to failed
    const aiMessage = typeof message === 'string' ? JSON.parse(message) : message;
    if ((aiMessage as any)?.operationId) {
      await db.updateItem('ai-operations', {
        ...(aiMessage as any).data,
        id: (aiMessage as any).operationId,
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        processingFailedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    throw error; // Re-throw to trigger dead letter processing
  } finally {
    updateMetrics(Date.now() - startTime);
  }
}

/**
 * Scheduled Emails handler - processes scheduled email delivery from the queue
 */
async function scheduledEmailsHandler(message: unknown, context: InvocationContext): Promise<void> {
  const startTime = Date.now();
  metrics.messagesReceived++;

  try {
    logger.info('Processing scheduled email from Service Bus', {
      messageId: context.invocationId,
      triggerMetadata: context.triggerMetadata
    });

    const emailMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const { emailId, type, emailData } = emailMessage as any;

    if (!emailId || !emailData) {
      throw new Error('Invalid email message: missing emailId or emailData');
    }

    // Update email status to processing
    await db.updateItem('email-messages', {
      ...emailData,
      id: emailId,
      status: 'processing',
      delivery: {
        ...emailData.delivery,
        processingStartedAt: new Date().toISOString(),
        attempts: emailData.delivery.attempts + 1
      },
      updatedAt: new Date().toISOString()
    });

    // Process email delivery using Postmark
    const postmarkToken = process.env.POSTMARK_SERVER_TOKEN;
    const fromEmail = process.env.POSTMARK_FROM_EMAIL || '<EMAIL>';

    if (!postmarkToken) {
      throw new Error('Postmark server token not configured');
    }

    const { Client } = require('postmark');
    const postmarkClient = new Client(postmarkToken);

    // Prepare email for delivery
    const emailPayload = {
      From: fromEmail,
      To: emailData.to.join(','),
      Cc: emailData.cc?.join(','),
      Bcc: emailData.bcc?.join(','),
      Subject: emailData.subject,
      HtmlBody: emailData.content,
      TextBody: emailData.content.replace(/<[^>]*>/g, ''), // Strip HTML for text version
      Tag: emailData.type || 'general',
      TrackOpens: emailData.options?.trackOpens || true,
      TrackLinks: emailData.options?.trackLinks || 'HtmlAndText',
      MessageStream: 'outbound',
      Attachments: emailData.attachments?.map((att: any) => ({
        Name: att.filename,
        Content: att.content,
        ContentType: att.contentType
      })) || []
    };

    // Send email via Postmark
    const result = await postmarkClient.sendEmail(emailPayload);

    // Update email status to sent
    await db.updateItem('email-messages', {
      ...emailData,
      id: emailId,
      status: 'sent',
      delivery: {
        ...emailData.delivery,
        sentAt: new Date().toISOString(),
        messageId: result.MessageID,
        attempts: emailData.delivery.attempts + 1
      },
      updatedAt: new Date().toISOString()
    });

    // Publish email sent event
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `emails/${emailId}`,
      {
        emailId,
        messageId: result.MessageID,
        to: emailData.to,
        subject: emailData.subject,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Scheduled email processed successfully', {
      emailId,
      messageId: result.MessageID,
      processingTime: Date.now() - startTime
    });

  } catch (error) {
    metrics.errors++;
    logger.error('Scheduled email handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update email status to failed
    const emailMessage = typeof message === 'string' ? JSON.parse(message) : message;
    if ((emailMessage as any)?.emailId && (emailMessage as any)?.emailData) {
      await db.updateItem('email-messages', {
        ...(emailMessage as any).emailData,
        id: (emailMessage as any).emailId,
        status: 'failed',
        delivery: {
          ...(emailMessage as any).emailData.delivery,
          failedAt: new Date().toISOString(),
          errorMessage: error instanceof Error ? error.message : String(error),
          attempts: (emailMessage as any).emailData.delivery.attempts + 1
        },
        updatedAt: new Date().toISOString()
      });
    }

    throw error; // Re-throw to trigger dead letter processing
  } finally {
    updateMetrics(Date.now() - startTime);
  }
}

/**
 * Document Processing handler - processes document analysis requests from the queue
 */
async function documentProcessingHandler(message: unknown, context: InvocationContext): Promise<void> {
  const startTime = Date.now();
  metrics.messagesReceived++;

  try {
    logger.info('Processing document processing request from Service Bus', {
      messageId: context.invocationId,
      triggerMetadata: context.triggerMetadata
    });

    const docMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const { documentId, userId, action, analysisType = 'layout' } = docMessage as any;

    if (!documentId || !userId || !action) {
      throw new Error('Invalid document processing message: missing required fields');
    }

    // Get document from database
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      throw new Error(`Document not found: ${documentId}`);
    }

    // Update document status to processing
    await db.updateItem('documents', {
      ...document,
      id: documentId,
      status: 'processing',
      processingStartedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // Process document based on action
    let result;
    switch (action) {
      case 'analyze':
        result = await processDocumentAnalysisAction(document, analysisType);
        break;
      case 'extract-text':
        result = await extractTextFromDocumentAction(document);
        break;
      case 'generate-thumbnail':
        result = await generateThumbnailAction(document);
        break;
      case 'classify':
        result = await classifyDocumentAction(document);
        break;
      default:
        throw new Error(`Unknown document processing action: ${action}`);
    }

    // Update document status to processed
    await db.updateItem('documents', {
      ...document,
      id: documentId,
      status: 'processed',
      processingResult: result,
      processingCompletedAt: new Date().toISOString(),
      processingTime: Date.now() - startTime,
      updatedAt: new Date().toISOString()
    });

    // Publish completion event
    await publishEvent(
      EventType.DOCUMENT_PROCESSED,
      `documents/${documentId}`,
      {
        documentId,
        action,
        status: 'completed',
        processingTime: Date.now() - startTime,
        timestamp: new Date().toISOString()
      }
    );

    // Cache result in Redis
    await redis.setJson(`doc-result:${documentId}`, result, 3600); // 1 hour

    logger.info('Document processing completed successfully', {
      documentId,
      action,
      processingTime: Date.now() - startTime
    });

  } catch (error) {
    metrics.errors++;
    logger.error('Document processing handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update document status to failed
    const docMessage = typeof message === 'string' ? JSON.parse(message) : message;
    if ((docMessage as any)?.documentId) {
      await db.updateItem('documents', {
        id: (docMessage as any).documentId,
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        processingFailedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    throw error; // Re-throw to trigger dead letter processing
  } finally {
    updateMetrics(Date.now() - startTime);
  }
}

/**
 * Notification Delivery handler - processes push notification delivery from the queue
 */
async function notificationDeliveryHandler(message: unknown, context: InvocationContext): Promise<void> {
  const startTime = Date.now();
  metrics.messagesReceived++;

  try {
    logger.info('Processing notification delivery from Service Bus', {
      messageId: context.invocationId,
      triggerMetadata: context.triggerMetadata
    });

    const notificationMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const { notificationId, platform, title, body, data, userId, deviceToken } = notificationMessage as any;

    if (!notificationId || !platform || !title || !body) {
      throw new Error('Invalid notification message: missing required fields');
    }

    // Update notification status to processing
    await db.updateItem('push-notifications', {
      id: notificationId,
      status: 'processing',
      processingStartedAt: new Date().toISOString(),
      updatedAt: new Date().toISOString()
    });

    // Process notification delivery using Azure Notification Hubs
    const hubName = process.env.NOTIFICATION_HUB_NAME || 'hepztech';
    const connectionString = process.env.NOTIFICATION_HUB_CONNECTION_STRING;

    if (!connectionString) {
      throw new Error('Notification Hub connection string not configured');
    }

    const { NotificationHubsClient, createAppleNotification, createFcmV1Notification } = require('@azure/notification-hubs');
    const client = new NotificationHubsClient(connectionString, hubName);

    // Create platform-specific notification
    let notification;
    switch (platform.toLowerCase()) {
      case 'ios':
      case 'apple':
        notification = createAppleNotification({
          body: {
            aps: {
              alert: { title, body },
              badge: 1,
              sound: 'default'
            },
            data: data || {}
          }
        });
        break;
      case 'android':
      case 'fcm':
        notification = createFcmV1Notification({
          body: {
            notification: { title, body },
            data: data || {},
            android: { priority: 'high' }
          }
        });
        break;
      default:
        throw new Error(`Unsupported platform: ${platform}`);
    }

    // Send notification
    const result = await client.sendNotification(notification, { tags: [`userId:${userId}`] });

    // Update notification status to sent
    await db.updateItem('push-notifications', {
      id: notificationId,
      status: 'sent',
      delivery: {
        sentAt: new Date().toISOString(),
        notificationId: result.notificationId,
        trackingId: result.trackingId
      },
      processingCompletedAt: new Date().toISOString(),
      processingTime: Date.now() - startTime,
      updatedAt: new Date().toISOString()
    });

    // Publish notification sent event
    await publishEvent(
      EventType.NOTIFICATION_SENT,
      `notifications/${notificationId}`,
      {
        notificationId,
        platform,
        userId,
        title,
        hubNotificationId: result.notificationId,
        timestamp: new Date().toISOString()
      }
    );

    logger.info('Notification delivery completed successfully', {
      notificationId,
      platform,
      userId,
      hubNotificationId: result.notificationId,
      processingTime: Date.now() - startTime
    });

  } catch (error) {
    metrics.errors++;
    logger.error('Notification delivery handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Update notification status to failed
    const notificationMessage = typeof message === 'string' ? JSON.parse(message) : message;
    if ((notificationMessage as any)?.notificationId) {
      await db.updateItem('push-notifications', {
        id: (notificationMessage as any).notificationId,
        status: 'failed',
        error: error instanceof Error ? error.message : String(error),
        processingFailedAt: new Date().toISOString(),
        updatedAt: new Date().toISOString()
      });
    }

    throw error; // Re-throw to trigger dead letter processing
  } finally {
    updateMetrics(Date.now() - startTime);
  }
}

/**
 * Workflow orchestration queue handler
 * Handles workflow step execution messages
 */
async function workflowOrchestrationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Workflow orchestration handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const workflowMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      workflowId,
      stepId,
      action,
      data = {},
      userId,
      priority = 'normal'
    } = workflowMessage as any;

    if (!workflowId || !stepId || !action) {
      throw new Error('Invalid workflow message: missing required fields');
    }

    // Get workflow from database
    const workflows = await db.queryItems<any>('workflows',
      'SELECT * FROM c WHERE c.id = @workflowId',
      [workflowId]
    );

    if (workflows.length === 0) {
      throw new Error(`Workflow not found: ${workflowId}`);
    }

    const workflow = workflows[0];

    // Process workflow step
    const stepResult = await processWorkflowStep(workflow, stepId, action, data, userId);

    // Update workflow status
    await db.updateItem('workflows', {
      ...workflow,
      currentStep: stepResult.nextStep,
      status: stepResult.workflowStatus,
      lastExecutedAt: new Date().toISOString(),
      steps: workflow.steps.map((step: any) =>
        step.id === stepId
          ? { ...step, status: stepResult.stepStatus, executedAt: new Date().toISOString(), result: stepResult.result }
          : step
      )
    });

    // Publish workflow events
    if (stepResult.workflowStatus === 'completed') {
      await publishEvent(
        EventType.WORKFLOW_COMPLETED,
        `workflows/${workflowId}`,
        {
          workflowId,
          completedBy: userId,
          duration: Date.now() - new Date(workflow.createdAt).getTime(),
          timestamp: new Date().toISOString()
        }
      );
    }

    // Queue next step if workflow continues
    if (stepResult.nextStep && stepResult.workflowStatus === 'active') {
      await queueNextWorkflowStep(workflowId, stepResult.nextStep, userId);
    }

    logger.info('Workflow step processed successfully', {
      workflowId,
      stepId,
      action,
      nextStep: stepResult.nextStep,
      workflowStatus: stepResult.workflowStatus
    });

  } catch (error) {
    logger.error('Workflow orchestration handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });

    // Handle workflow failure
    if (typeof message === 'object' && message !== null && 'workflowId' in message) {
      const workflowId = (message as any).workflowId;
      try {
        const workflows = await db.queryItems<any>('workflows',
          'SELECT * FROM c WHERE c.id = @workflowId',
          [workflowId]
        );

        if (workflows.length > 0) {
          const workflow = workflows[0];
          await db.updateItem('workflows', {
            ...workflow,
            status: 'failed',
            error: error instanceof Error ? error.message : String(error),
            failedAt: new Date().toISOString()
          });
        }
      } catch (updateError) {
        logger.error('Failed to update workflow status after failure', { updateError });
      }
    }
  }
}

/**
 * Document collaboration handler
 * Handles real-time document collaboration messages
 */
async function documentCollaborationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Document collaboration handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const collaborationMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      documentId,
      userId,
      action,
      data = {},
      sessionId,
      timestamp
    } = collaborationMessage as any;

    if (!documentId || !userId || !action) {
      throw new Error('Invalid collaboration message: missing required fields');
    }

    // Process collaboration action
    let result;
    switch (action) {
      case 'join':
        result = await handleUserJoinDocument(documentId, userId, sessionId);
        break;
      case 'leave':
        result = await handleUserLeaveDocument(documentId, userId, sessionId);
        break;
      case 'edit':
        result = await handleDocumentEdit(documentId, userId, data);
        break;
      case 'comment':
        result = await handleDocumentComment(documentId, userId, data);
        break;
      case 'share':
        result = await handleDocumentShare(documentId, userId, data);
        break;
      default:
        throw new Error(`Unknown collaboration action: ${action}`);
    }

    // Log collaboration activity
    await db.createItem('collaboration-logs', {
      id: `collab-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      documentId,
      userId,
      action,
      data,
      sessionId,
      result,
      timestamp: new Date().toISOString()
    });

    // Broadcast to other collaborators via SignalR
    await broadcastCollaborationUpdate(documentId, {
      action,
      userId,
      data,
      result,
      timestamp: new Date().toISOString()
    });

    logger.info('Document collaboration processed successfully', {
      documentId,
      userId,
      action,
      sessionId
    });

  } catch (error) {
    logger.error('Document collaboration handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Analytics aggregation handler
 * Handles analytics data aggregation messages
 */
async function analyticsAggregationHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('Analytics aggregation handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const analyticsMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      eventType,
      entityId,
      entityType,
      metrics = {},
      timestamp,
      userId
    } = analyticsMessage as any;

    if (!eventType || !entityId || !entityType) {
      throw new Error('Invalid analytics message: missing required fields');
    }

    // Aggregate metrics based on event type
    const aggregationResult = await aggregateMetrics(eventType, entityId, entityType, metrics, userId);

    // Update analytics data
    await updateAnalyticsData(entityType, entityId, aggregationResult);

    // Check for analytics thresholds and alerts
    await checkAnalyticsThresholds(entityType, entityId, aggregationResult);

    logger.info('Analytics aggregation processed successfully', {
      eventType,
      entityId,
      entityType,
      metricsCount: Object.keys(metrics).length
    });

  } catch (error) {
    logger.error('Analytics aggregation handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * System monitoring handler
 * Handles system monitoring and alerting messages
 */
async function systemMonitoringHandler(message: unknown, context: InvocationContext): Promise<void> {
  logger.info('System monitoring handler triggered', {
    message,
    messageId: context.triggerMetadata?.messageId
  });

  try {
    const monitoringMessage = typeof message === 'string' ? JSON.parse(message) : message;
    const {
      source,
      metricType,
      value,
      threshold = {},
      severity = 'info',
      timestamp
    } = monitoringMessage as any;

    if (!source || !metricType || value === undefined) {
      throw new Error('Invalid monitoring message: missing required fields');
    }

    // Store monitoring data
    await db.createItem('monitoring-data', {
      id: `monitor-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      source,
      metricType,
      value,
      severity,
      timestamp: timestamp || new Date().toISOString()
    });

    // Check thresholds and generate alerts
    if (threshold.warning && value > threshold.warning) {
      await generateMonitoringAlert('warning', source, metricType, value, threshold.warning);
    }

    if (threshold.critical && value > threshold.critical) {
      await generateMonitoringAlert('critical', source, metricType, value, threshold.critical);
    }

    logger.info('System monitoring processed successfully', {
      source,
      metricType,
      value,
      severity
    });

  } catch (error) {
    logger.error('System monitoring handler failed', {
      message,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

// Helper functions (placeholder implementations)
async function processWorkflowStep(workflow: any, stepId: string, action: string, data: any, userId: string): Promise<any> {
  // Implement workflow step processing logic
  return {
    stepStatus: 'completed',
    workflowStatus: 'active',
    nextStep: 'step-2',
    result: { processed: true }
  };
}

async function queueNextWorkflowStep(workflowId: string, stepId: string, userId: string): Promise<void> {
  // Queue next workflow step using shared service
  await serviceBusEnhanced.initialize();

  const success = await serviceBusEnhanced.sendToQueue('workflow-orchestration', {
    body: {
      workflowId,
      stepId,
      action: 'execute',
      userId,
      timestamp: new Date().toISOString()
    },
    messageId: `workflow-${workflowId}-${stepId}-${Date.now()}`,
    correlationId: workflowId,
    applicationProperties: {
      workflowId,
      stepId,
      userId,
      source: 'service-bus-handlers'
    }
  });

  if (!success) {
    throw new Error('Failed to queue workflow step');
  }
}

async function handleUserJoinDocument(documentId: string, userId: string, sessionId: string): Promise<any> {
  return { action: 'joined', activeUsers: 2 };
}

async function handleUserLeaveDocument(documentId: string, userId: string, sessionId: string): Promise<any> {
  return { action: 'left', activeUsers: 1 };
}

async function handleDocumentEdit(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'edited', changes: data.changes };
}

async function handleDocumentComment(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'commented', commentId: 'comment-123' };
}

async function handleDocumentShare(documentId: string, userId: string, data: any): Promise<any> {
  return { action: 'shared', shareId: 'share-123' };
}

async function broadcastCollaborationUpdate(documentId: string, update: any): Promise<void> {
  // Implement SignalR broadcast
}

async function aggregateMetrics(eventType: string, entityId: string, entityType: string, metrics: any, userId: string): Promise<any> {
  return { aggregated: true, count: 1 };
}

async function updateAnalyticsData(entityType: string, entityId: string, data: any): Promise<void> {
  // Update analytics data in database
}

async function checkAnalyticsThresholds(entityType: string, entityId: string, data: any): Promise<void> {
  // Check for threshold breaches
}

async function generateMonitoringAlert(severity: string, source: string, metricType: string, value: number, threshold: number): Promise<void> {
  await publishEvent(
    EventType.PERFORMANCE_ALERT,
    `monitoring/${source}`,
    {
      alertType: 'threshold_breach',
      severity,
      source,
      metricType,
      value,
      threshold,
      timestamp: new Date().toISOString()
    }
  );
}

// AI Operation Processing Functions
async function processDocumentAnalysis(data: any): Promise<any> {
  // Implement AI document analysis logic
  return {
    type: 'document_analysis',
    confidence: 0.95,
    entities: [],
    extractedText: 'Sample extracted text',
    processingTime: 2500
  };
}

async function processContentGeneration(data: any): Promise<any> {
  // Implement AI content generation logic
  return {
    type: 'content_generation',
    generatedContent: 'Sample generated content',
    confidence: 0.92,
    processingTime: 3000
  };
}

async function processContentCompletion(data: any): Promise<any> {
  // Implement AI content completion logic
  return {
    type: 'content_completion',
    completedContent: 'Sample completed content',
    confidence: 0.88,
    processingTime: 1500
  };
}

async function processDocumentSummarization(data: any): Promise<any> {
  // Implement AI document summarization logic
  return {
    type: 'document_summarization',
    summary: 'Sample document summary',
    keyPoints: ['Point 1', 'Point 2', 'Point 3'],
    confidence: 0.90,
    processingTime: 2000
  };
}

async function processIntelligentSearch(data: any): Promise<any> {
  // Implement AI intelligent search logic
  return {
    type: 'intelligent_search',
    results: [],
    relevanceScore: 0.85,
    processingTime: 800
  };
}

async function processWorkflowOptimization(data: any): Promise<any> {
  // Implement AI workflow optimization logic
  return {
    type: 'workflow_optimization',
    optimizations: [],
    efficiency_gain: 0.15,
    processingTime: 4000
  };
}

async function processBatchOperation(data: any): Promise<any> {
  // Implement AI batch processing logic
  return {
    type: 'batch_processing',
    processedItems: 0,
    totalItems: 0,
    processingTime: 5000
  };
}

// Document Processing Action Functions
async function processDocumentAnalysisAction(document: any, analysisType: string): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');
  const { ragService } = require('../shared/services/rag-service');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
    const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error('Failed to download document content');
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of downloadResponse.readableStreamBody) {
      chunks.push(Buffer.from(chunk));
    }
    const documentBuffer = Buffer.concat(chunks);

    // Perform enhanced document analysis
    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      analysisType === 'invoice' ? 'prebuilt-invoice' : 'prebuilt-layout'
    );

    // Index for RAG if substantial content
    if (analysisResult.extractedText && analysisResult.extractedText.length > 500) {
      await ragService.indexDocument({
        documentId: document.id,
        content: analysisResult.extractedText,
        metadata: {
          analysisType,
          modelUsed: analysisResult.modelUsed,
          confidence: analysisResult.confidence
        }
      });
    }

    return {
      type: 'document_analysis',
      analysisType,
      extractedText: analysisResult.extractedText,
      tablesCount: analysisResult.tables.length,
      keyValuePairsCount: analysisResult.keyValuePairs.length,
      entitiesCount: analysisResult.entities.length,
      confidence: analysisResult.confidence,
      processingTime: analysisResult.processingTime
    };
  } catch (error) {
    logger.error('Document analysis action failed', { error, documentId: document.id });
    throw error;
  }
}

async function extractTextFromDocumentAction(document: any): Promise<any> {
  const { enhancedDocumentIntelligence } = require('../shared/services/enhanced-document-intelligence');

  try {
    // Get document buffer from blob storage
    const { BlobServiceClient } = require('@azure/storage-blob');
    const blobServiceClient = new BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
    const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
    const blobClient = containerClient.getBlobClient(document.blobName);

    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error('Failed to download document content');
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of downloadResponse.readableStreamBody) {
      chunks.push(Buffer.from(chunk));
    }
    const documentBuffer = Buffer.concat(chunks);

    // Extract text using enhanced document intelligence
    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      document.id,
      'prebuilt-read'
    );

    return {
      type: 'text_extraction',
      extractedText: analysisResult.extractedText,
      wordCount: analysisResult.extractedText.split(/\s+/).length,
      confidence: analysisResult.confidence,
      processingTime: analysisResult.processingTime
    };
  } catch (error) {
    logger.error('Text extraction action failed', { error, documentId: document.id });
    throw error;
  }
}

async function generateThumbnailAction(document: any): Promise<any> {
  // This would require image processing capabilities
  // For now, return a placeholder response indicating the service needs implementation
  return {
    type: 'thumbnail_generation',
    thumbnailUrl: `thumbnails/${document.id}/thumb.jpg`,
    dimensions: { width: 200, height: 300 },
    note: 'Thumbnail generation requires additional image processing service implementation'
  };
}

async function classifyDocumentAction(document: any): Promise<any> {
  const { aiServices } = require('../shared/services/ai-services');

  try {
    // Get document text for classification
    let documentText = document.extractedText || '';

    if (!documentText) {
      // Extract text first if not available
      const textResult = await extractTextFromDocumentAction(document);
      documentText = textResult.extractedText;
    }

    if (!documentText || documentText.length < 50) {
      return {
        type: 'document_classification',
        category: 'unknown',
        subcategory: 'insufficient_content',
        confidence: 0.1,
        tags: ['unknown']
      };
    }

    // Use AI services for classification
    const classificationPrompt = `Classify this document and determine its category, subcategory, and relevant tags.

Document Content:
${documentText.substring(0, 2000)}...

Provide a JSON response with:
- category: string (main document category)
- subcategory: string (specific document type)
- confidence: number (0-1)
- tags: string[] (relevant tags)`;

    const classificationResult = await aiServices.reason(classificationPrompt, [], {
      systemPrompt: 'You are a document classification expert. Analyze documents and provide structured classification results.',
      temperature: 0.2,
      maxTokens: 500
    });

    try {
      const parsed = JSON.parse(classificationResult.content);
      return {
        type: 'document_classification',
        category: parsed.category || 'general',
        subcategory: parsed.subcategory || 'document',
        confidence: parsed.confidence || classificationResult.confidence,
        tags: parsed.tags || ['document'],
        processingTime: classificationResult.processingTime
      };
    } catch {
      return {
        type: 'document_classification',
        category: 'general',
        subcategory: 'document',
        confidence: classificationResult.confidence,
        tags: ['document'],
        reasoning: classificationResult.reasoning
      };
    }
  } catch (error) {
    logger.error('Document classification action failed', { error, documentId: document.id });
    throw error;
  }
}

// Metrics and utility functions
function updateMetrics(processingTime: number): void {
  metrics.averageProcessingTime =
    (metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
}

// Register Service Bus triggers
app.serviceBusQueue('workflowOrchestration', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'workflow-orchestration',
  handler: workflowOrchestrationHandler
});

app.serviceBusTopic('documentCollaboration', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'document-collaboration',
  subscriptionName: 'collaboration-processor',
  handler: documentCollaborationHandler
});

app.serviceBusTopic('analyticsAggregation', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'analytics-events',
  subscriptionName: 'analytics-aggregator',
  handler: analyticsAggregationHandler
});

app.serviceBusTopic('systemMonitoring', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  topicName: 'monitoring-events',
  subscriptionName: 'system-monitor',
  handler: systemMonitoringHandler
});

// Register missing Service Bus queue triggers
app.serviceBusQueue('aiOperations', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'ai-operations',
  handler: aiOperationsHandler
});

app.serviceBusQueue('scheduledEmails', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'scheduled-emails',
  handler: scheduledEmailsHandler
});

app.serviceBusQueue('documentProcessing', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'document-processing',
  handler: documentProcessingHandler
});

app.serviceBusQueue('notificationDelivery', {
  connection: 'SERVICE_BUS_CONNECTION_STRING',
  queueName: 'notification-delivery',
  handler: notificationDeliveryHandler
});

/**
 * Enhanced Service Bus utilities
 */

/**
 * Send message with enhanced features using shared serviceBusEnhanced service
 */
export async function sendEnhancedMessage(
  destination: string,
  message: any,
  options: {
    messageId?: string;
    sessionId?: string;
    timeToLive?: number;
    scheduledEnqueueTime?: Date;
    correlationId?: string;
    isQueue?: boolean;
  } = {}
): Promise<boolean> {
  const startTime = Date.now();

  try {
    // Initialize shared service
    await serviceBusEnhanced.initialize();

    // Check circuit breaker
    if (isCircuitBreakerOpen(destination)) {
      logger.warn('Circuit breaker is open for destination', { destination });
      return false;
    }

    // Check for duplicate message
    if (options.messageId && await isDuplicateMessage(options.messageId)) {
      logger.info('Duplicate message detected, skipping', { messageId: options.messageId });
      return true;
    }

    const serviceBusMessage = {
      body: message,
      messageId: options.messageId || generateMessageId(),
      sessionId: options.sessionId,
      timeToLive: options.timeToLive,
      scheduledEnqueueTime: options.scheduledEnqueueTime,
      correlationId: options.correlationId,
      applicationProperties: {
        originalTimestamp: new Date().toISOString(),
        source: 'service-bus-handlers'
      }
    };

    // Use shared service to send message
    const success = options.isQueue !== false
      ? await serviceBusEnhanced.sendToQueue(destination, serviceBusMessage)
      : await serviceBusEnhanced.sendToTopic(destination, serviceBusMessage);

    if (!success) {
      throw new Error('Failed to send message via shared service');
    }

    // Mark message as sent to prevent duplicates
    if (options.messageId) {
      await markMessageAsSent(options.messageId);
    }

    metrics.messagesSent++;
    updateProcessingTime(Date.now() - startTime);
    resetCircuitBreaker(destination);

    logger.info('Enhanced message sent successfully via shared service', {
      destination,
      messageId: serviceBusMessage.messageId,
      isQueue: options.isQueue !== false
    });

    await publishMetricsEvent('message_sent', {
      destination,
      messageId: serviceBusMessage.messageId
    });

    return true;
  } catch (error) {
    metrics.errors++;
    recordCircuitBreakerFailure(destination);

    logger.error('Error sending enhanced message via shared service', {
      destination,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Send batch of messages using shared serviceBusEnhanced service
 */
export async function sendBatchMessages(
  destination: string,
  messages: any[],
  isQueue: boolean = true
): Promise<boolean> {
  try {
    // Initialize shared service
    await serviceBusEnhanced.initialize();

    const serviceBusMessages = messages.map(msg => ({
      body: msg.body || msg,
      messageId: msg.messageId || generateMessageId(),
      sessionId: msg.sessionId,
      timeToLive: msg.timeToLive,
      correlationId: msg.correlationId,
      applicationProperties: {
        ...msg.applicationProperties,
        batchId: generateMessageId(),
        originalTimestamp: new Date().toISOString(),
        source: 'service-bus-handlers'
      }
    }));

    // Use shared service to send batch (send messages individually since batch method doesn't exist)
    let successCount = 0;
    for (const message of serviceBusMessages) {
      const success = isQueue
        ? await serviceBusEnhanced.sendToQueue(destination, message)
        : await serviceBusEnhanced.sendToTopic(destination, message);
      if (success) successCount++;
    }

    const allSuccessful = successCount === serviceBusMessages.length;

    if (!allSuccessful) {
      throw new Error(`Failed to send ${serviceBusMessages.length - successCount} out of ${serviceBusMessages.length} batch messages`);
    }

    metrics.messagesSent += messages.length;

    logger.info('Batch messages sent successfully via shared service', {
      destination,
      messageCount: messages.length,
      isQueue
    });

    await publishMetricsEvent('batch_sent', {
      destination,
      messageCount: messages.length,
      isQueue
    });

    return true;
  } catch (error) {
    metrics.errors++;
    logger.error('Error sending batch messages via shared service', {
      destination,
      messageCount: messages.length,
      error: error instanceof Error ? error.message : String(error)
    });
    return false;
  }
}

/**
 * Process dead letter messages using shared service
 * Note: This is a simplified implementation - the shared service handles dead letter processing internally
 */
export async function processDeadLetterQueue(
  queueOrTopicName: string,
  subscriptionName?: string
): Promise<void> {
  try {
    // Initialize shared service
    await serviceBusEnhanced.initialize();

    logger.info('Dead letter queue processing initiated', {
      queueOrTopicName,
      subscriptionName
    });

    // The shared serviceBusEnhanced service handles dead letter processing internally
    // This function is kept for compatibility but delegates to the shared service

    // Log that dead letter processing is handled by the shared service
    logger.info('Dead letter processing delegated to shared serviceBusEnhanced service', {
      queueOrTopicName,
      subscriptionName,
      note: 'Dead letter handling is built into the shared service'
    });

    metrics.messagesDeadLettered++;

  } catch (error) {
    logger.error('Error processing dead letter queue via shared service', {
      queueOrTopicName,
      subscriptionName,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Get service metrics
 */
export function getServiceBusMetrics(): ServiceBusMetrics {
  return { ...metrics };
}

/**
 * Helper functions for enhanced Service Bus features
 * Note: Legacy sender management removed - now using shared serviceBusEnhanced service
 */

async function isDuplicateMessage(messageId: string): Promise<boolean> {
  const key = `servicebus:sent:${messageId}`;
  return await redis.exists(key);
}

async function markMessageAsSent(messageId: string): Promise<void> {
  const key = `servicebus:sent:${messageId}`;
  await redis.setex(key, 3600, 'sent'); // Keep for 1 hour
}

function generateMessageId(): string {
  return `msg-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
}

function isCircuitBreakerOpen(destination: string): boolean {
  const breaker = circuitBreakers.get(destination);
  if (!breaker) {
    return false;
  }

  if (breaker.isOpen) {
    const now = new Date();
    if (now.getTime() - breaker.lastFailureTime.getTime() > breaker.timeout) {
      // Reset circuit breaker after timeout
      breaker.isOpen = false;
      breaker.failureCount = 0;
      logger.info('Circuit breaker reset', { destination });
    }
  }

  return breaker.isOpen;
}

function recordCircuitBreakerFailure(destination: string): void {
  let breaker = circuitBreakers.get(destination);
  if (!breaker) {
    breaker = {
      isOpen: false,
      failureCount: 0,
      lastFailureTime: new Date(),
      threshold: 5,
      timeout: 60000 // 1 minute
    };
    circuitBreakers.set(destination, breaker);
  }

  breaker.failureCount++;
  breaker.lastFailureTime = new Date();

  if (breaker.failureCount >= breaker.threshold) {
    breaker.isOpen = true;
    logger.warn('Circuit breaker opened', {
      destination,
      failureCount: breaker.failureCount
    });
  }
}

function resetCircuitBreaker(destination: string): void {
  const breaker = circuitBreakers.get(destination);
  if (breaker) {
    breaker.failureCount = 0;
    breaker.isOpen = false;
  }
}

function updateProcessingTime(processingTime: number): void {
  metrics.averageProcessingTime =
    (metrics.averageProcessingTime * 0.9) + (processingTime * 0.1);
}

async function storeDeadLetterMessage(message: any): Promise<void> {
  try {
    await db.createItem('dead-letter-messages', {
      id: `dl-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
      messageId: message.messageId,
      body: message.body,
      deadLetterReason: message.deadLetterReason,
      deadLetterErrorDescription: message.deadLetterErrorDescription,
      deliveryCount: message.deliveryCount,
      enqueuedTimeUtc: message.enqueuedTimeUtc,
      timestamp: new Date().toISOString()
    });
  } catch (error) {
    logger.debug('Failed to store dead letter message', {
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

async function publishMetricsEvent(eventType: string, data?: any): Promise<void> {
  try {
    await publishEvent(
      EventType.PERFORMANCE_ALERT,
      'servicebus/metrics',
      {
        service: 'servicebus',
        eventType,
        metrics,
        circuitBreakers: Array.from(circuitBreakers.entries()).map(([dest, state]) => ({
          destination: dest,
          isOpen: state.isOpen,
          failureCount: state.failureCount
        })),
        timestamp: new Date().toISOString(),
        ...data
      }
    );
  } catch (error) {
    // Don't log errors for metrics publishing to avoid recursion
  }
}

// Initialize periodic tasks
setInterval(async () => {
  // Process dead letter queues every 10 minutes
  const knownQueues = [
    'workflow-orchestration',
    'ai-operations',
    'scheduled-emails',
    'document-processing',
    'notification-delivery'
  ];
  const knownTopics = [
    { topic: 'analytics-events', subscription: 'analytics-aggregator' },
    { topic: 'document-collaboration', subscription: 'collaboration-processor' },
    { topic: 'monitoring-events', subscription: 'system-monitor' }
  ];

  // Process queue dead letter queues
  for (const queue of knownQueues) {
    await processDeadLetterQueue(queue);
  }

  // Process topic dead letter queues
  for (const { topic, subscription } of knownTopics) {
    await processDeadLetterQueue(topic, subscription);
  }
}, 10 * 60 * 1000);

// Publish metrics every minute
setInterval(async () => {
  await publishMetricsEvent('periodic_metrics');
}, 60 * 1000);

// Service Bus utilities now provided by shared serviceBusEnhanced service
// Legacy exports removed - use serviceBusEnhanced instead
