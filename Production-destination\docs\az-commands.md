# Service Bus Management

1. az servicebus namespace show --name hepzbackend --resource-group docucontext --query "{name:name,status:status,location:location,sku:sku}" --output table

Output:
Name         Status    Location
-----------  --------  ----------
hepzbackend  Active    eastus


2. az servicebus queue list --namespace-name hepzbackend --resource-group docucontext --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                    Status    MessageCount
----------------------  --------  --------------
ai-operations           Active    0
audit-log               Active    0
connection              Active    0
dead-letter-queue       Active    0
document-processing     Active    0
health-check            Active    29
monitoring-event        Active    0
notification-delivery   Active    0
notification-queue      Active    0
notifications           Active    0
scheduled-emails        Active    0
workflow-orchestration  Active    0


3. az servicebus topic list --namespace-name hepzbackend --resource-group docucontext --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                    Status
----------------------  --------
analytics-events        Active
blob-events             Active
document-collaboration  Active
monitoring-events       Active

4. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name analytics-events --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                  Status    MessageCount
--------------------  --------  --------------
analytics-aggregator  Active    0

5. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name document-collaboration --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name                     Status    MessageCount
-----------------------  --------  --------------
collaboration-processor  Active    0

6. az servicebus topic subscription list --namespace-name hepzbackend --resource-group docucontext --topic-name monitoring-events --query "[].{Name:name,Status:status,MessageCount:messageCount}" --output table

Name            Status    MessageCount
--------------  --------  --------------
system-monitor  Active    0