/**
 * Validation utilities for Azure Functions
 * Provides common validation functions and schemas
 */
import * as Jo<PERSON> from 'joi';
import { HttpRequest } from '@azure/functions';
export interface ValidationResult {
    isValid: boolean;
    error?: string;
    value?: any;
}
/**
 * Common validation schemas
 */
export declare const schemas: {
    email: Joi.StringSchema<string>;
    password: Joi.StringSchema<string>;
    uuid: Joi.StringSchema<string>;
    tenantId: Joi.StringSchema<string>;
    userId: Joi.StringSchema<string>;
    documentId: Joi.StringSchema<string>;
    workflowId: Joi.StringSchema<string>;
    templateId: Joi.StringSchema<string>;
    pagination: Joi.ObjectSchema<any>;
    dateRange: Joi.ObjectSchema<any>;
};
/**
 * Validate request body against schema
 */
export declare function validateBody(request: HttpRequest, schema: Joi.Schema): Promise<ValidationResult>;
/**
 * Validate query parameters against schema
 */
export declare function validateQuery(request: HttpRequest, schema: Joi.Schema): ValidationResult;
/**
 * Validate path parameters against schema
 */
export declare function validateParams(params: Record<string, string>, schema: Joi.Schema): ValidationResult;
/**
 * Create validation middleware
 */
export declare function withValidation(bodySchema?: Joi.Schema, querySchema?: Joi.Schema, paramsSchema?: Joi.Schema): (handler: (request: HttpRequest, context: any, validated: any) => Promise<any>) => (request: HttpRequest, context: any) => Promise<any>;
