{"version": 3, "file": "document-enhance.js", "sourceRoot": "", "sources": ["../../src/functions/document-enhance.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwDA,0CA4NC;AApRD;;;;GAIG;AACH,gDAAyF;AACzF,sDAAwD;AACxD,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,yBAAyB;AACzB,IAAK,eAOJ;AAPD,WAAK,eAAe;IAClB,8DAA2C,CAAA;IAC3C,0DAAuC,CAAA;IACvC,sDAAmC,CAAA;IACnC,4DAAyC,CAAA;IACzC,gDAA6B,CAAA;IAC7B,8DAA2C,CAAA;AAC7C,CAAC,EAPI,eAAe,KAAf,eAAe,QAOnB;AAED,oBAAoB;AACpB,MAAM,qBAAqB,GAAG,GAAG,CAAC,MAAM,CAAC;IACvC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,QAAQ,EAAE;IACjF,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC;QAClB,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;QACrC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAChD,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC7C,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QAC1C,WAAW,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACrC,cAAc,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;QACxC,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,OAAO,EAAE,WAAW,EAAE,YAAY,CAAC,CAAC,QAAQ,EAAE;KAC7E,CAAC,CAAC,QAAQ,EAAE;IACb,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC1C,CAAC,CAAC;AAcH;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,OAAoB,EAAE,OAA0B;IACpF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE/D,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,EAAE,UAAU,EAAE,eAAe,EAAE,OAAO,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAClF,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,iCAAiC;QACjC,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QAEF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QAEF,0CAA0C;QAC1C,MAAM,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAE,QAAgB,CAAC,QAAQ,CAAC,CAAC;QAClF,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAEtD,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YACzC,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,6BAA6B,EAAE;aACnD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAEjF,oCAAoC;QACpC,MAAM,iBAAiB,GAAG,MAAM,kBAAkB,CAChD,cAAc,EACd,eAAe,EACf,OAAO,IAAI,EAAE,EACZ,QAAgB,CAAC,WAAW,CAC9B,CAAC;QAEF,yCAAyC;QACzC,MAAM,kBAAkB,GAAG,IAAA,SAAM,GAAE,CAAC;QACpC,MAAM,gBAAgB,GAAG,GAAG,cAAc,IAAI,SAAS,IAAI,kBAAkB,aAAa,gBAAgB,CAAC,iBAAiB,CAAC,YAAY,IAAK,QAAgB,CAAC,WAAW,CAAC,EAAE,CAAC;QAC9K,MAAM,kBAAkB,GAAG,eAAe,CAAC,kBAAkB,CAAC,gBAAgB,CAAC,CAAC;QAEhF,MAAM,kBAAkB,CAAC,MAAM,CAAC,iBAAiB,CAAC,cAAc,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM,EAAE;YACzG,eAAe,EAAE,EAAE,eAAe,EAAE,iBAAiB,CAAC,YAAY,IAAK,QAAgB,CAAC,WAAW,EAAE;SACtG,CAAC,CAAC;QAEH,kCAAkC;QAClC,MAAM,gBAAgB,GAAG;YACvB,EAAE,EAAE,kBAAkB;YACtB,kBAAkB,EAAE,UAAU;YAC9B,IAAI,EAAE,GAAI,QAAgB,CAAC,IAAI,aAAa;YAC5C,WAAW,EAAE,uBAAwB,QAAgB,CAAC,IAAI,UAAU,eAAe,EAAE;YACrF,QAAQ,EAAE,gBAAgB;YAC1B,WAAW,EAAE,iBAAiB,CAAC,YAAY,IAAK,QAAgB,CAAC,WAAW;YAC5E,IAAI,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM;YAC7C,cAAc;YACd,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,UAAU;YAClB,QAAQ,EAAE;gBACR,eAAe;gBACf,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,YAAY,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM;gBACrD,YAAY,EAAE,iBAAiB,CAAC,YAAY;gBAC5C,YAAY,EAAE,iBAAiB,CAAC,YAAY;gBAC5C,UAAU,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACpC,UAAU,EAAE,IAAI,CAAC,EAAE;gBACnB,OAAO;aACR;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QAEnD,4BAA4B;QAC5B,MAAM,WAAW,GAAG;YAClB,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,UAAU;YACV,kBAAkB;YAClB,eAAe;YACf,OAAO;YACP,MAAM,EAAE;gBACN,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,YAAY,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM;gBACrD,YAAY,EAAE,iBAAiB,CAAC,YAAY;gBAC5C,YAAY,EAAE,iBAAiB,CAAC,YAAY;gBAC5C,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACvC;YACD,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,cAAc;YACd,SAAS;YACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,uBAAuB,EAAE,WAAW,CAAC,CAAC;QAE1D,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc;YACd,SAAS;YACT,UAAU;YACV,kBAAkB;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE;gBACP,eAAe;gBACf,YAAY,EAAE,cAAc,CAAC,MAAM;gBACnC,YAAY,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM;gBACrD,kBAAkB,EAAE,iBAAiB,CAAC,YAAY;gBAClD,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACvC;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAsB;YAClC,UAAU;YACV,kBAAkB;YAClB,eAAe;YACf,YAAY,EAAE,cAAc,CAAC,MAAM;YACnC,YAAY,EAAE,iBAAiB,CAAC,cAAc,CAAC,MAAM;YACrD,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YACtC,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,aAAa;YACb,UAAU;YACV,kBAAkB;YAClB,eAAe;YACf,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,QAAQ;SACnB,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAC/B,cAAsB,EACtB,eAAgC,EAChC,OAAY,EACZ,WAAmB;IAEnB,sCAAsC;IACtC,sEAAsE;IACtE,yCAAyC;IAEzC,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;QAC7C,eAAe;QACf,YAAY,EAAE,cAAc,CAAC,MAAM;QACnC,WAAW;KACZ,CAAC,CAAC;IAEH,MAAM,YAAY,GAAa,EAAE,CAAC;IAClC,IAAI,YAAY,GAAG,EAAE,CAAC,CAAC,qBAAqB;IAE5C,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,eAAe,CAAC,mBAAmB;YACtC,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC/C,YAAY,CAAC,IAAI,CAAC,kCAAkC,CAAC,CAAC;YACtD,YAAY,GAAG,EAAE,CAAC;YAClB,MAAM;QAER,KAAK,eAAe,CAAC,eAAe;YAClC,YAAY,CAAC,IAAI,CAAC,oCAAoC,CAAC,CAAC;YACxD,YAAY,CAAC,IAAI,CAAC,4BAA4B,CAAC,CAAC;YAChD,YAAY,GAAG,EAAE,CAAC;YAClB,MAAM;QAER,KAAK,eAAe,CAAC,kBAAkB;YACrC,YAAY,CAAC,IAAI,CAAC,qBAAqB,CAAC,CAAC;YACzC,YAAY,CAAC,IAAI,CAAC,iCAAiC,CAAC,CAAC;YACrD,YAAY,GAAG,EAAE,CAAC;YAClB,MAAM;QAER,KAAK,eAAe,CAAC,YAAY;YAC/B,YAAY,CAAC,IAAI,CAAC,6BAA6B,CAAC,CAAC;YACjD,YAAY,CAAC,IAAI,CAAC,2BAA2B,CAAC,CAAC;YAC/C,YAAY,GAAG,EAAE,CAAC;YAClB,MAAM;QAER;YACE,YAAY,CAAC,IAAI,CAAC,sCAAsC,CAAC,CAAC;YAC1D,MAAM;IACV,CAAC;IAED,kCAAkC;IAClC,0DAA0D;IAC1D,MAAM,cAAc,GAAG,cAAc,CAAC,CAAC,2BAA2B;IAElE,OAAO;QACL,cAAc;QACd,YAAY,EAAE,OAAO,CAAC,YAAY,IAAI,WAAW;QACjD,YAAY;QACZ,YAAY;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,WAAmB;IAC3C,MAAM,UAAU,GAA8B;QAC5C,iBAAiB,EAAE,KAAK;QACxB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,MAAM;QACpB,oBAAoB,EAAE,KAAK;QAC3B,yEAAyE,EAAE,MAAM;KAClF,CAAC;IAEF,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc,CAAC,cAAqC;IACjE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,wBAAwB;IAC/B,OAAO,EAAE,eAAe;CACzB,CAAC,CAAC"}