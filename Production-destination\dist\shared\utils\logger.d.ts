/**
 * Centralized logging utility for Azure Functions
 * Provides structured logging with different levels and correlation tracking
 */
export interface LogContext {
    correlationId?: string;
    userId?: string;
    tenantId?: string;
    functionName?: string;
    [key: string]: any;
}
export declare class Logger {
    private static instance;
    private logLevel;
    private constructor();
    static getInstance(): Logger;
    private shouldLog;
    private formatMessage;
    error(message: string, context?: LogContext): void;
    warn(message: string, context?: LogContext): void;
    info(message: string, context?: LogContext): void;
    debug(message: string, context?: LogContext): void;
}
export declare const logger: Logger;
export default logger;
