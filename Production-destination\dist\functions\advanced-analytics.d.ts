/**
 * Advanced Analytics Function
 * Handles comprehensive analytics, reporting, and business intelligence
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get analytics handler
 */
export declare function getAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get dashboard data handler
 */
export declare function getDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
