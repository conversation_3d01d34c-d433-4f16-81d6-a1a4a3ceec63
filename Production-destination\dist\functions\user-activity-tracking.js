"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.trackUserActivity = trackUserActivity;
exports.getActivityAnalytics = getActivityAnalytics;
/**
 * User Activity Tracking Function
 * Handles user activity tracking and analytics
 * Migrated from old-arch/src/user-service/activity/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Activity types and enums
var ActivityType;
(function (ActivityType) {
    ActivityType["LOGIN"] = "LOGIN";
    ActivityType["LOGOUT"] = "LOGOUT";
    ActivityType["DOCUMENT_VIEW"] = "DOCUMENT_VIEW";
    ActivityType["DOCUMENT_EDIT"] = "DOCUMENT_EDIT";
    ActivityType["DOCUMENT_DOWNLOAD"] = "DOCUMENT_DOWNLOAD";
    ActivityType["DOCUMENT_SHARE"] = "DOCUMENT_SHARE";
    ActivityType["WORKFLOW_START"] = "WORKFLOW_START";
    ActivityType["WORKFLOW_COMPLETE"] = "WORKFLOW_COMPLETE";
    ActivityType["SEARCH_PERFORMED"] = "SEARCH_PERFORMED";
    ActivityType["COMMENT_ADDED"] = "COMMENT_ADDED";
    ActivityType["COLLABORATION_JOIN"] = "COLLABORATION_JOIN";
    ActivityType["REPORT_GENERATED"] = "REPORT_GENERATED";
    ActivityType["SETTINGS_CHANGED"] = "SETTINGS_CHANGED";
    ActivityType["API_CALL"] = "API_CALL";
})(ActivityType || (ActivityType = {}));
var ActivityCategory;
(function (ActivityCategory) {
    ActivityCategory["AUTHENTICATION"] = "AUTHENTICATION";
    ActivityCategory["DOCUMENT"] = "DOCUMENT";
    ActivityCategory["WORKFLOW"] = "WORKFLOW";
    ActivityCategory["COLLABORATION"] = "COLLABORATION";
    ActivityCategory["SEARCH"] = "SEARCH";
    ActivityCategory["REPORTING"] = "REPORTING";
    ActivityCategory["ADMINISTRATION"] = "ADMINISTRATION";
    ActivityCategory["API"] = "API";
})(ActivityCategory || (ActivityCategory = {}));
// Validation schemas
const trackActivitySchema = Joi.object({
    type: Joi.string().valid(...Object.values(ActivityType)).required(),
    category: Joi.string().valid(...Object.values(ActivityCategory)).required(),
    description: Joi.string().max(500).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    resourceType: Joi.string().max(50).optional(),
    resourceId: Joi.string().max(100).optional(),
    metadata: Joi.object({
        duration: Joi.number().min(0).optional(),
        fileSize: Joi.number().min(0).optional(),
        searchQuery: Joi.string().max(200).optional(),
        resultCount: Joi.number().min(0).optional(),
        ipAddress: Joi.string().ip().optional(),
        userAgent: Joi.string().max(500).optional(),
        location: Joi.object({
            country: Joi.string().max(50).optional(),
            region: Joi.string().max(50).optional(),
            city: Joi.string().max(50).optional()
        }).optional(),
        device: Joi.object({
            type: Joi.string().valid('desktop', 'mobile', 'tablet').optional(),
            os: Joi.string().max(50).optional(),
            browser: Joi.string().max(50).optional()
        }).optional()
    }).optional()
});
const getActivityAnalyticsSchema = Joi.object({
    userId: Joi.string().uuid().optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    activityTypes: Joi.array().items(Joi.string().valid(...Object.values(ActivityType))).optional(),
    categories: Joi.array().items(Joi.string().valid(...Object.values(ActivityCategory))).optional(),
    dateRange: Joi.object({
        startDate: Joi.string().isoDate().required(),
        endDate: Joi.string().isoDate().required()
    }).optional(),
    groupBy: Joi.string().valid('hour', 'day', 'week', 'month', 'type', 'category', 'user').default('day'),
    limit: Joi.number().min(1).max(1000).default(100)
});
/**
 * Track user activity handler
 */
async function trackUserActivity(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Track user activity started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = trackActivitySchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const activityRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(activityRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Extract additional metadata from request
        const requestMetadata = {
            ipAddress: request.headers.get('x-forwarded-for') || request.headers.get('x-real-ip') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            sessionId: request.headers.get('x-session-id'),
            ...activityRequest.metadata
        };
        // Create activity record
        const activityId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const activity = {
            id: activityId,
            userId: user.id,
            type: activityRequest.type,
            category: activityRequest.category,
            description: activityRequest.description,
            organizationId: activityRequest.organizationId,
            projectId: activityRequest.projectId,
            resourceType: activityRequest.resourceType,
            resourceId: activityRequest.resourceId,
            metadata: requestMetadata,
            timestamp: now,
            sessionId: requestMetadata.sessionId || undefined,
            tenantId: user.tenantId || user.id
        };
        // Store activity in database
        await database_1.db.createItem('user-activities', activity);
        // Update real-time activity cache
        await updateActivityCache(user.id, activity);
        // Update activity statistics
        await updateActivityStatistics(activityRequest.organizationId, user.id, activityRequest.type, activityRequest.category);
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'UserActivityTracked',
            aggregateId: user.id,
            aggregateType: 'User',
            version: 1,
            data: {
                activity,
                trackedBy: user.id
            },
            userId: user.id,
            organizationId: activityRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("User activity tracked successfully", {
            correlationId,
            activityId,
            userId: user.id,
            activityType: activityRequest.type,
            category: activityRequest.category
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                activityId,
                type: activityRequest.type,
                category: activityRequest.category,
                timestamp: now,
                message: "Activity tracked successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Track user activity failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get activity analytics handler
 */
async function getActivityAnalytics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get activity analytics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            userId: url.searchParams.get('userId') || undefined,
            organizationId: url.searchParams.get('organizationId') || '',
            projectId: url.searchParams.get('projectId') || undefined,
            activityTypes: url.searchParams.get('activityTypes')?.split(',') || undefined,
            categories: url.searchParams.get('categories')?.split(',') || undefined,
            dateRange: url.searchParams.get('startDate') && url.searchParams.get('endDate') ? {
                startDate: url.searchParams.get('startDate'),
                endDate: url.searchParams.get('endDate')
            } : undefined,
            groupBy: url.searchParams.get('groupBy') || 'day',
            limit: parseInt(url.searchParams.get('limit') || '100')
        };
        // Validate query parameters
        const { error, value } = getActivityAnalyticsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const analyticsRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(analyticsRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check if user can view other users' activities
        const canViewOthers = await checkAnalyticsAccess(user, analyticsRequest.organizationId);
        if (analyticsRequest.userId && analyticsRequest.userId !== user.id && !canViewOthers) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to view other users' activities" }
            }, request);
        }
        // Get activity analytics
        const analytics = await getActivityAnalyticsData(analyticsRequest, user);
        logger_1.logger.info("Activity analytics retrieved successfully", {
            correlationId,
            organizationId: analyticsRequest.organizationId,
            userId: analyticsRequest.userId || user.id,
            groupBy: analyticsRequest.groupBy,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                analytics,
                filters: {
                    userId: analyticsRequest.userId,
                    organizationId: analyticsRequest.organizationId,
                    projectId: analyticsRequest.projectId,
                    activityTypes: analyticsRequest.activityTypes,
                    categories: analyticsRequest.categories,
                    dateRange: analyticsRequest.dateRange,
                    groupBy: analyticsRequest.groupBy
                },
                generatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get activity analytics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkAnalyticsAccess(user, organizationId) {
    try {
        // Check if user has admin role or analytics access
        if (user.roles?.includes('admin') || user.roles?.includes('analytics')) {
            return true;
        }
        // Check organization-level permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN';
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check analytics access', { error, userId: user.id, organizationId });
        return false;
    }
}
async function updateActivityCache(userId, activity) {
    try {
        // Store recent activities in Redis for real-time access
        const cacheKey = `user:${userId}:recent_activities`;
        const activityData = JSON.stringify({
            id: activity.id,
            type: activity.type,
            category: activity.category,
            description: activity.description,
            timestamp: activity.timestamp,
            resourceType: activity.resourceType,
            resourceId: activity.resourceId
        });
        // Add to list and keep only last 50 activities
        await redis_1.redis.lpush(cacheKey, activityData);
        await redis_1.redis.ltrim(cacheKey, 0, 49);
        await redis_1.redis.expire(cacheKey, 86400); // 24 hours
        // Update user session activity
        if (activity.sessionId) {
            const sessionKey = `session:${activity.sessionId}:activities`;
            await redis_1.redis.lpush(sessionKey, activityData);
            await redis_1.redis.expire(sessionKey, 3600); // 1 hour
        }
        logger_1.logger.debug('Activity cached successfully', { userId, activityId: activity.id, type: activity.type });
    }
    catch (error) {
        logger_1.logger.error('Failed to update activity cache', { error, userId, activityId: activity.id });
        // Don't throw error - activity is already stored in database
    }
}
async function updateActivityStatistics(organizationId, userId, activityType, category) {
    try {
        const today = new Date().toISOString().split('T')[0];
        // Update daily statistics
        const dailyStatsKey = `stats:${organizationId}:${today}`;
        await redis_1.redis.hincrby(dailyStatsKey, `total_activities`, 1);
        await redis_1.redis.hincrby(dailyStatsKey, `activity_${activityType}`, 1);
        await redis_1.redis.hincrby(dailyStatsKey, `category_${category}`, 1);
        await redis_1.redis.hincrby(dailyStatsKey, `user_${userId}`, 1);
        await redis_1.redis.expire(dailyStatsKey, 86400 * 7); // 7 days
        // Update user statistics
        const userStatsKey = `stats:user:${userId}:${today}`;
        await redis_1.redis.hincrby(userStatsKey, `total_activities`, 1);
        await redis_1.redis.hincrby(userStatsKey, `activity_${activityType}`, 1);
        await redis_1.redis.hincrby(userStatsKey, `category_${category}`, 1);
        await redis_1.redis.expire(userStatsKey, 86400 * 30); // 30 days
    }
    catch (error) {
        logger_1.logger.error('Failed to update activity statistics', { error, organizationId, userId, activityType });
    }
}
async function getActivityAnalyticsData(request, user) {
    try {
        // Build query
        let query = 'SELECT * FROM c WHERE c.organizationId = @orgId';
        const parameters = [request.organizationId];
        if (request.userId) {
            query += ' AND c.userId = @userId';
            parameters.push(request.userId);
        }
        if (request.projectId) {
            query += ' AND c.projectId = @projectId';
            parameters.push(request.projectId);
        }
        if (request.activityTypes && request.activityTypes.length > 0) {
            query += ' AND c.type IN (@types)';
            parameters.push(request.activityTypes);
        }
        if (request.categories && request.categories.length > 0) {
            query += ' AND c.category IN (@categories)';
            parameters.push(request.categories);
        }
        if (request.dateRange) {
            query += ' AND c.timestamp >= @startDate AND c.timestamp <= @endDate';
            parameters.push(request.dateRange.startDate, request.dateRange.endDate);
        }
        query += ' ORDER BY c.timestamp DESC';
        // Execute query
        const activities = await database_1.db.queryItems('user-activities', query, parameters);
        // Group and aggregate data
        const analytics = {
            totalActivities: activities.length,
            activityBreakdown: groupActivitiesByType(activities),
            categoryBreakdown: groupActivitiesByCategory(activities),
            timelineData: groupActivitiesByTime(activities, request.groupBy),
            topUsers: getTopActiveUsers(activities),
            recentActivities: activities.slice(0, request.limit)
        };
        return analytics;
    }
    catch (error) {
        logger_1.logger.error('Failed to get activity analytics data', { error, request });
        return {
            totalActivities: 0,
            activityBreakdown: {},
            categoryBreakdown: {},
            timelineData: [],
            topUsers: [],
            recentActivities: []
        };
    }
}
function groupActivitiesByType(activities) {
    const breakdown = {};
    activities.forEach(activity => {
        breakdown[activity.type] = (breakdown[activity.type] || 0) + 1;
    });
    return breakdown;
}
function groupActivitiesByCategory(activities) {
    const breakdown = {};
    activities.forEach(activity => {
        breakdown[activity.category] = (breakdown[activity.category] || 0) + 1;
    });
    return breakdown;
}
function groupActivitiesByTime(activities, groupBy) {
    const groups = {};
    activities.forEach(activity => {
        const date = new Date(activity.timestamp);
        let key;
        switch (groupBy) {
            case 'hour':
                key = date.toISOString().substring(0, 13) + ':00:00.000Z';
                break;
            case 'day':
                key = date.toISOString().substring(0, 10);
                break;
            case 'week':
                const weekStart = new Date(date);
                weekStart.setDate(date.getDate() - date.getDay());
                key = weekStart.toISOString().substring(0, 10);
                break;
            case 'month':
                key = date.toISOString().substring(0, 7);
                break;
            default:
                key = date.toISOString().substring(0, 10);
        }
        groups[key] = (groups[key] || 0) + 1;
    });
    return Object.entries(groups).map(([date, count]) => ({ date, count })).sort((a, b) => a.date.localeCompare(b.date));
}
function getTopActiveUsers(activities) {
    const userCounts = {};
    activities.forEach(activity => {
        userCounts[activity.userId] = (userCounts[activity.userId] || 0) + 1;
    });
    return Object.entries(userCounts)
        .map(([userId, count]) => ({ userId, activityCount: count }))
        .sort((a, b) => b.activityCount - a.activityCount)
        .slice(0, 10);
}
// Register functions
functions_1.app.http('user-activity-track', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/activity/track',
    handler: trackUserActivity
});
functions_1.app.http('user-activity-analytics', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/activity/analytics',
    handler: getActivityAnalytics
});
//# sourceMappingURL=user-activity-tracking.js.map