/**
 * Real-Time Collaboration Function
 * Handles real-time document collaboration and editing
 * Migrated from old-arch/src/collaboration-service/session/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create collaboration session handler
 */
export declare function createCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Join collaboration session handler
 */
export declare function joinCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
