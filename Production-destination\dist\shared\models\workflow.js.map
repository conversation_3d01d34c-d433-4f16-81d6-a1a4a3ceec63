{"version": 3, "file": "workflow.js", "sourceRoot": "", "sources": ["../../../src/shared/models/workflow.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AA0BH,IAAY,cASX;AATD,WAAY,cAAc;IACxB,iCAAe,CAAA;IACf,mCAAiB,CAAA;IACjB,qCAAmB,CAAA;IACnB,mCAAiB,CAAA;IACjB,yCAAuB,CAAA;IACvB,yCAAuB,CAAA;IACvB,iCAAe,CAAA;IACf,uCAAqB,CAAA;AACvB,CAAC,EATW,cAAc,8BAAd,cAAc,QASzB;AAED,IAAY,gBAKX;AALD,WAAY,gBAAgB;IAC1B,+BAAW,CAAA;IACX,qCAAiB,CAAA;IACjB,iCAAa,CAAA;IACb,qCAAiB,CAAA;AACnB,CAAC,EALW,gBAAgB,gCAAhB,gBAAgB,QAK3B;AAkCD,IAAY,gBAcX;AAdD,WAAY,gBAAgB;IAC1B,qCAAiB,CAAA;IACjB,2CAAuB,CAAA;IACvB,yCAAqB,CAAA;IACrB,qCAAiB,CAAA;IACjB,iDAA6B,CAAA;IAC7B,2CAAuB,CAAA;IACvB,yCAAqB,CAAA;IACrB,6CAAyB,CAAA;IACzB,iCAAa,CAAA;IACb,uCAAmB,CAAA;IACnB,yCAAqB,CAAA;IACrB,+DAA2C,CAAA;IAC3C,+CAA2B,CAAA;AAC7B,CAAC,EAdW,gBAAgB,gCAAhB,gBAAgB,QAc3B;AAED,IAAY,kBAQX;AARD,WAAY,kBAAkB;IAC5B,yCAAmB,CAAA;IACnB,yCAAmB,CAAA;IACnB,6CAAuB,CAAA;IACvB,uCAAiB,CAAA;IACjB,yCAAmB,CAAA;IACnB,6CAAuB,CAAA;IACvB,mEAA6C,CAAA;AAC/C,CAAC,EARW,kBAAkB,kCAAlB,kBAAkB,QAQ7B;AAgBD,IAAY,mBASX;AATD,WAAY,mBAAmB;IAC7B,wCAAiB,CAAA;IACjB,8CAAuB,CAAA;IACvB,sCAAe,CAAA;IACf,0CAAmB,CAAA;IACnB,0DAAmC,CAAA;IACnC,gEAAyC,CAAA;IACzC,kDAA2B,CAAA;IAC3B,4CAAqB,CAAA;AACvB,CAAC,EATW,mBAAmB,mCAAnB,mBAAmB,QAS9B;AASD,IAAY,iBAeX;AAfD,WAAY,iBAAiB;IAC3B,sCAAiB,CAAA;IACjB,8CAAyB,CAAA;IACzB,kDAA6B,CAAA;IAC7B,4CAAuB,CAAA;IACvB,oEAA+C,CAAA;IAC/C,8DAAyC,CAAA;IACzC,0CAAqB,CAAA;IACrB,kDAA6B,CAAA;IAC7B,gDAA2B,CAAA;IAC3B,4CAAuB,CAAA;IACvB,8BAAS,CAAA;IACT,sCAAiB,CAAA;IACjB,wCAAmB,CAAA;IACnB,gDAA2B,CAAA;AAC7B,CAAC,EAfW,iBAAiB,iCAAjB,iBAAiB,QAe5B"}