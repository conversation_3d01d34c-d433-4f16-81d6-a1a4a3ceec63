/**
 * Production Cache Manager
 * Implements production-standard cache invalidation without relying on Redis pattern operations
 * Uses application-level tracking for better performance and reliability
 */

import { redis } from './redis';
import { logger } from '../utils/logger';

export interface CacheKey {
  key: string;
  tags: string[];
  ttl?: number;
  createdAt: Date;
}

export interface CacheInvalidationRule {
  pattern: string;
  tags: string[];
  relatedKeys: string[];
}

export class ProductionCacheManager {
  private static instance: ProductionCacheManager;
  private keyRegistry: Map<string, CacheKey> = new Map();
  private tagToKeys: Map<string, Set<string>> = new Map();
  private invalidationRules: Map<string, CacheInvalidationRule> = new Map();

  private constructor() {
    this.setupDefaultRules();
  }

  public static getInstance(): ProductionCacheManager {
    if (!ProductionCacheManager.instance) {
      ProductionCacheManager.instance = new ProductionCacheManager();
    }
    return ProductionCacheManager.instance;
  }

  /**
   * Setup default cache invalidation rules
   */
  private setupDefaultRules(): void {
    // Document-related cache invalidation
    this.addInvalidationRule('document-update', {
      pattern: 'document:*',
      tags: ['document', 'content', 'collaboration'],
      relatedKeys: ['session:*', 'bi_report:*']
    });

    // User-related cache invalidation
    this.addInvalidationRule('user-update', {
      pattern: 'user:*',
      tags: ['user', 'activity', 'session'],
      relatedKeys: ['device:*', 'user_advanced_roles:*']
    });

    // Session-related cache invalidation
    this.addInvalidationRule('session-update', {
      pattern: 'session:*',
      tags: ['session', 'collaboration'],
      relatedKeys: ['document:*:content']
    });

    // Configuration cache invalidation
    this.addInvalidationRule('config-update', {
      pattern: 'config:*',
      tags: ['configuration', 'system'],
      relatedKeys: ['feature_flag:*']
    });
  }

  /**
   * Add cache invalidation rule
   */
  public addInvalidationRule(ruleId: string, rule: CacheInvalidationRule): void {
    this.invalidationRules.set(ruleId, rule);
    logger.debug('Cache invalidation rule added', { ruleId, rule });
  }

  /**
   * Register a cache key with tags for tracking
   */
  public async registerKey(key: string, tags: string[], ttl?: number): Promise<void> {
    const cacheKey: CacheKey = {
      key,
      tags,
      ttl,
      createdAt: new Date()
    };

    this.keyRegistry.set(key, cacheKey);

    // Update tag-to-keys mapping
    for (const tag of tags) {
      if (!this.tagToKeys.has(tag)) {
        this.tagToKeys.set(tag, new Set());
      }
      this.tagToKeys.get(tag)!.add(key);
    }

    logger.debug('Cache key registered', { key, tags, ttl });
  }

  /**
   * Set cache value with automatic registration
   */
  public async set(key: string, value: any, ttl: number = 3600, tags: string[] = []): Promise<boolean> {
    try {
      // Register the key for tracking
      await this.registerKey(key, tags, ttl);

      // Set the value in Redis
      const success = await redis.setJson(key, value, ttl);
      
      if (success) {
        logger.debug('Cache value set and registered', { key, tags, ttl });
      }
      
      return success;
    } catch (error) {
      logger.error('Failed to set cache value', {
        error: error instanceof Error ? error.message : String(error),
        key
      });
      return false;
    }
  }

  /**
   * Get cache value
   */
  public async get<T>(key: string): Promise<T | null> {
    try {
      return await redis.getJson<T>(key);
    } catch (error) {
      logger.error('Failed to get cache value', {
        error: error instanceof Error ? error.message : String(error),
        key
      });
      return null;
    }
  }

  /**
   * Delete cache key and unregister
   */
  public async delete(key: string): Promise<boolean> {
    try {
      // Remove from Redis
      const success = await redis.delete(key);
      
      // Unregister from tracking
      this.unregisterKey(key);
      
      return success;
    } catch (error) {
      logger.error('Failed to delete cache key', {
        error: error instanceof Error ? error.message : String(error),
        key
      });
      return false;
    }
  }

  /**
   * Invalidate cache by tags (production-safe approach)
   */
  public async invalidateByTags(tags: string[]): Promise<void> {
    try {
      const keysToDelete = new Set<string>();

      // Find all keys associated with the given tags
      for (const tag of tags) {
        const tagKeys = this.tagToKeys.get(tag);
        if (tagKeys) {
          tagKeys.forEach(key => keysToDelete.add(key));
        }
      }

      // Delete the keys
      const deletionPromises = Array.from(keysToDelete).map(key => this.delete(key));
      await Promise.all(deletionPromises);

      logger.info('Cache invalidated by tags', { 
        tags, 
        keysDeleted: keysToDelete.size 
      });
    } catch (error) {
      logger.error('Failed to invalidate cache by tags', {
        error: error instanceof Error ? error.message : String(error),
        tags
      });
    }
  }

  /**
   * Invalidate cache by rule ID
   */
  public async invalidateByRule(ruleId: string, context: Record<string, any> = {}): Promise<void> {
    try {
      const rule = this.invalidationRules.get(ruleId);
      if (!rule) {
        logger.warn('Cache invalidation rule not found', { ruleId });
        return;
      }

      // Invalidate by tags
      await this.invalidateByTags(rule.tags);

      // Handle related keys with context substitution
      for (const relatedPattern of rule.relatedKeys) {
        await this.invalidateRelatedKeys(relatedPattern, context);
      }

      logger.info('Cache invalidated by rule', { ruleId, context });
    } catch (error) {
      logger.error('Failed to invalidate cache by rule', {
        error: error instanceof Error ? error.message : String(error),
        ruleId,
        context
      });
    }
  }

  /**
   * Invalidate related keys with context substitution
   */
  private async invalidateRelatedKeys(pattern: string, context: Record<string, any>): Promise<void> {
    try {
      // Find keys that match the pattern with context substitution
      const keysToDelete: string[] = [];

      for (const [key, cacheKey] of this.keyRegistry) {
        if (this.matchesPattern(key, pattern, context)) {
          keysToDelete.push(key);
        }
      }

      // Delete the matching keys
      const deletionPromises = keysToDelete.map(key => this.delete(key));
      await Promise.all(deletionPromises);

      logger.debug('Related keys invalidated', { 
        pattern, 
        context, 
        keysDeleted: keysToDelete.length 
      });
    } catch (error) {
      logger.error('Failed to invalidate related keys', {
        error: error instanceof Error ? error.message : String(error),
        pattern,
        context
      });
    }
  }

  /**
   * Check if a key matches a pattern with context substitution
   */
  private matchesPattern(key: string, pattern: string, context: Record<string, any>): boolean {
    try {
      // Simple pattern matching with context substitution
      let processedPattern = pattern;

      // Replace context variables in pattern
      for (const [contextKey, contextValue] of Object.entries(context)) {
        processedPattern = processedPattern.replace(`{${contextKey}}`, String(contextValue));
      }

      // Convert pattern to regex
      const regexPattern = processedPattern
        .replace(/\*/g, '.*')
        .replace(/\?/g, '.');

      const regex = new RegExp(`^${regexPattern}$`);
      return regex.test(key);
    } catch (error) {
      logger.error('Pattern matching failed', { key, pattern, context, error });
      return false;
    }
  }

  /**
   * Unregister a key from tracking
   */
  private unregisterKey(key: string): void {
    const cacheKey = this.keyRegistry.get(key);
    if (cacheKey) {
      // Remove from tag mappings
      for (const tag of cacheKey.tags) {
        const tagKeys = this.tagToKeys.get(tag);
        if (tagKeys) {
          tagKeys.delete(key);
          if (tagKeys.size === 0) {
            this.tagToKeys.delete(tag);
          }
        }
      }

      // Remove from registry
      this.keyRegistry.delete(key);
    }
  }

  /**
   * Clean up expired keys from registry
   */
  public async cleanupExpiredKeys(): Promise<void> {
    try {
      const now = new Date();
      const expiredKeys: string[] = [];

      for (const [key, cacheKey] of this.keyRegistry) {
        if (cacheKey.ttl) {
          const expiryTime = new Date(cacheKey.createdAt.getTime() + (cacheKey.ttl * 1000));
          if (now > expiryTime) {
            expiredKeys.push(key);
          }
        }
      }

      // Remove expired keys from registry
      for (const key of expiredKeys) {
        this.unregisterKey(key);
      }

      logger.debug('Expired keys cleaned up', { count: expiredKeys.length });
    } catch (error) {
      logger.error('Failed to cleanup expired keys', {
        error: error instanceof Error ? error.message : String(error)
      });
    }
  }

  /**
   * Get cache statistics
   */
  public getStatistics(): {
    totalKeys: number;
    totalTags: number;
    totalRules: number;
    keysByTag: Record<string, number>;
  } {
    const keysByTag: Record<string, number> = {};
    
    for (const [tag, keys] of this.tagToKeys) {
      keysByTag[tag] = keys.size;
    }

    return {
      totalKeys: this.keyRegistry.size,
      totalTags: this.tagToKeys.size,
      totalRules: this.invalidationRules.size,
      keysByTag
    };
  }
}

// Export singleton instance
export const productionCacheManager = ProductionCacheManager.getInstance();
export default productionCacheManager;
