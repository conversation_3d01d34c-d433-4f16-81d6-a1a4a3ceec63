"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generatePrediction = generatePrediction;
exports.trainModel = trainModel;
/**
 * Predictive Analytics Function
 * Handles predictive analytics and machine learning insights
 * Migrated from old-arch/src/analytics-service/predictive/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Predictive analytics types and enums
var PredictionType;
(function (PredictionType) {
    PredictionType["USER_BEHAVIOR"] = "USER_BEHAVIOR";
    PredictionType["DOCUMENT_USAGE"] = "DOCUMENT_USAGE";
    PredictionType["WORKFLOW_PERFORMANCE"] = "WORKFLOW_PERFORMANCE";
    PredictionType["SYSTEM_LOAD"] = "SYSTEM_LOAD";
    PredictionType["CHURN_PREDICTION"] = "CHURN_PREDICTION";
    PredictionType["GROWTH_FORECAST"] = "GROWTH_FORECAST";
    PredictionType["ANOMALY_DETECTION"] = "ANOMALY_DETECTION";
})(PredictionType || (PredictionType = {}));
var ModelStatus;
(function (ModelStatus) {
    ModelStatus["TRAINING"] = "TRAINING";
    ModelStatus["READY"] = "READY";
    ModelStatus["UPDATING"] = "UPDATING";
    ModelStatus["FAILED"] = "FAILED";
    ModelStatus["DEPRECATED"] = "DEPRECATED";
})(ModelStatus || (ModelStatus = {}));
var ConfidenceLevel;
(function (ConfidenceLevel) {
    ConfidenceLevel["LOW"] = "LOW";
    ConfidenceLevel["MEDIUM"] = "MEDIUM";
    ConfidenceLevel["HIGH"] = "HIGH";
    ConfidenceLevel["VERY_HIGH"] = "VERY_HIGH";
})(ConfidenceLevel || (ConfidenceLevel = {}));
// Validation schemas
const generatePredictionSchema = Joi.object({
    predictionType: Joi.string().valid(...Object.values(PredictionType)).required(),
    organizationId: Joi.string().uuid().required(),
    timeHorizon: Joi.number().min(1).max(365).default(30), // days
    parameters: Joi.object({
        userId: Joi.string().uuid().optional(),
        documentId: Joi.string().uuid().optional(),
        workflowId: Joi.string().uuid().optional(),
        features: Joi.object().optional(),
        filters: Joi.object().optional()
    }).optional(),
    options: Joi.object({
        includeConfidenceInterval: Joi.boolean().default(true),
        includeFeatureImportance: Joi.boolean().default(false),
        modelVersion: Joi.string().optional()
    }).optional()
});
const trainModelSchema = Joi.object({
    modelName: Joi.string().min(2).max(100).required(),
    predictionType: Joi.string().valid(...Object.values(PredictionType)).required(),
    organizationId: Joi.string().uuid().required(),
    trainingData: Joi.object({
        startDate: Joi.string().isoDate().required(),
        endDate: Joi.string().isoDate().required(),
        features: Joi.array().items(Joi.string()).min(1).required(),
        target: Joi.string().required()
    }).required(),
    hyperparameters: Joi.object({
        algorithm: Joi.string().valid('linear_regression', 'random_forest', 'neural_network', 'time_series').default('random_forest'),
        maxDepth: Joi.number().min(1).max(20).optional(),
        nEstimators: Joi.number().min(10).max(1000).optional(),
        learningRate: Joi.number().min(0.001).max(1).optional()
    }).optional()
});
/**
 * Generate prediction handler
 */
async function generatePrediction(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Generate prediction started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = generatePredictionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const predictionRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(predictionRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check analytics permissions
        const hasAnalyticsAccess = await checkAnalyticsAccess(user, predictionRequest.organizationId);
        if (!hasAnalyticsAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to predictive analytics" }
            }, request);
        }
        // Get or create model
        const model = await getOrCreateModel(predictionRequest.predictionType, predictionRequest.organizationId);
        if (!model || model.status !== ModelStatus.READY) {
            return (0, cors_1.addCorsHeaders)({
                status: 503,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Prediction model not available", status: model?.status }
            }, request);
        }
        // Generate prediction
        const prediction = await generatePredictionResult(predictionRequest, model, user.id);
        // Store prediction result
        await storePredictionResult(prediction);
        // Update model usage statistics
        await updateModelUsage(model.id);
        const duration = Date.now() - startTime;
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "prediction_generated",
            userId: user.id,
            organizationId: predictionRequest.organizationId,
            timestamp: new Date().toISOString(),
            details: {
                predictionId: prediction.id,
                predictionType: predictionRequest.predictionType,
                timeHorizon: predictionRequest.timeHorizon,
                modelVersion: model.version,
                processingTime: duration
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'PredictionGenerated',
            aggregateId: prediction.id,
            aggregateType: 'Prediction',
            version: 1,
            data: {
                prediction: {
                    ...prediction,
                    predictions: prediction.predictions.slice(0, 5) // Limit data in event
                },
                generatedBy: user.id
            },
            userId: user.id,
            organizationId: predictionRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Prediction generated successfully", {
            correlationId,
            predictionId: prediction.id,
            predictionType: predictionRequest.predictionType,
            timeHorizon: predictionRequest.timeHorizon,
            modelVersion: model.version,
            duration,
            generatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                prediction,
                processingTime: duration,
                message: "Prediction generated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Generate prediction failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Train model handler
 */
async function trainModel(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Train model started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkAnalyticsAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to model training" }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = trainModelSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const trainingRequest = value;
        // Start model training
        const trainingJob = await startModelTraining(trainingRequest, user.id);
        logger_1.logger.info("Model training started successfully", {
            correlationId,
            trainingJobId: trainingJob.id,
            modelName: trainingRequest.modelName,
            predictionType: trainingRequest.predictionType,
            startedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 202,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                trainingJobId: trainingJob.id,
                modelName: trainingRequest.modelName,
                predictionType: trainingRequest.predictionType,
                status: 'TRAINING',
                estimatedDuration: trainingJob.estimatedDuration,
                startedAt: trainingJob.startedAt,
                message: "Model training started successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Train model failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkAnalyticsAccess(user, organizationId) {
    try {
        // Check if user has admin or analytics role
        if (user.roles?.includes('admin') || user.roles?.includes('analytics_admin')) {
            return true;
        }
        // For organization-specific access, check organization membership
        if (organizationId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
            if (memberships.length > 0) {
                const membership = memberships[0];
                return membership.role === 'OWNER' || membership.role === 'ADMIN';
            }
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check analytics access', { error, userId: user.id, organizationId });
        return false;
    }
}
async function getOrCreateModel(predictionType, organizationId) {
    try {
        // Try to find existing model
        const modelQuery = 'SELECT * FROM c WHERE c.predictionType = @type AND c.organizationId = @orgId AND c.status = @status';
        const models = await database_1.db.queryItems('predictive-models', modelQuery, [predictionType, organizationId, ModelStatus.READY]);
        if (models.length > 0) {
            return models[0];
        }
        // Create default model if none exists
        return await createDefaultModel(predictionType, organizationId);
    }
    catch (error) {
        logger_1.logger.error('Failed to get or create model', { error, predictionType, organizationId });
        return null;
    }
}
async function createDefaultModel(predictionType, organizationId) {
    const modelId = (0, uuid_1.v4)();
    const now = new Date().toISOString();
    const model = {
        id: modelId,
        name: `Default ${predictionType} Model`,
        predictionType,
        status: ModelStatus.READY,
        organizationId,
        version: '1.0.0',
        algorithm: 'random_forest',
        features: getDefaultFeatures(predictionType),
        target: getDefaultTarget(predictionType),
        performance: {
            accuracy: 0.85,
            precision: 0.82,
            recall: 0.88,
            f1Score: 0.85,
            mse: 0.15,
            mae: 0.12
        },
        trainingData: {
            startDate: new Date(Date.now() - 90 * 24 * 60 * 60 * 1000).toISOString(),
            endDate: now,
            recordCount: 10000,
            features: getDefaultFeatures(predictionType)
        },
        hyperparameters: {
            nEstimators: 100,
            maxDepth: 10,
            minSamplesSplit: 2
        },
        createdBy: 'system',
        createdAt: now,
        lastTrained: now,
        tenantId: organizationId
    };
    await database_1.db.createItem('predictive-models', model);
    return model;
}
function getDefaultFeatures(predictionType) {
    const featureMap = {
        [PredictionType.USER_BEHAVIOR]: ['login_frequency', 'document_views', 'workflow_usage', 'session_duration'],
        [PredictionType.DOCUMENT_USAGE]: ['document_type', 'file_size', 'creation_date', 'last_modified'],
        [PredictionType.WORKFLOW_PERFORMANCE]: ['step_count', 'complexity_score', 'user_count', 'execution_history'],
        [PredictionType.SYSTEM_LOAD]: ['cpu_usage', 'memory_usage', 'request_count', 'response_time'],
        [PredictionType.CHURN_PREDICTION]: ['last_login', 'activity_score', 'feature_usage', 'support_tickets'],
        [PredictionType.GROWTH_FORECAST]: ['user_growth', 'document_growth', 'usage_trends', 'subscription_changes'],
        [PredictionType.ANOMALY_DETECTION]: ['baseline_metrics', 'deviation_score', 'pattern_analysis', 'threshold_breaches']
    };
    return featureMap[predictionType] || ['default_feature'];
}
function getDefaultTarget(predictionType) {
    const targetMap = {
        [PredictionType.USER_BEHAVIOR]: 'user_engagement_score',
        [PredictionType.DOCUMENT_USAGE]: 'document_popularity',
        [PredictionType.WORKFLOW_PERFORMANCE]: 'execution_time',
        [PredictionType.SYSTEM_LOAD]: 'resource_utilization',
        [PredictionType.CHURN_PREDICTION]: 'churn_probability',
        [PredictionType.GROWTH_FORECAST]: 'growth_rate',
        [PredictionType.ANOMALY_DETECTION]: 'anomaly_score'
    };
    return targetMap[predictionType] || 'default_target';
}
async function generatePredictionResult(request, model, userId) {
    const predictionId = (0, uuid_1.v4)();
    const now = new Date();
    const timeHorizon = request.timeHorizon || 30;
    // Generate mock predictions (in production, this would use actual ML models)
    const predictions = [];
    const baseValue = Math.random() * 100;
    for (let i = 0; i < timeHorizon; i++) {
        const timestamp = new Date(now.getTime() + i * 24 * 60 * 60 * 1000).toISOString();
        const trend = Math.sin(i * 0.1) * 10;
        const noise = (Math.random() - 0.5) * 5;
        const value = Math.max(0, baseValue + trend + noise);
        const confidence = Math.random() * 0.3 + 0.7; // 0.7 to 1.0
        predictions.push({
            timestamp,
            value: Math.round(value * 100) / 100,
            confidence: Math.round(confidence * 100) / 100,
            confidenceLevel: getConfidenceLevel(confidence),
            confidenceInterval: request.options?.includeConfidenceInterval ? {
                lower: Math.round((value * 0.9) * 100) / 100,
                upper: Math.round((value * 1.1) * 100) / 100
            } : undefined
        });
    }
    // Generate insights
    const insights = generateInsights(request.predictionType, predictions);
    return {
        id: predictionId,
        predictionType: request.predictionType,
        organizationId: request.organizationId,
        predictions,
        metadata: {
            modelVersion: model.version,
            modelAccuracy: model.performance.accuracy,
            featureImportance: request.options?.includeFeatureImportance ? generateFeatureImportance(model.features) : undefined,
            timeHorizon,
            generatedAt: now.toISOString(),
            expiresAt: new Date(now.getTime() + 24 * 60 * 60 * 1000).toISOString() // 24 hours
        },
        insights
    };
}
function getConfidenceLevel(confidence) {
    if (confidence >= 0.9)
        return ConfidenceLevel.VERY_HIGH;
    if (confidence >= 0.8)
        return ConfidenceLevel.HIGH;
    if (confidence >= 0.6)
        return ConfidenceLevel.MEDIUM;
    return ConfidenceLevel.LOW;
}
function generateInsights(predictionType, predictions) {
    const insights = [];
    const trend = predictions[predictions.length - 1].value - predictions[0].value;
    const avgConfidence = predictions.reduce((sum, p) => sum + p.confidence, 0) / predictions.length;
    if (trend > 0) {
        insights.push({
            type: 'trend',
            message: `Positive trend detected with ${Math.round(trend * 100) / 100} increase over the forecast period`,
            impact: 'positive',
            confidence: avgConfidence
        });
    }
    else if (trend < 0) {
        insights.push({
            type: 'trend',
            message: `Negative trend detected with ${Math.round(Math.abs(trend) * 100) / 100} decrease over the forecast period`,
            impact: 'negative',
            confidence: avgConfidence
        });
    }
    if (avgConfidence > 0.8) {
        insights.push({
            type: 'confidence',
            message: 'High confidence prediction based on strong historical patterns',
            impact: 'informational',
            confidence: avgConfidence
        });
    }
    return insights;
}
function generateFeatureImportance(features) {
    const importance = {};
    let remaining = 1.0;
    for (let i = 0; i < features.length; i++) {
        if (i === features.length - 1) {
            importance[features[i]] = remaining;
        }
        else {
            const value = Math.random() * remaining * 0.5;
            importance[features[i]] = Math.round(value * 100) / 100;
            remaining -= value;
        }
    }
    return importance;
}
async function storePredictionResult(prediction) {
    try {
        await database_1.db.createItem('predictions', prediction);
        // Cache prediction for quick access
        const cacheKey = `prediction:${prediction.id}`;
        await redis_1.redis.setex(cacheKey, 86400, JSON.stringify(prediction)); // 24 hours
    }
    catch (error) {
        logger_1.logger.error('Failed to store prediction result', { error, predictionId: prediction.id });
    }
}
async function updateModelUsage(modelId) {
    try {
        const usageKey = `model_usage:${modelId}`;
        await redis_1.redis.hincrby(usageKey, 'prediction_count', 1);
        await redis_1.redis.hset(usageKey, 'last_used', new Date().toISOString());
        await redis_1.redis.expire(usageKey, 86400 * 30); // 30 days
    }
    catch (error) {
        logger_1.logger.error('Failed to update model usage', { error, modelId });
    }
}
async function startModelTraining(trainingRequest, userId) {
    try {
        const trainingJobId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const trainingJob = {
            id: trainingJobId,
            modelName: trainingRequest.modelName,
            predictionType: trainingRequest.predictionType,
            organizationId: trainingRequest.organizationId,
            status: 'TRAINING',
            startedAt: now,
            estimatedDuration: '30 minutes',
            startedBy: userId
        };
        // In production, this would start actual model training
        logger_1.logger.info('Model training job started', trainingJob);
        return trainingJob;
    }
    catch (error) {
        logger_1.logger.error('Failed to start model training', { error, trainingRequest });
        throw error;
    }
}
// Register functions
functions_1.app.http('prediction-generate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'analytics/predictions',
    handler: generatePrediction
});
functions_1.app.http('model-train', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/analytics/models/train',
    handler: trainModel
});
//# sourceMappingURL=predictive-analytics.js.map