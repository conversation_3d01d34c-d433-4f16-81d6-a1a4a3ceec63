"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createApiConnection = createApiConnection;
exports.testApiConnection = testApiConnection;
/**
 * External API Management Function
 * Handles external API connections, authentication, and management
 * Migrated from old-arch/src/integration-service/external-apis/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// API types and enums
var ApiType;
(function (ApiType) {
    ApiType["REST"] = "REST";
    ApiType["GRAPHQL"] = "GRAPHQL";
    ApiType["SOAP"] = "SOAP";
    ApiType["WEBHOOK"] = "WEBHOOK";
})(ApiType || (ApiType = {}));
var AuthType;
(function (AuthType) {
    AuthType["NONE"] = "NONE";
    AuthType["API_KEY"] = "API_KEY";
    AuthType["BEARER_TOKEN"] = "BEARER_TOKEN";
    AuthType["BASIC_AUTH"] = "BASIC_AUTH";
    AuthType["OAUTH2"] = "OAUTH2";
    AuthType["CUSTOM"] = "CUSTOM";
})(AuthType || (AuthType = {}));
var ApiStatus;
(function (ApiStatus) {
    ApiStatus["ACTIVE"] = "ACTIVE";
    ApiStatus["INACTIVE"] = "INACTIVE";
    ApiStatus["ERROR"] = "ERROR";
    ApiStatus["TESTING"] = "TESTING";
})(ApiStatus || (ApiStatus = {}));
// Validation schemas
const createApiConnectionSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(ApiType)).required(),
    baseUrl: Joi.string().uri().required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    authentication: Joi.object({
        type: Joi.string().valid(...Object.values(AuthType)).required(),
        credentials: Joi.object({
            apiKey: Joi.string().optional(),
            token: Joi.string().optional(),
            username: Joi.string().optional(),
            password: Joi.string().optional(),
            clientId: Joi.string().optional(),
            clientSecret: Joi.string().optional(),
            scope: Joi.string().optional(),
            tokenUrl: Joi.string().uri().optional(),
            customHeaders: Joi.object().optional()
        }).optional()
    }).required(),
    configuration: Joi.object({
        timeout: Joi.number().min(1000).max(300000).default(30000),
        retryAttempts: Joi.number().min(0).max(5).default(3),
        retryDelay: Joi.number().min(100).max(10000).default(1000),
        rateLimit: Joi.object({
            requests: Joi.number().min(1).required(),
            window: Joi.number().min(1000).required()
        }).optional(),
        headers: Joi.object().optional(),
        queryParams: Joi.object().optional()
    }).optional(),
    endpoints: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        path: Joi.string().required(),
        method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').required(),
        description: Joi.string().optional(),
        parameters: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().valid('query', 'path', 'header', 'body').required(),
            required: Joi.boolean().default(false),
            description: Joi.string().optional()
        })).optional()
    })).optional()
});
const testApiConnectionSchema = Joi.object({
    connectionId: Joi.string().uuid().required(),
    endpoint: Joi.string().optional(),
    method: Joi.string().valid('GET', 'POST', 'PUT', 'DELETE', 'PATCH').default('GET'),
    parameters: Joi.object().optional()
});
/**
 * Create API connection handler
 */
async function createApiConnection(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create API connection started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createApiConnectionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const connectionRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(connectionRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check API connection limits
        const canCreate = await checkApiConnectionLimits(connectionRequest.organizationId);
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Create API connection
        const connectionId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const apiConnection = {
            id: connectionId,
            name: connectionRequest.name,
            description: connectionRequest.description,
            type: connectionRequest.type,
            baseUrl: connectionRequest.baseUrl,
            status: ApiStatus.TESTING,
            organizationId: connectionRequest.organizationId,
            projectId: connectionRequest.projectId,
            authentication: {
                type: connectionRequest.authentication.type,
                credentials: await encryptCredentials(connectionRequest.authentication.credentials)
            },
            configuration: {
                timeout: 30000,
                retryAttempts: 3,
                retryDelay: 1000,
                ...connectionRequest.configuration
            },
            endpoints: connectionRequest.endpoints || [],
            metrics: {
                totalRequests: 0,
                successfulRequests: 0,
                failedRequests: 0,
                averageResponseTime: 0
            },
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('api-connections', apiConnection);
        // Test the connection
        const testResult = await testApiConnectionInternal(apiConnection);
        // Update status based on test result
        const updatedConnection = {
            ...apiConnection,
            id: connectionId,
            status: testResult.success ? ApiStatus.ACTIVE : ApiStatus.ERROR,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('api-connections', updatedConnection);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "api_connection_created",
            userId: user.id,
            organizationId: connectionRequest.organizationId,
            projectId: connectionRequest.projectId,
            timestamp: now,
            details: {
                connectionId,
                connectionName: connectionRequest.name,
                apiType: connectionRequest.type,
                baseUrl: connectionRequest.baseUrl,
                authType: connectionRequest.authentication.type,
                endpointCount: connectionRequest.endpoints?.length || 0,
                testResult: testResult.success
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ApiConnectionCreated',
            aggregateId: connectionId,
            aggregateType: 'ApiConnection',
            version: 1,
            data: {
                connection: {
                    ...updatedConnection,
                    authentication: { type: updatedConnection.authentication.type } // Don't include credentials in event
                },
                testResult,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: connectionRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("API connection created successfully", {
            correlationId,
            connectionId,
            connectionName: connectionRequest.name,
            apiType: connectionRequest.type,
            status: updatedConnection.status,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: connectionId,
                name: connectionRequest.name,
                type: connectionRequest.type,
                status: updatedConnection.status,
                baseUrl: connectionRequest.baseUrl,
                endpointCount: connectionRequest.endpoints?.length || 0,
                testResult,
                createdAt: now,
                message: "API connection created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create API connection failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Test API connection handler
 */
async function testApiConnection(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Test API connection started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = testApiConnectionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const testRequest = value;
        // Get API connection
        const connection = await database_1.db.readItem('api-connections', testRequest.connectionId, testRequest.connectionId);
        if (!connection) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "API connection not found" }
            }, request);
        }
        const connectionData = connection;
        // Check access
        const hasAccess = await checkOrganizationAccess(connectionData.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to API connection" }
            }, request);
        }
        // Test the connection
        const testResult = await testApiConnectionInternal(connectionData, testRequest.endpoint, testRequest.method, testRequest.parameters);
        // Update connection metrics
        await updateConnectionMetrics(testRequest.connectionId, testResult);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "api_connection_tested",
            userId: user.id,
            organizationId: connectionData.organizationId,
            projectId: connectionData.projectId,
            timestamp: new Date().toISOString(),
            details: {
                connectionId: testRequest.connectionId,
                connectionName: connectionData.name,
                endpoint: testRequest.endpoint,
                method: testRequest.method,
                success: testResult.success,
                responseTime: testResult.responseTime,
                statusCode: testResult.statusCode
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("API connection tested successfully", {
            correlationId,
            connectionId: testRequest.connectionId,
            connectionName: connectionData.name,
            success: testResult.success,
            responseTime: testResult.responseTime,
            testedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                connectionId: testRequest.connectionId,
                connectionName: connectionData.name,
                testResult,
                testedAt: new Date().toISOString(),
                message: testResult.success ? "API connection test successful" : "API connection test failed"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Test API connection failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkApiConnectionLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxConnections: 3 },
            'PROFESSIONAL': { maxConnections: 25 },
            'ENTERPRISE': { maxConnections: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxConnections === -1) {
            return { allowed: true };
        }
        // Check current connection count
        const connectionCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != @inactive';
        const countResult = await database_1.db.queryItems('api-connections', connectionCountQuery, [organizationId, ApiStatus.INACTIVE]);
        const currentCount = Number(countResult[0]) || 0;
        if (currentCount >= limit.maxConnections) {
            return {
                allowed: false,
                reason: `API connection limit reached (${limit.maxConnections})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check API connection limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
async function encryptCredentials(credentials) {
    // In production, this would encrypt sensitive credentials
    // For now, we'll just return them as-is (not recommended for production)
    return credentials;
}
async function testApiConnectionInternal(connection, endpoint, method, parameters) {
    const startTime = Date.now();
    try {
        // Build test URL
        const testUrl = endpoint ? `${connection.baseUrl}${endpoint}` : connection.baseUrl;
        const testMethod = method || 'GET';
        // Prepare headers
        const headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'DocuContext-API-Client/1.0',
            ...connection.configuration.headers
        };
        // Add authentication headers
        if (connection.authentication.type === AuthType.API_KEY && connection.authentication.credentials?.apiKey) {
            headers['X-API-Key'] = connection.authentication.credentials.apiKey;
        }
        else if (connection.authentication.type === AuthType.BEARER_TOKEN && connection.authentication.credentials?.token) {
            headers['Authorization'] = `Bearer ${connection.authentication.credentials.token}`;
        }
        else if (connection.authentication.type === AuthType.BASIC_AUTH && connection.authentication.credentials?.username) {
            const auth = Buffer.from(`${connection.authentication.credentials.username}:${connection.authentication.credentials.password || ''}`).toString('base64');
            headers['Authorization'] = `Basic ${auth}`;
        }
        // Make test request (simplified - in production, use proper HTTP client)
        const response = await fetch(testUrl, {
            method: testMethod,
            headers,
            body: testMethod !== 'GET' && parameters ? JSON.stringify(parameters) : undefined,
            signal: AbortSignal.timeout(connection.configuration.timeout || 30000)
        });
        const responseTime = Date.now() - startTime;
        const responseData = await response.text();
        return {
            success: response.ok,
            statusCode: response.status,
            statusText: response.statusText,
            responseTime,
            responseSize: responseData.length,
            headers: Object.fromEntries(response.headers.entries()),
            data: responseData.substring(0, 1000), // First 1000 chars
            error: response.ok ? null : `HTTP ${response.status}: ${response.statusText}`
        };
    }
    catch (error) {
        const responseTime = Date.now() - startTime;
        const errorMessage = error instanceof Error ? error.message : String(error);
        return {
            success: false,
            statusCode: 0,
            statusText: 'Connection Failed',
            responseTime,
            responseSize: 0,
            headers: {},
            data: null,
            error: errorMessage
        };
    }
}
async function updateConnectionMetrics(connectionId, testResult) {
    try {
        const connection = await database_1.db.readItem('api-connections', connectionId, connectionId);
        if (!connection)
            return;
        const connectionData = connection;
        const metrics = connectionData.metrics || {
            totalRequests: 0,
            successfulRequests: 0,
            failedRequests: 0,
            averageResponseTime: 0
        };
        // Update metrics
        metrics.totalRequests += 1;
        if (testResult.success) {
            metrics.successfulRequests += 1;
        }
        else {
            metrics.failedRequests += 1;
        }
        // Update average response time
        metrics.averageResponseTime = Math.round((metrics.averageResponseTime * (metrics.totalRequests - 1) + testResult.responseTime) / metrics.totalRequests);
        metrics.lastUsed = new Date().toISOString();
        // Update connection
        const updatedConnection = {
            ...connectionData,
            id: connectionId,
            metrics,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('api-connections', updatedConnection);
    }
    catch (error) {
        logger_1.logger.error('Failed to update connection metrics', { error, connectionId });
    }
}
// Register functions
functions_1.app.http('api-connection-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'integrations/api-connections',
    handler: createApiConnection
});
functions_1.app.http('api-connection-test', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'integrations/api-connections/test',
    handler: testApiConnection
});
//# sourceMappingURL=external-api-management.js.map