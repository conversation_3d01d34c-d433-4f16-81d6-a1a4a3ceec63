/**
 * Workflow Execution Functions
 * Handles workflow execution operations (start, complete steps, assign)
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
export declare enum WorkflowStatus {
    DRAFT = "DRAFT",
    ACTIVE = "ACTIVE",
    COMPLETED = "COMPLETED",
    CANCELLED = "CANCELLED",
    PAUSED = "PAUSED"
}
export declare enum StepStatus {
    PENDING = "PENDING",
    IN_PROGRESS = "IN_PROGRESS",
    COMPLETED = "COMPLETED",
    SKIPPED = "SKIPPED",
    FAILED = "FAILED"
}
/**
 * Start workflow handler
 */
export declare function startWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Complete workflow step handler
 */
export declare function completeWorkflowStep(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
