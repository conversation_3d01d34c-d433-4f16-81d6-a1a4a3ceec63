{"version": 3, "file": "document-processing.js", "sourceRoot": "", "sources": ["../../src/functions/document-processing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA4JA,0CA+LC;AA3VD;;;GAGG;AACH,gDAAyF;AACzF,kEAAuF;AACvF,sDAAwD;AACxD,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AACjD,sBAAsB;AACtB,IAAY,YAWX;AAXD,WAAY,YAAY;IACtB,mCAAmB,CAAA;IACnB,mCAAmB,CAAA;IACnB,qCAAqB,CAAA;IACrB,mCAAmB,CAAA;IACnB,qCAAqB,CAAA;IACrB,uCAAuB,CAAA;IACvB,+BAAe,CAAA;IACf,mCAAmB,CAAA;IACnB,2BAAW,CAAA;IACX,uCAAuB,CAAA;AACzB,CAAC,EAXW,YAAY,4BAAZ,YAAY,QAWvB;AAED,IAAY,cAOX;AAPD,WAAY,cAAc;IACxB,qCAAmB,CAAA;IACnB,uCAAqB,CAAA;IACrB,2CAAyB,CAAA;IACzB,yCAAuB,CAAA;IACvB,mCAAiB,CAAA;IACjB,uCAAqB,CAAA;AACvB,CAAC,EAPW,cAAc,8BAAd,cAAc,QAOzB;AAED,4BAA4B;AAC5B,MAAM,gBAAgB,GAAG,GAAG,CAAC,MAAM,CAAC;IAClC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,QAAQ,EAAE,SAAS,EAAE,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,QAAQ,CAAC,CAAC,OAAO,CAAC,QAAQ,CAAC;IACnH,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC1C,oBAAoB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACjD,eAAe,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IAC7C,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;CACvC,CAAC,CAAC;AAuBH;;GAEG;AACH,KAAK,UAAU,uBAAuB,CACpC,cAAsB,EACtB,OAAe,EACf,MAA8B;IAE9B,IAAI,CAAC;QACH,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,oBAAoB,CAAC,OAAO,EAAE,cAAc,CAAC,CAAC;QAC1E,MAAM,MAAM,GAAG,MAAM,MAAM,CAAC,aAAa,EAAE,CAAC;QAE5C,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,MAAM,IAAI,KAAK,CAAC,6BAA6B,CAAC,CAAC;QACjD,CAAC;QAED,uBAAuB;QACvB,IAAI,aAAa,GAAG,EAAE,CAAC;QACvB,IAAI,MAAM,CAAC,OAAO,EAAE,CAAC;YACnB,aAAa,GAAG,MAAM,CAAC,OAAO,CAAC;QACjC,CAAC;QAED,iBAAiB;QACjB,MAAM,MAAM,GAAU,EAAE,CAAC;QACzB,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;YAClB,KAAK,MAAM,KAAK,IAAI,MAAM,CAAC,MAAM,EAAE,CAAC;gBAClC,MAAM,SAAS,GAAG;oBAChB,QAAQ,EAAE,KAAK,CAAC,QAAQ;oBACxB,WAAW,EAAE,KAAK,CAAC,WAAW;oBAC9B,KAAK,EAAE,KAAK,CAAC,KAAK,CAAC,GAAG,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC;wBAC9B,OAAO,EAAE,IAAI,CAAC,OAAO;wBACrB,QAAQ,EAAE,IAAI,CAAC,QAAQ;wBACvB,WAAW,EAAE,IAAI,CAAC,WAAW;wBAC7B,UAAU,EAAG,IAAY,CAAC,UAAU,IAAI,CAAC;qBAC1C,CAAC,CAAC;iBACJ,CAAC;gBACF,MAAM,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YACzB,CAAC;QACH,CAAC;QAED,0BAA0B;QAC1B,MAAM,aAAa,GAAU,EAAE,CAAC;QAChC,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;YACzB,KAAK,MAAM,GAAG,IAAI,MAAM,CAAC,aAAa,EAAE,CAAC;gBACvC,aAAa,CAAC,IAAI,CAAC;oBACjB,GAAG,EAAE,GAAG,CAAC,GAAG,EAAE,OAAO,IAAI,EAAE;oBAC3B,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,OAAO,IAAI,EAAE;oBAC/B,UAAU,EAAE,GAAG,CAAC,UAAU;iBAC3B,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,+BAA+B;QAC/B,MAAM,gBAAgB,GAAa,EAAE,CAAC;QACtC,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;YACjB,KAAK,MAAM,IAAI,IAAI,MAAM,CAAC,KAAK,EAAE,CAAC;gBAChC,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;oBACf,KAAK,MAAM,IAAI,IAAI,IAAI,CAAC,KAAK,EAAE,CAAC;wBAC9B,MAAM,UAAU,GAAI,IAAY,CAAC,UAAU,CAAC;wBAC5C,IAAI,UAAU,KAAK,SAAS,EAAE,CAAC;4BAC7B,gBAAgB,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;wBACpC,CAAC;oBACH,CAAC;gBACH,CAAC;YACH,CAAC;QACH,CAAC;QAED,MAAM,iBAAiB,GAAG,gBAAgB,CAAC,MAAM,GAAG,CAAC;YACnD,CAAC,CAAC,gBAAgB,CAAC,MAAM,CAAC,CAAC,GAAG,EAAE,IAAI,EAAE,EAAE,CAAC,GAAG,GAAG,IAAI,EAAE,CAAC,CAAC,GAAG,gBAAgB,CAAC,MAAM;YACjF,CAAC,CAAC,CAAC,CAAC;QAEN,OAAO;YACL,IAAI,EAAE,aAAa;YACnB,MAAM;YACN,aAAa;YACb,UAAU,EAAE,iBAAiB;SAC9B,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;YACvC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,OAAO;SACR,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,OAAoB,EAAE,OAA0B;IACpF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;IAE7B,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE9D,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,gBAAgB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEzD,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAAsB,KAAK,CAAC;QAEnD,wBAAwB;QACxB,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAE5G,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,uCAAuC;QACvC,MAAM,eAAe,GAAG;YACtB,GAAG,QAAQ;YACX,EAAE,EAAG,QAAgB,CAAC,EAAE;YACxB,MAAM,EAAE,cAAc,CAAC,UAAU;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;SACnB,CAAC;QACF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,sCAAsC;QACtC,MAAM,gBAAgB,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;QACrE,IAAI,CAAC,gBAAgB,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,gDAAgD,CAAC,CAAC;QACpE,CAAC;QAED,MAAM,iBAAiB,GAAG,gCAAiB,CAAC,oBAAoB,CAAC,gBAAgB,CAAC,CAAC;QACnF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QACF,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAE,QAAgB,CAAC,QAAQ,CAAC,CAAC;QAE7E,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;QACrD,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YACzC,MAAM,IAAI,KAAK,CAAC,qCAAqC,CAAC,CAAC;QACzD,CAAC;QAED,2BAA2B;QAC3B,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YAC9D,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;QAClC,CAAC;QACD,MAAM,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC;QAE7C,0CAA0C;QAC1C,MAAM,QAAQ,GAAG,OAAO,CAAC,GAAG,CAAC,oCAAoC,CAAC;QAClE,MAAM,GAAG,GAAG,OAAO,CAAC,GAAG,CAAC,+BAA+B,CAAC;QAExD,IAAI,CAAC,QAAQ,IAAI,CAAC,GAAG,EAAE,CAAC;YACtB,MAAM,IAAI,KAAK,CAAC,wDAAwD,CAAC,CAAC;QAC5E,CAAC;QAED,MAAM,MAAM,GAAG,IAAI,2CAAsB,CAAC,QAAQ,EAAE,IAAI,uCAAkB,CAAC,GAAG,CAAC,CAAC,CAAC;QAEjF,yBAAyB;QACzB,IAAI,OAAO,GAAG,iBAAiB,CAAC;QAChC,IAAI,iBAAiB,CAAC,aAAa,EAAE,CAAC;YACpC,OAAO,GAAG,iBAAiB,CAAC,aAAa,CAAC;QAC5C,CAAC;aAAM,CAAC;YACN,QAAQ,iBAAiB,CAAC,YAAY,EAAE,CAAC;gBACvC,KAAK,SAAS;oBACZ,OAAO,GAAG,kBAAkB,CAAC;oBAC7B,MAAM;gBACR,KAAK,SAAS;oBACZ,OAAO,GAAG,kBAAkB,CAAC;oBAC7B,MAAM;gBACR,KAAK,UAAU;oBACb,OAAO,GAAG,qBAAqB,CAAC;oBAChC,MAAM;gBACR,KAAK,SAAS;oBACZ,OAAO,GAAG,mBAAmB,CAAC;oBAC9B,MAAM;gBACR;oBACE,OAAO,GAAG,iBAAiB,CAAC;YAChC,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,MAAM,cAAc,GAAG,MAAM,uBAAuB,CAAC,cAAc,EAAE,OAAO,EAAE,MAAM,CAAC,CAAC;QAEtF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE9C,2BAA2B;QAC3B,MAAM,MAAM,GAAqB;YAC/B,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,MAAM,EAAE,WAAW;YACnB,aAAa,EAAE,cAAc,CAAC,IAAI;YAClC,MAAM,EAAE,iBAAiB,CAAC,aAAa,CAAC,CAAC,CAAC,cAAc,CAAC,MAAM,CAAC,CAAC,CAAC,SAAS;YAC3E,aAAa,EAAE,iBAAiB,CAAC,oBAAoB,CAAC,CAAC,CAAC,cAAc,CAAC,aAAa,CAAC,CAAC,CAAC,SAAS;YAChG,QAAQ,EAAE,iBAAiB,CAAC,eAAe,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,EAAE,oCAAoC;YAClG,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,cAAc;YACd,SAAS,EAAE,OAAO;SACnB,CAAC;QAEF,sCAAsC;QACtC,MAAM,aAAa,GAAG;YACpB,GAAG,QAAQ;YACX,EAAE,EAAG,QAAgB,CAAC,EAAE;YACxB,MAAM,EAAE,cAAc,CAAC,SAAS;YAChC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,gBAAgB,EAAE,MAAM;SACzB,CAAC;QACF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,aAAa,CAAC,CAAC;QAEhD,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;YAC3C,aAAa;YACb,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,cAAc;YACd,SAAS,EAAE,OAAO;YAClB,UAAU,EAAE,cAAc,CAAC,UAAU;SACtC,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,MAAM;SACjB,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,4BAA4B,EAAE;SAClD,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED,eAAG,CAAC,IAAI,CAAC,qBAAqB,EAAE;IAC9B,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,mBAAmB;IAC1B,OAAO,EAAE,eAAe;CACzB,CAAC,CAAC"}