"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createTemplate = createTemplate;
exports.listTemplates = listTemplates;
exports.applyTemplate = applyTemplate;
/**
 * Template Management Function
 * Handles document and workflow template operations
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Template types enum
var TemplateType;
(function (TemplateType) {
    TemplateType["DOCUMENT"] = "DOCUMENT";
    TemplateType["WORKFLOW"] = "WORKFLOW";
    TemplateType["PROJECT"] = "PROJECT";
    TemplateType["FORM"] = "FORM";
})(TemplateType || (TemplateType = {}));
// Template visibility enum
var TemplateVisibility;
(function (TemplateVisibility) {
    TemplateVisibility["PRIVATE"] = "PRIVATE";
    TemplateVisibility["ORGANIZATION"] = "ORGANIZATION";
    TemplateVisibility["PUBLIC"] = "PUBLIC";
})(TemplateVisibility || (TemplateVisibility = {}));
// Validation schemas
const createTemplateSchema = Joi.object({
    name: Joi.string().required().min(2).max(100),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(TemplateType)).required(),
    visibility: Joi.string().valid(...Object.values(TemplateVisibility)).default(TemplateVisibility.PRIVATE),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(10).default([]),
    content: Joi.object().required(),
    variables: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        type: Joi.string().valid('text', 'number', 'date', 'boolean', 'select').required(),
        label: Joi.string().required(),
        required: Joi.boolean().default(false),
        defaultValue: Joi.any().optional(),
        options: Joi.array().items(Joi.string()).optional()
    })).default([]),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional()
});
const listTemplatesSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    type: Joi.string().valid(...Object.values(TemplateType)).optional(),
    visibility: Joi.string().valid(...Object.values(TemplateVisibility)).optional(),
    category: Joi.string().optional(),
    search: Joi.string().max(100).optional(),
    organizationId: Joi.string().uuid().optional()
});
const applyTemplateSchema = Joi.object({
    templateId: Joi.string().uuid().required(),
    variables: Joi.object().optional(),
    name: Joi.string().max(100).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional()
});
/**
 * Create template handler
 */
async function createTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const templateData = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, templateData.organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Create template
        const templateId = (0, uuid_1.v4)();
        const template = {
            id: templateId,
            name: templateData.name,
            description: templateData.description || "",
            type: templateData.type,
            visibility: templateData.visibility,
            category: templateData.category || "General",
            tags: templateData.tags,
            content: templateData.content,
            variables: templateData.variables,
            organizationId: templateData.organizationId,
            projectId: templateData.projectId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            usageCount: 0,
            isActive: true,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('templates', template);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "template_created",
            userId: user.id,
            organizationId: templateData.organizationId,
            projectId: templateData.projectId,
            templateId,
            timestamp: new Date().toISOString(),
            details: {
                templateName: template.name,
                templateType: template.type,
                visibility: template.visibility,
                variableCount: template.variables.length
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Template created successfully", {
            correlationId,
            templateId,
            userId: user.id,
            type: template.type,
            organizationId: templateData.organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: templateId,
                name: template.name,
                type: template.type,
                visibility: template.visibility,
                message: "Template created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List templates handler
 */
async function listTemplates(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List templates started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listTemplatesSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, type, visibility, category, search, organizationId } = value;
        // Build query
        let queryText = 'SELECT * FROM c WHERE c.isActive = true';
        const parameters = [];
        // Add access control
        queryText += ' AND (c.visibility = @publicVisibility OR c.createdBy = @userId';
        parameters.push('PUBLIC', user.id);
        if (user.tenantId) {
            queryText += ' OR c.organizationId = @tenantId';
            parameters.push(user.tenantId);
        }
        queryText += ')';
        // Add filters
        if (type) {
            queryText += ' AND c.type = @type';
            parameters.push(type);
        }
        if (visibility) {
            queryText += ' AND c.visibility = @visibility';
            parameters.push(visibility);
        }
        if (category) {
            queryText += ' AND c.category = @category';
            parameters.push(category);
        }
        if (organizationId) {
            queryText += ' AND c.organizationId = @organizationId';
            parameters.push(organizationId);
        }
        if (search) {
            queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
            parameters.push(search);
        }
        // Add ordering
        queryText += ' ORDER BY c.usageCount DESC, c.createdAt DESC';
        // Get total count
        const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
        const countResult = await database_1.db.queryItems('templates', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const templates = await database_1.db.queryItems('templates', paginatedQuery, parameters);
        logger_1.logger.info("Templates listed successfully", {
            correlationId,
            userId: user.id,
            count: templates.length,
            page,
            limit,
            type,
            organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                items: templates,
                total,
                page,
                limit,
                hasMore: page * limit < total
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List templates failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Apply template handler
 */
async function applyTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Apply template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = applyTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { templateId, variables, name, organizationId, projectId } = value;
        // Get template
        const template = await database_1.db.readItem('templates', templateId, templateId);
        if (!template) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Template not found" }
            }, request);
        }
        // Check access to template
        const hasAccess = (template.visibility === 'PUBLIC' ||
            template.createdBy === user.id ||
            template.organizationId === user.tenantId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to template" }
            }, request);
        }
        // Apply template variables to content
        const appliedContent = applyVariablesToContent(template.content, variables || {});
        // Create new item based on template type
        let createdItemId;
        let createdItemType;
        switch (template.type) {
            case TemplateType.DOCUMENT:
                createdItemId = await createDocumentFromTemplate(template, appliedContent, name, organizationId, projectId, user);
                createdItemType = 'document';
                break;
            case TemplateType.WORKFLOW:
                createdItemId = await createWorkflowFromTemplate(template, appliedContent, name, organizationId, projectId, user);
                createdItemType = 'workflow';
                break;
            case TemplateType.PROJECT:
                createdItemId = await createProjectFromTemplate(template, appliedContent, name, organizationId, user);
                createdItemType = 'project';
                break;
            default:
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Unsupported template type" }
                }, request);
        }
        // Update template usage count
        const updatedTemplate = {
            ...template,
            id: templateId,
            usageCount: (template.usageCount || 0) + 1,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('templates', updatedTemplate);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "template_applied",
            userId: user.id,
            organizationId,
            projectId,
            templateId,
            timestamp: new Date().toISOString(),
            details: {
                templateName: template.name,
                templateType: template.type,
                createdItemId,
                createdItemType,
                variablesUsed: Object.keys(variables || {}).length
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Template applied successfully", {
            correlationId,
            templateId,
            createdItemId,
            createdItemType,
            userId: user.id,
            organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                templateId,
                createdItemId,
                createdItemType,
                templateName: template.name,
                message: "Template applied successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Apply template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Apply variables to template content
 */
function applyVariablesToContent(content, variables) {
    const contentStr = JSON.stringify(content);
    let appliedContent = contentStr;
    // Replace variable placeholders with actual values
    Object.entries(variables).forEach(([key, value]) => {
        const placeholder = `{{${key}}}`;
        appliedContent = appliedContent.replace(new RegExp(placeholder, 'g'), String(value));
    });
    return JSON.parse(appliedContent);
}
/**
 * Create document from template
 */
async function createDocumentFromTemplate(template, content, name, organizationId, projectId, user) {
    const documentId = (0, uuid_1.v4)();
    const document = {
        id: documentId,
        name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
        description: `Created from template: ${template.name}`,
        templateId: template.id,
        content,
        organizationId,
        projectId,
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
        status: "DRAFT",
        tenantId: user.tenantId
    };
    await database_1.db.createItem('documents', document);
    return documentId;
}
/**
 * Create workflow from template
 */
async function createWorkflowFromTemplate(template, content, name, organizationId, projectId, user) {
    const workflowId = (0, uuid_1.v4)();
    const workflow = {
        id: workflowId,
        name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
        description: `Created from template: ${template.name}`,
        templateId: template.id,
        definition: content,
        organizationId,
        projectId,
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
        status: "DRAFT",
        tenantId: user.tenantId
    };
    await database_1.db.createItem('workflows', workflow);
    return workflowId;
}
/**
 * Create project from template
 */
async function createProjectFromTemplate(template, content, name, organizationId, user) {
    const projectId = (0, uuid_1.v4)();
    const project = {
        id: projectId,
        name: name || `${template.name} - ${new Date().toLocaleDateString()}`,
        description: `Created from template: ${template.name}`,
        templateId: template.id,
        settings: content,
        organizationId,
        createdBy: user.id,
        createdAt: new Date().toISOString(),
        updatedBy: user.id,
        updatedAt: new Date().toISOString(),
        tenantId: user.tenantId
    };
    await database_1.db.createItem('projects', project);
    return projectId;
}
/**
 * Combined templates handler
 */
async function handleTemplates(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createTemplate(request, context);
        case 'GET':
            return await listTemplates(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('templates', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'templates/manage',
    handler: handleTemplates
});
functions_1.app.http('template-apply', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'templates/{templateId}/apply',
    handler: applyTemplate
});
//# sourceMappingURL=template-management.js.map