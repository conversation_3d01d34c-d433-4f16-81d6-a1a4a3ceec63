"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.userLogin = userLogin;
exports.getCurrentUser = getCurrentUser;
/**
 * Authentication Functions
 * Handles user login, token validation, and user sync operations
 */
const functions_1 = require("@azure/functions");
const jsonwebtoken_1 = __importDefault(require("jsonwebtoken"));
const bcrypt = __importStar(require("bcryptjs"));
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const loginSchema = Joi.object({
    email: Joi.string().email().required(),
    password: Joi.string().required()
});
const syncUserSchema = Joi.object({
    sub: Joi.string().required(),
    oid: Joi.string().required(),
    email: Joi.string().email().required(),
    emails: Joi.array().items(Joi.string().email()).optional(),
    given_name: Joi.string().optional(),
    family_name: Joi.string().optional(),
    name: Joi.string().optional(),
    picture: Joi.string().uri().optional(),
    tfp: Joi.string().optional()
});
/**
 * User login handler
 */
async function userLogin(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("User login started", { correlationId });
    try {
        // Validate request body
        const body = await request.json();
        const { error, value } = loginSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { email, password } = value;
        // Query for user by email
        const query = 'SELECT * FROM c WHERE c.email = @email';
        const users = await database_1.db.queryItems('users', query, [email.toLowerCase()]);
        if (users.length === 0) {
            logger_1.logger.warn("Login attempt with invalid email", { email, correlationId });
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid email or password" }
            }, request);
        }
        const user = users[0];
        // Check if account is locked or disabled
        if (user.status === 'locked' || user.status === 'disabled') {
            logger_1.logger.warn("Login attempt on locked/disabled account", {
                userId: user.id,
                email: user.email,
                status: user.status,
                correlationId
            });
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Account is not accessible. Please contact support." }
            }, request);
        }
        // Verify password
        let passwordValid = false;
        if (user.passwordHash) {
            passwordValid = await bcrypt.compare(password, user.passwordHash);
        }
        if (!passwordValid) {
            logger_1.logger.warn("Login attempt with invalid password", { email, correlationId });
            // Log failed login attempt
            await database_1.db.createItem('login-history', {
                id: (0, uuid_1.v4)(),
                userId: user.id,
                email: user.email,
                timestamp: new Date().toISOString(),
                ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
                userAgent: request.headers.get("user-agent") || "unknown",
                success: false,
                reason: "invalid_password",
                tenantId: user.tenantId
            });
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Invalid email or password" }
            }, request);
        }
        // Generate tokens
        const refreshToken = (0, uuid_1.v4)();
        const refreshTokenExpiry = new Date();
        refreshTokenExpiry.setDate(refreshTokenExpiry.getDate() + 30); // 30 days
        const jwtSecret = process.env.AUTH_CLIENT_SECRET;
        if (!jwtSecret) {
            logger_1.logger.error("AUTH_CLIENT_SECRET environment variable is not set", { correlationId });
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Authentication service misconfigured" }
            }, request);
        }
        const now = Math.floor(Date.now() / 1000);
        const expiresIn = 3600; // 1 hour
        const accessToken = jsonwebtoken_1.default.sign({
            sub: user.id,
            email: user.email,
            name: user.displayName || `${user.firstName} ${user.lastName}`,
            tenantId: user.tenantId,
            roles: user.roles || [],
            systemRoles: user.systemRoles || [],
            organizationIds: user.organizationIds || [],
            defaultOrganizationId: user.defaultOrganizationId,
            iat: now,
            exp: now + expiresIn,
            jti: (0, uuid_1.v4)(),
            auth_time: now
        }, jwtSecret, {
            expiresIn: `${expiresIn}s`,
            issuer: process.env.AUTH_ISSUER || "hepz-platform",
            audience: process.env.AUTH_CLIENT_ID || "hepz-client",
            algorithm: "HS256"
        });
        // Update user with refresh token and last login
        const updatedUser = {
            ...user,
            refreshTokens: [
                ...(user.refreshTokens || []),
                {
                    token: refreshToken,
                    expiresAt: refreshTokenExpiry.toISOString(),
                    createdAt: new Date().toISOString(),
                    userAgent: request.headers.get("user-agent") || "unknown",
                    ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown"
                }
            ],
            lastLoginAt: new Date().toISOString()
        };
        await database_1.db.updateItem('users', updatedUser);
        // Log successful login
        await database_1.db.createItem('login-history', {
            id: (0, uuid_1.v4)(),
            userId: user.id,
            email: user.email,
            timestamp: new Date().toISOString(),
            ip: request.headers.get("x-forwarded-for") || request.headers.get("x-real-ip") || "unknown",
            userAgent: request.headers.get("user-agent") || "unknown",
            success: true,
            tokenId: accessToken.split('.')[2],
            tenantId: user.tenantId
        });
        logger_1.logger.info("User authenticated successfully", {
            userId: user.id,
            email: user.email,
            correlationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                accessToken,
                refreshToken,
                expiresIn,
                tokenType: "Bearer",
                user: {
                    id: user.id,
                    email: user.email,
                    displayName: user.displayName || `${user.firstName} ${user.lastName}`,
                    firstName: user.firstName,
                    lastName: user.lastName,
                    tenantId: user.tenantId,
                    roles: user.roles || [],
                    systemRoles: user.systemRoles || [],
                    status: user.status || "active",
                    organizationIds: user.organizationIds || [],
                    defaultOrganizationId: user.defaultOrganizationId,
                    avatarUrl: user.avatarUrl,
                    lastLoginAt: new Date().toISOString(),
                    createdAt: user.createdAt,
                    updatedAt: user.updatedAt,
                    preferences: user.preferences || {
                        theme: "system",
                        language: "en",
                        timezone: "UTC",
                        dateFormat: "MM/DD/YYYY"
                    }
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("User login failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get current user info handler
 */
async function getCurrentUser(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get current user started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get full user profile from database
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User profile not found" }
            }, request);
        }
        // Remove sensitive fields
        const sanitizedProfile = {
            ...userProfile,
            refreshTokens: undefined,
            passwordHash: undefined,
            _rid: undefined,
            _self: undefined,
            _etag: undefined,
            _attachments: undefined,
            _ts: undefined
        };
        logger_1.logger.info("Current user retrieved successfully", {
            correlationId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: sanitizedProfile
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get current user failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('auth-login', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'auth/login',
    handler: userLogin
});
functions_1.app.http('auth-me', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'auth/me',
    handler: getCurrentUser
});
//# sourceMappingURL=auth.js.map