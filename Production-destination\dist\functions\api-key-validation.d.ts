/**
 * API Key Validation Function
 * Handles API key validation, rate limiting, and usage tracking
 * Migrated from old-arch/src/integration-service/api-key-validation/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create API key handler
 */
export declare function createApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Validate API key handler
 */
export declare function validateApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
