/**
 * Advanced Commenting Function
 * Handles advanced commenting system with threading, mentions, and reactions
 * Migrated from old-arch/src/collaboration-service/comments/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create comment handler
 */
export declare function createComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Add reaction to comment handler
 */
export declare function addReactionToComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
