"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StepStatus = exports.WorkflowStatus = void 0;
exports.startWorkflow = startWorkflow;
exports.completeWorkflowStep = completeWorkflowStep;
/**
 * Workflow Execution Functions
 * Handles workflow execution operations (start, complete steps, assign)
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Workflow enums
var WorkflowStatus;
(function (WorkflowStatus) {
    WorkflowStatus["DRAFT"] = "DRAFT";
    WorkflowStatus["ACTIVE"] = "ACTIVE";
    WorkflowStatus["COMPLETED"] = "COMPLETED";
    WorkflowStatus["CANCELLED"] = "CANCELLED";
    WorkflowStatus["PAUSED"] = "PAUSED";
})(WorkflowStatus || (exports.WorkflowStatus = WorkflowStatus = {}));
var StepStatus;
(function (StepStatus) {
    StepStatus["PENDING"] = "PENDING";
    StepStatus["IN_PROGRESS"] = "IN_PROGRESS";
    StepStatus["COMPLETED"] = "COMPLETED";
    StepStatus["SKIPPED"] = "SKIPPED";
    StepStatus["FAILED"] = "FAILED";
})(StepStatus || (exports.StepStatus = StepStatus = {}));
// Validation schemas
const completeStepSchema = Joi.object({
    comment: Joi.string().max(1000).optional(),
    outcome: Joi.string().valid('approved', 'rejected', 'completed').optional(),
    metadata: Joi.object().optional()
});
const assignStepSchema = Joi.object({
    assigneeId: Joi.string().uuid().required(),
    comment: Joi.string().max(500).optional()
});
/**
 * Start workflow handler
 */
async function startWorkflow(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const workflowId = request.params.id;
    if (!workflowId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Workflow ID is required' }
        }, request);
    }
    logger_1.logger.info("Start workflow started", { correlationId, workflowId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get workflow
        const workflow = await database_1.db.readItem('workflows', workflowId, workflowId);
        if (!workflow) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Workflow not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (workflow.createdBy === user.id ||
            workflow.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Check if workflow is in DRAFT status
        if (workflow.status !== WorkflowStatus.DRAFT) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Only workflows in DRAFT status can be started" }
            }, request);
        }
        // Check if workflow has steps
        const steps = workflow.steps || [];
        if (steps.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Workflow has no steps" }
            }, request);
        }
        // Sort steps by order and get first step
        const sortedSteps = [...steps].sort((a, b) => a.order - b.order);
        const firstStep = sortedSteps[0];
        // Update workflow status and current step
        const updatedWorkflow = {
            ...workflow,
            id: workflow.id,
            status: WorkflowStatus.ACTIVE,
            currentStepId: firstStep.id,
            startedAt: new Date().toISOString(),
            startedBy: user.id,
            steps: steps.map((step) => step.id === firstStep.id
                ? { ...step, status: StepStatus.IN_PROGRESS, startedAt: new Date().toISOString() }
                : step)
        };
        await database_1.db.updateItem('workflows', updatedWorkflow);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "workflow_started",
            userId: user.id,
            organizationId: workflow.organizationId,
            projectId: workflow.projectId,
            workflowId: workflow.id,
            timestamp: new Date().toISOString(),
            details: {
                workflowName: workflow.name,
                firstStepId: firstStep.id,
                firstStepName: firstStep.name
            }
        });
        logger_1.logger.info("Workflow started successfully", {
            correlationId,
            workflowId,
            userId: user.id,
            firstStepId: firstStep.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: workflow.id,
                message: "Workflow started successfully",
                currentStep: {
                    id: firstStep.id,
                    name: firstStep.name,
                    status: StepStatus.IN_PROGRESS
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Start workflow failed", {
            correlationId,
            workflowId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Complete workflow step handler
 */
async function completeWorkflowStep(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const workflowId = request.params.id;
    const stepId = request.params.stepId;
    if (!workflowId || !stepId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Workflow ID and Step ID are required' }
        }, request);
    }
    logger_1.logger.info("Complete workflow step started", { correlationId, workflowId, stepId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = completeStepSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { comment, outcome, metadata } = value;
        // Get workflow
        const workflow = await database_1.db.readItem('workflows', workflowId, workflowId);
        if (!workflow) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Workflow not found" }
            }, request);
        }
        // Find the step
        const steps = workflow.steps || [];
        const stepIndex = steps.findIndex((step) => step.id === stepId);
        if (stepIndex === -1) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Step not found" }
            }, request);
        }
        const step = steps[stepIndex];
        // Check if user can complete this step
        const canComplete = (step.assigneeId === user.id ||
            workflow.createdBy === user.id ||
            user.roles?.includes('admin'));
        if (!canComplete) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You don't have permission to complete this step" }
            }, request);
        }
        // Check if step is in progress
        if (step.status !== StepStatus.IN_PROGRESS) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Only steps in progress can be completed" }
            }, request);
        }
        // Update step status
        const updatedSteps = [...steps];
        updatedSteps[stepIndex] = {
            ...step,
            status: StepStatus.COMPLETED,
            completedAt: new Date().toISOString(),
            completedBy: user.id,
            comment,
            outcome,
            metadata
        };
        // Determine next step
        const sortedSteps = [...updatedSteps].sort((a, b) => a.order - b.order);
        const currentStepOrder = step.order;
        const nextStep = sortedSteps.find(s => s.order > currentStepOrder && s.status === StepStatus.PENDING);
        let workflowStatus = workflow.status;
        let currentStepId = workflow.currentStepId;
        if (nextStep) {
            // Start next step
            const nextStepIndex = updatedSteps.findIndex(s => s.id === nextStep.id);
            updatedSteps[nextStepIndex] = {
                ...nextStep,
                status: StepStatus.IN_PROGRESS,
                startedAt: new Date().toISOString()
            };
            currentStepId = nextStep.id;
        }
        else {
            // No more steps, complete workflow
            workflowStatus = WorkflowStatus.COMPLETED;
            currentStepId = null;
        }
        // Update workflow
        const updatedWorkflow = {
            ...workflow,
            id: workflow.id,
            status: workflowStatus,
            currentStepId,
            steps: updatedSteps,
            ...(workflowStatus === WorkflowStatus.COMPLETED && {
                completedAt: new Date().toISOString(),
                completedBy: user.id
            })
        };
        await database_1.db.updateItem('workflows', updatedWorkflow);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: workflowStatus === WorkflowStatus.COMPLETED ? "workflow_completed" : "workflow_step_completed",
            userId: user.id,
            organizationId: workflow.organizationId,
            projectId: workflow.projectId,
            workflowId: workflow.id,
            stepId,
            timestamp: new Date().toISOString(),
            details: {
                stepName: step.name,
                outcome,
                comment,
                nextStepId: nextStep?.id,
                nextStepName: nextStep?.name
            }
        });
        logger_1.logger.info("Workflow step completed successfully", {
            correlationId,
            workflowId,
            stepId,
            userId: user.id,
            outcome,
            nextStepId: nextStep?.id,
            workflowCompleted: workflowStatus === WorkflowStatus.COMPLETED
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: "Step completed successfully",
                stepId,
                workflowStatus,
                nextStep: nextStep ? {
                    id: nextStep.id,
                    name: nextStep.name,
                    status: StepStatus.IN_PROGRESS
                } : null,
                workflowCompleted: workflowStatus === WorkflowStatus.COMPLETED
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Complete workflow step failed", {
            correlationId,
            workflowId,
            stepId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('workflow-start', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflows/{id}/start',
    handler: startWorkflow
});
functions_1.app.http('workflow-execution-complete-step', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflows/{id}/steps/{stepId}/complete',
    handler: completeWorkflowStep
});
//# sourceMappingURL=workflow-execution.js.map