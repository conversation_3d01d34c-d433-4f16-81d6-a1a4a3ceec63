/**
 * Organization Create Function
 * Handles creating new organizations with proper tier management
 * Enhanced with notification service and event system integration
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create organization handler
 */
export declare function createOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
