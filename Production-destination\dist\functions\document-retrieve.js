"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.retrieveDocument = retrieveDocument;
exports.listDocuments = listDocuments;
/**
 * Document Retrieve Function
 * Handles document metadata and content retrieval with proper authorization
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
/**
 * Retrieve document handler
 */
async function retrieveDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.id;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Document retrieve started", {
        correlationId,
        documentId
    });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get document metadata from Cosmos DB
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            logger_1.logger.warn("Document not found", {
                correlationId,
                documentId,
                userId: user.id
            });
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check if user has access to this document
        // In a real implementation, you would check permissions based on:
        // - User's organization/tenant
        // - Document permissions
        // - User roles
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            logger_1.logger.warn("Access denied to document", {
                correlationId,
                documentId,
                userId: user.id,
                documentCreatedBy: document.createdBy,
                documentOrgId: document.organizationId
            });
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get content option (metadata-only or full-content)
        const contentOption = request.query.get('content') || "metadata";
        const includeDownloadUrl = contentOption === "full" || contentOption === "url";
        // If download URL is requested, generate it
        if (includeDownloadUrl) {
            try {
                const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
                if (!connectionString) {
                    throw new Error('Azure Storage connection string not configured');
                }
                const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(connectionString);
                const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
                const blobClient = containerClient.getBlobClient(document.blobName);
                // Get blob properties
                const properties = await blobClient.getProperties();
                // Production: Generate proper SAS URL for secure download (valid for 1 hour)
                try {
                    const sasUrl = await blobClient.generateSasUrl({
                        permissions: 'r', // Read permission only
                        expiresOn: new Date(Date.now() + 60 * 60 * 1000), // 1 hour from now
                        contentType: properties.contentType,
                        contentDisposition: `attachment; filename="${document.filename}"`
                    });
                    document.downloadUrl = sasUrl;
                    logger_1.logger.info('SAS URL generated for document download', {
                        documentId: document.id,
                        filename: document.filename,
                        expiresIn: '1 hour'
                    });
                }
                catch (sasError) {
                    logger_1.logger.warn('Failed to generate SAS URL, using blob URL', {
                        documentId: document.id,
                        error: sasError instanceof Error ? sasError.message : String(sasError)
                    });
                    document.downloadUrl = blobClient.url;
                }
                document.contentLength = properties.contentLength;
                document.lastModified = properties.lastModified?.toISOString();
            }
            catch (blobError) {
                logger_1.logger.error("Failed to get blob properties", {
                    correlationId,
                    documentId,
                    blobName: document.blobName,
                    error: blobError instanceof Error ? blobError.message : String(blobError)
                });
                // Don't fail the entire request, just don't include download URL
                document.downloadUrl = null;
            }
        }
        // Remove sensitive fields before returning
        const sanitizedDocument = {
            ...document,
            // Remove internal fields that shouldn't be exposed
            _rid: undefined,
            _self: undefined,
            _etag: undefined,
            _attachments: undefined,
            _ts: undefined
        };
        logger_1.logger.info("Document retrieved successfully", {
            correlationId,
            documentId,
            userId: user.id,
            includeDownloadUrl
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: sanitizedDocument
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document retrieve failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List documents handler
 */
async function listDocuments(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Document list started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get query parameters
        const page = parseInt(request.query.get('page') || '1');
        const limit = Math.min(parseInt(request.query.get('limit') || '20'), 100);
        const organizationId = request.query.get('organizationId');
        const projectId = request.query.get('projectId');
        const documentType = request.query.get('documentType');
        // Build query
        let query = 'SELECT * FROM c WHERE c.isArchived = false';
        const parameters = [];
        // Add filters based on user access
        if (user.tenantId) {
            query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
            parameters.push(user.tenantId, user.id);
        }
        else {
            query += ' AND c.createdBy = @userId';
            parameters.push(user.id);
        }
        if (organizationId) {
            query += ' AND c.organizationId = @orgId';
            parameters.push(organizationId);
        }
        if (projectId) {
            query += ' AND c.projectId = @projectId';
            parameters.push(projectId);
        }
        if (documentType) {
            query += ' AND c.type = @documentType';
            parameters.push(documentType);
        }
        query += ' ORDER BY c.createdAt DESC';
        // Execute query with pagination
        const offset = (page - 1) * limit;
        query += ` OFFSET ${offset} LIMIT ${limit}`;
        const documents = await database_1.db.queryItems('documents', query, parameters);
        logger_1.logger.info("Documents listed successfully", {
            correlationId,
            userId: user.id,
            count: documents.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documents,
                pagination: {
                    page,
                    limit,
                    hasMore: documents.length === limit
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document list failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('document-retrieve', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}',
    handler: retrieveDocument
});
functions_1.app.http('document-list', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents',
    handler: listDocuments
});
//# sourceMappingURL=document-retrieve.js.map