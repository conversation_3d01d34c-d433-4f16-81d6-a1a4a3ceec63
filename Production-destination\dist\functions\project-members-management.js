"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectMembers = getProjectMembers;
exports.addProjectMember = addProjectMember;
exports.updateProjectMember = updateProjectMember;
/**
 * Project Members Management Function
 * Handles project member management, role assignments, and permissions
 * Migrated from old-arch/src/project-service/members/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Project member types and enums
var ProjectRole;
(function (ProjectRole) {
    ProjectRole["OWNER"] = "OWNER";
    ProjectRole["MANAGER"] = "MANAGER";
    ProjectRole["CONTRIBUTOR"] = "CONTRIBUTOR";
    ProjectRole["VIEWER"] = "VIEWER";
})(ProjectRole || (ProjectRole = {}));
var MemberStatus;
(function (MemberStatus) {
    MemberStatus["ACTIVE"] = "ACTIVE";
    MemberStatus["INVITED"] = "INVITED";
    MemberStatus["SUSPENDED"] = "SUSPENDED";
    MemberStatus["REMOVED"] = "REMOVED";
})(MemberStatus || (MemberStatus = {}));
// Validation schemas
const addMemberSchema = Joi.object({
    projectId: Joi.string().uuid().required(),
    userId: Joi.string().uuid().optional(),
    email: Joi.string().email().optional(),
    role: Joi.string().valid(...Object.values(ProjectRole)).default(ProjectRole.CONTRIBUTOR),
    permissions: Joi.array().items(Joi.string()).optional(),
    message: Joi.string().max(500).optional(),
    expiresAt: Joi.string().isoDate().optional()
}).xor('userId', 'email');
const updateMemberSchema = Joi.object({
    projectId: Joi.string().uuid().required(),
    memberId: Joi.string().uuid().required(),
    role: Joi.string().valid(...Object.values(ProjectRole)).optional(),
    permissions: Joi.array().items(Joi.string()).optional(),
    status: Joi.string().valid(...Object.values(MemberStatus)).optional()
});
const removeMemberSchema = Joi.object({
    projectId: Joi.string().uuid().required(),
    memberId: Joi.string().uuid().required(),
    reason: Joi.string().max(500).optional()
});
/**
 * Get project members handler
 */
async function getProjectMembers(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const projectId = request.params.projectId;
    logger_1.logger.info("Get project members started", { correlationId, projectId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!projectId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project ID is required" }
            }, request);
        }
        // Verify project exists and user has access
        const project = await database_1.db.readItem('projects', projectId, projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        // Check if user has permission to view project members
        const hasAccess = await checkProjectAccess(projectId, user.id, 'VIEW');
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to project" }
            }, request);
        }
        // Get project members
        const membersQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.status != @removedStatus';
        const members = await database_1.db.queryItems('project-members', membersQuery, [projectId, MemberStatus.REMOVED]);
        // Enrich members with user information
        const enrichedMembers = await Promise.all(members.map(async (member) => {
            const userInfo = await database_1.db.readItem('users', member.userId, member.userId);
            return {
                id: member.id,
                userId: member.userId,
                projectId: member.projectId,
                role: member.role,
                permissions: member.permissions || [],
                status: member.status,
                joinedAt: member.createdAt,
                expiresAt: member.expiresAt,
                addedBy: member.addedBy,
                user: userInfo ? {
                    id: userInfo.id,
                    email: userInfo.email,
                    displayName: userInfo.displayName,
                    firstName: userInfo.firstName,
                    lastName: userInfo.lastName,
                    avatarUrl: userInfo.avatarUrl
                } : null
            };
        }));
        // Parse query parameters for filtering
        const url = new URL(request.url);
        const roleFilter = url.searchParams.get('role');
        const statusFilter = url.searchParams.get('status');
        // Apply filters
        let filteredMembers = enrichedMembers;
        if (roleFilter) {
            filteredMembers = filteredMembers.filter(m => m.role === roleFilter);
        }
        if (statusFilter) {
            filteredMembers = filteredMembers.filter(m => m.status === statusFilter);
        }
        logger_1.logger.info("Project members retrieved successfully", {
            correlationId,
            projectId,
            memberCount: filteredMembers.length,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                projectId,
                members: filteredMembers,
                totalCount: filteredMembers.length,
                filters: {
                    role: roleFilter,
                    status: statusFilter
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get project members failed", {
            correlationId,
            projectId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Add project member handler
 */
async function addProjectMember(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Add project member started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = addMemberSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const memberRequest = value;
        // Verify project exists and user has permission to add members
        const project = await database_1.db.readItem('projects', memberRequest.projectId, memberRequest.projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        const hasPermission = await checkProjectAccess(memberRequest.projectId, user.id, 'MANAGE_MEMBERS');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to add project members" }
            }, request);
        }
        // Resolve user ID if email is provided
        let targetUserId = memberRequest.userId;
        if (memberRequest.email && !targetUserId) {
            const userQuery = 'SELECT * FROM c WHERE c.email = @email';
            const users = await database_1.db.queryItems('users', userQuery, [memberRequest.email.toLowerCase()]);
            if (users.length > 0) {
                targetUserId = users[0].id;
            }
        }
        // Check if user is already a member
        if (targetUserId) {
            const existingMemberQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status != @removedStatus';
            const existingMembers = await database_1.db.queryItems('project-members', existingMemberQuery, [memberRequest.projectId, targetUserId, MemberStatus.REMOVED]);
            if (existingMembers.length > 0) {
                return (0, cors_1.addCorsHeaders)({
                    status: 409,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "User is already a member of this project" }
                }, request);
            }
        }
        // Check member limits for organization tier
        const projectData = project;
        if (await isMemberLimitReached(memberRequest.projectId, projectData.organizationId)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project member limit reached for this organization tier" }
            }, request);
        }
        // Create project member record
        const memberId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const projectMember = {
            id: memberId,
            projectId: memberRequest.projectId,
            userId: targetUserId,
            email: memberRequest.email,
            role: memberRequest.role,
            permissions: memberRequest.permissions || getDefaultRolePermissions(memberRequest.role),
            status: targetUserId ? MemberStatus.ACTIVE : MemberStatus.INVITED,
            expiresAt: memberRequest.expiresAt,
            addedBy: user.id,
            createdAt: now,
            updatedAt: now,
            organizationId: projectData.organizationId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('project-members', projectMember);
        // Update project member count
        const updatedProject = {
            ...projectData,
            memberIds: [...(projectData.memberIds || []), targetUserId || memberRequest.email],
            metadata: {
                ...projectData.metadata,
                memberCount: (projectData.metadata?.memberCount || 0) + 1
            },
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('projects', updatedProject);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "project_member_added",
            userId: user.id,
            organizationId: projectData.organizationId,
            projectId: memberRequest.projectId,
            timestamp: now,
            details: {
                memberId,
                targetUserId,
                targetEmail: memberRequest.email,
                role: memberRequest.role,
                projectName: projectData.name,
                isInvitation: !targetUserId
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ProjectMemberAdded',
            aggregateId: memberId,
            aggregateType: 'ProjectMember',
            version: 1,
            data: {
                member: projectMember,
                project: projectData,
                addedBy: user.id
            },
            userId: user.id,
            organizationId: projectData.organizationId,
            tenantId: user.tenantId
        });
        // Send notification
        if (targetUserId) {
            await notification_1.notificationService.sendNotification({
                userId: targetUserId,
                type: 'PROJECT_MEMBER_ADDED',
                title: 'Added to project',
                message: `You have been added to the project "${projectData.name}" as ${memberRequest.role}.`,
                priority: 'normal',
                metadata: {
                    projectId: memberRequest.projectId,
                    projectName: projectData.name,
                    role: memberRequest.role,
                    addedBy: user.id,
                    organizationId: projectData.organizationId
                },
                organizationId: projectData.organizationId,
                projectId: memberRequest.projectId
            });
        }
        logger_1.logger.info("Project member added successfully", {
            correlationId,
            memberId,
            projectId: memberRequest.projectId,
            targetUserId,
            targetEmail: memberRequest.email,
            role: memberRequest.role,
            addedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: memberId,
                projectId: memberRequest.projectId,
                projectName: projectData.name,
                userId: targetUserId,
                email: memberRequest.email,
                role: memberRequest.role,
                permissions: projectMember.permissions,
                status: projectMember.status,
                addedBy: user.id,
                addedAt: now,
                message: "Project member added successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Add project member failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update project member handler
 */
async function updateProjectMember(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update project member started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateMemberSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const updateRequest = value;
        // Verify project and member exist
        const [project, member] = await Promise.all([
            database_1.db.readItem('projects', updateRequest.projectId, updateRequest.projectId),
            database_1.db.readItem('project-members', updateRequest.memberId, updateRequest.memberId)
        ]);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        if (!member) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project member not found" }
            }, request);
        }
        // Check permissions
        const hasPermission = await checkProjectAccess(updateRequest.projectId, user.id, 'MANAGE_MEMBERS');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to update project members" }
            }, request);
        }
        const memberData = member;
        const projectData = project;
        const now = new Date().toISOString();
        // Update member
        const updatedMember = {
            ...memberData,
            role: updateRequest.role || memberData.role,
            permissions: updateRequest.permissions || memberData.permissions,
            status: updateRequest.status || memberData.status,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('project-members', updatedMember);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "project_member_updated",
            userId: user.id,
            organizationId: projectData.organizationId,
            projectId: updateRequest.projectId,
            timestamp: now,
            details: {
                memberId: updateRequest.memberId,
                targetUserId: memberData.userId,
                previousRole: memberData.role,
                newRole: updatedMember.role,
                previousStatus: memberData.status,
                newStatus: updatedMember.status,
                projectName: projectData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ProjectMemberUpdated',
            aggregateId: updateRequest.memberId,
            aggregateType: 'ProjectMember',
            version: 1,
            data: {
                member: updatedMember,
                previousData: memberData,
                project: projectData,
                updatedBy: user.id
            },
            userId: user.id,
            organizationId: projectData.organizationId,
            tenantId: user.tenantId
        });
        // Send notification if role changed
        if (updateRequest.role && updateRequest.role !== memberData.role && memberData.userId) {
            await notification_1.notificationService.sendNotification({
                userId: memberData.userId,
                type: 'PROJECT_ROLE_UPDATED',
                title: 'Project role updated',
                message: `Your role in project "${projectData.name}" has been updated to ${updateRequest.role}.`,
                priority: 'normal',
                metadata: {
                    projectId: updateRequest.projectId,
                    projectName: projectData.name,
                    previousRole: memberData.role,
                    newRole: updateRequest.role,
                    updatedBy: user.id,
                    organizationId: projectData.organizationId
                },
                organizationId: projectData.organizationId,
                projectId: updateRequest.projectId
            });
        }
        logger_1.logger.info("Project member updated successfully", {
            correlationId,
            memberId: updateRequest.memberId,
            projectId: updateRequest.projectId,
            targetUserId: memberData.userId,
            roleChanged: updateRequest.role !== memberData.role,
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: updatedMember.id,
                projectId: updateRequest.projectId,
                projectName: projectData.name,
                userId: updatedMember.userId,
                role: updatedMember.role,
                permissions: updatedMember.permissions,
                status: updatedMember.status,
                updatedBy: user.id,
                updatedAt: now,
                message: "Project member updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update project member failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkProjectAccess(projectId, userId, action) {
    try {
        // Check if user is a member of the project
        const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('project-members', membershipQuery, [projectId, userId, MemberStatus.ACTIVE]);
        if (memberships.length === 0) {
            return false;
        }
        const membership = memberships[0];
        // Check role-based permissions
        switch (action) {
            case 'VIEW':
                return true; // All members can view
            case 'MANAGE_MEMBERS':
                return membership.role === ProjectRole.OWNER || membership.role === ProjectRole.MANAGER;
            default:
                return false;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to check project access', { error, projectId, userId, action });
        return false;
    }
}
async function isMemberLimitReached(projectId, organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization)
            return false;
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Get current member count
        const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId AND c.status != @removedStatus';
        const result = await database_1.db.queryItems('project-members', memberCountQuery, [projectId, MemberStatus.REMOVED]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 5,
            'PROFESSIONAL': 50,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check member limit', { error, projectId, organizationId });
        return false;
    }
}
function getDefaultRolePermissions(role) {
    switch (role) {
        case ProjectRole.OWNER:
            return ['*']; // All permissions
        case ProjectRole.MANAGER:
            return ['manage_project', 'manage_members', 'create_workflow', 'manage_workflow'];
        case ProjectRole.CONTRIBUTOR:
            return ['view_project', 'upload_document', 'update_document', 'execute_workflow'];
        case ProjectRole.VIEWER:
            return ['view_project', 'view_document', 'comment_document'];
        default:
            return ['view_project'];
    }
}
// Register functions
functions_1.app.http('project-members-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/{projectId}/members',
    handler: getProjectMembers
});
functions_1.app.http('project-members-add', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/members',
    handler: addProjectMember
});
functions_1.app.http('project-members-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/members/update',
    handler: updateProjectMember
});
//# sourceMappingURL=project-members-management.js.map