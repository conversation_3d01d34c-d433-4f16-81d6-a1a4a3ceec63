"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendNotification = sendNotification;
/**
 * Notification Send Function
 * Handles sending notifications to users through multiple channels
 * Enhanced with notification service integration
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
// Notification types enum
var NotificationType;
(function (NotificationType) {
    NotificationType["DOCUMENT_SHARED"] = "DOCUMENT_SHARED";
    NotificationType["DOCUMENT_COMMENTED"] = "DOCUMENT_COMMENTED";
    NotificationType["WORKFLOW_ASSIGNED"] = "WORKFLOW_ASSIGNED";
    NotificationType["WORKFLOW_COMPLETED"] = "WORKFLOW_COMPLETED";
    NotificationType["PROJECT_INVITATION"] = "PROJECT_INVITATION";
    NotificationType["ORGANIZATION_INVITATION"] = "ORGANIZATION_INVITATION";
    NotificationType["SYSTEM_ANNOUNCEMENT"] = "SYSTEM_ANNOUNCEMENT";
    NotificationType["DOCUMENT_PROCESSED"] = "DOCUMENT_PROCESSED";
    NotificationType["APPROVAL_REQUEST"] = "APPROVAL_REQUEST";
})(NotificationType || (NotificationType = {}));
// Notification priority enum
var NotificationPriority;
(function (NotificationPriority) {
    NotificationPriority["LOW"] = "LOW";
    NotificationPriority["MEDIUM"] = "MEDIUM";
    NotificationPriority["HIGH"] = "HIGH";
    NotificationPriority["URGENT"] = "URGENT";
})(NotificationPriority || (NotificationPriority = {}));
// Validation schema
const sendNotificationSchema = Joi.object({
    recipientIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
    type: Joi.string().valid(...Object.values(NotificationType)).required(),
    title: Joi.string().required().max(200),
    message: Joi.string().required().max(1000),
    priority: Joi.string().valid(...Object.values(NotificationPriority)).default(NotificationPriority.MEDIUM),
    actionUrl: Joi.string().uri().optional(),
    actionText: Joi.string().max(50).optional(),
    metadata: Joi.object().optional(),
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional(),
    documentId: Joi.string().uuid().optional(),
    workflowId: Joi.string().uuid().optional(),
    expiresAt: Joi.date().iso().optional(),
    sendEmail: Joi.boolean().default(false),
    sendPush: Joi.boolean().default(true)
});
/**
 * Send notification handler
 */
async function sendNotification(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Send notification started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = sendNotificationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { recipientIds, type, title, message, priority, actionUrl, actionText, metadata, organizationId, projectId, documentId, workflowId, expiresAt, sendEmail, sendPush } = value;
        // Validate recipients exist and user has permission to send to them
        const validRecipients = [];
        for (const recipientId of recipientIds) {
            const recipient = await database_1.db.readItem('users', recipientId, recipientId);
            if (recipient) {
                // Check if user can send notifications to this recipient
                // For now, allow if they're in the same organization or it's a system notification
                if (organizationId) {
                    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
                    const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [recipientId, organizationId, 'active']);
                    if (memberships.length > 0) {
                        validRecipients.push(recipientId);
                    }
                }
                else {
                    // Allow system notifications or direct notifications
                    validRecipients.push(recipientId);
                }
            }
        }
        if (validRecipients.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "No valid recipients found" }
            }, request);
        }
        // Send notifications using the notification service
        const notificationResults = [];
        const notificationIds = [];
        for (const recipientId of validRecipients) {
            try {
                // Prepare notification channels
                const channels = [];
                if (sendEmail) {
                    channels.push(notification_1.NotificationChannelType.EMAIL);
                }
                if (sendPush) {
                    channels.push(notification_1.NotificationChannelType.PUSH);
                }
                // Prepare notification request
                const notificationRequest = {
                    userId: recipientId,
                    type,
                    title,
                    message,
                    resourceId: documentId || workflowId || projectId,
                    resourceType: documentId ? 'document' : workflowId ? 'workflow' : projectId ? 'project' : undefined,
                    priority: priority.toLowerCase(),
                    metadata: {
                        ...metadata,
                        actionUrl,
                        actionText,
                        expiresAt,
                        senderId: user.id
                    },
                    channels: channels.length > 0 ? channels : undefined,
                    organizationId,
                    projectId
                };
                // Send notification through the service
                const result = await notification_1.notificationService.sendNotification(notificationRequest);
                if (result.success && result.notificationId) {
                    notificationIds.push(result.notificationId);
                }
                notificationResults.push({
                    recipientId,
                    success: result.success,
                    notificationId: result.notificationId,
                    channels: result.channels
                });
            }
            catch (error) {
                logger_1.logger.error("Failed to send notification to recipient", {
                    error,
                    recipientId,
                    correlationId
                });
                notificationResults.push({
                    recipientId,
                    success: false,
                    error: error instanceof Error ? error.message : String(error)
                });
            }
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "notification_sent",
            userId: user.id,
            organizationId,
            projectId,
            timestamp: new Date().toISOString(),
            details: {
                notificationType: type,
                recipientCount: validRecipients.length,
                priority,
                title
            },
            tenantId: user.tenantId
        });
        const successCount = notificationResults.filter(r => r.success).length;
        const failureCount = notificationResults.length - successCount;
        logger_1.logger.info("Notifications sent successfully", {
            correlationId,
            senderId: user.id,
            recipientCount: validRecipients.length,
            successCount,
            failureCount,
            type,
            priority
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                success: successCount > 0,
                notificationIds,
                recipientCount: validRecipients.length,
                successCount,
                failureCount,
                results: notificationResults,
                message: `${successCount} notifications sent successfully${failureCount > 0 ? `, ${failureCount} failed` : ''}`
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Send notification failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('notification-send', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/send',
    handler: sendNotification
});
//# sourceMappingURL=notification-send.js.map