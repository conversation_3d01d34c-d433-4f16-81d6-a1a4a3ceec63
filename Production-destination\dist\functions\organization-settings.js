"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getOrganizationSettings = getOrganizationSettings;
exports.updateOrganizationSettings = updateOrganizationSettings;
/**
 * Organization Settings Function
 * Handles organization settings, configuration, and policy management
 * Migrated from old-arch/src/organization-service/settings/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Validation schemas
const updateOrganizationSettingsSchema = Joi.object({
    organizationId: Joi.string().uuid().required(),
    general: Joi.object({
        allowPublicProjects: Joi.boolean().optional(),
        requireApprovalForNewMembers: Joi.boolean().optional(),
        enableGuestAccess: Joi.boolean().optional(),
        defaultProjectVisibility: Joi.string().valid('private', 'organization', 'public').optional(),
        defaultDocumentRetentionDays: Joi.number().min(1).max(3650).optional(),
        enableAuditLog: Joi.boolean().optional()
    }).optional(),
    security: Joi.object({
        enforcePasswordPolicy: Joi.boolean().optional(),
        passwordPolicy: Joi.object({
            minLength: Joi.number().min(6).max(128).optional(),
            requireUppercase: Joi.boolean().optional(),
            requireLowercase: Joi.boolean().optional(),
            requireNumbers: Joi.boolean().optional(),
            requireSpecialChars: Joi.boolean().optional(),
            maxAge: Joi.number().min(1).max(365).optional()
        }).optional(),
        requireTwoFactor: Joi.boolean().optional(),
        sessionTimeoutMinutes: Joi.number().min(5).max(1440).optional(),
        allowedIpRanges: Joi.array().items(Joi.string()).optional(),
        enableDeviceTracking: Joi.boolean().optional(),
        requireApprovalForSensitiveActions: Joi.boolean().optional()
    }).optional(),
    features: Joi.object({
        aiAnalysis: Joi.boolean().optional(),
        advancedWorkflows: Joi.boolean().optional(),
        bulkProcessing: Joi.boolean().optional(),
        apiAccess: Joi.boolean().optional(),
        customBranding: Joi.boolean().optional(),
        advancedAnalytics: Joi.boolean().optional(),
        integrations: Joi.boolean().optional(),
        webhooks: Joi.boolean().optional()
    }).optional(),
    limits: Joi.object({
        maxProjects: Joi.number().min(1).optional(),
        maxMembers: Joi.number().min(1).optional(),
        maxStorageGB: Joi.number().min(1).optional(),
        maxFileSize: Joi.number().min(1).optional(),
        maxApiCallsPerMonth: Joi.number().min(1).optional(),
        maxWorkflowExecutionsPerMonth: Joi.number().min(1).optional()
    }).optional(),
    branding: Joi.object({
        logoUrl: Joi.string().uri().optional(),
        primaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
        secondaryColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
        accentColor: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
        customDomain: Joi.string().domain().optional(),
        organizationName: Joi.string().max(100).optional()
    }).optional(),
    notifications: Joi.object({
        enableEmailNotifications: Joi.boolean().optional(),
        enableSlackIntegration: Joi.boolean().optional(),
        enableTeamsIntegration: Joi.boolean().optional(),
        defaultNotificationSettings: Joi.object({
            documentUploaded: Joi.boolean().optional(),
            documentProcessed: Joi.boolean().optional(),
            workflowAssigned: Joi.boolean().optional(),
            memberAdded: Joi.boolean().optional(),
            projectCreated: Joi.boolean().optional()
        }).optional(),
        adminNotifications: Joi.object({
            newMemberRequests: Joi.boolean().optional(),
            securityAlerts: Joi.boolean().optional(),
            systemUpdates: Joi.boolean().optional(),
            usageAlerts: Joi.boolean().optional()
        }).optional()
    }).optional(),
    integrations: Joi.object({
        enabledProviders: Joi.array().items(Joi.string()).optional(),
        ssoProvider: Joi.string().optional(),
        ssoConfiguration: Joi.object().optional(),
        webhookEndpoints: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            url: Joi.string().uri().required(),
            events: Joi.array().items(Joi.string()).required(),
            secret: Joi.string().optional()
        })).optional()
    }).optional(),
    compliance: Joi.object({
        dataRetentionPolicy: Joi.object({
            enabled: Joi.boolean().optional(),
            retentionDays: Joi.number().min(1).max(3650).optional(),
            archiveBeforeDelete: Joi.boolean().optional(),
            autoDeleteAfterRetention: Joi.boolean().optional()
        }).optional(),
        auditSettings: Joi.object({
            logAllActions: Joi.boolean().optional(),
            logRetentionDays: Joi.number().min(1).max(2555).optional(), // 7 years max
            enableRealTimeAlerts: Joi.boolean().optional()
        }).optional(),
        privacySettings: Joi.object({
            allowDataExport: Joi.boolean().optional(),
            allowDataDeletion: Joi.boolean().optional(),
            requireConsentForAnalytics: Joi.boolean().optional()
        }).optional()
    }).optional()
});
/**
 * Get organization settings handler
 */
async function getOrganizationSettings(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const organizationId = request.params.organizationId;
    logger_1.logger.info("Get organization settings started", { correlationId, organizationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!organizationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization ID is required" }
            }, request);
        }
        // Check if user has permission to view organization settings
        const hasPermission = await checkOrganizationSettingsPermission(organizationId, user.id, 'VIEW');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization settings" }
            }, request);
        }
        // Get organization
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        const organizationData = organization;
        // Get organization settings or return defaults
        const settings = organizationData.settings || getDefaultOrganizationSettings(organizationData.tier);
        logger_1.logger.info("Organization settings retrieved successfully", {
            correlationId,
            organizationId,
            userId: user.id,
            hasCustomSettings: !!organizationData.settings
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                organizationId,
                organizationName: organizationData.name,
                tier: organizationData.tier,
                settings,
                lastUpdated: organizationData.updatedAt,
                isDefault: !organizationData.settings
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get organization settings failed", {
            correlationId,
            organizationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update organization settings handler
 */
async function updateOrganizationSettings(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update organization settings started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateOrganizationSettingsSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const settingsUpdate = value;
        const organizationId = settingsUpdate.organizationId;
        // Check if user has permission to update organization settings
        const hasPermission = await checkOrganizationSettingsPermission(organizationId, user.id, 'UPDATE');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to update organization settings" }
            }, request);
        }
        // Get organization
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        const organizationData = organization;
        const now = new Date().toISOString();
        // Get current settings or defaults
        const currentSettings = organizationData.settings || getDefaultOrganizationSettings(organizationData.tier);
        // Validate settings against organization tier
        const validationResult = validateSettingsForTier(settingsUpdate, organizationData.tier);
        if (!validationResult.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Settings validation failed',
                    message: validationResult.errors.join(', ')
                }
            }, request);
        }
        // Deep merge settings
        const updatedSettings = deepMergeSettings(currentSettings, settingsUpdate);
        // Update organization with new settings
        const updatedOrganization = {
            ...organizationData,
            settings: updatedSettings,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('organizations', updatedOrganization);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "organization_settings_updated",
            userId: user.id,
            organizationId,
            timestamp: now,
            details: {
                updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
                organizationName: organizationData.name,
                tier: organizationData.tier
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'OrganizationSettingsUpdated',
            aggregateId: organizationId,
            aggregateType: 'Organization',
            version: 1,
            data: {
                organization: organizationData,
                previousSettings: currentSettings,
                newSettings: updatedSettings,
                updatedBy: user.id
            },
            userId: user.id,
            organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Organization settings updated successfully", {
            correlationId,
            organizationId,
            updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                organizationId,
                organizationName: organizationData.name,
                settings: updatedSettings,
                updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'organizationId'),
                updatedAt: now,
                updatedBy: user.id,
                message: "Organization settings updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update organization settings failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationSettingsPermission(organizationId, userId, action) {
    try {
        // Check if user is an owner or admin of the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        if (memberships.length === 0) {
            return false;
        }
        const membership = memberships[0];
        switch (action) {
            case 'VIEW':
                return ['OWNER', 'ADMIN', 'MEMBER'].includes(membership.role);
            case 'UPDATE':
                return ['OWNER', 'ADMIN'].includes(membership.role);
            default:
                return false;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization settings permission', { error, organizationId, userId, action });
        return false;
    }
}
function getDefaultOrganizationSettings(tier) {
    const tierLimits = getTierLimits(tier);
    return {
        general: {
            allowPublicProjects: false,
            requireApprovalForNewMembers: true,
            enableGuestAccess: false,
            defaultProjectVisibility: 'private',
            defaultDocumentRetentionDays: 365,
            enableAuditLog: tier !== 'FREE'
        },
        security: {
            enforcePasswordPolicy: tier !== 'FREE',
            passwordPolicy: {
                minLength: 8,
                requireUppercase: true,
                requireLowercase: true,
                requireNumbers: true,
                requireSpecialChars: false,
                maxAge: 90
            },
            requireTwoFactor: false,
            sessionTimeoutMinutes: 480, // 8 hours
            allowedIpRanges: [],
            enableDeviceTracking: tier === 'ENTERPRISE',
            requireApprovalForSensitiveActions: tier === 'ENTERPRISE'
        },
        features: {
            aiAnalysis: tier !== 'FREE',
            advancedWorkflows: tier === 'ENTERPRISE',
            bulkProcessing: tier !== 'FREE',
            apiAccess: tier !== 'FREE',
            customBranding: tier === 'ENTERPRISE',
            advancedAnalytics: tier !== 'FREE',
            integrations: tier !== 'FREE',
            webhooks: tier !== 'FREE'
        },
        limits: tierLimits,
        branding: {
            primaryColor: '#007bff',
            secondaryColor: '#6c757d',
            accentColor: '#28a745'
        },
        notifications: {
            enableEmailNotifications: true,
            enableSlackIntegration: tier !== 'FREE',
            enableTeamsIntegration: tier !== 'FREE',
            defaultNotificationSettings: {
                documentUploaded: true,
                documentProcessed: true,
                workflowAssigned: true,
                memberAdded: true,
                projectCreated: true
            },
            adminNotifications: {
                newMemberRequests: true,
                securityAlerts: true,
                systemUpdates: true,
                usageAlerts: true
            }
        },
        integrations: {
            enabledProviders: [],
            webhookEndpoints: []
        },
        compliance: {
            dataRetentionPolicy: {
                enabled: false,
                retentionDays: 365,
                archiveBeforeDelete: true,
                autoDeleteAfterRetention: false
            },
            auditSettings: {
                logAllActions: tier !== 'FREE',
                logRetentionDays: tier === 'ENTERPRISE' ? 2555 : 365, // 7 years for enterprise
                enableRealTimeAlerts: tier === 'ENTERPRISE'
            },
            privacySettings: {
                allowDataExport: true,
                allowDataDeletion: true,
                requireConsentForAnalytics: false
            }
        }
    };
}
function getTierLimits(tier) {
    switch (tier) {
        case 'FREE':
            return {
                maxProjects: 3,
                maxMembers: 5,
                maxStorageGB: 1,
                maxFileSize: 10, // MB
                maxApiCallsPerMonth: 1000,
                maxWorkflowExecutionsPerMonth: 100
            };
        case 'PROFESSIONAL':
            return {
                maxProjects: 50,
                maxMembers: 50,
                maxStorageGB: 100,
                maxFileSize: 100, // MB
                maxApiCallsPerMonth: 50000,
                maxWorkflowExecutionsPerMonth: 5000
            };
        case 'ENTERPRISE':
            return {
                maxProjects: -1, // Unlimited
                maxMembers: -1, // Unlimited
                maxStorageGB: 1000,
                maxFileSize: 1000, // MB
                maxApiCallsPerMonth: -1, // Unlimited
                maxWorkflowExecutionsPerMonth: -1 // Unlimited
            };
        default:
            return getTierLimits('FREE');
    }
}
function validateSettingsForTier(settings, tier) {
    const errors = [];
    const tierLimits = getTierLimits(tier);
    // Validate feature access based on tier
    if (settings.features) {
        if (tier === 'FREE') {
            if (settings.features.advancedWorkflows) {
                errors.push('Advanced workflows not available in FREE tier');
            }
            if (settings.features.customBranding) {
                errors.push('Custom branding not available in FREE tier');
            }
            if (settings.features.apiAccess) {
                errors.push('API access not available in FREE tier');
            }
        }
    }
    // Validate limits
    if (settings.limits) {
        Object.entries(settings.limits).forEach(([key, value]) => {
            const tierLimit = tierLimits[key];
            if (tierLimit !== -1 && value > tierLimit) {
                errors.push(`${key} exceeds tier limit of ${tierLimit}`);
            }
        });
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
function deepMergeSettings(current, updates) {
    const result = { ...current };
    for (const key in updates) {
        if (key === 'organizationId')
            continue; // Skip organizationId
        if (updates[key] !== null && typeof updates[key] === 'object' && !Array.isArray(updates[key])) {
            result[key] = deepMergeSettings(current[key] || {}, updates[key]);
        }
        else {
            result[key] = updates[key];
        }
    }
    return result;
}
// Register functions
functions_1.app.http('organization-settings-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/{organizationId}/settings',
    handler: getOrganizationSettings
});
functions_1.app.http('organization-settings-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/settings',
    handler: updateOrganizationSettings
});
//# sourceMappingURL=organization-settings.js.map