"use strict";
/**
 * Event Grid Handlers for Azure Functions
 * Handles various Event Grid events and publishes events to Event Grid
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.EventType = void 0;
exports.publishEvent = publishEvent;
exports.validateEventGridEvent = validateEventGridEvent;
exports.processEventGridEventEnhanced = processEventGridEventEnhanced;
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_grid_integration_1 = require("../shared/services/event-grid-integration");
/**
 * Event types enum
 */
var EventType;
(function (EventType) {
    EventType["DOCUMENT_UPLOADED"] = "Document.Uploaded";
    EventType["DOCUMENT_PROCESSED"] = "Document.Processed";
    EventType["DOCUMENT_SHARED"] = "Document.Shared";
    EventType["WORKFLOW_STARTED"] = "Workflow.Started";
    EventType["WORKFLOW_COMPLETED"] = "Workflow.Completed";
    EventType["USER_REGISTERED"] = "User.Registered";
    EventType["NOTIFICATION_SENT"] = "Notification.Sent";
    EventType["ANALYTICS_GENERATED"] = "Analytics.Generated";
    EventType["SYSTEM_HEALTH_CHECK"] = "System.HealthCheck";
    EventType["PERFORMANCE_ALERT"] = "Performance.Alert";
})(EventType || (exports.EventType = EventType = {}));
/**
 * Publish event to Event Grid using enhanced integration service
 */
async function publishEvent(eventType, subject, data, dataVersion = '1.0') {
    try {
        const eventId = await event_grid_integration_1.eventGridIntegration.publishEvent({
            eventType: eventType,
            subject: subject,
            data: data,
            dataVersion: dataVersion
        });
        if (eventId) {
            logger_1.logger.info('Event published to Event Grid', {
                eventType,
                subject,
                eventId
            });
        }
        else {
            logger_1.logger.warn('Event was filtered or failed to publish', {
                eventType,
                subject
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to publish event to Event Grid', {
            eventType,
            subject,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error;
    }
}
/**
 * Handle Event Grid webhook events
 */
async function handleEventGridWebhook(request, _context) {
    try {
        const body = await request.text();
        const events = JSON.parse(body);
        // Handle validation handshake
        if (events.length === 1 && events[0].eventType === 'Microsoft.EventGrid.SubscriptionValidationEvent') {
            const validationCode = events[0].data.validationCode;
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { validationResponse: validationCode }
            }, request);
        }
        // Process events
        for (const event of events) {
            await processEventGridEvent(event);
        }
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { message: 'Events processed successfully' }
        }, request);
    }
    catch (error) {
        logger_1.logger.error('Error handling Event Grid webhook', {
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Failed to process events' }
        }, request);
    }
}
/**
 * Process individual Event Grid event
 */
async function processEventGridEvent(event) {
    logger_1.logger.info('Processing Event Grid event', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id
    });
    try {
        switch (event.eventType) {
            // Azure Storage Events
            case 'Microsoft.Storage.BlobCreated':
                await handleBlobCreatedEvent(event);
                break;
            case 'Microsoft.Storage.BlobDeleted':
                await handleBlobDeletedEvent(event);
                break;
            // Document Events
            case EventType.DOCUMENT_UPLOADED:
                await handleDocumentUploadedEvent(event);
                break;
            case EventType.DOCUMENT_PROCESSED:
                await handleDocumentProcessedEvent(event);
                break;
            case EventType.DOCUMENT_SHARED:
                await handleDocumentSharedEvent(event);
                break;
            // Workflow Events
            case EventType.WORKFLOW_STARTED:
                await handleWorkflowStartedEvent(event);
                break;
            case EventType.WORKFLOW_COMPLETED:
                await handleWorkflowCompletedEvent(event);
                break;
            // User Events
            case EventType.USER_REGISTERED:
                await handleUserRegisteredEvent(event);
                break;
            // System Events
            case EventType.SYSTEM_HEALTH_CHECK:
                await handleSystemHealthCheckEvent(event);
                break;
            case EventType.PERFORMANCE_ALERT:
                await handlePerformanceAlertEvent(event);
                break;
            case EventType.ANALYTICS_GENERATED:
                await handleAnalyticsGeneratedEvent(event);
                break;
            case EventType.NOTIFICATION_SENT:
                await handleNotificationSentEvent(event);
                break;
            default:
                logger_1.logger.info('Unhandled event type', { eventType: event.eventType });
        }
    }
    catch (error) {
        logger_1.logger.error('Error processing event', {
            eventType: event.eventType,
            eventId: event.id,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle blob created event
 */
async function handleBlobCreatedEvent(event) {
    const blobUrl = event.subject;
    const blobName = blobUrl.split('/').pop();
    logger_1.logger.info('Blob created event received', { blobName, blobUrl });
    // Trigger document processing if it's in the documents container
    if (blobUrl.includes('/documents/')) {
        await publishEvent(EventType.DOCUMENT_UPLOADED, `documents/${blobName}`, {
            blobName,
            blobUrl,
            timestamp: new Date().toISOString(),
            autoProcessing: true
        });
    }
}
/**
 * Handle blob deleted event
 */
async function handleBlobDeletedEvent(event) {
    const blobUrl = event.subject;
    const blobName = blobUrl.split('/').pop();
    logger_1.logger.info('Blob deleted event received', { blobName, blobUrl });
    // Update document status in database
    try {
        const documents = await database_1.db.queryItems('documents', 'SELECT * FROM c WHERE c.blobName = @blobName', [blobName]);
        for (const doc of documents) {
            await database_1.db.updateItem('documents', {
                ...doc,
                status: 'deleted',
                deletedAt: new Date().toISOString()
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error updating document status after blob deletion', {
            blobName,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle document uploaded event
 */
async function handleDocumentUploadedEvent(event) {
    const { blobName, documentId } = event.data;
    logger_1.logger.info('Document uploaded event received', { blobName, documentId });
    // Trigger AI processing, thumbnail generation, etc.
    // This would typically trigger other functions or services
}
/**
 * Handle workflow completed event
 */
async function handleWorkflowCompletedEvent(event) {
    const { workflowId, status, completedBy } = event.data;
    logger_1.logger.info('Workflow completed event received', { workflowId, status });
    // Send notifications, update analytics, etc.
    await publishEvent(EventType.NOTIFICATION_SENT, `workflows/${workflowId}/completion`, {
        workflowId,
        status,
        completedBy,
        timestamp: new Date().toISOString()
    });
}
/**
 * Handle document processed event
 */
async function handleDocumentProcessedEvent(event) {
    const { documentId, processingResults, processingTime } = event.data;
    logger_1.logger.info('Document processed event received', { documentId, processingTime });
    try {
        // Update document status in database
        const documents = await database_1.db.queryItems('documents', 'SELECT * FROM c WHERE c.id = @documentId', [{ name: '@documentId', value: documentId }]);
        if (documents.length > 0) {
            const document = documents[0];
            await database_1.db.updateItem('documents', {
                ...document,
                status: 'processed',
                processedAt: new Date().toISOString(),
                processingResults
            });
            // Trigger analytics event
            await publishEvent(EventType.ANALYTICS_GENERATED, `documents/${documentId}/processing-analytics`, {
                documentId,
                processingTime,
                timestamp: new Date().toISOString()
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error handling document processed event', {
            documentId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle document shared event
 */
async function handleDocumentSharedEvent(event) {
    const { documentId, sharedWith, sharedBy, permissions } = event.data;
    logger_1.logger.info('Document shared event received', { documentId, sharedWith, sharedBy });
    try {
        // Send notification to shared users
        await publishEvent(EventType.NOTIFICATION_SENT, `documents/${documentId}/shared`, {
            documentId,
            sharedWith,
            sharedBy,
            permissions,
            notificationType: 'document_shared',
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling document shared event', {
            documentId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle workflow started event
 */
async function handleWorkflowStartedEvent(event) {
    const { workflowId, startedBy, workflowType } = event.data;
    logger_1.logger.info('Workflow started event received', { workflowId, startedBy, workflowType });
    try {
        // Track workflow analytics
        await publishEvent(EventType.ANALYTICS_GENERATED, `workflows/${workflowId}/started-analytics`, {
            workflowId,
            startedBy,
            workflowType,
            startedAt: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling workflow started event', {
            workflowId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle user registered event
 */
async function handleUserRegisteredEvent(event) {
    const { userId, email, registrationMethod } = event.data;
    logger_1.logger.info('User registered event received', { userId, email, registrationMethod });
    try {
        // Send welcome notification
        await publishEvent(EventType.NOTIFICATION_SENT, `users/${userId}/welcome`, {
            userId,
            email,
            notificationType: 'welcome',
            registrationMethod,
            timestamp: new Date().toISOString()
        });
        // Track user analytics
        await publishEvent(EventType.ANALYTICS_GENERATED, `users/${userId}/registration-analytics`, {
            userId,
            registrationMethod,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling user registered event', {
            userId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle system health check event
 */
async function handleSystemHealthCheckEvent(event) {
    const eventData = event.data || {};
    const { timestamp, database, storage, redis, documentCount, activeWorkflows, queueDepth } = eventData;
    // Determine overall health status
    const services = { database, storage, redis };
    const serviceStatuses = Object.values(services).map((service) => service?.status).filter(Boolean);
    let overallHealthStatus = 'healthy';
    if (serviceStatuses.includes('unhealthy')) {
        overallHealthStatus = 'unhealthy';
    }
    else if (serviceStatuses.includes('unknown')) {
        overallHealthStatus = 'degraded';
    }
    logger_1.logger.info('System health check event received', {
        overallHealthStatus,
        timestamp,
        services: serviceStatuses.length
    });
    try {
        // Store health metrics
        await database_1.db.createItem('system-health', {
            id: `health-${Date.now()}`,
            overallHealthStatus,
            services: {
                database: database || { status: 'unknown', responseTime: 0 },
                storage: storage || { status: 'unknown', responseTime: 0 },
                redis: redis || { status: 'unknown', responseTime: 0 }
            },
            metrics: {
                documentCount: documentCount || 0,
                activeWorkflows: activeWorkflows || 0,
                queueDepth: queueDepth || 0
            },
            timestamp: timestamp || new Date().toISOString(),
            createdAt: new Date().toISOString()
        });
        // Generate alert if unhealthy
        if (overallHealthStatus !== 'healthy') {
            await publishEvent(EventType.PERFORMANCE_ALERT, 'system/health-alert', {
                alertType: 'health_check_failed',
                severity: overallHealthStatus === 'unhealthy' ? 'high' : 'medium',
                healthStatus: overallHealthStatus,
                services,
                metrics: {
                    documentCount,
                    activeWorkflows,
                    queueDepth
                },
                timestamp: new Date().toISOString()
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error handling system health check event', {
            overallHealthStatus,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle performance alert event
 */
async function handlePerformanceAlertEvent(event) {
    const { alertType, severity, metrics } = event.data;
    logger_1.logger.warn('Performance alert received', { alertType, severity, metrics });
    try {
        // Store alert in database
        await database_1.db.createItem('performance-alerts', {
            id: `alert-${Date.now()}`,
            alertType,
            severity,
            metrics,
            timestamp: new Date().toISOString(),
            status: 'active'
        });
        // Send notification for high severity alerts
        if (severity === 'high' || severity === 'critical') {
            await publishEvent(EventType.NOTIFICATION_SENT, `system/performance-alert/${alertType}`, {
                alertType,
                severity,
                metrics,
                notificationType: 'performance_alert',
                timestamp: new Date().toISOString()
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error handling performance alert event', {
            alertType,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle analytics generated event
 */
async function handleAnalyticsGeneratedEvent(event) {
    const { analyticsType, data, generatedFor } = event.data;
    logger_1.logger.info('Analytics generated event received', { analyticsType, generatedFor });
    try {
        // Store analytics data
        await database_1.db.createItem('analytics-data', {
            id: `analytics-${Date.now()}`,
            analyticsType,
            data,
            generatedFor,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling analytics generated event', {
            analyticsType,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Handle notification sent event
 */
async function handleNotificationSentEvent(event) {
    const { notificationId, notificationType, recipient, status } = event.data;
    logger_1.logger.info('Notification sent event received', { notificationId, notificationType, recipient });
    try {
        // Track notification analytics
        await database_1.db.createItem('notification-analytics', {
            id: `notification-${Date.now()}`,
            notificationId,
            notificationType,
            recipient,
            status,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error handling notification sent event', {
            notificationId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Publish custom event endpoint
 */
async function publishCustomEvent(request, _context) {
    try {
        // Authenticate request
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized' }
            }, request);
        }
        const body = await request.json();
        const { eventType, subject, data } = body;
        if (!eventType || !subject || !data) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Missing required fields: eventType, subject, data' }
            }, request);
        }
        await publishEvent(eventType, subject, data);
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: 'Event published successfully',
                eventType,
                subject
            }
        }, request);
    }
    catch (error) {
        logger_1.logger.error('Error publishing custom event', {
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Failed to publish event' }
        }, request);
    }
}
/**
 * Native Event Grid trigger for storage events
 */
async function handleStorageEventGridTrigger(event, context) {
    logger_1.logger.info('Storage Event Grid trigger activated', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id,
        invocationId: context.invocationId
    });
    try {
        await processEventGridEvent(event);
        logger_1.logger.info('Storage event processed successfully', {
            eventId: event.id,
            eventType: event.eventType
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing storage event', {
            eventId: event.id,
            eventType: event.eventType,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error; // Re-throw to trigger retry mechanism
    }
}
/**
 * Native Event Grid trigger for custom application events
 */
async function handleCustomEventGridTrigger(event, context) {
    logger_1.logger.info('Custom Event Grid trigger activated', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id,
        invocationId: context.invocationId
    });
    try {
        await processEventGridEvent(event);
        logger_1.logger.info('Custom event processed successfully', {
            eventId: event.id,
            eventType: event.eventType
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing custom event', {
            eventId: event.id,
            eventType: event.eventType,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error; // Re-throw to trigger retry mechanism
    }
}
/**
 * Enhanced event validation
 */
function validateEventGridEvent(event) {
    const requiredFields = ['id', 'eventType', 'subject', 'eventTime', 'data'];
    for (const field of requiredFields) {
        if (!event[field]) {
            logger_1.logger.warn('Event validation failed - missing field', { field, eventId: event.id });
            return false;
        }
    }
    // Validate event time is not too old (24 hours)
    const eventTime = new Date(event.eventTime);
    const now = new Date();
    const hoursDiff = (now.getTime() - eventTime.getTime()) / (1000 * 60 * 60);
    if (hoursDiff > 24) {
        logger_1.logger.warn('Event validation failed - event too old', {
            eventId: event.id,
            eventTime: event.eventTime,
            hoursDiff
        });
        return false;
    }
    return true;
}
/**
 * Enhanced event processing with validation and error handling
 */
async function processEventGridEventEnhanced(event) {
    // Validate event
    if (!validateEventGridEvent(event)) {
        logger_1.logger.error('Event validation failed', { eventId: event.id });
        return; // Don't process invalid events
    }
    // Add processing metadata
    const processingMetadata = {
        processedAt: new Date().toISOString(),
        processingId: `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`,
        retryCount: event.retryCount || 0
    };
    logger_1.logger.info('Processing Event Grid event with enhanced handling', {
        eventType: event.eventType,
        subject: event.subject,
        eventId: event.id,
        ...processingMetadata
    });
    try {
        // Process the event
        await processEventGridEvent(event);
        // Log successful processing
        logger_1.logger.info('Event processed successfully', {
            eventId: event.id,
            eventType: event.eventType,
            ...processingMetadata
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing event with enhanced handling', {
            eventType: event.eventType,
            eventId: event.id,
            error: error instanceof Error ? error.message : String(error),
            ...processingMetadata
        });
        throw error;
    }
}
// Register HTTP functions
functions_1.app.http('event-grid-webhook', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous', // Event Grid needs anonymous access
    route: 'eventgrid/webhook',
    handler: handleEventGridWebhook
});
functions_1.app.http('event-grid-publish', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'eventgrid/publish',
    handler: publishCustomEvent
});
// Register native Event Grid triggers
functions_1.app.eventGrid('storage-events-trigger', {
    handler: handleStorageEventGridTrigger
});
functions_1.app.eventGrid('custom-events-trigger', {
    handler: handleCustomEventGridTrigger
});
//# sourceMappingURL=event-grid-handlers.js.map