"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.processForm = processForm;
/**
 * AI Smart Form Processing Function
 * Handles intelligent form processing and field extraction
 * Migrated from old-arch/src/ai-service/smart-form-processing/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const ai_form_recognizer_1 = require("@azure/ai-form-recognizer");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Form processing types and enums
var FormType;
(function (FormType) {
    FormType["INVOICE"] = "INVOICE";
    FormType["RECEIPT"] = "RECEIPT";
    FormType["CONTRACT"] = "CONTRACT";
    FormType["APPLICATION"] = "APPLICATION";
    FormType["SURVEY"] = "SURVEY";
    FormType["TAX_FORM"] = "TAX_FORM";
    FormType["INSURANCE_CLAIM"] = "INSURANCE_CLAIM";
    FormType["MEDICAL_FORM"] = "MEDICAL_FORM";
    FormType["CUSTOM"] = "CUSTOM";
})(FormType || (FormType = {}));
var FieldType;
(function (FieldType) {
    FieldType["TEXT"] = "TEXT";
    FieldType["NUMBER"] = "NUMBER";
    FieldType["DATE"] = "DATE";
    FieldType["BOOLEAN"] = "BOOLEAN";
    FieldType["EMAIL"] = "EMAIL";
    FieldType["PHONE"] = "PHONE";
    FieldType["ADDRESS"] = "ADDRESS";
    FieldType["CURRENCY"] = "CURRENCY";
    FieldType["PERCENTAGE"] = "PERCENTAGE";
    FieldType["SIGNATURE"] = "SIGNATURE";
})(FieldType || (FieldType = {}));
var ProcessingStatus;
(function (ProcessingStatus) {
    ProcessingStatus["PENDING"] = "PENDING";
    ProcessingStatus["PROCESSING"] = "PROCESSING";
    ProcessingStatus["COMPLETED"] = "COMPLETED";
    ProcessingStatus["FAILED"] = "FAILED";
})(ProcessingStatus || (ProcessingStatus = {}));
// Validation schemas
const processFormSchema = Joi.object({
    documentId: Joi.string().uuid().optional(),
    documentUrl: Joi.string().uri().optional(),
    documentContent: Joi.string().base64().optional(),
    formType: Joi.string().valid(...Object.values(FormType)).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    configuration: Joi.object({
        extractTables: Joi.boolean().default(true),
        extractSignatures: Joi.boolean().default(true),
        validateFields: Joi.boolean().default(true),
        confidenceThreshold: Joi.number().min(0).max(1).default(0.7),
        customFields: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().valid(...Object.values(FieldType)).required(),
            required: Joi.boolean().default(false),
            pattern: Joi.string().optional(),
            description: Joi.string().optional()
        })).optional(),
        templateId: Joi.string().uuid().optional()
    }).optional(),
    metadata: Joi.object().optional()
}).xor('documentId', 'documentUrl', 'documentContent');
/**
 * Process form handler
 */
async function processForm(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Process form started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = processFormSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const formRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(formRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check form processing limits
        const canProcess = await checkFormProcessingLimits(formRequest.organizationId);
        if (!canProcess.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canProcess.reason }
            }, request);
        }
        // Get document content
        let documentBuffer;
        let documentMetadata = {};
        if (formRequest.documentId) {
            const document = await database_1.db.readItem('documents', formRequest.documentId, formRequest.documentId);
            if (!document) {
                return (0, cors_1.addCorsHeaders)({
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Document not found" }
                }, request);
            }
            const documentData = document;
            documentMetadata = {
                name: documentData.name,
                contentType: documentData.contentType,
                size: documentData.size
            };
            // Download document from blob storage
            const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
            const containerClient = blobServiceClient.getContainerClient("documents");
            const blobClient = containerClient.getBlobClient(`${formRequest.documentId}/content`);
            const downloadResponse = await blobClient.download();
            const chunks = [];
            if (downloadResponse.readableStreamBody) {
                for await (const chunk of downloadResponse.readableStreamBody) {
                    chunks.push(Buffer.isBuffer(chunk) ? chunk : Buffer.from(chunk));
                }
            }
            documentBuffer = Buffer.concat(chunks);
        }
        else if (formRequest.documentUrl) {
            const response = await fetch(formRequest.documentUrl);
            const arrayBuffer = await response.arrayBuffer();
            documentBuffer = Buffer.from(arrayBuffer);
            documentMetadata = {
                name: formRequest.documentUrl.split('/').pop() || 'unknown',
                contentType: response.headers.get('content-type') || 'application/octet-stream',
                size: arrayBuffer.byteLength
            };
        }
        else if (formRequest.documentContent) {
            documentBuffer = Buffer.from(formRequest.documentContent, 'base64');
            documentMetadata = {
                name: 'uploaded-form',
                contentType: 'application/pdf',
                size: documentBuffer.length
            };
        }
        else {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "No document content provided" }
            }, request);
        }
        // Create processing record
        const processingId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const startTime = Date.now();
        const processingRecord = {
            id: processingId,
            documentId: formRequest.documentId,
            formType: formRequest.formType,
            status: ProcessingStatus.PROCESSING,
            organizationId: formRequest.organizationId,
            projectId: formRequest.projectId,
            configuration: formRequest.configuration || {},
            metadata: {
                ...formRequest.metadata,
                document: documentMetadata
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('form-processing', processingRecord);
        // Process form with AI
        const processingResult = await processFormWithAI(documentBuffer, formRequest.formType, formRequest.configuration || {});
        const processingTime = Date.now() - startTime;
        // Create response
        const response = {
            processingId,
            documentId: formRequest.documentId,
            formType: formRequest.formType,
            status: ProcessingStatus.COMPLETED,
            extractedFields: processingResult.fields,
            tables: processingResult.tables,
            signatures: processingResult.signatures,
            validation: processingResult.validation,
            metadata: {
                processingTime,
                modelUsed: processingResult.modelUsed,
                confidence: processingResult.confidence,
                pageCount: processingResult.pageCount
            }
        };
        // Update processing record
        const updatedRecord = {
            ...processingRecord,
            id: processingId,
            status: ProcessingStatus.COMPLETED,
            results: response,
            completedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('form-processing', updatedRecord);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "form_processed",
            userId: user.id,
            organizationId: formRequest.organizationId,
            projectId: formRequest.projectId,
            timestamp: now,
            details: {
                processingId,
                formType: formRequest.formType,
                fieldsExtracted: processingResult.fields.length,
                tablesExtracted: processingResult.tables?.length || 0,
                signaturesFound: processingResult.signatures?.length || 0,
                processingTime,
                confidence: processingResult.confidence
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'FormProcessed',
            aggregateId: processingId,
            aggregateType: 'FormProcessing',
            version: 1,
            data: {
                processing: updatedRecord,
                results: response,
                processedBy: user.id
            },
            userId: user.id,
            organizationId: formRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Form processed successfully", {
            correlationId,
            processingId,
            formType: formRequest.formType,
            fieldsExtracted: processingResult.fields.length,
            processingTime,
            confidence: processingResult.confidence,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Process form failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkFormProcessingLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxFormsPerMonth: 50 },
            'PROFESSIONAL': { maxFormsPerMonth: 1000 },
            'ENTERPRISE': { maxFormsPerMonth: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxFormsPerMonth === -1) {
            return { allowed: true };
        }
        // Check monthly usage
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const monthlyUsageQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
        const usageResult = await database_1.db.queryItems('form-processing', monthlyUsageQuery, [organizationId, startOfMonth.toISOString()]);
        const monthlyUsage = Number(usageResult[0]) || 0;
        if (monthlyUsage >= limit.maxFormsPerMonth) {
            return {
                allowed: false,
                reason: `Monthly form processing limit reached (${limit.maxFormsPerMonth})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check form processing limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
async function processFormWithAI(documentBuffer, formType, configuration) {
    try {
        // Production Azure Document Intelligence integration
        const endpoint = process.env.AI_DOCUMENT_INTELLIGENCE_ENDPOINT;
        const key = process.env.AI_DOCUMENT_INTELLIGENCE_KEY;
        if (!endpoint || !key) {
            throw new Error('Azure Document Intelligence credentials not configured');
        }
        const client = new ai_form_recognizer_1.DocumentAnalysisClient(endpoint, new ai_form_recognizer_1.AzureKeyCredential(key));
        // Determine the appropriate model based on form type
        let modelId = 'prebuilt-layout';
        switch (formType) {
            case FormType.INVOICE:
                modelId = 'prebuilt-invoice';
                break;
            case FormType.RECEIPT:
                modelId = 'prebuilt-receipt';
                break;
            case FormType.CONTRACT:
                modelId = 'prebuilt-contract';
                break;
            case FormType.TAX_FORM:
                modelId = 'prebuilt-tax.us.w2';
                break;
            case FormType.INSURANCE_CLAIM:
            case FormType.APPLICATION:
            case FormType.SURVEY:
            case FormType.MEDICAL_FORM:
                modelId = 'prebuilt-document';
                break;
            case FormType.CUSTOM:
                modelId = configuration.templateId || 'prebuilt-layout';
                break;
            default:
                modelId = 'prebuilt-layout';
        }
        logger_1.logger.info('Starting Azure Document Intelligence analysis', {
            formType,
            modelId,
            documentSize: documentBuffer.length,
            confidenceThreshold: configuration.confidenceThreshold || 0.7
        });
        // Analyze document with Azure Document Intelligence
        const poller = await client.beginAnalyzeDocument(modelId, documentBuffer);
        const result = await poller.pollUntilDone();
        if (!result) {
            throw new Error('No analysis result received from Azure Document Intelligence');
        }
        // Extract fields from the analysis result
        const extractedFields = [];
        if (result.documents && result.documents.length > 0) {
            const document = result.documents[0];
            // Process document fields
            if (document.fields) {
                for (const [fieldName, field] of Object.entries(document.fields)) {
                    if (field.value !== undefined && field.confidence && field.confidence >= (configuration.confidenceThreshold || 0.7)) {
                        const extractedField = {
                            name: fieldName,
                            value: field.value,
                            type: mapFieldType(field.kind),
                            confidence: field.confidence,
                            page: field.boundingRegions?.[0]?.pageNumber || 1,
                            validated: field.confidence >= 0.8
                        };
                        // Add bounding box if available
                        if (field.boundingRegions && field.boundingRegions.length > 0) {
                            const region = field.boundingRegions[0];
                            if (region.polygon && region.polygon.length >= 4) {
                                const points = region.polygon;
                                extractedField.boundingBox = {
                                    x: Math.min(...points.filter((_, i) => i % 2 === 0)),
                                    y: Math.min(...points.filter((_, i) => i % 2 === 1)),
                                    width: Math.max(...points.filter((_, i) => i % 2 === 0)) - Math.min(...points.filter((_, i) => i % 2 === 0)),
                                    height: Math.max(...points.filter((_, i) => i % 2 === 1)) - Math.min(...points.filter((_, i) => i % 2 === 1))
                                };
                            }
                        }
                        extractedFields.push(extractedField);
                    }
                }
            }
        }
        // Extract tables if requested
        const extractedTables = [];
        if (configuration.extractTables && result.tables) {
            for (const table of result.tables) {
                if (table.cells && table.rowCount && table.columnCount) {
                    // Build table structure
                    const headers = [];
                    const rows = [];
                    // Initialize rows array
                    for (let i = 0; i < table.rowCount; i++) {
                        rows[i] = new Array(table.columnCount).fill('');
                    }
                    // Fill table data
                    for (const cell of table.cells) {
                        const content = cell.content || '';
                        if (cell.rowIndex === 0) {
                            headers[cell.columnIndex] = content;
                        }
                        if (cell.rowIndex < table.rowCount && cell.columnIndex < table.columnCount) {
                            rows[cell.rowIndex][cell.columnIndex] = content;
                        }
                    }
                    extractedTables.push({
                        headers: headers.length > 0 ? headers : rows[0] || [],
                        rows: headers.length > 0 ? rows.slice(1) : rows.slice(1),
                        confidence: table.boundingRegions?.[0] ? 0.85 : 0.7, // Estimate confidence
                        page: table.boundingRegions?.[0]?.pageNumber || 1
                    });
                }
            }
        }
        // Extract signatures if requested (simplified detection)
        const extractedSignatures = [];
        if (configuration.extractSignatures) {
            // Note: Azure Document Intelligence doesn't have built-in signature detection
            // This would require custom model training or additional image analysis
            logger_1.logger.info('Signature extraction requested but not implemented with current model');
        }
        // Calculate overall confidence
        const overallConfidence = extractedFields.length > 0
            ? extractedFields.reduce((sum, field) => sum + field.confidence, 0) / extractedFields.length
            : 0.5;
        // Validate extracted data
        const validation = validateExtractedData(extractedFields, configuration);
        const processingResult = {
            fields: extractedFields,
            tables: extractedTables,
            signatures: extractedSignatures,
            validation,
            modelUsed: modelId,
            confidence: overallConfidence,
            pageCount: result.pages?.length || 1
        };
        logger_1.logger.info('Azure Document Intelligence processing completed', {
            formType,
            modelId,
            fieldsExtracted: extractedFields.length,
            tablesExtracted: extractedTables.length,
            confidence: overallConfidence,
            pageCount: processingResult.pageCount
        });
        return processingResult;
    }
    catch (error) {
        logger_1.logger.error('Azure Document Intelligence processing failed', {
            error: error instanceof Error ? error.message : String(error),
            formType
        });
        // Fallback to basic processing
        return {
            fields: [],
            tables: [],
            signatures: [],
            validation: {
                isValid: false,
                errors: ['AI processing failed'],
                warnings: [],
                completeness: 0
            },
            modelUsed: 'fallback',
            confidence: 0,
            pageCount: 1
        };
    }
}
/**
 * Map Azure Document Intelligence field types to our FieldType enum
 */
function mapFieldType(kind) {
    switch (kind) {
        case 'string':
            return FieldType.TEXT;
        case 'number':
            return FieldType.NUMBER;
        case 'date':
            return FieldType.DATE;
        case 'boolean':
            return FieldType.BOOLEAN;
        case 'phoneNumber':
            return FieldType.PHONE;
        case 'currency':
            return FieldType.CURRENCY;
        case 'percentage':
            return FieldType.PERCENTAGE;
        case 'address':
            return FieldType.ADDRESS;
        default:
            return FieldType.TEXT;
    }
}
/**
 * Validate extracted data based on configuration
 */
function validateExtractedData(fields, configuration) {
    const errors = [];
    const warnings = [];
    let validFieldCount = 0;
    // Check required custom fields
    if (configuration.customFields) {
        for (const customField of configuration.customFields) {
            if (customField.required) {
                const foundField = fields.find(f => f.name === customField.name);
                if (!foundField) {
                    errors.push(`Required field '${customField.name}' not found`);
                }
                else if (!foundField.value || foundField.value === '') {
                    errors.push(`Required field '${customField.name}' is empty`);
                }
                else {
                    validFieldCount++;
                }
            }
        }
    }
    // Check field patterns
    for (const field of fields) {
        const customField = configuration.customFields?.find((cf) => cf.name === field.name);
        if (customField?.pattern) {
            try {
                const regex = new RegExp(customField.pattern);
                if (!regex.test(String(field.value))) {
                    warnings.push(`Field '${field.name}' does not match expected pattern`);
                }
            }
            catch (error) {
                warnings.push(`Invalid pattern for field '${field.name}'`);
            }
        }
        // Check confidence threshold
        if (field.confidence < (configuration.confidenceThreshold || 0.7)) {
            warnings.push(`Low confidence (${field.confidence.toFixed(2)}) for field '${field.name}'`);
        }
        else {
            validFieldCount++;
        }
    }
    const totalExpectedFields = (configuration.customFields?.length || 0) + fields.length;
    const completeness = totalExpectedFields > 0 ? validFieldCount / totalExpectedFields : 0;
    return {
        isValid: errors.length === 0,
        errors,
        warnings,
        completeness: Math.min(completeness, 1.0)
    };
}
// Register functions
functions_1.app.http('ai-form-process', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'ai/forms/process',
    handler: processForm
});
//# sourceMappingURL=ai-smart-form-processing.js.map