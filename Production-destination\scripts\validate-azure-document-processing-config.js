/**
 * Azure Document Processing Pipeline Configuration Validator
 * Validates Event Grid subscriptions, Service Bus queues/topics, and integration setup
 */

const { exec } = require('child_process');
const util = require('util');
const execAsync = util.promisify(exec);

// Configuration
const RESOURCE_GROUP = 'docucontext';
const EVENT_GRID_TOPIC = 'hepzeg';
const SERVICE_BUS_NAMESPACE = 'hepzbackend';
const FUNCTION_APP = 'hepzlogic';

// Required configurations for document processing pipeline
const REQUIRED_CONFIG = {
  eventGridSubscriptions: [
    {
      name: 'document-processing-events',
      eventTypes: [
        'Document.ProcessingStarted',
        'Document.ProcessingCompleted', 
        'Document.ProcessingFailed',
        'Document.AIAnalysisStarted',
        'Document.AIAnalysisCompleted'
      ],
      destination: 'ServiceBusTopic'
    },
    {
      name: 'custom-analytics',
      eventTypes: [
        'Analytics.Generated',
        'Performance.Alert',
        'System.HealthCheck'
      ],
      destination: 'ServiceBusTopic'
    }
  ],
  serviceBusQueues: [
    'ai-operations',
    'document-processing',
    'scheduled-emails',
    'notification-delivery',
    'workflow-orchestration',
    'system-monitoring'
  ],
  serviceBusTopics: [
    {
      name: 'analytics-events',
      subscriptions: ['analytics-aggregator']
    },
    {
      name: 'document-collaboration',
      subscriptions: ['collaboration-processor']
    },
    {
      name: 'monitoring-events',
      subscriptions: ['system-monitor']
    }
  ]
};

/**
 * Main validation function
 */
async function validateDocumentProcessingConfig() {
  console.log('🔍 Validating Azure Document Processing Pipeline Configuration\n');

  const results = {
    eventGrid: { passed: 0, failed: 0, details: [] },
    serviceBus: { passed: 0, failed: 0, details: [] },
    integration: { passed: 0, failed: 0, details: [] }
  };

  try {
    // Validate Event Grid configuration
    console.log('📋 Validating Event Grid Configuration...');
    await validateEventGridConfig(results.eventGrid);

    // Validate Service Bus configuration
    console.log('\n📋 Validating Service Bus Configuration...');
    await validateServiceBusConfig(results.serviceBus);

    // Validate integration setup
    console.log('\n📋 Validating Integration Setup...');
    await validateIntegrationConfig(results.integration);

    // Display summary
    displayValidationSummary(results);

  } catch (error) {
    console.error('\n❌ Validation failed:', error.message);
    process.exit(1);
  }
}

/**
 * Validate Event Grid subscriptions
 */
async function validateEventGridConfig(results) {
  try {
    // Get Event Grid subscriptions
    const { stdout } = await execAsync(`az eventgrid event-subscription list --source-resource-id "/subscriptions/a7b6fcd5-038d-41ed-9d87-2098820fd014/resourceGroups/DocuContext/providers/Microsoft.EventGrid/topics/${EVENT_GRID_TOPIC}"`);
    const subscriptions = JSON.parse(stdout);

    // Validate required subscriptions
    for (const requiredSub of REQUIRED_CONFIG.eventGridSubscriptions) {
      const subscription = subscriptions.find(s => s.name === requiredSub.name);
      
      if (subscription) {
        // Check event types
        const hasAllEventTypes = requiredSub.eventTypes.every(eventType => 
          subscription.filter.includedEventTypes.includes(eventType)
        );

        if (hasAllEventTypes) {
          results.passed++;
          results.details.push(`✅ Event Grid subscription '${requiredSub.name}' configured correctly`);
        } else {
          results.failed++;
          results.details.push(`❌ Event Grid subscription '${requiredSub.name}' missing event types`);
        }
      } else {
        results.failed++;
        results.details.push(`❌ Event Grid subscription '${requiredSub.name}' not found`);
      }
    }

    // Check for document processing events subscription
    const docProcessingSub = subscriptions.find(s => s.name === 'document-processing-events');
    if (docProcessingSub) {
      results.passed++;
      results.details.push(`✅ Document processing events subscription active`);
    } else {
      results.failed++;
      results.details.push(`❌ Document processing events subscription missing`);
    }

  } catch (error) {
    results.failed++;
    results.details.push(`❌ Failed to validate Event Grid: ${error.message}`);
  }
}

/**
 * Validate Service Bus queues and topics
 */
async function validateServiceBusConfig(results) {
  try {
    // Validate queues
    for (const queueName of REQUIRED_CONFIG.serviceBusQueues) {
      try {
        await execAsync(`az servicebus queue show --namespace-name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --name ${queueName} --query "name"`);
        results.passed++;
        results.details.push(`✅ Service Bus queue '${queueName}' exists`);
      } catch (error) {
        results.failed++;
        results.details.push(`❌ Service Bus queue '${queueName}' not found`);
      }
    }

    // Validate topics and subscriptions
    for (const topicConfig of REQUIRED_CONFIG.serviceBusTopics) {
      try {
        // Check topic exists
        await execAsync(`az servicebus topic show --namespace-name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --name ${topicConfig.name} --query "name"`);
        results.passed++;
        results.details.push(`✅ Service Bus topic '${topicConfig.name}' exists`);

        // Check subscriptions
        const { stdout } = await execAsync(`az servicebus topic subscription list --namespace-name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --topic-name ${topicConfig.name}`);
        const subscriptions = JSON.parse(stdout);

        for (const subName of topicConfig.subscriptions) {
          const subscription = subscriptions.find(s => s.name === subName);
          if (subscription) {
            results.passed++;
            results.details.push(`✅ Service Bus subscription '${subName}' exists on topic '${topicConfig.name}'`);
          } else {
            results.failed++;
            results.details.push(`❌ Service Bus subscription '${subName}' not found on topic '${topicConfig.name}'`);
          }
        }
      } catch (error) {
        results.failed++;
        results.details.push(`❌ Service Bus topic '${topicConfig.name}' not found`);
      }
    }

  } catch (error) {
    results.failed++;
    results.details.push(`❌ Failed to validate Service Bus: ${error.message}`);
  }
}

/**
 * Validate integration configuration
 */
async function validateIntegrationConfig(results) {
  try {
    // Check if system-monitoring queue exists (created for error handling)
    try {
      await execAsync(`az servicebus queue show --namespace-name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --name system-monitoring --query "name"`);
      results.passed++;
      results.details.push(`✅ System monitoring queue configured for error handling`);
    } catch (error) {
      results.failed++;
      results.details.push(`❌ System monitoring queue missing - error handling may not work`);
    }

    // Check Event Grid topic endpoint
    try {
      const { stdout } = await execAsync(`az eventgrid topic show --name ${EVENT_GRID_TOPIC} --resource-group ${RESOURCE_GROUP} --query "endpoint"`);
      const endpoint = JSON.parse(stdout);
      if (endpoint && endpoint.includes('eventgrid.azure.net')) {
        results.passed++;
        results.details.push(`✅ Event Grid topic endpoint configured: ${endpoint}`);
      } else {
        results.failed++;
        results.details.push(`❌ Event Grid topic endpoint invalid`);
      }
    } catch (error) {
      results.failed++;
      results.details.push(`❌ Failed to get Event Grid endpoint: ${error.message}`);
    }

    // Check Service Bus namespace
    try {
      const { stdout } = await execAsync(`az servicebus namespace show --name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --query "serviceBusEndpoint"`);
      const endpoint = JSON.parse(stdout);
      if (endpoint && endpoint.includes('servicebus.windows.net')) {
        results.passed++;
        results.details.push(`✅ Service Bus namespace endpoint configured: ${endpoint}`);
      } else {
        results.failed++;
        results.details.push(`❌ Service Bus namespace endpoint invalid`);
      }
    } catch (error) {
      results.failed++;
      results.details.push(`❌ Failed to get Service Bus endpoint: ${error.message}`);
    }

    // Validate document processing pipeline readiness
    const requiredQueues = ['ai-operations', 'document-processing', 'system-monitoring'];
    const queueCheckPromises = requiredQueues.map(async (queue) => {
      try {
        await execAsync(`az servicebus queue show --namespace-name ${SERVICE_BUS_NAMESPACE} --resource-group ${RESOURCE_GROUP} --name ${queue} --query "name"`);
        return { queue, exists: true };
      } catch {
        return { queue, exists: false };
      }
    });

    const queueResults = await Promise.all(queueCheckPromises);
    const allQueuesExist = queueResults.every(result => result.exists);

    if (allQueuesExist) {
      results.passed++;
      results.details.push(`✅ Document processing pipeline queues ready`);
    } else {
      results.failed++;
      const missingQueues = queueResults.filter(r => !r.exists).map(r => r.queue);
      results.details.push(`❌ Missing document processing queues: ${missingQueues.join(', ')}`);
    }

  } catch (error) {
    results.failed++;
    results.details.push(`❌ Failed to validate integration: ${error.message}`);
  }
}

/**
 * Display validation summary
 */
function displayValidationSummary(results) {
  console.log('\n📊 Validation Summary');
  console.log('━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');

  const totalPassed = results.eventGrid.passed + results.serviceBus.passed + results.integration.passed;
  const totalFailed = results.eventGrid.failed + results.serviceBus.failed + results.integration.failed;
  const totalTests = totalPassed + totalFailed;

  console.log(`\n🎯 Overall Results:`);
  console.log(`   ✅ Passed: ${totalPassed}/${totalTests}`);
  console.log(`   ❌ Failed: ${totalFailed}/${totalTests}`);
  console.log(`   📈 Success Rate: ${((totalPassed / totalTests) * 100).toFixed(1)}%`);

  console.log(`\n📋 Event Grid Configuration:`);
  console.log(`   ✅ Passed: ${results.eventGrid.passed}`);
  console.log(`   ❌ Failed: ${results.eventGrid.failed}`);
  results.eventGrid.details.forEach(detail => console.log(`   ${detail}`));

  console.log(`\n📋 Service Bus Configuration:`);
  console.log(`   ✅ Passed: ${results.serviceBus.passed}`);
  console.log(`   ❌ Failed: ${results.serviceBus.failed}`);
  results.serviceBus.details.forEach(detail => console.log(`   ${detail}`));

  console.log(`\n📋 Integration Configuration:`);
  console.log(`   ✅ Passed: ${results.integration.passed}`);
  console.log(`   ❌ Failed: ${results.integration.failed}`);
  results.integration.details.forEach(detail => console.log(`   ${detail}`));

  console.log('\n🚀 Document Processing Pipeline Status:');
  if (totalFailed === 0) {
    console.log('   ✅ READY FOR PRODUCTION - All configurations validated successfully!');
    console.log('   📄 Document processing events will be published to Event Grid');
    console.log('   🔄 Service Bus queues ready for workflow orchestration');
    console.log('   📊 Analytics and monitoring configured');
    console.log('   ⚡ Error handling and dead letter queues configured');
  } else {
    console.log('   ⚠️  CONFIGURATION ISSUES DETECTED - Please fix the failed validations');
    console.log('   📝 Review the failed items above and ensure all Azure resources are properly configured');
  }

  console.log('\n━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━');
}

// Run validation
if (require.main === module) {
  validateDocumentProcessingConfig()
    .then(() => {
      console.log('\n✅ Validation completed successfully!');
      process.exit(0);
    })
    .catch((error) => {
      console.error('\n💥 Validation failed:', error);
      process.exit(1);
    });
}

module.exports = {
  validateDocumentProcessingConfig,
  validateEventGridConfig,
  validateServiceBusConfig,
  validateIntegrationConfig
};
