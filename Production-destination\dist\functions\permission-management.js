"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.grantPermission = grantPermission;
exports.checkPermission = checkPermission;
exports.batchCheckPermissions = batchCheckPermissions;
/**
 * Permission Management Function
 * Handles advanced permission operations like granting, revoking, and checking permissions
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Permission types enum
var PermissionType;
(function (PermissionType) {
    PermissionType["READ"] = "READ";
    PermissionType["WRITE"] = "WRITE";
    PermissionType["DELETE"] = "DELETE";
    PermissionType["ADMIN"] = "ADMIN";
    PermissionType["SHARE"] = "SHARE";
    PermissionType["COMMENT"] = "COMMENT";
    PermissionType["APPROVE"] = "APPROVE";
    PermissionType["EXECUTE"] = "EXECUTE";
})(PermissionType || (PermissionType = {}));
// Resource types enum
var ResourceType;
(function (ResourceType) {
    ResourceType["DOCUMENT"] = "DOCUMENT";
    ResourceType["PROJECT"] = "PROJECT";
    ResourceType["ORGANIZATION"] = "ORGANIZATION";
    ResourceType["WORKFLOW"] = "WORKFLOW";
    ResourceType["TEMPLATE"] = "TEMPLATE";
})(ResourceType || (ResourceType = {}));
// Validation schemas
const grantPermissionSchema = Joi.object({
    userId: Joi.string().uuid().required(),
    resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
    resourceId: Joi.string().uuid().required(),
    permissions: Joi.array().items(Joi.string().valid(...Object.values(PermissionType))).min(1).required(),
    expiresAt: Joi.date().iso().optional(),
    reason: Joi.string().max(500).optional()
});
const revokePermissionSchema = Joi.object({
    userId: Joi.string().uuid().required(),
    resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
    resourceId: Joi.string().uuid().required(),
    permissions: Joi.array().items(Joi.string().valid(...Object.values(PermissionType))).optional(),
    reason: Joi.string().max(500).optional()
});
const checkPermissionSchema = Joi.object({
    userId: Joi.string().uuid().optional(),
    resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
    resourceId: Joi.string().uuid().required(),
    permission: Joi.string().valid(...Object.values(PermissionType)).required()
});
const batchCheckSchema = Joi.object({
    userId: Joi.string().uuid().optional(),
    checks: Joi.array().items(Joi.object({
        resourceType: Joi.string().valid(...Object.values(ResourceType)).required(),
        resourceId: Joi.string().uuid().required(),
        permission: Joi.string().valid(...Object.values(PermissionType)).required()
    })).min(1).max(50).required()
});
/**
 * Grant permission handler
 */
async function grantPermission(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Grant permission started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = grantPermissionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { userId, resourceType, resourceId, permissions, expiresAt, reason } = value;
        // Check if granter has permission to grant permissions on this resource
        const canGrant = await checkUserPermission(user.id, resourceType, resourceId, PermissionType.ADMIN);
        if (!canGrant) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to grant access" }
            }, request);
        }
        // Check if target user exists
        const targetUser = await database_1.db.readItem('users', userId, userId);
        if (!targetUser) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Target user not found" }
            }, request);
        }
        // Check if resource exists
        const resource = await getResource(resourceType, resourceId);
        if (!resource) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Resource not found" }
            }, request);
        }
        // Get existing permissions for this user and resource
        const existingPermissionQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.resourceType = @resourceType AND c.resourceId = @resourceId';
        const existingPermissions = await database_1.db.queryItems('permissions', existingPermissionQuery, [userId, resourceType, resourceId]);
        let permissionRecord;
        if (existingPermissions.length > 0) {
            // Update existing permission record
            const existing = existingPermissions[0];
            const updatedPermissions = Array.from(new Set([...existing.permissions, ...permissions]));
            permissionRecord = {
                ...existing,
                id: existing.id,
                permissions: updatedPermissions,
                expiresAt: expiresAt || existing.expiresAt,
                updatedBy: user.id,
                updatedAt: new Date().toISOString(),
                lastGrantedBy: user.id,
                lastGrantedAt: new Date().toISOString(),
                grantReason: reason
            };
            await database_1.db.updateItem('permissions', permissionRecord);
        }
        else {
            // Create new permission record
            const permissionId = (0, uuid_1.v4)();
            permissionRecord = {
                id: permissionId,
                userId,
                resourceType,
                resourceId,
                permissions,
                grantedBy: user.id,
                grantedAt: new Date().toISOString(),
                expiresAt,
                grantReason: reason,
                isActive: true,
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                tenantId: user.tenantId
            };
            await database_1.db.createItem('permissions', permissionRecord);
        }
        // Create permission audit record
        await database_1.db.createItem('permission-audit', {
            id: (0, uuid_1.v4)(),
            action: 'GRANT',
            userId,
            resourceType,
            resourceId,
            permissions,
            performedBy: user.id,
            performedAt: new Date().toISOString(),
            reason,
            expiresAt,
            tenantId: user.tenantId
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "permission_granted",
            userId: user.id,
            timestamp: new Date().toISOString(),
            details: {
                targetUserId: userId,
                resourceType,
                resourceId,
                permissions,
                reason
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Permission granted successfully", {
            correlationId,
            grantedBy: user.id,
            targetUserId: userId,
            resourceType,
            resourceId,
            permissions
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId,
                resourceType,
                resourceId,
                permissions: permissionRecord.permissions,
                expiresAt: permissionRecord.expiresAt,
                message: "Permissions granted successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Grant permission failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check permission handler
 */
async function checkPermission(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Check permission started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = checkPermissionSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { userId, resourceType, resourceId, permission } = value;
        const targetUserId = userId || user.id;
        // Check if requesting user can check permissions for the target user
        if (targetUserId !== user.id && !user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Can only check your own permissions" }
            }, request);
        }
        // Check the permission
        const hasPermission = await checkUserPermission(targetUserId, resourceType, resourceId, permission);
        // Get permission details if they have access
        let permissionDetails = null;
        if (hasPermission) {
            const permissionQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.resourceType = @resourceType AND c.resourceId = @resourceId';
            const permissions = await database_1.db.queryItems('permissions', permissionQuery, [targetUserId, resourceType, resourceId]);
            if (permissions.length > 0) {
                const perm = permissions[0];
                permissionDetails = {
                    permissions: perm.permissions,
                    grantedBy: perm.grantedBy,
                    grantedAt: perm.grantedAt,
                    expiresAt: perm.expiresAt
                };
            }
        }
        logger_1.logger.info("Permission checked successfully", {
            correlationId,
            checkedBy: user.id,
            targetUserId,
            resourceType,
            resourceId,
            permission,
            hasPermission
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: targetUserId,
                resourceType,
                resourceId,
                permission,
                hasPermission,
                details: permissionDetails
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Check permission failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Batch check permissions handler
 */
async function batchCheckPermissions(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Batch check permissions started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = batchCheckSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { userId, checks } = value;
        const targetUserId = userId || user.id;
        // Check if requesting user can check permissions for the target user
        if (targetUserId !== user.id && !user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Can only check your own permissions" }
            }, request);
        }
        // Perform batch permission checks
        const results = await Promise.all(checks.map(async (check) => {
            const hasPermission = await checkUserPermission(targetUserId, check.resourceType, check.resourceId, check.permission);
            return {
                resourceType: check.resourceType,
                resourceId: check.resourceId,
                permission: check.permission,
                hasPermission
            };
        }));
        logger_1.logger.info("Batch permission check completed", {
            correlationId,
            checkedBy: user.id,
            targetUserId,
            checksCount: checks.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: targetUserId,
                results,
                totalChecks: checks.length
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Batch check permissions failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check if user has specific permission on resource
 */
async function checkUserPermission(userId, resourceType, resourceId, permission) {
    try {
        // Check direct permissions
        const permissionQuery = `
      SELECT * FROM c 
      WHERE c.userId = @userId 
      AND c.resourceType = @resourceType 
      AND c.resourceId = @resourceId 
      AND c.isActive = true
      AND (c.expiresAt IS NULL OR c.expiresAt > @now)
    `;
        const permissions = await database_1.db.queryItems('permissions', permissionQuery, [userId, resourceType, resourceId, new Date().toISOString()]);
        if (permissions.length > 0) {
            const userPermissions = permissions[0].permissions || [];
            if (userPermissions.includes(permission) || userPermissions.includes(PermissionType.ADMIN)) {
                return true;
            }
        }
        // Check inherited permissions (e.g., organization admin, resource owner)
        return await checkInheritedPermissions(userId, resourceType, resourceId, permission);
    }
    catch (error) {
        logger_1.logger.error("Error checking user permission", { userId, resourceType, resourceId, permission, error });
        return false;
    }
}
/**
 * Check inherited permissions
 */
async function checkInheritedPermissions(userId, resourceType, resourceId, permission) {
    try {
        // Get the resource to check ownership
        const resource = await getResource(resourceType, resourceId);
        if (!resource)
            return false;
        // Check if user is the owner/creator
        if (resource.createdBy === userId) {
            return true;
        }
        // Check organization-level permissions
        if (resource.organizationId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [userId, resource.organizationId, 'active']);
            if (memberships.length > 0) {
                const membership = memberships[0];
                // Organization admins have all permissions
                if (membership.role === 'ADMIN') {
                    return true;
                }
                // Members have read access by default
                if (permission === PermissionType.READ && membership.role === 'MEMBER') {
                    return true;
                }
            }
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error("Error checking inherited permissions", { userId, resourceType, resourceId, permission, error });
        return false;
    }
}
/**
 * Get resource by type and ID
 */
async function getResource(resourceType, resourceId) {
    const containerMap = {
        [ResourceType.DOCUMENT]: 'documents',
        [ResourceType.PROJECT]: 'projects',
        [ResourceType.ORGANIZATION]: 'organizations',
        [ResourceType.WORKFLOW]: 'workflows',
        [ResourceType.TEMPLATE]: 'templates'
    };
    const container = containerMap[resourceType];
    if (!container)
        return null;
    try {
        return await database_1.db.readItem(container, resourceId, resourceId);
    }
    catch (error) {
        return null;
    }
}
// Register functions
functions_1.app.http('permission-grant', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'permissions/grant',
    handler: grantPermission
});
functions_1.app.http('permission-check', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'permissions/check',
    handler: checkPermission
});
functions_1.app.http('permission-batch-check', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'permissions/batch-check',
    handler: batchCheckPermissions
});
//# sourceMappingURL=permission-management.js.map