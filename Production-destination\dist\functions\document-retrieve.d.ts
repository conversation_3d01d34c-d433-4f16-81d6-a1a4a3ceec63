/**
 * Document Retrieve Function
 * Handles document metadata and content retrieval with proper authorization
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Retrieve document handler
 */
export declare function retrieveDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List documents handler
 */
export declare function listDocuments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
