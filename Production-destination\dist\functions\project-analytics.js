"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectAnalytics = getProjectAnalytics;
/**
 * Project Analytics Function
 * Handles project analytics, reporting, and performance metrics
 * Migrated from old-arch/src/project-service/analytics/index.ts
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Analytics types and enums
var AnalyticsPeriod;
(function (AnalyticsPeriod) {
    AnalyticsPeriod["DAY"] = "day";
    AnalyticsPeriod["WEEK"] = "week";
    AnalyticsPeriod["MONTH"] = "month";
    AnalyticsPeriod["QUARTER"] = "quarter";
    AnalyticsPeriod["YEAR"] = "year";
})(AnalyticsPeriod || (AnalyticsPeriod = {}));
var MetricType;
(function (MetricType) {
    MetricType["DOCUMENTS"] = "documents";
    MetricType["WORKFLOWS"] = "workflows";
    MetricType["MEMBERS"] = "members";
    MetricType["ACTIVITIES"] = "activities";
    MetricType["STORAGE"] = "storage";
})(MetricType || (MetricType = {}));
// Validation schema
const getProjectAnalyticsSchema = Joi.object({
    projectId: Joi.string().uuid().required(),
    period: Joi.string().valid(...Object.values(AnalyticsPeriod)).default(AnalyticsPeriod.MONTH),
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional(),
    metrics: Joi.array().items(Joi.string().valid(...Object.values(MetricType))).optional(),
    includeComparisons: Joi.boolean().default(true),
    includeTrends: Joi.boolean().default(true),
    includeBreakdowns: Joi.boolean().default(true)
});
/**
 * Get project analytics handler
 */
async function getProjectAnalytics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get project analytics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            projectId: url.searchParams.get('projectId') || '',
            period: url.searchParams.get('period') || AnalyticsPeriod.MONTH,
            startDate: url.searchParams.get('startDate') || undefined,
            endDate: url.searchParams.get('endDate') || undefined,
            metrics: url.searchParams.get('metrics')?.split(',') || undefined,
            includeComparisons: url.searchParams.get('includeComparisons') !== 'false',
            includeTrends: url.searchParams.get('includeTrends') !== 'false',
            includeBreakdowns: url.searchParams.get('includeBreakdowns') !== 'false'
        };
        // Validate request
        const { error, value } = getProjectAnalyticsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const analyticsRequest = value;
        // Verify project exists and user has access
        const project = await database_1.db.readItem('projects', analyticsRequest.projectId, analyticsRequest.projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        // Check if user has analytics access
        const hasAccess = await checkProjectAnalyticsAccess(analyticsRequest.projectId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to project analytics" }
            }, request);
        }
        const projectData = project;
        // Calculate date range
        const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);
        // Get analytics data
        const analyticsData = await getProjectAnalyticsData(analyticsRequest, dateRange);
        const response = {
            projectId: analyticsRequest.projectId,
            projectName: projectData.name,
            period: analyticsRequest.period,
            dateRange,
            summary: analyticsData.summary,
            trends: analyticsRequest.includeTrends ? analyticsData.trends : undefined,
            breakdowns: analyticsRequest.includeBreakdowns ? analyticsData.breakdowns : undefined,
            comparisons: analyticsRequest.includeComparisons ? analyticsData.comparisons : undefined,
            performance: analyticsData.performance
        };
        logger_1.logger.info("Project analytics retrieved successfully", {
            correlationId,
            projectId: analyticsRequest.projectId,
            period: analyticsRequest.period,
            totalDocuments: analyticsData.summary.totalDocuments,
            totalWorkflows: analyticsData.summary.totalWorkflows,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get project analytics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Calculate date range based on period
 */
function calculateDateRange(period, startDate, endDate) {
    const now = new Date();
    const end = endDate ? new Date(endDate) : now;
    let start;
    if (startDate) {
        start = new Date(startDate);
    }
    else {
        switch (period) {
            case AnalyticsPeriod.DAY:
                start = new Date(end.getTime() - 24 * 60 * 60 * 1000); // 1 day ago
                break;
            case AnalyticsPeriod.WEEK:
                start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000); // 1 week ago
                break;
            case AnalyticsPeriod.MONTH:
                start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate()); // 1 month ago
                break;
            case AnalyticsPeriod.QUARTER:
                start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate()); // 3 months ago
                break;
            case AnalyticsPeriod.YEAR:
                start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate()); // 1 year ago
                break;
            default:
                start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate()); // Default to 1 month
        }
    }
    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}
/**
 * Check project analytics access
 */
async function checkProjectAnalyticsAccess(projectId, userId) {
    try {
        // Check if user is a member of the project
        const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('project-members', membershipQuery, [projectId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check project analytics access', { error, projectId, userId });
        return false;
    }
}
/**
 * Get project analytics data
 */
async function getProjectAnalyticsData(request, dateRange) {
    try {
        // Build base queries
        const baseDocumentQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.createdAt >= @startDate AND c.createdAt <= @endDate';
        const baseWorkflowQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.createdAt >= @startDate AND c.createdAt <= @endDate';
        const baseActivityQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.timestamp >= @startDate AND c.timestamp <= @endDate';
        const baseMemberQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.status = @status';
        const parameters = [request.projectId, dateRange.start, dateRange.end];
        // Get data from different collections
        const [documents, workflows, activities, members] = await Promise.all([
            database_1.db.queryItems('documents', baseDocumentQuery, parameters),
            database_1.db.queryItems('workflow-executions', baseWorkflowQuery, parameters),
            database_1.db.queryItems('activities', baseActivityQuery, parameters),
            database_1.db.queryItems('project-members', baseMemberQuery, [request.projectId, 'ACTIVE'])
        ]);
        // Calculate summary statistics
        const summary = calculateSummaryStatistics(documents, workflows, activities, members);
        // Calculate trends if requested
        const trends = request.includeTrends ? calculateTrends(documents, workflows, activities, members, dateRange) : undefined;
        // Calculate breakdowns if requested
        const breakdowns = request.includeBreakdowns ? calculateBreakdowns(documents, workflows, activities, members) : undefined;
        // Calculate comparisons if requested
        const comparisons = request.includeComparisons ? await calculateComparisons(request, dateRange) : undefined;
        // Calculate performance metrics
        const performance = calculatePerformanceMetrics(documents, workflows, activities, members);
        return {
            summary,
            trends,
            breakdowns,
            comparisons,
            performance
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to get project analytics data', { error, request });
        throw error;
    }
}
/**
 * Calculate summary statistics
 */
function calculateSummaryStatistics(documents, workflows, activities, members) {
    const totalDocuments = documents.length;
    const totalWorkflows = workflows.length;
    const totalMembers = members.length;
    const totalActivities = activities.length;
    // Calculate storage used (simplified)
    const storageUsedMB = Math.round(documents.reduce((sum, doc) => sum + (doc.size || 0), 0) / (1024 * 1024));
    // Calculate active users (users with activities in the period)
    const activeUserIds = new Set(activities.map(a => a.userId));
    const activeUsers = activeUserIds.size;
    // Calculate completed workflows
    const completedWorkflows = workflows.filter(w => w.status === 'COMPLETED').length;
    // Calculate average processing time for completed workflows
    const completedWithDuration = workflows.filter(w => w.status === 'COMPLETED' && w.startedAt && w.completedAt);
    const totalDuration = completedWithDuration.reduce((sum, w) => {
        const duration = new Date(w.completedAt).getTime() - new Date(w.startedAt).getTime();
        return sum + duration;
    }, 0);
    const averageProcessingTime = completedWithDuration.length > 0
        ? Math.round(totalDuration / completedWithDuration.length / 1000) // Convert to seconds
        : 0;
    return {
        totalDocuments,
        totalWorkflows,
        totalMembers,
        totalActivities,
        storageUsedMB,
        activeUsers,
        completedWorkflows,
        averageProcessingTime
    };
}
/**
 * Calculate trends
 */
function calculateTrends(documents, workflows, activities, members, dateRange) {
    // Group data by date
    const dailyStats = {};
    // Initialize daily stats
    const start = new Date(dateRange.start);
    const end = new Date(dateRange.end);
    for (let d = new Date(start); d <= end; d.setDate(d.getDate() + 1)) {
        const dateKey = d.toISOString().split('T')[0];
        dailyStats[dateKey] = {
            documents: { count: 0, uploads: 0, processed: 0 },
            workflows: { started: 0, completed: 0, failed: 0 },
            activities: { activities: 0, uniqueUsers: new Set() },
            members: { totalMembers: 0, activeMembers: new Set(), newMembers: 0 }
        };
    }
    // Process documents
    documents.forEach(doc => {
        const date = new Date(doc.createdAt).toISOString().split('T')[0];
        if (dailyStats[date]) {
            dailyStats[date].documents.count++;
            dailyStats[date].documents.uploads++;
            if (doc.status === 'PROCESSED') {
                dailyStats[date].documents.processed++;
            }
        }
    });
    // Process workflows
    workflows.forEach(workflow => {
        const date = new Date(workflow.createdAt).toISOString().split('T')[0];
        if (dailyStats[date]) {
            dailyStats[date].workflows.started++;
            if (workflow.status === 'COMPLETED') {
                dailyStats[date].workflows.completed++;
            }
            else if (workflow.status === 'FAILED') {
                dailyStats[date].workflows.failed++;
            }
        }
    });
    // Process activities
    activities.forEach(activity => {
        const date = new Date(activity.timestamp).toISOString().split('T')[0];
        if (dailyStats[date]) {
            dailyStats[date].activities.activities++;
            dailyStats[date].activities.uniqueUsers.add(activity.userId);
        }
    });
    // Convert to arrays
    const documentTrend = Object.entries(dailyStats).map(([date, stats]) => ({
        date,
        count: stats.documents.count,
        uploads: stats.documents.uploads,
        processed: stats.documents.processed
    }));
    const workflowTrend = Object.entries(dailyStats).map(([date, stats]) => ({
        date,
        started: stats.workflows.started,
        completed: stats.workflows.completed,
        failed: stats.workflows.failed
    }));
    const activityTrend = Object.entries(dailyStats).map(([date, stats]) => ({
        date,
        activities: stats.activities.activities,
        uniqueUsers: stats.activities.uniqueUsers.size
    }));
    const memberTrend = Object.entries(dailyStats).map(([date, stats]) => ({
        date,
        totalMembers: members.length, // Simplified - would need historical data
        activeMembers: stats.activities.uniqueUsers.size,
        newMembers: 0 // Simplified - would need member creation dates
    }));
    return {
        documentTrend,
        workflowTrend,
        activityTrend,
        memberTrend
    };
}
/**
 * Calculate breakdowns
 */
function calculateBreakdowns(documents, workflows, activities, members) {
    // Documents by type
    const documentTypes = {};
    documents.forEach(doc => {
        const type = doc.contentType?.split('/')[0] || 'unknown';
        documentTypes[type] = (documentTypes[type] || 0) + 1;
    });
    const documentsByType = Object.entries(documentTypes).map(([type, count]) => ({
        type,
        count,
        percentage: Math.round((count / documents.length) * 100)
    }));
    // Workflows by status
    const workflowStatuses = {};
    workflows.forEach(workflow => {
        const status = workflow.status || 'unknown';
        workflowStatuses[status] = (workflowStatuses[status] || 0) + 1;
    });
    const workflowsByStatus = Object.entries(workflowStatuses).map(([status, count]) => ({
        status,
        count,
        percentage: Math.round((count / workflows.length) * 100)
    }));
    // Activities by type
    const activityTypes = {};
    activities.forEach(activity => {
        const type = activity.type || 'unknown';
        activityTypes[type] = (activityTypes[type] || 0) + 1;
    });
    const activitiesByType = Object.entries(activityTypes).map(([type, count]) => ({
        type,
        count,
        percentage: Math.round((count / activities.length) * 100)
    }));
    // Members by role
    const memberRoles = {};
    members.forEach(member => {
        const role = member.role || 'unknown';
        memberRoles[role] = (memberRoles[role] || 0) + 1;
    });
    const membersByRole = Object.entries(memberRoles).map(([role, count]) => ({
        role,
        count,
        percentage: Math.round((count / members.length) * 100)
    }));
    return {
        documentsByType,
        workflowsByStatus,
        activitiesByType,
        membersByRole
    };
}
/**
 * Calculate comparisons with previous period
 */
async function calculateComparisons(request, dateRange) {
    // Calculate previous period date range
    const periodLength = new Date(dateRange.end).getTime() - new Date(dateRange.start).getTime();
    const previousStart = new Date(new Date(dateRange.start).getTime() - periodLength);
    const previousEnd = new Date(dateRange.start);
    const previousDateRange = {
        start: previousStart.toISOString(),
        end: previousEnd.toISOString()
    };
    // Get previous period data (simplified)
    const previousData = await getProjectAnalyticsData({ ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false }, previousDateRange);
    const currentData = await getProjectAnalyticsData({ ...request, includeComparisons: false, includeTrends: false, includeBreakdowns: false }, dateRange);
    return {
        previousPeriod: {
            documents: {
                current: currentData.summary.totalDocuments,
                previous: previousData.summary.totalDocuments,
                change: currentData.summary.totalDocuments - previousData.summary.totalDocuments
            },
            workflows: {
                current: currentData.summary.totalWorkflows,
                previous: previousData.summary.totalWorkflows,
                change: currentData.summary.totalWorkflows - previousData.summary.totalWorkflows
            },
            activities: {
                current: currentData.summary.totalActivities,
                previous: previousData.summary.totalActivities,
                change: currentData.summary.totalActivities - previousData.summary.totalActivities
            },
            members: {
                current: currentData.summary.totalMembers,
                previous: previousData.summary.totalMembers,
                change: currentData.summary.totalMembers - previousData.summary.totalMembers
            }
        }
    };
}
/**
 * Calculate performance metrics
 */
function calculatePerformanceMetrics(documents, workflows, activities, members) {
    // Average document processing time (simplified)
    const processedDocs = documents.filter(d => d.processedAt && d.createdAt);
    const avgDocProcessingTime = processedDocs.length > 0
        ? processedDocs.reduce((sum, doc) => {
            const time = new Date(doc.processedAt).getTime() - new Date(doc.createdAt).getTime();
            return sum + time;
        }, 0) / processedDocs.length / 1000 // Convert to seconds
        : 0;
    // Average workflow completion time
    const completedWorkflows = workflows.filter(w => w.completedAt && w.startedAt);
    const avgWorkflowCompletionTime = completedWorkflows.length > 0
        ? completedWorkflows.reduce((sum, workflow) => {
            const time = new Date(workflow.completedAt).getTime() - new Date(workflow.startedAt).getTime();
            return sum + time;
        }, 0) / completedWorkflows.length / 1000 // Convert to seconds
        : 0;
    // User engagement score (activities per active user)
    const activeUsers = new Set(activities.map(a => a.userId)).size;
    const userEngagementScore = activeUsers > 0 ? Math.round((activities.length / activeUsers) * 10) / 10 : 0;
    // Project health score (simplified calculation)
    const completionRate = workflows.length > 0 ? workflows.filter(w => w.status === 'COMPLETED').length / workflows.length : 1;
    const activityRate = members.length > 0 ? activeUsers / members.length : 0;
    const projectHealthScore = Math.round((completionRate * 0.6 + activityRate * 0.4) * 100);
    return {
        averageDocumentProcessingTime: Math.round(avgDocProcessingTime),
        averageWorkflowCompletionTime: Math.round(avgWorkflowCompletionTime),
        userEngagementScore,
        projectHealthScore
    };
}
// Register functions
functions_1.app.http('project-analytics', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/analytics',
    handler: getProjectAnalytics
});
//# sourceMappingURL=project-analytics.js.map