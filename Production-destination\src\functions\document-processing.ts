/**
 * Document Processing Function
 * Handles AI-powered document analysis and content extraction
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { DocumentAnalysisClient, AzureKeyCredential } from '@azure/ai-form-recognizer';
import { BlobServiceClient } from "@azure/storage-blob";
import * as <PERSON><PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { enhancedDocumentIntelligence } from '../shared/services/enhanced-document-intelligence';
import { ragService } from '../shared/services/rag-service';
import { redis } from '../shared/services/redis';
import { eventGridIntegration } from '../shared/services/event-grid-integration';
import { ServiceBusEnhancedService } from '../shared/services/service-bus';
import { eventDrivenCache } from '../shared/services/event-driven-cache';
// Document types enum
export enum DocumentType {
  GENERAL = 'general',
  INVOICE = 'invoice',
  CONTRACT = 'contract',
  RECEIPT = 'receipt',
  IDENTITY = 'identity',
  FINANCIAL = 'financial',
  LEGAL = 'legal',
  MEDICAL = 'medical',
  TAX = 'tax',
  INSURANCE = 'insurance'
}

export enum DocumentStatus {
  PENDING = 'pending',
  UPLOADED = 'uploaded',
  PROCESSING = 'processing',
  PROCESSED = 'processed',
  FAILED = 'failed',
  ARCHIVED = 'archived'
}

// Processing request schema
const processingSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  analysisType: Joi.string().valid('layout', 'general', 'invoice', 'receipt', 'identity', 'custom').default('layout'),
  extractTables: Joi.boolean().default(true),
  extractKeyValuePairs: Joi.boolean().default(true),
  extractEntities: Joi.boolean().default(false),
  customModelId: Joi.string().optional()
});

interface ProcessingRequest {
  documentId: string;
  analysisType: string;
  extractTables: boolean;
  extractKeyValuePairs: boolean;
  extractEntities: boolean;
  customModelId?: string;
}

interface ProcessingResult {
  documentId: string;
  status: string;
  extractedText: string;
  tables?: any[];
  keyValuePairs?: any[];
  entities?: any[];
  confidence: number;
  processingTime: number;
  modelUsed: string;
}

/**
 * Extract text from document using Azure Document Intelligence
 */
async function extractTextFromDocument(
  documentBuffer: Buffer,
  modelId: string,
  client: DocumentAnalysisClient
): Promise<{ text: string; tables: any[]; keyValuePairs: any[]; confidence: number }> {
  try {
    const poller = await client.beginAnalyzeDocument(modelId, documentBuffer);
    const result = await poller.pollUntilDone();

    if (!result) {
      throw new Error('No analysis result received');
    }

    // Extract text content
    let extractedText = '';
    if (result.content) {
      extractedText = result.content;
    }

    // Extract tables
    const tables: any[] = [];
    if (result.tables) {
      for (const table of result.tables) {
        const tableData = {
          rowCount: table.rowCount,
          columnCount: table.columnCount,
          cells: table.cells.map(cell => ({
            content: cell.content,
            rowIndex: cell.rowIndex,
            columnIndex: cell.columnIndex,
            confidence: (cell as any).confidence || 0
          }))
        };
        tables.push(tableData);
      }
    }

    // Extract key-value pairs
    const keyValuePairs: any[] = [];
    if (result.keyValuePairs) {
      for (const kvp of result.keyValuePairs) {
        keyValuePairs.push({
          key: kvp.key?.content || '',
          value: kvp.value?.content || '',
          confidence: kvp.confidence
        });
      }
    }

    // Calculate average confidence
    const confidenceValues: number[] = [];
    if (result.pages) {
      for (const page of result.pages) {
        if (page.lines) {
          for (const line of page.lines) {
            const confidence = (line as any).confidence;
            if (confidence !== undefined) {
              confidenceValues.push(confidence);
            }
          }
        }
      }
    }

    const averageConfidence = confidenceValues.length > 0
      ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length
      : 0;

    return {
      text: extractedText,
      tables,
      keyValuePairs,
      confidence: averageConfidence
    };

  } catch (error) {
    logger.error('Document analysis failed', {
      error: error instanceof Error ? error.message : String(error),
      modelId
    });
    throw error;
  }
}

/**
 * Process document handler
 */
export async function processDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const startTime = Date.now();

  logger.info("Document processing started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Initialize services
    const serviceBusService = ServiceBusEnhancedService.getInstance();
    await Promise.all([
      eventGridIntegration.initialize(),
      serviceBusService.initialize(),
      eventDrivenCache.initialize()
    ]);

    // Validate request body
    const body = await request.json();
    const { error, value } = processingSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const processingRequest: ProcessingRequest = value;

    // Get document metadata
    const document = await db.readItem('documents', processingRequest.documentId, processingRequest.documentId);

    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check access permissions
    const hasAccess = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    // Check Redis cache for recent processing results
    const cacheKey = `doc-processing:${processingRequest.documentId}:${JSON.stringify(processingRequest)}`;
    const cachedResult = await redis.getJson(cacheKey);

    if (cachedResult && cachedResult.timestamp && (Date.now() - cachedResult.timestamp) < 3600000) { // 1 hour cache
      logger.info('Returning cached document processing result', {
        correlationId,
        documentId: processingRequest.documentId,
        cacheAge: Date.now() - cachedResult.timestamp
      });

      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { ...cachedResult.result, fromCache: true }
      }, request);
    }

    // Update document status to processing
    const updatedDocument = {
      ...document,
      id: (document as any).id,
      status: DocumentStatus.PROCESSING,
      updatedAt: new Date().toISOString(),
      updatedBy: user.id
    };
    await db.updateItem('documents', updatedDocument);

    // Publish document processing started event
    await eventGridIntegration.publishEvent({
      eventType: 'Document.ProcessingStarted',
      subject: `documents/${processingRequest.documentId}/processing`,
      data: {
        documentId: processingRequest.documentId,
        userId: user.id,
        organizationId: user.tenantId,
        analysisType: processingRequest.analysisType,
        features: {
          extractTables: processingRequest.extractTables,
          extractKeyValuePairs: processingRequest.extractKeyValuePairs,
          extractEntities: processingRequest.extractEntities
        },
        startedAt: new Date().toISOString(),
        correlationId
      }
    });

    // Send processing message to Service Bus for workflow orchestration
    await serviceBusService.sendToQueue('document-processing', {
      body: {
        documentId: processingRequest.documentId,
        action: 'process',
        analysisType: processingRequest.analysisType,
        userId: user.id,
        organizationId: user.tenantId,
        correlationId,
        timestamp: new Date().toISOString()
      },
      messageId: `doc-proc-${processingRequest.documentId}-${Date.now()}`,
      correlationId,
      subject: 'document.processing.started'
    });

    // Download document from blob storage
    const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
    if (!connectionString) {
      throw new Error('Azure Storage connection string not configured');
    }

    const blobServiceClient = BlobServiceClient.fromConnectionString(connectionString);
    const containerClient = blobServiceClient.getContainerClient(
      process.env.DOCUMENT_CONTAINER || "documents"
    );
    const blobClient = containerClient.getBlobClient((document as any).blobName);

    const downloadResponse = await blobClient.download();
    if (!downloadResponse.readableStreamBody) {
      throw new Error('Failed to download document content');
    }

    // Convert stream to buffer
    const chunks: Buffer[] = [];
    for await (const chunk of downloadResponse.readableStreamBody) {
      chunks.push(Buffer.from(chunk));
    }
    const documentBuffer = Buffer.concat(chunks);

    // Initialize Document Intelligence client
    const endpoint = process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT;
    const key = process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY;

    if (!endpoint || !key) {
      throw new Error('Azure Document Intelligence credentials not configured');
    }

    const client = new DocumentAnalysisClient(endpoint, new AzureKeyCredential(key));

    // Determine model to use
    let modelId = 'prebuilt-layout';
    if (processingRequest.customModelId) {
      modelId = processingRequest.customModelId;
    } else {
      switch (processingRequest.analysisType) {
        case 'invoice':
          modelId = 'prebuilt-invoice';
          break;
        case 'receipt':
          modelId = 'prebuilt-receipt';
          break;
        case 'identity':
          modelId = 'prebuilt-idDocument';
          break;
        case 'general':
          modelId = 'prebuilt-document';
          break;
        default:
          modelId = 'prebuilt-layout';
      }
    }

    // Process document using enhanced document intelligence
    const features = [];
    if (processingRequest.extractTables) features.push('tables');
    if (processingRequest.extractKeyValuePairs) features.push('keyValuePairs');
    if (processingRequest.extractEntities) features.push('entities');

    const analysisResult = await enhancedDocumentIntelligence.analyzeDocument(
      documentBuffer,
      processingRequest.documentId,
      modelId,
      features
    );

    const processingTime = Date.now() - startTime;

    // Create processing result
    const result: ProcessingResult = {
      documentId: processingRequest.documentId,
      status: 'completed',
      extractedText: analysisResult.extractedText,
      tables: processingRequest.extractTables ? analysisResult.tables : undefined,
      keyValuePairs: processingRequest.extractKeyValuePairs ? analysisResult.keyValuePairs : undefined,
      entities: processingRequest.extractEntities ? analysisResult.entities : undefined,
      confidence: analysisResult.confidence,
      processingTime,
      modelUsed: analysisResult.modelUsed
    };

    // Update document status to processed with comprehensive analysis results
    const finalDocument = {
      ...document,
      id: (document as any).id,
      status: DocumentStatus.PROCESSED,
      extractedText: analysisResult.extractedText,
      documentIntelligence: {
        lastAnalysisId: `analysis-${processingRequest.documentId}-${Date.now()}`,
        lastAnalyzedAt: new Date().toISOString(),
        layout: analysisResult.layout,
        tablesCount: analysisResult.tables.length,
        keyValuePairsCount: analysisResult.keyValuePairs.length,
        entitiesCount: analysisResult.entities.length,
        signaturesCount: analysisResult.signatures.length,
        barcodesCount: analysisResult.barcodes.length,
        formulasCount: analysisResult.formulas.length,
        confidence: analysisResult.confidence,
        hasStructuredData: analysisResult.tables.length > 0 || analysisResult.keyValuePairs.length > 0,
        metadata: analysisResult.metadata
      },
      updatedAt: new Date().toISOString(),
      updatedBy: user.id,
      processingResult: result
    };
    await db.updateItem('documents', finalDocument);

    // Cache the processing result
    await redis.setJson(cacheKey, {
      result,
      timestamp: Date.now(),
      documentId: processingRequest.documentId,
      analysisResult: {
        confidence: analysisResult.confidence,
        modelUsed: analysisResult.modelUsed,
        processingTime: analysisResult.processingTime
      }
    }, 3600); // 1 hour cache

    // Invalidate related cache entries using Redis
    await redis.del(`doc:${processingRequest.documentId}:content`);
    await redis.del(`doc:${processingRequest.documentId}:metadata`);
    await redis.del(`user:${user.id}:documents`);
    await redis.del(`org:${user.tenantId}:documents`);

    // Publish document processing completed event
    await eventGridIntegration.publishEvent({
      eventType: 'Document.ProcessingCompleted',
      subject: `documents/${processingRequest.documentId}/completed`,
      data: {
        documentId: processingRequest.documentId,
        userId: user.id,
        organizationId: user.tenantId,
        analysisType: processingRequest.analysisType,
        results: {
          confidence: analysisResult.confidence,
          modelUsed: analysisResult.modelUsed,
          processingTime: analysisResult.processingTime,
          extractedTextLength: analysisResult.extractedText.length,
          tablesCount: analysisResult.tables.length,
          keyValuePairsCount: analysisResult.keyValuePairs.length,
          entitiesCount: analysisResult.entities.length,
          hasStructuredData: analysisResult.tables.length > 0 || analysisResult.keyValuePairs.length > 0
        },
        completedAt: new Date().toISOString(),
        correlationId
      }
    });

    // Send completion message to Service Bus for downstream processing
    await serviceBusService.sendToQueue('ai-operations', {
      body: {
        documentId: processingRequest.documentId,
        action: 'analyze-completed',
        analysisType: processingRequest.analysisType,
        results: {
          confidence: analysisResult.confidence,
          extractedTextLength: analysisResult.extractedText.length,
          tablesCount: analysisResult.tables.length,
          entitiesCount: analysisResult.entities.length
        },
        userId: user.id,
        organizationId: user.tenantId,
        correlationId,
        timestamp: new Date().toISOString()
      },
      messageId: `doc-complete-${processingRequest.documentId}-${Date.now()}`,
      correlationId,
      subject: 'document.processing.completed'
    });

    // Index document for RAG if substantial text content
    if (analysisResult.extractedText && analysisResult.extractedText.length > 500) {
      try {
        await ragService.indexDocument({
          documentId: processingRequest.documentId,
          content: analysisResult.extractedText,
          metadata: {
            documentType: processingRequest.analysisType,
            modelUsed: analysisResult.modelUsed,
            confidence: analysisResult.confidence,
            hasLayout: analysisResult.layout.pages.length > 0,
            tablesCount: analysisResult.tables.length,
            keyValuePairsCount: analysisResult.keyValuePairs.length,
            entitiesCount: analysisResult.entities.length
          }
        });
        logger.info('Document indexed for RAG', {
          documentId: processingRequest.documentId,
          contentLength: analysisResult.extractedText.length
        });
      } catch (ragError) {
        logger.warn('Failed to index document for RAG', {
          documentId: processingRequest.documentId,
          error: ragError instanceof Error ? ragError.message : String(ragError)
        });
      }
    }

    logger.info("Document processing completed", {
      correlationId,
      documentId: processingRequest.documentId,
      processingTime,
      modelUsed: modelId,
      confidence: analysisResult.confidence
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: result
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Document processing failed", {
      correlationId,
      error: errorMessage,
      stack: error instanceof Error ? error.stack : undefined
    });

    // Publish document processing failed event
    try {
      await eventGridIntegration.publishEvent({
        eventType: 'Document.ProcessingFailed',
        subject: `documents/processing/failed`,
        data: {
          documentId: (request as any).documentId || 'unknown',
          error: errorMessage,
          correlationId,
          failedAt: new Date().toISOString(),
          processingTime: Date.now() - startTime
        }
      });

      // Send failure message to Service Bus for error handling
      await serviceBusService.sendToQueue('system-monitoring', {
        body: {
          eventType: 'document-processing-error',
          documentId: (request as any).documentId || 'unknown',
          error: errorMessage,
          correlationId,
          timestamp: new Date().toISOString(),
          severity: 'error'
        },
        messageId: `doc-error-${Date.now()}`,
        correlationId,
        subject: 'document.processing.error'
      });
    } catch (eventError) {
      logger.error('Failed to publish error events', {
        correlationId,
        eventError: eventError instanceof Error ? eventError.message : String(eventError)
      });
    }

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        error: "Document processing failed",
        correlationId,
        message: errorMessage
      }
    }, request);
  }
}

app.http('document-processing', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/process',
  handler: processDocument
});
