/**
 * Webhook Management Function
 * Handles webhook registration, management, and delivery
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create webhook handler
 */
export declare function createWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List webhooks handler
 */
export declare function listWebhooks(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Test webhook handler
 */
export declare function testWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
