{"version": 3, "file": "cache-warming-scheduler.js", "sourceRoot": "", "sources": ["../../src/functions/cache-warming-scheduler.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;AAEH,gDAAiE;AACjE,mDAAgD;AAChD,8EAAyE;AACzE,gEAA4D;AAC5D,oDAAiD;AACjD,0DAAiD;AAmBjD;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,OAAc,EAAE,OAA0B;IAC7E,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAE3C,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE;QAC7C,aAAa;QACb,YAAY,EAAE,OAAO,CAAC,YAAY;QAClC,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,wCAAwC;QACxC,MAAM,qCAAgB,CAAC,UAAU,EAAE,CAAC;QAEpC,yBAAyB;QACzB,MAAM,KAAK,GAAG,qCAAgB,CAAC,aAAa,EAAE,CAAC;QAC/C,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;QAElE,oCAAoC;QACpC,MAAM,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAE/C,+BAA+B;QAC/B,MAAM,yBAAyB,CAAC,aAAa,CAAC,CAAC;QAE/C,gCAAgC;QAChC,MAAM,kBAAkB,CAAC,aAAa,CAAC,CAAC;QAExC,kDAAkD;QAClD,MAAM,0BAA0B,CAAC,aAAa,CAAC,CAAC;QAEhD,eAAM,CAAC,IAAI,CAAC,gDAAgD,EAAE;YAC5D,aAAa;YACb,QAAQ,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,IAAI,IAAI,CAAC,OAAO,CAAC,YAAY,CAAC,CAAC,OAAO,EAAE;SAChE,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,CAAC,SAAS;SACxD,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,yBAAyB,CAAC,aAAqB;IAC5D,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAErE,wBAAwB;QACxB,MAAM,SAAS,GAAG,MAAM,mBAAmB,EAAE,CAAC;QAE9C,0BAA0B;QAC1B,MAAM,oBAAoB,CAAC,SAAS,CAAC,gBAAgB,EAAE,aAAa,CAAC,CAAC;QAEtE,oCAAoC;QACpC,MAAM,qBAAqB,CAAC,SAAS,CAAC,iBAAiB,EAAE,aAAa,CAAC,CAAC;QAExE,uCAAuC;QACvC,MAAM,wBAAwB,CAAC,SAAS,CAAC,oBAAoB,EAAE,aAAa,CAAC,CAAC;QAE9E,oCAAoC;QACpC,MAAM,qBAAqB,CAAC,SAAS,CAAC,kBAAkB,EAAE,aAAa,CAAC,CAAC;QAEzE,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAExE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;YACpD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB;IAChC,IAAI,CAAC;QACH,gFAAgF;QAChF,gEAAgE;QAEhE,OAAO;YACL,gBAAgB,EAAE;gBAChB,qCAAqC;gBACrC,gCAAgC;gBAChC,oCAAoC;gBACpC,sCAAsC;gBACtC,WAAW;gBACX,oBAAoB;aACrB;YACD,iBAAiB,EAAE;gBACjB,0BAA0B;gBAC1B,cAAc;gBACd,qBAAqB;gBACrB,aAAa;aACd;YACD,kBAAkB,EAAE;gBAClB,EAAE,IAAI,EAAE,CAAC,EAAE,KAAK,EAAE,IAAI,EAAE,EAAG,OAAO;gBAClC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,OAAO;gBAClC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,EAAE,OAAO;gBAClC,EAAE,IAAI,EAAE,EAAE,EAAE,KAAK,EAAE,IAAI,EAAE,CAAE,OAAO;aACnC;YACD,oBAAoB,EAAE;gBACpB,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE;gBACvC,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE;gBACvC,EAAE,MAAM,EAAE,UAAU,EAAE,WAAW,EAAE,EAAE,EAAE;aACxC;SACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QAEH,kCAAkC;QAClC,OAAO;YACL,gBAAgB,EAAE,EAAE;YACpB,iBAAiB,EAAE,EAAE;YACrB,kBAAkB,EAAE,EAAE;YACtB,oBAAoB,EAAE,EAAE;SACzB,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAAC,IAAc,EAAE,aAAqB;IACvE,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,IAAI,CAAC,MAAM,EAAE,CAAC,CAAC;QAEjF,KAAK,MAAM,UAAU,IAAI,IAAI,EAAE,CAAC;YAC9B,IAAI,UAAU,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,+BAA+B;gBAC/B,MAAM,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAC3C,CAAC;iBAAM,CAAC;gBACN,8BAA8B;gBAC9B,MAAM,eAAe,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;YAC5C,CAAC;QACH,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,QAAkB,EAAE,aAAqB;IAC5E,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEtF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;YACjD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,wBAAwB,CAAC,QAAmD,EAAE,aAAqB;IAChH,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,QAAQ,CAAC,MAAM,EAAE,CAAC,CAAC;QAEzF,KAAK,MAAM,OAAO,IAAI,QAAQ,EAAE,CAAC;YAC/B,oDAAoD;YACpD,IAAI,OAAO,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;gBAC7B,MAAM,sBAAsB,CAAC,OAAO,CAAC,MAAM,EAAE,MAAM,CAAC,CAAC;YACvD,CAAC;iBAAM,IAAI,OAAO,CAAC,WAAW,GAAG,EAAE,EAAE,CAAC;gBACpC,MAAM,sBAAsB,CAAC,OAAO,CAAC,MAAM,EAAE,QAAQ,CAAC,CAAC;YACzD,CAAC;QACH,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;YACpD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAAC,OAA0C,EAAE,aAAqB;IACpG,IAAI,CAAC;QACH,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC,QAAQ,EAAE,CAAC;QAC1C,MAAM,QAAQ,GAAG,CAAC,WAAW,GAAG,CAAC,CAAC,GAAG,EAAE,CAAC;QAExC,6CAA6C;QAC7C,MAAM,UAAU,GAAG,OAAO,CAAC,IAAI,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,IAAI,KAAK,QAAQ,CAAC,CAAC;QAE1D,IAAI,UAAU,IAAI,UAAU,CAAC,KAAK,GAAG,IAAI,EAAE,CAAC;YAC1C,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;gBACjD,aAAa;gBACb,QAAQ;gBACR,aAAa,EAAE,UAAU,CAAC,KAAK;aAChC,CAAC,CAAC;YAEH,gCAAgC;YAChC,MAAM,cAAc,CAAC,WAAW,EAAE,MAAM,CAAC,CAAC;YAC1C,MAAM,cAAc,CAAC,gBAAgB,EAAE,MAAM,CAAC,CAAC;YAC/C,MAAM,cAAc,CAAC,UAAU,EAAE,MAAM,CAAC,CAAC;QAC3C,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;YACjD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc,CAAC,OAAe,EAAE,QAAmC;IAChF,IAAI,CAAC;QACH,qBAAqB;QACrB,wBAAU,CAAC,cAAc,CAAC;YACxB,IAAI,EAAE,MAAM;YACZ,GAAG,EAAE,OAAO;YACZ,OAAO;YACP,QAAQ;YACR,SAAS,EAAE,IAAI,IAAI,EAAE;YACrB,MAAM,EAAE,mBAAmB;SAC5B,CAAC,CAAC;QAEH,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,OAAO,EAAE,QAAQ,EAAE,CAAC,CAAC;IAE3E,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,OAAO;SACR,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe,CAAC,GAAW,EAAE,QAAmC;IAC7E,IAAI,CAAC;QACH,+BAA+B;QAC/B,MAAM,MAAM,GAAG,MAAM,aAAK,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAEpC,IAAI,CAAC,MAAM,EAAE,CAAC;YACZ,qCAAqC;YACrC,wBAAU,CAAC,cAAc,CAAC;gBACxB,IAAI,EAAE,MAAM;gBACZ,GAAG;gBACH,QAAQ;gBACR,SAAS,EAAE,IAAI,IAAI,EAAE;gBACrB,MAAM,EAAE,oBAAoB;aAC7B,CAAC,CAAC;YAEH,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,GAAG,EAAE,QAAQ,EAAE,CAAC,CAAC;QACxE,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,GAAG;SACJ,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CAAC,MAAc,EAAE,QAAmC;IACvF,IAAI,CAAC;QACH,MAAM,YAAY,GAAG;YACnB,QAAQ,MAAM,oBAAoB;YAClC,QAAQ,MAAM,cAAc;YAC5B,UAAU,MAAM,IAAI;YACpB,WAAW,MAAM,IAAI;SACtB,CAAC;QAEF,KAAK,MAAM,OAAO,IAAI,YAAY,EAAE,CAAC;YACnC,MAAM,cAAc,CAAC,OAAO,EAAE,QAAQ,CAAC,CAAC;QAC1C,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,qCAAqC,EAAE;YAClD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,MAAM;SACP,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,yBAAyB,CAAC,aAAqB;IAC5D,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAEjE,MAAM,OAAO,GAAG,aAAK,CAAC,UAAU,EAAE,CAAC;QAEnC,gCAAgC;QAChC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;YACvC,aAAa;YACb,UAAU,EAAE,OAAO,CAAC,UAAU;YAC9B,MAAM,EAAE,OAAO,CAAC,MAAM;YACtB,YAAY,EAAE,OAAO,CAAC,YAAY;YAClC,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,CAAC,CAAC;QAEH,0CAA0C;QAC1C,MAAM,mBAAmB,CAAC,OAAO,EAAE,aAAa,CAAC,CAAC;IAEpD,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;YACrD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAC,OAAY,EAAE,aAAqB;IACpE,IAAI,CAAC;QACH,MAAM,SAAS,GAAG;YAChB,EAAE,EAAE,mBAAmB,IAAI,CAAC,GAAG,EAAE,EAAE;YACnC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO;YACP,aAAa;YACb,IAAI,EAAE,mBAAmB;SAC1B,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,SAAS,CAAC,CAAC;QAC5C,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE5D,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,aAAqB;IACrD,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAEnE,8CAA8C;QAC9C,yDAAyD;QAEzD,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAEnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,0BAA0B,CAAC,aAAqB;IAC7D,IAAI,CAAC;QACH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;QAEjE,0DAA0D;QAC1D,oDAAoD;QAEpD,MAAM,KAAK,GAAG,qCAAgB,CAAC,aAAa,EAAE,CAAC;QAC/C,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,CAAC,CAAC;IAEzE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;YACrD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,qDAAqD;AACrD,eAAG,CAAC,KAAK,CAAC,yBAAyB,EAAE;IACnC,QAAQ,EAAE,eAAe,EAAE,kBAAkB;IAC7C,OAAO,EAAE,qBAAqB;CAC/B,CAAC,CAAC"}