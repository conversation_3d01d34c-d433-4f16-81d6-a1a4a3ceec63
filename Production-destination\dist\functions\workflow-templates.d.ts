/**
 * Workflow Templates Function
 * Handles workflow template management (create, read, update, delete)
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create workflow template handler
 */
export declare function createWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get workflow template handler
 */
export declare function getWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List workflow templates handler
 */
export declare function listWorkflowTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
