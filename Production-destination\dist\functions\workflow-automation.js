"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAutomation = createAutomation;
exports.executeAutomation = executeAutomation;
/**
 * Workflow Automation Function
 * Handles advanced workflow automation, triggers, and conditions
 * Migrated from old-arch/src/workflow-service/automation/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Workflow automation types and enums
var TriggerType;
(function (TriggerType) {
    TriggerType["DOCUMENT_UPLOADED"] = "DOCUMENT_UPLOADED";
    TriggerType["DOCUMENT_PROCESSED"] = "DOCUMENT_PROCESSED";
    TriggerType["DOCUMENT_SHARED"] = "DOCUMENT_SHARED";
    TriggerType["USER_CREATED"] = "USER_CREATED";
    TriggerType["WORKFLOW_COMPLETED"] = "WORKFLOW_COMPLETED";
    TriggerType["SCHEDULE"] = "SCHEDULE";
    TriggerType["WEBHOOK"] = "WEBHOOK";
    TriggerType["EMAIL_RECEIVED"] = "EMAIL_RECEIVED";
    TriggerType["FORM_SUBMITTED"] = "FORM_SUBMITTED";
    TriggerType["CUSTOM_EVENT"] = "CUSTOM_EVENT";
})(TriggerType || (TriggerType = {}));
var ActionType;
(function (ActionType) {
    ActionType["SEND_EMAIL"] = "SEND_EMAIL";
    ActionType["CREATE_DOCUMENT"] = "CREATE_DOCUMENT";
    ActionType["START_WORKFLOW"] = "START_WORKFLOW";
    ActionType["SEND_NOTIFICATION"] = "SEND_NOTIFICATION";
    ActionType["UPDATE_DATABASE"] = "UPDATE_DATABASE";
    ActionType["CALL_WEBHOOK"] = "CALL_WEBHOOK";
    ActionType["GENERATE_REPORT"] = "GENERATE_REPORT";
    ActionType["ARCHIVE_DOCUMENT"] = "ARCHIVE_DOCUMENT";
    ActionType["ASSIGN_TASK"] = "ASSIGN_TASK";
    ActionType["CUSTOM_SCRIPT"] = "CUSTOM_SCRIPT";
})(ActionType || (ActionType = {}));
var ConditionOperator;
(function (ConditionOperator) {
    ConditionOperator["EQUALS"] = "EQUALS";
    ConditionOperator["NOT_EQUALS"] = "NOT_EQUALS";
    ConditionOperator["CONTAINS"] = "CONTAINS";
    ConditionOperator["NOT_CONTAINS"] = "NOT_CONTAINS";
    ConditionOperator["GREATER_THAN"] = "GREATER_THAN";
    ConditionOperator["LESS_THAN"] = "LESS_THAN";
    ConditionOperator["IN"] = "IN";
    ConditionOperator["NOT_IN"] = "NOT_IN";
    ConditionOperator["REGEX"] = "REGEX";
})(ConditionOperator || (ConditionOperator = {}));
var AutomationStatus;
(function (AutomationStatus) {
    AutomationStatus["ACTIVE"] = "ACTIVE";
    AutomationStatus["INACTIVE"] = "INACTIVE";
    AutomationStatus["PAUSED"] = "PAUSED";
    AutomationStatus["ERROR"] = "ERROR";
})(AutomationStatus || (AutomationStatus = {}));
// Validation schemas
const createAutomationSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    organizationId: Joi.string().uuid().required(),
    trigger: Joi.object({
        type: Joi.string().valid(...Object.values(TriggerType)).required(),
        config: Joi.object({
            eventType: Joi.string().optional(),
            schedule: Joi.string().optional(), // cron expression
            webhookUrl: Joi.string().uri().optional(),
            filters: Joi.object().optional()
        }).optional()
    }).required(),
    conditions: Joi.array().items(Joi.object({
        field: Joi.string().required(),
        operator: Joi.string().valid(...Object.values(ConditionOperator)).required(),
        value: Joi.any().required(),
        logicalOperator: Joi.string().valid('AND', 'OR').default('AND')
    })).optional(),
    actions: Joi.array().items(Joi.object({
        type: Joi.string().valid(...Object.values(ActionType)).required(),
        config: Joi.object({
            template: Joi.string().optional(),
            recipients: Joi.array().items(Joi.string()).optional(),
            webhookUrl: Joi.string().uri().optional(),
            workflowId: Joi.string().uuid().optional(),
            documentTemplate: Joi.string().optional(),
            script: Joi.string().optional(),
            parameters: Joi.object().optional()
        }).required(),
        order: Joi.number().min(1).required(),
        continueOnError: Joi.boolean().default(false)
    })).min(1).required(),
    settings: Joi.object({
        enabled: Joi.boolean().default(true),
        maxExecutions: Joi.number().min(1).optional(),
        retryAttempts: Joi.number().min(0).max(5).default(3),
        timeout: Joi.number().min(1).max(300).default(60) // seconds
    }).optional()
});
const executeAutomationSchema = Joi.object({
    automationId: Joi.string().uuid().required(),
    triggerData: Joi.object().required(),
    context: Joi.object({
        userId: Joi.string().uuid().optional(),
        organizationId: Joi.string().uuid().optional(),
        correlationId: Joi.string().optional()
    }).optional()
});
/**
 * Create automation handler
 */
async function createAutomation(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Create automation started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        const { error, value } = createAutomationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const automationRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(automationRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check automation permissions
        const hasAutomationAccess = await checkAutomationAccess(user, automationRequest.organizationId);
        if (!hasAutomationAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to workflow automation" }
            }, request);
        }
        // Create automation
        const automationId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const automation = {
            id: automationId,
            name: automationRequest.name,
            description: automationRequest.description,
            organizationId: automationRequest.organizationId,
            trigger: automationRequest.trigger,
            conditions: automationRequest.conditions || [],
            actions: automationRequest.actions.sort((a, b) => a.order - b.order),
            settings: {
                enabled: true,
                retryAttempts: 3,
                timeout: 60,
                ...automationRequest.settings
            },
            status: AutomationStatus.ACTIVE,
            statistics: {
                totalExecutions: 0,
                successfulExecutions: 0,
                failedExecutions: 0
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('workflow-automations', automation);
        // Register automation trigger
        await registerAutomationTrigger(automation);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "automation_created",
            userId: user.id,
            organizationId: automationRequest.organizationId,
            timestamp: now,
            details: {
                automationId,
                automationName: automationRequest.name,
                triggerType: automationRequest.trigger.type,
                actionCount: automationRequest.actions.length,
                conditionCount: automationRequest.conditions?.length || 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'AutomationCreated',
            aggregateId: automationId,
            aggregateType: 'WorkflowAutomation',
            version: 1,
            data: {
                automation,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: automationRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Automation created successfully", {
            correlationId,
            automationId,
            automationName: automationRequest.name,
            triggerType: automationRequest.trigger.type,
            actionCount: automationRequest.actions.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                automationId,
                name: automationRequest.name,
                triggerType: automationRequest.trigger.type,
                actionCount: automationRequest.actions.length,
                status: AutomationStatus.ACTIVE,
                createdAt: now,
                message: "Automation created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create automation failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Execute automation handler
 */
async function executeAutomation(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Execute automation started", { correlationId });
    try {
        const body = await request.json();
        const { error, value } = executeAutomationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const executeRequest = value;
        // Get automation
        const automation = await database_1.db.readItem('workflow-automations', executeRequest.automationId, executeRequest.automationId);
        if (!automation) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Automation not found" }
            }, request);
        }
        const automationData = automation;
        // Check if automation is enabled
        if (!automationData.settings.enabled || automationData.status !== AutomationStatus.ACTIVE) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Automation is not active" }
            }, request);
        }
        // Execute automation
        const execution = await processAutomationExecution(automationData, executeRequest.triggerData, executeRequest.context);
        // Update automation statistics
        await updateAutomationStatistics(automationData.id, execution.status === 'COMPLETED');
        const duration = Date.now() - startTime;
        logger_1.logger.info("Automation executed", {
            correlationId,
            automationId: executeRequest.automationId,
            executionId: execution.id,
            status: execution.status,
            duration,
            actionResults: execution.results.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                executionId: execution.id,
                automationId: executeRequest.automationId,
                status: execution.status,
                duration: execution.duration,
                results: execution.results,
                completedAt: execution.completedAt,
                message: "Automation executed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Execute automation failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkAutomationAccess(user, organizationId) {
    try {
        // Check if user has admin or automation role
        if (user.roles?.includes('admin') || user.roles?.includes('automation_admin')) {
            return true;
        }
        // Check organization-level permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN';
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check automation access', { error, userId: user.id, organizationId });
        return false;
    }
}
async function registerAutomationTrigger(automation) {
    try {
        // Register trigger in cache for quick lookup
        const triggerKey = `automation_trigger:${automation.trigger.type}:${automation.organizationId}`;
        await redis_1.redis.sadd(triggerKey, automation.id);
        await redis_1.redis.expire(triggerKey, 86400); // 24 hours
        // For scheduled triggers, register with scheduler
        if (automation.trigger.type === TriggerType.SCHEDULE && automation.trigger.config?.schedule) {
            await registerScheduledTrigger(automation);
        }
        logger_1.logger.info('Automation trigger registered', {
            automationId: automation.id,
            triggerType: automation.trigger.type
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to register automation trigger', { error, automationId: automation.id });
    }
}
async function registerScheduledTrigger(automation) {
    try {
        // In production, this would integrate with a job scheduler
        const scheduleKey = `scheduled_automation:${automation.id}`;
        await redis_1.redis.hset(scheduleKey, {
            automationId: automation.id,
            schedule: automation.trigger.config?.schedule || '',
            nextRun: calculateNextRun(automation.trigger.config?.schedule || ''),
            enabled: automation.settings.enabled.toString()
        });
        await redis_1.redis.expire(scheduleKey, 86400 * 7); // 7 days
        logger_1.logger.info('Scheduled trigger registered', {
            automationId: automation.id,
            schedule: automation.trigger.config?.schedule
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to register scheduled trigger', { error, automationId: automation.id });
    }
}
function calculateNextRun(cronExpression) {
    // Simplified next run calculation - in production use a proper cron library
    const now = new Date();
    const nextRun = new Date(now.getTime() + 60 * 60 * 1000); // 1 hour from now
    return nextRun.toISOString();
}
async function processAutomationExecution(automation, triggerData, context) {
    const executionId = (0, uuid_1.v4)();
    const startTime = Date.now();
    const now = new Date().toISOString();
    const execution = {
        id: executionId,
        automationId: automation.id,
        triggerData,
        context: context || {},
        status: 'RUNNING',
        startedAt: now,
        results: [],
        tenantId: automation.tenantId
    };
    try {
        // Store execution record
        await database_1.db.createItem('automation-executions', execution);
        // Check conditions
        const conditionsPass = await evaluateConditions(automation.conditions, triggerData, context);
        if (!conditionsPass) {
            execution.status = 'COMPLETED';
            execution.completedAt = new Date().toISOString();
            execution.duration = Date.now() - startTime;
            await database_1.db.updateItem('automation-executions', execution);
            return execution;
        }
        // Execute actions
        for (const action of automation.actions) {
            const actionStartTime = Date.now();
            try {
                const actionResult = await executeAction(action, triggerData, context);
                execution.results.push({
                    actionType: action.type,
                    actionOrder: action.order,
                    status: 'SUCCESS',
                    result: actionResult,
                    duration: Date.now() - actionStartTime
                });
            }
            catch (actionError) {
                const errorMessage = actionError instanceof Error ? actionError.message : String(actionError);
                execution.results.push({
                    actionType: action.type,
                    actionOrder: action.order,
                    status: 'FAILED',
                    error: errorMessage,
                    duration: Date.now() - actionStartTime
                });
                if (!action.continueOnError) {
                    throw actionError;
                }
            }
        }
        execution.status = 'COMPLETED';
        execution.completedAt = new Date().toISOString();
        execution.duration = Date.now() - startTime;
    }
    catch (error) {
        execution.status = 'FAILED';
        execution.error = error instanceof Error ? error.message : String(error);
        execution.completedAt = new Date().toISOString();
        execution.duration = Date.now() - startTime;
        logger_1.logger.error('Automation execution failed', {
            executionId,
            automationId: automation.id,
            error: execution.error
        });
    }
    // Update execution record
    await database_1.db.updateItem('automation-executions', execution);
    return execution;
}
async function evaluateConditions(conditions, triggerData, context) {
    if (!conditions || conditions.length === 0) {
        return true; // No conditions means always execute
    }
    try {
        let result = true;
        let currentLogicalOperator = 'AND';
        for (const condition of conditions) {
            const conditionResult = evaluateCondition(condition, triggerData, context);
            if (currentLogicalOperator === 'AND') {
                result = result && conditionResult;
            }
            else {
                result = result || conditionResult;
            }
            currentLogicalOperator = condition.logicalOperator || 'AND';
        }
        return result;
    }
    catch (error) {
        logger_1.logger.error('Failed to evaluate conditions', { error, conditions });
        return false;
    }
}
function evaluateCondition(condition, triggerData, context) {
    try {
        const fieldValue = getFieldValue(condition.field, triggerData, context);
        const expectedValue = condition.value;
        switch (condition.operator) {
            case ConditionOperator.EQUALS:
                return fieldValue === expectedValue;
            case ConditionOperator.NOT_EQUALS:
                return fieldValue !== expectedValue;
            case ConditionOperator.CONTAINS:
                return String(fieldValue).includes(String(expectedValue));
            case ConditionOperator.NOT_CONTAINS:
                return !String(fieldValue).includes(String(expectedValue));
            case ConditionOperator.GREATER_THAN:
                return Number(fieldValue) > Number(expectedValue);
            case ConditionOperator.LESS_THAN:
                return Number(fieldValue) < Number(expectedValue);
            case ConditionOperator.IN:
                return Array.isArray(expectedValue) && expectedValue.includes(fieldValue);
            case ConditionOperator.NOT_IN:
                return Array.isArray(expectedValue) && !expectedValue.includes(fieldValue);
            case ConditionOperator.REGEX:
                const regex = new RegExp(String(expectedValue));
                return regex.test(String(fieldValue));
            default:
                return false;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to evaluate condition', { error, condition });
        return false;
    }
}
function getFieldValue(field, triggerData, context) {
    try {
        // Support nested field access with dot notation
        const fieldParts = field.split('.');
        let value = triggerData;
        for (const part of fieldParts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            }
            else {
                // Try context if not found in trigger data
                value = context;
                for (const contextPart of fieldParts) {
                    if (value && typeof value === 'object' && contextPart in value) {
                        value = value[contextPart];
                    }
                    else {
                        return undefined;
                    }
                }
                break;
            }
        }
        return value;
    }
    catch (error) {
        logger_1.logger.error('Failed to get field value', { error, field });
        return undefined;
    }
}
async function executeAction(action, triggerData, context) {
    try {
        switch (action.type) {
            case ActionType.SEND_EMAIL:
                return await executeSendEmailAction(action, triggerData, context);
            case ActionType.SEND_NOTIFICATION:
                return await executeSendNotificationAction(action, triggerData, context);
            case ActionType.START_WORKFLOW:
                return await executeStartWorkflowAction(action, triggerData, context);
            case ActionType.CALL_WEBHOOK:
                return await executeCallWebhookAction(action, triggerData, context);
            case ActionType.CREATE_DOCUMENT:
                return await executeCreateDocumentAction(action, triggerData, context);
            default:
                throw new Error(`Unsupported action type: ${action.type}`);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to execute action', { error, actionType: action.type });
        throw error;
    }
}
async function executeSendEmailAction(action, triggerData, context) {
    // Mock email sending
    logger_1.logger.info('Email action executed', {
        recipients: action.config.recipients,
        template: action.config.template
    });
    return {
        type: 'email_sent',
        recipients: action.config.recipients,
        success: true
    };
}
async function executeSendNotificationAction(action, triggerData, context) {
    // Mock notification sending
    logger_1.logger.info('Notification action executed', {
        recipients: action.config.recipients,
        template: action.config.template
    });
    return {
        type: 'notification_sent',
        recipients: action.config.recipients,
        success: true
    };
}
async function executeStartWorkflowAction(action, triggerData, context) {
    // Mock workflow start
    logger_1.logger.info('Workflow action executed', {
        workflowId: action.config.workflowId,
        parameters: action.config.parameters
    });
    return {
        type: 'workflow_started',
        workflowId: action.config.workflowId,
        executionId: (0, uuid_1.v4)(),
        success: true
    };
}
async function executeCallWebhookAction(action, triggerData, context) {
    // Mock webhook call
    logger_1.logger.info('Webhook action executed', {
        webhookUrl: action.config.webhookUrl,
        parameters: action.config.parameters
    });
    return {
        type: 'webhook_called',
        url: action.config.webhookUrl,
        statusCode: 200,
        success: true
    };
}
async function executeCreateDocumentAction(action, triggerData, context) {
    // Mock document creation
    const documentId = (0, uuid_1.v4)();
    logger_1.logger.info('Document creation action executed', {
        documentTemplate: action.config.documentTemplate,
        documentId
    });
    return {
        type: 'document_created',
        documentId,
        template: action.config.documentTemplate,
        success: true
    };
}
async function updateAutomationStatistics(automationId, success) {
    try {
        const statsKey = `automation_stats:${automationId}`;
        await redis_1.redis.hincrby(statsKey, 'totalExecutions', 1);
        if (success) {
            await redis_1.redis.hincrby(statsKey, 'successfulExecutions', 1);
            await redis_1.redis.hset(statsKey, 'lastSuccess', new Date().toISOString());
        }
        else {
            await redis_1.redis.hincrby(statsKey, 'failedExecutions', 1);
            await redis_1.redis.hset(statsKey, 'lastFailure', new Date().toISOString());
        }
        await redis_1.redis.hset(statsKey, 'lastExecuted', new Date().toISOString());
        await redis_1.redis.expire(statsKey, 86400 * 30); // 30 days
    }
    catch (error) {
        logger_1.logger.error('Failed to update automation statistics', { error, automationId });
    }
}
// Register functions
functions_1.app.http('automation-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'automations',
    handler: createAutomation
});
functions_1.app.http('automation-execute', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'automations/execute',
    handler: executeAutomation
});
//# sourceMappingURL=workflow-automation.js.map