/**
 * Enhanced Service Bus Service for production-ready messaging
 * Features:
 * - Dead letter queue handling
 * - Message deduplication
 * - Session management
 * - Retry policies and circuit breaker
 * - Message routing and filtering
 * - Performance monitoring
 * - Batch processing
 */
export interface ServiceBusEnhancedMessage {
    body: any;
    subject?: string;
    messageId?: string;
    sessionId?: string;
    replyTo?: string;
    timeToLive?: number;
    scheduledEnqueueTime?: Date;
    correlationId?: string;
    partitionKey?: string;
    applicationProperties?: Record<string, any>;
}
export interface ServiceBusMetrics {
    messagesSent: number;
    messagesReceived: number;
    messagesDeadLettered: number;
    errors: number;
    averageProcessingTime: number;
    activeConnections: number;
}
export interface RetryPolicy {
    maxRetries: number;
    retryDelay: number;
    exponentialBackoff: boolean;
    maxRetryDelay: number;
}
export interface CircuitBreakerState {
    isOpen: boolean;
    failureCount: number;
    lastFailureTime: Date;
    threshold: number;
    timeout: number;
}
export declare class ServiceBusEnhancedService {
    private static instance;
    private client;
    private senders;
    private receivers;
    private sessionReceivers;
    private metrics;
    private circuitBreakers;
    private defaultRetryPolicy;
    private connectionString;
    private isInitialized;
    private constructor();
    static getInstance(): ServiceBusEnhancedService;
    /**
     * Initialize Service Bus service
     */
    initialize(): Promise<void>;
    /**
     * Send message to queue with enhanced features
     */
    sendToQueue(queueName: string, message: ServiceBusEnhancedMessage, retryPolicy?: RetryPolicy): Promise<boolean>;
    /**
     * Send message to topic with enhanced features
     */
    sendToTopic(topicName: string, message: ServiceBusEnhancedMessage, retryPolicy?: RetryPolicy): Promise<boolean>;
    /**
     * Send batch of messages
     */
    sendBatch(destination: string, messages: ServiceBusEnhancedMessage[], isQueue?: boolean): Promise<boolean>;
    /**
     * Get service metrics
     */
    getMetrics(): ServiceBusMetrics;
    private getSender;
    private isDuplicateMessage;
    private markMessageAsSent;
    private generateMessageId;
    private calculateRetryDelay;
    private isCircuitBreakerOpen;
    private recordCircuitBreakerFailure;
    private resetCircuitBreaker;
    private updateProcessingTime;
    private startPeriodicTasks;
    /**
     * Close all connections
     */
    close(): Promise<void>;
}
export declare const serviceBusEnhanced: ServiceBusEnhancedService;
