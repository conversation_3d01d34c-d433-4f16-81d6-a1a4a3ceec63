/**
 * Logging Service Function
 * Handles centralized logging and log management
 * Migrated from old-arch/src/logging-service/logs/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create log entry handler
 */
export declare function createLogEntry(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Query logs handler
 */
export declare function queryLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get log statistics handler
 */
export declare function getLogStatistics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
