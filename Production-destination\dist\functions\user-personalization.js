"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserPersonalization = getUserPersonalization;
exports.updateUserPersonalization = updateUserPersonalization;
/**
 * User Personalization Function
 * Handles user personalization settings for search and content preferences
 * Migrated from old-arch/src/user-service/personalization/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const updatePersonalizationSchema = Joi.object({
    personalizationStrength: Joi.number().min(0).max(1).optional(),
    defaultSearchMode: Joi.string().valid('basic', 'advanced', 'semantic', 'hybrid').optional(),
    preferredSortField: Joi.string().valid('relevance', 'date', 'name', 'size', 'type').optional(),
    preferredSortDirection: Joi.string().valid('asc', 'desc').optional(),
    defaultResultsPerPage: Joi.number().min(5).max(100).optional(),
    preferredCategories: Joi.array().items(Joi.string()).optional(),
    preferredContentTypes: Joi.array().items(Joi.string()).optional(),
    preferredAuthors: <AUTHORS>
    preferredTags: Joi.array().items(Joi.string()).optional(),
    useFacetedSearch: Joi.boolean().optional(),
    defaultFacets: Joi.array().items(Joi.string()).optional(),
    clusterResults: Joi.boolean().optional(),
    maxClusters: Joi.number().min(2).max(20).optional(),
    minClusterSize: Joi.number().min(2).max(10).optional(),
    additionalSettings: Joi.object().optional()
});
/**
 * Get user personalization settings handler
 */
async function getUserPersonalization(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get user personalization started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get user's personalization settings
        const settingsQuery = 'SELECT * FROM c WHERE c.userId = @userId';
        const settings = await database_1.db.queryItems('user-personalization', settingsQuery, [user.id]);
        let userSettings;
        if (settings.length === 0) {
            // Create default settings
            userSettings = {
                id: (0, uuid_1.v4)(),
                userId: user.id,
                personalizationStrength: 0.5,
                defaultSearchMode: "hybrid",
                preferredSortField: "relevance",
                preferredSortDirection: "desc",
                defaultResultsPerPage: 10,
                preferredCategories: [],
                preferredContentTypes: [],
                preferredAuthors: <AUTHORS>
                preferredTags: [],
                useFacetedSearch: true,
                defaultFacets: ["category", "contentType", "author"],
                clusterResults: false,
                maxClusters: 5,
                minClusterSize: 3,
                additionalSettings: {},
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            await database_1.db.createItem('user-personalization', userSettings);
        }
        else {
            userSettings = settings[0];
        }
        // Get recent search history for enhanced personalization
        const searchHistoryQuery = 'SELECT TOP 10 * FROM c WHERE c.userId = @userId ORDER BY c.timestamp DESC';
        const recentSearches = await database_1.db.queryItems('search-history', searchHistoryQuery, [user.id]);
        // Get popular categories from analytics
        const analyticsQuery = `
      SELECT c.category, COUNT(1) as count
      FROM c
      WHERE c.userId = @userId
      GROUP BY c.category
      ORDER BY count DESC
      OFFSET 0 LIMIT 5
    `;
        const popularCategories = await database_1.db.queryItems('search-analytics', analyticsQuery, [user.id]);
        // Enhance settings with additional data
        const enhancedSettings = {
            ...userSettings,
            additionalSettings: {
                ...userSettings.additionalSettings,
                recentSearches,
                popularCategories
            }
        };
        logger_1.logger.info("User personalization retrieved successfully", {
            correlationId,
            userId: user.id,
            personalizationStrength: userSettings.personalizationStrength
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: enhancedSettings
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user personalization failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update user personalization settings handler
 */
async function updateUserPersonalization(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update user personalization started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updatePersonalizationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const updates = value;
        // Get existing settings
        const settingsQuery = 'SELECT * FROM c WHERE c.userId = @userId';
        const settings = await database_1.db.queryItems('user-personalization', settingsQuery, [user.id]);
        let userSettings;
        if (settings.length === 0) {
            // Create new settings with updates
            userSettings = {
                id: (0, uuid_1.v4)(),
                userId: user.id,
                personalizationStrength: updates.personalizationStrength || 0.5,
                defaultSearchMode: updates.defaultSearchMode || "hybrid",
                preferredSortField: updates.preferredSortField || "relevance",
                preferredSortDirection: updates.preferredSortDirection || "desc",
                defaultResultsPerPage: updates.defaultResultsPerPage || 10,
                preferredCategories: updates.preferredCategories || [],
                preferredContentTypes: updates.preferredContentTypes || [],
                preferredAuthors: <AUTHORS>
                preferredTags: updates.preferredTags || [],
                useFacetedSearch: updates.useFacetedSearch !== undefined ? updates.useFacetedSearch : true,
                defaultFacets: updates.defaultFacets || ["category", "contentType", "author"],
                clusterResults: updates.clusterResults || false,
                maxClusters: updates.maxClusters || 5,
                minClusterSize: updates.minClusterSize || 3,
                additionalSettings: updates.additionalSettings || {},
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            await database_1.db.createItem('user-personalization', userSettings);
        }
        else {
            // Update existing settings
            userSettings = {
                ...settings[0],
                ...updates,
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('user-personalization', userSettings);
        }
        // Get enhanced data for response
        const searchHistoryQuery = 'SELECT TOP 10 * FROM c WHERE c.userId = @userId ORDER BY c.timestamp DESC';
        const recentSearches = await database_1.db.queryItems('search-history', searchHistoryQuery, [user.id]);
        const analyticsQuery = `
      SELECT c.category, COUNT(1) as count
      FROM c
      WHERE c.userId = @userId
      GROUP BY c.category
      ORDER BY count DESC
      OFFSET 0 LIMIT 5
    `;
        const popularCategories = await database_1.db.queryItems('search-analytics', analyticsQuery, [user.id]);
        const enhancedSettings = {
            ...userSettings,
            additionalSettings: {
                ...userSettings.additionalSettings,
                recentSearches,
                popularCategories
            }
        };
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "user_personalization_updated",
            userId: user.id,
            organizationId: user.tenantId,
            timestamp: new Date().toISOString(),
            details: {
                updatedFields: Object.keys(updates),
                personalizationStrength: userSettings.personalizationStrength
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("User personalization updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(updates)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: enhancedSettings
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update user personalization failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('user-personalization-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/personalization',
    handler: getUserPersonalization
});
functions_1.app.http('user-personalization-update', {
    methods: ['PATCH', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/personalization/update',
    handler: updateUserPersonalization
});
//# sourceMappingURL=user-personalization.js.map