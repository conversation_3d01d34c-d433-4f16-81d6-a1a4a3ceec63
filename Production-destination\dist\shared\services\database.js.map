{"version": 3, "file": "database.js", "sourceRoot": "", "sources": ["../../../src/shared/services/database.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;;AAEH,0CAAkE;AAClE,4CAAyC;AACzC,mCAAmC;AAEnC,MAAa,eAAe;IAM1B;QAFQ,eAAU,GAA2B,IAAI,GAAG,EAAE,CAAC;QAGrD,kEAAkE;QAClE,IAAI,CAAC,MAAM,GAAG,IAAW,CAAC;QAC1B,IAAI,CAAC,QAAQ,GAAG,IAAW,CAAC;IAC9B,CAAC;IAED;;OAEG;IACK,kBAAkB;QACxB,IAAI,IAAI,CAAC,MAAM,KAAK,IAAI,EAAE,CAAC;YACzB,OAAO,CAAC,sBAAsB;QAChC,CAAC;QAED,iDAAiD;QACjD,IAAI,CAAC,YAAM,CAAC,QAAQ,CAAC,QAAQ,IAAI,CAAC,YAAM,CAAC,QAAQ,CAAC,GAAG,EAAE,CAAC;YACtD,eAAM,CAAC,IAAI,CAAC,qFAAqF,CAAC,CAAC;YACnG,OAAO;QACT,CAAC;QAED,IAAI,CAAC;YACH,6CAA6C;YAC7C,MAAM,GAAG,GAAG,IAAI,GAAG,CAAC,YAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC9C,IAAI,CAAC,GAAG,CAAC,QAAQ,CAAC,UAAU,CAAC,OAAO,CAAC,EAAE,CAAC;gBACtC,MAAM,IAAI,KAAK,CAAC,4CAA4C,CAAC,CAAC;YAChE,CAAC;YAED,IAAI,CAAC,MAAM,GAAG,IAAI,qBAAY,CAAC;gBAC7B,QAAQ,EAAE,YAAM,CAAC,QAAQ,CAAC,QAAQ;gBAClC,GAAG,EAAE,YAAM,CAAC,QAAQ,CAAC,GAAG;aACzB,CAAC,CAAC;YACH,IAAI,CAAC,QAAQ,GAAG,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,YAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;YAC/D,eAAM,CAAC,IAAI,CAAC,2CAA2C,CAAC,CAAC;QAC3D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,uCAAuC,EAAE;gBACpD,QAAQ,EAAE,YAAM,CAAC,QAAQ,CAAC,QAAQ;gBAClC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,IAAI,CAAC,MAAM,GAAG,IAAW,CAAC;YAC1B,IAAI,CAAC,QAAQ,GAAG,IAAW,CAAC;QAC9B,CAAC;IACH,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,eAAe,CAAC,QAAQ,EAAE,CAAC;YAC9B,eAAe,CAAC,QAAQ,GAAG,IAAI,eAAe,EAAE,CAAC;QACnD,CAAC;QACD,OAAO,eAAe,CAAC,QAAQ,CAAC;IAClC,CAAC;IAED;;OAEG;IACK,mBAAmB;QACzB,IAAI,CAAC,kBAAkB,EAAE,CAAC;QAC1B,OAAO,IAAI,CAAC,MAAM,KAAK,IAAI,IAAI,IAAI,CAAC,QAAQ,KAAK,IAAI,CAAC;IACxD,CAAC;IAED;;OAEG;IACI,YAAY,CAAC,aAAqB;QACvC,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAChC,MAAM,IAAI,KAAK,CAAC,gEAAgE,CAAC,CAAC;QACpF,CAAC;QAED,IAAI,CAAC,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAC,EAAE,CAAC;YACxC,MAAM,SAAS,GAAG,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC;YACzD,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;QAChD,CAAC;QACD,OAAO,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,CAAE,CAAC;IAC7C,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CAAgC,aAAqB,EAAE,IAAO;QACnF,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,0BAA0B,aAAa,EAAE,EAAE,EAAE,MAAM,EAAG,IAAY,EAAE,EAAE,EAAE,CAAC,CAAC;YACtF,OAAO,IAAI,CAAC,CAAC,qCAAqC;QACpD,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAW,CAAC,CAAC;YAC/D,eAAM,CAAC,IAAI,CAAC,mBAAmB,aAAa,EAAE,EAAE,EAAE,MAAM,EAAG,QAAgB,EAAE,EAAE,EAAE,CAAC,CAAC;YACnF,OAAO,QAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,aAAa,EAAE,EAAE,EAAE,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,EAAE,CAAC,CAAC;YAC7H,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,QAAQ,CAAgC,aAAqB,EAAE,EAAU,EAAE,YAAoB;QAC1G,IAAI,CAAC,IAAI,CAAC,mBAAmB,EAAE,EAAE,CAAC;YAChC,eAAM,CAAC,IAAI,CAAC,2BAA2B,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;YAC9E,OAAO,IAAI,CAAC,CAAC,2BAA2B;QAC1C,CAAC;QAED,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,IAAI,EAAE,CAAC;YACnE,OAAQ,QAAc,IAAI,IAAI,CAAC;QACjC,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACvB,OAAO,IAAI,CAAC;YACd,CAAC;YACD,eAAM,CAAC,KAAK,CAAC,4BAA4B,aAAa,EAAE,EAAE;gBACxD,EAAE;gBACF,YAAY;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CAAI,aAAqB,EAAE,IAAwB;QACxE,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,IAAI,CAAC,IAAI,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,IAAI,CAAC,CAAC;YACjE,eAAM,CAAC,IAAI,CAAC,mBAAmB,aAAa,EAAE,EAAE,EAAE,MAAM,EAAE,IAAI,CAAC,EAAE,EAAE,CAAC,CAAC;YACrE,OAAO,QAAa,CAAC;QACvB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,aAAa,EAAE,EAAE;gBACxD,MAAM,EAAG,IAAY,CAAC,EAAE;gBACxB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAID;;OAEG;IACI,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,EAAU,EAAE,YAAoB;QAC7E,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,SAAS,CAAC,IAAI,CAAC,EAAE,EAAE,YAAY,CAAC,CAAC,MAAM,EAAE,CAAC;YAChD,eAAM,CAAC,IAAI,CAAC,qBAAqB,aAAa,EAAE,EAAE,EAAE,EAAE,EAAE,YAAY,EAAE,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,EAAE;gBAC1D,EAAE;gBACF,YAAY;gBACZ,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CACrB,aAAqB,EACrB,KAAa,EACb,UAAkB;QAElB,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YAEnD,iDAAiD;YACjD,MAAM,SAAS,GAAG;gBAChB,KAAK;gBACL,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC7C,IAAI,EAAE,SAAS,KAAK,EAAE;oBACtB,KAAK;iBACN,CAAC,CAAC,IAAI,EAAE;aACV,CAAC;YAEF,uEAAuE;YACvE,IAAI,UAAU,GAAG,KAAK,CAAC;YACvB,IAAI,UAAU,IAAI,UAAU,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBACxC,MAAM,UAAU,GAAG,CAAC,aAAa,EAAE,aAAa,EAAE,SAAS,EAAE,WAAW,EAAE,SAAS,EAAE,aAAa,EAAE,WAAW,EAAE,iBAAiB,EAAE,YAAY,EAAE,SAAS,EAAE,aAAa,EAAE,QAAQ,EAAE,UAAU,EAAE,SAAS,EAAE,aAAa,EAAE,OAAO,EAAE,aAAa,EAAE,QAAQ,CAAC,CAAC;gBAC9P,UAAU,CAAC,OAAO,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE;oBAClC,IAAI,KAAK,GAAG,UAAU,CAAC,MAAM,EAAE,CAAC;wBAC9B,UAAU,GAAG,UAAU,CAAC,OAAO,CAAC,IAAI,MAAM,CAAC,UAAU,CAAC,KAAK,CAAC,EAAE,GAAG,CAAC,EAAE,SAAS,KAAK,EAAE,CAAC,CAAC;oBACxF,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,SAAS,CAAC,KAAK,GAAG,UAAU,CAAC;YAE7B,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,KAAK,CAAI,SAAS,CAAC,CAAC,QAAQ,EAAE,CAAC;YAC3E,eAAM,CAAC,IAAI,CAAC,sBAAsB,aAAa,EAAE,EAAE;gBACjD,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC9B,WAAW,EAAE,SAAS,CAAC,MAAM;aAC9B,CAAC,CAAC;YACH,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,EAAE;gBAC1D,KAAK,EAAE,KAAK,CAAC,SAAS,CAAC,CAAC,EAAE,GAAG,CAAC;gBAC9B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,mBAAmB,CAC9B,aAAqB,EACrB,KAAa,EACb,UAAkB,EAClB,eAAuB,EAAE,EACzB,iBAA0B;QAE1B,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,SAAS,GAAG;gBAChB,KAAK;gBACL,UAAU,EAAE,UAAU,EAAE,GAAG,CAAC,CAAC,KAAK,EAAE,KAAK,EAAE,EAAE,CAAC,CAAC;oBAC7C,IAAI,EAAE,SAAS,KAAK,EAAE;oBACtB,KAAK;iBACN,CAAC,CAAC,IAAI,EAAE;aACV,CAAC;YAEF,MAAM,aAAa,GAAG,SAAS,CAAC,KAAK,CAAC,KAAK,CAAI,SAAS,EAAE;gBACxD,YAAY;gBACZ,iBAAiB;aAClB,CAAC,CAAC;YAEH,MAAM,EAAE,SAAS,EAAE,iBAAiB,EAAE,SAAS,EAAE,GAAG,MAAM,aAAa,CAAC,SAAS,EAAE,CAAC;YAEpF,OAAO;gBACL,KAAK,EAAE,SAAS;gBAChB,iBAAiB,EAAE,SAAS;aAC7B,CAAC;QACJ,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8CAA8C,aAAa,EAAE,EAAE;gBAC1E,KAAK;gBACL,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,eAAe,CAAC,aAAqB;QAChD,IAAI,CAAC;YACH,MAAM,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,aAAa,CAAC,CAAC,IAAI,EAAE,CAAC;YACpD,OAAO,IAAI,CAAC;QACd,CAAC;QAAC,OAAO,KAAU,EAAE,CAAC;YACpB,IAAI,KAAK,CAAC,IAAI,KAAK,GAAG,EAAE,CAAC;gBACvB,OAAO,KAAK,CAAC;YACf,CAAC;YACD,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,UAAU,CAAC,aAAqB,EAAE,IAAS;QACtD,IAAI,CAAC;YACH,MAAM,SAAS,GAAG,IAAI,CAAC,YAAY,CAAC,aAAa,CAAC,CAAC;YACnD,MAAM,EAAE,QAAQ,EAAE,GAAG,MAAM,SAAS,CAAC,KAAK,CAAC,MAAM,CAAC,IAAI,CAAC,CAAC;YAExD,eAAM,CAAC,KAAK,CAAC,wBAAwB,EAAE;gBACrC,SAAS,EAAE,aAAa;gBACxB,EAAE,EAAE,IAAI,CAAC,EAAE;aACZ,CAAC,CAAC;YAEH,OAAO,QAAQ,CAAC;QAClB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC7C,SAAS,EAAE,aAAa;gBACxB,EAAE,EAAE,IAAI,CAAC,EAAE;gBACX,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,0BAA0B,CACrC,aAAqB,EACrB,YAAoB;QAEpB,IAAI,CAAC;YACH,MAAM,EAAE,SAAS,EAAE,GAAG,MAAM,IAAI,CAAC,QAAQ,CAAC,UAAU,CAAC,iBAAiB,CAAC;gBACrE,EAAE,EAAE,aAAa;gBACjB,YAAY,EAAE,EAAE,KAAK,EAAE,CAAC,YAAY,CAAC,EAAE;aACxC,CAAC,CAAC;YAEH,IAAI,CAAC,UAAU,CAAC,GAAG,CAAC,aAAa,EAAE,SAAS,CAAC,CAAC;YAC9C,eAAM,CAAC,IAAI,CAAC,aAAa,aAAa,4BAA4B,CAAC,CAAC;YACpE,OAAO,SAAS,CAAC;QACnB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,aAAa,EAAE,EAAE;gBAC1D,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;YACH,MAAM,KAAK,CAAC;QACd,CAAC;IACH,CAAC;CACF;AAzTD,0CAyTC;AAED,4BAA4B;AACf,QAAA,EAAE,GAAG,eAAe,CAAC,WAAW,EAAE,CAAC;AAChD,kBAAe,UAAE,CAAC"}