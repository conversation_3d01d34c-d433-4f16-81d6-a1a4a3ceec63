/**
 * Organization Analytics Function
 * Handles organization-level analytics, reporting, and insights
 * Migrated from old-arch/src/analytics-service/organization/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get organization analytics handler
 */
export declare function getOrganizationAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
