"use strict";
/**
 * Event Service
 * Handles domain events and event publishing/subscribing
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.workflowEventService = exports.documentEventService = exports.eventService = exports.WorkflowEventService = exports.DocumentEventService = exports.EventService = void 0;
const logger_1 = require("../utils/logger");
const database_1 = require("./database");
const uuid_1 = require("uuid");
class EventService {
    constructor() {
        this.handlers = new Map();
        this.subscriptions = new Map();
    }
    /**
     * Publish a domain event
     */
    async publishEvent(event) {
        try {
            const domainEvent = {
                ...event,
                id: (0, uuid_1.v4)(),
                timestamp: new Date().toISOString()
            };
            // Store event in event store
            await this.storeEvent(domainEvent);
            // Process event handlers
            await this.processEvent(domainEvent);
            logger_1.logger.info('Event published successfully', {
                eventId: domainEvent.id,
                eventType: domainEvent.type,
                aggregateId: domainEvent.aggregateId,
                aggregateType: domainEvent.aggregateType
            });
            return domainEvent.id;
        }
        catch (error) {
            logger_1.logger.error('Failed to publish event', { error, event });
            throw error;
        }
    }
    /**
     * Subscribe to events
     */
    subscribe(eventType, handler) {
        const subscriptionId = (0, uuid_1.v4)();
        if (!this.handlers.has(eventType)) {
            this.handlers.set(eventType, []);
        }
        this.handlers.get(eventType).push(handler);
        const subscription = {
            id: subscriptionId,
            eventType,
            handler,
            isActive: true
        };
        this.subscriptions.set(subscriptionId, subscription);
        logger_1.logger.info('Event subscription created', {
            subscriptionId,
            eventType,
            handlerName: handler.constructor.name
        });
        return subscriptionId;
    }
    /**
     * Unsubscribe from events
     */
    unsubscribe(subscriptionId) {
        const subscription = this.subscriptions.get(subscriptionId);
        if (!subscription) {
            return false;
        }
        const handlers = this.handlers.get(subscription.eventType);
        if (handlers) {
            const index = handlers.indexOf(subscription.handler);
            if (index > -1) {
                handlers.splice(index, 1);
            }
        }
        this.subscriptions.delete(subscriptionId);
        logger_1.logger.info('Event subscription removed', {
            subscriptionId,
            eventType: subscription.eventType
        });
        return true;
    }
    /**
     * Store event in event store
     */
    async storeEvent(event) {
        try {
            await database_1.db.createItem('events', event);
        }
        catch (error) {
            logger_1.logger.error('Failed to store event', { error, eventId: event.id });
            throw error;
        }
    }
    /**
     * Process event by calling all registered handlers
     */
    async processEvent(event) {
        const handlers = this.handlers.get(event.type) || [];
        if (handlers.length === 0) {
            logger_1.logger.debug('No handlers registered for event type', { eventType: event.type });
            return;
        }
        const promises = handlers.map(async (handler) => {
            try {
                await handler.handle(event);
                logger_1.logger.debug('Event handler completed successfully', {
                    eventId: event.id,
                    eventType: event.type,
                    handlerName: handler.constructor.name
                });
            }
            catch (error) {
                logger_1.logger.error('Event handler failed', {
                    error,
                    eventId: event.id,
                    eventType: event.type,
                    handlerName: handler.constructor.name
                });
                // Don't throw here to prevent one handler failure from affecting others
            }
        });
        await Promise.allSettled(promises);
    }
    /**
     * Get events for an aggregate
     */
    async getEventsForAggregate(aggregateId, aggregateType) {
        try {
            const query = 'SELECT * FROM c WHERE c.aggregateId = @aggregateId AND c.aggregateType = @aggregateType ORDER BY c.version ASC';
            const events = await database_1.db.queryItems('events', query, [aggregateId, aggregateType]);
            return events;
        }
        catch (error) {
            logger_1.logger.error('Failed to get events for aggregate', { error, aggregateId, aggregateType });
            throw error;
        }
    }
    /**
     * Get events by type
     */
    async getEventsByType(eventType, limit = 100) {
        try {
            const query = 'SELECT * FROM c WHERE c.type = @eventType ORDER BY c.timestamp DESC OFFSET 0 LIMIT @limit';
            const events = await database_1.db.queryItems('events', query, [eventType, limit]);
            return events;
        }
        catch (error) {
            logger_1.logger.error('Failed to get events by type', { error, eventType });
            throw error;
        }
    }
    /**
     * Replay events for an aggregate
     */
    async replayEvents(aggregateId, aggregateType, fromVersion) {
        try {
            let query = 'SELECT * FROM c WHERE c.aggregateId = @aggregateId AND c.aggregateType = @aggregateType';
            const parameters = [aggregateId, aggregateType];
            if (fromVersion !== undefined) {
                query += ' AND c.version >= @param1';
                parameters.push(fromVersion.toString());
            }
            query += ' ORDER BY c.version ASC';
            const events = await database_1.db.queryItems('events', query, parameters);
            for (const event of events) {
                await this.processEvent(event);
            }
            logger_1.logger.info('Events replayed successfully', {
                aggregateId,
                aggregateType,
                eventCount: events.length,
                fromVersion
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to replay events', { error, aggregateId, aggregateType, fromVersion });
            throw error;
        }
    }
}
exports.EventService = EventService;
// Document Events
class DocumentEventService {
    constructor(eventService) {
        this.eventService = eventService;
    }
    async documentCreated(documentId, document, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'DocumentCreated',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document,
                createdBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async documentUpdated(documentId, changes, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'DocumentUpdated',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1, // This should be incremented based on current version
            data: {
                changes,
                updatedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async documentDeleted(documentId, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'DocumentDeleted',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                deletedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async documentShared(documentId, shareDetails, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'DocumentShared',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                shareDetails,
                sharedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async documentSigned(documentId, signatureDetails, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'DocumentSigned',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                signatureDetails,
                signedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
}
exports.DocumentEventService = DocumentEventService;
// Workflow Events
class WorkflowEventService {
    constructor(eventService) {
        this.eventService = eventService;
    }
    async workflowCreated(workflowId, workflow, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'WorkflowCreated',
            aggregateId: workflowId,
            aggregateType: 'Workflow',
            version: 1,
            data: {
                workflow,
                createdBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async workflowStarted(workflowId, executionId, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'WorkflowStarted',
            aggregateId: workflowId,
            aggregateType: 'Workflow',
            version: 1,
            data: {
                executionId,
                startedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
    async workflowCompleted(workflowId, executionId, result, userId, organizationId, tenantId) {
        await this.eventService.publishEvent({
            type: 'WorkflowCompleted',
            aggregateId: workflowId,
            aggregateType: 'Workflow',
            version: 1,
            data: {
                executionId,
                result,
                completedBy: userId
            },
            userId,
            organizationId,
            tenantId
        });
    }
}
exports.WorkflowEventService = WorkflowEventService;
// Export singleton instances
exports.eventService = new EventService();
exports.documentEventService = new DocumentEventService(exports.eventService);
exports.workflowEventService = new WorkflowEventService(exports.eventService);
//# sourceMappingURL=event.js.map