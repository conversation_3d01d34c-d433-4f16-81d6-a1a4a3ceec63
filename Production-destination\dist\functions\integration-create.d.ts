/**
 * Integration Create Function
 * Handles creation and management of external integrations
 * Migrated from old-arch/src/integration-service/create/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create integration handler
 */
export declare function createIntegration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
