"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createDashboard = createDashboard;
exports.getDashboard = getDashboard;
/**
 * Dashboard Management Function
 * Handles dashboard creation, customization, and widget management
 * Migrated from old-arch/src/analytics-service/dashboard/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Dashboard types and enums
var WidgetType;
(function (WidgetType) {
    WidgetType["CHART"] = "CHART";
    WidgetType["TABLE"] = "TABLE";
    WidgetType["METRIC"] = "METRIC";
    WidgetType["LIST"] = "LIST";
    WidgetType["MAP"] = "MAP";
    WidgetType["GAUGE"] = "GAUGE";
    WidgetType["PROGRESS"] = "PROGRESS";
    WidgetType["TEXT"] = "TEXT";
})(WidgetType || (WidgetType = {}));
var ChartType;
(function (ChartType) {
    ChartType["LINE"] = "LINE";
    ChartType["BAR"] = "BAR";
    ChartType["PIE"] = "PIE";
    ChartType["DOUGHNUT"] = "DOUGHNUT";
    ChartType["AREA"] = "AREA";
    ChartType["SCATTER"] = "SCATTER";
    ChartType["HEATMAP"] = "HEATMAP";
})(ChartType || (ChartType = {}));
var DashboardVisibility;
(function (DashboardVisibility) {
    DashboardVisibility["PRIVATE"] = "PRIVATE";
    DashboardVisibility["ORGANIZATION"] = "ORGANIZATION";
    DashboardVisibility["PROJECT"] = "PROJECT";
    DashboardVisibility["PUBLIC"] = "PUBLIC";
})(DashboardVisibility || (DashboardVisibility = {}));
// Validation schemas
const createDashboardSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    visibility: Joi.string().valid(...Object.values(DashboardVisibility)).default(DashboardVisibility.PRIVATE),
    layout: Joi.object({
        columns: Joi.number().min(1).max(12).default(12),
        rows: Joi.number().min(1).max(20).default(10),
        gap: Joi.number().min(0).max(50).default(16)
    }).optional(),
    widgets: Joi.array().items(Joi.object({
        id: Joi.string().optional(),
        type: Joi.string().valid(...Object.values(WidgetType)).required(),
        title: Joi.string().min(1).max(100).required(),
        position: Joi.object({
            x: Joi.number().min(0).required(),
            y: Joi.number().min(0).required(),
            width: Joi.number().min(1).max(12).required(),
            height: Joi.number().min(1).max(10).required()
        }).required(),
        configuration: Joi.object({
            chartType: Joi.string().valid(...Object.values(ChartType)).optional(),
            dataSource: Joi.string().required(),
            filters: Joi.object().optional(),
            refreshInterval: Joi.number().min(30).max(3600).default(300),
            showLegend: Joi.boolean().default(true),
            showGrid: Joi.boolean().default(true),
            colors: Joi.array().items(Joi.string()).optional()
        }).required()
    })).optional(),
    settings: Joi.object({
        autoRefresh: Joi.boolean().default(true),
        refreshInterval: Joi.number().min(60).max(3600).default(300),
        allowExport: Joi.boolean().default(true),
        allowSharing: Joi.boolean().default(false),
        theme: Joi.string().valid('light', 'dark', 'auto').default('light')
    }).optional()
});
const updateWidgetSchema = Joi.object({
    dashboardId: Joi.string().uuid().required(),
    widgetId: Joi.string().uuid().required(),
    title: Joi.string().min(1).max(100).optional(),
    position: Joi.object({
        x: Joi.number().min(0).optional(),
        y: Joi.number().min(0).optional(),
        width: Joi.number().min(1).max(12).optional(),
        height: Joi.number().min(1).max(10).optional()
    }).optional(),
    configuration: Joi.object().optional()
});
/**
 * Create dashboard handler
 */
async function createDashboard(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create dashboard started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createDashboardSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const dashboardRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(dashboardRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check dashboard creation limits
        const canCreate = await checkDashboardCreationLimits(dashboardRequest.organizationId);
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Create dashboard
        const dashboardId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Process widgets
        const widgets = (dashboardRequest.widgets || []).map(widget => ({
            ...widget,
            id: widget.id || (0, uuid_1.v4)(),
            configuration: {
                refreshInterval: 300,
                showLegend: true,
                showGrid: true,
                ...widget.configuration
            }
        }));
        const dashboard = {
            id: dashboardId,
            name: dashboardRequest.name,
            description: dashboardRequest.description,
            organizationId: dashboardRequest.organizationId,
            projectId: dashboardRequest.projectId,
            visibility: dashboardRequest.visibility || DashboardVisibility.PRIVATE,
            layout: {
                columns: 12,
                rows: 10,
                gap: 16,
                ...dashboardRequest.layout
            },
            widgets,
            settings: {
                autoRefresh: true,
                refreshInterval: 300,
                allowExport: true,
                allowSharing: false,
                theme: 'light',
                ...dashboardRequest.settings
            },
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            viewCount: 0,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('dashboards', dashboard);
        // Load initial widget data
        await loadWidgetData(dashboard);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "dashboard_created",
            userId: user.id,
            organizationId: dashboardRequest.organizationId,
            projectId: dashboardRequest.projectId,
            timestamp: now,
            details: {
                dashboardId,
                dashboardName: dashboardRequest.name,
                widgetCount: widgets.length,
                visibility: dashboard.visibility
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DashboardCreated',
            aggregateId: dashboardId,
            aggregateType: 'Dashboard',
            version: 1,
            data: {
                dashboard,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: dashboardRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Dashboard created successfully", {
            correlationId,
            dashboardId,
            dashboardName: dashboardRequest.name,
            widgetCount: widgets.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: dashboardId,
                name: dashboardRequest.name,
                visibility: dashboard.visibility,
                widgetCount: widgets.length,
                createdAt: now,
                message: "Dashboard created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create dashboard failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get dashboard handler
 */
async function getDashboard(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const dashboardId = request.params.dashboardId;
    logger_1.logger.info("Get dashboard started", { correlationId, dashboardId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!dashboardId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Dashboard ID is required" }
            }, request);
        }
        // Get dashboard
        const dashboard = await database_1.db.readItem('dashboards', dashboardId, dashboardId);
        if (!dashboard) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Dashboard not found" }
            }, request);
        }
        const dashboardData = dashboard;
        // Check access
        const hasAccess = await checkDashboardAccess(dashboardData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to dashboard" }
            }, request);
        }
        // Update view count and last viewed
        const updatedDashboard = {
            ...dashboardData,
            id: dashboardId,
            viewCount: (dashboardData.viewCount || 0) + 1,
            lastViewedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('dashboards', updatedDashboard);
        // Refresh widget data if auto-refresh is enabled
        if (dashboardData.settings?.autoRefresh) {
            await refreshWidgetData(dashboardData);
        }
        logger_1.logger.info("Dashboard retrieved successfully", {
            correlationId,
            dashboardId,
            dashboardName: dashboardData.name,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                ...dashboardData,
                viewCount: updatedDashboard.viewCount,
                lastViewedAt: updatedDashboard.lastViewedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get dashboard failed", {
            correlationId,
            dashboardId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkDashboardCreationLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxDashboards: 3 },
            'PROFESSIONAL': { maxDashboards: 25 },
            'ENTERPRISE': { maxDashboards: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxDashboards === -1) {
            return { allowed: true };
        }
        // Check current dashboard count
        const dashboardCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const countResult = await database_1.db.queryItems('dashboards', dashboardCountQuery, [organizationId]);
        const currentCount = Number(countResult[0]) || 0;
        if (currentCount >= limit.maxDashboards) {
            return {
                allowed: false,
                reason: `Dashboard limit reached (${limit.maxDashboards})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check dashboard creation limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
async function checkDashboardAccess(dashboard, userId) {
    try {
        // Check if user is the owner
        if (dashboard.createdBy === userId) {
            return true;
        }
        // Check visibility
        if (dashboard.visibility === DashboardVisibility.PUBLIC) {
            return true;
        }
        // Check organization membership
        if (dashboard.visibility === DashboardVisibility.ORGANIZATION) {
            const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [dashboard.organizationId, userId, 'ACTIVE']);
            return memberships.length > 0;
        }
        // Check project membership
        if (dashboard.visibility === DashboardVisibility.PROJECT && dashboard.projectId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('project-members', membershipQuery, [dashboard.projectId, userId, 'ACTIVE']);
            return memberships.length > 0;
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check dashboard access', { error, dashboardId: dashboard.id, userId });
        return false;
    }
}
async function loadWidgetData(dashboard) {
    try {
        for (const widget of dashboard.widgets) {
            widget.data = await getWidgetData(widget, dashboard.organizationId);
            widget.lastUpdated = new Date().toISOString();
        }
        // Update dashboard with widget data
        await database_1.db.updateItem('dashboards', dashboard);
    }
    catch (error) {
        logger_1.logger.error('Failed to load widget data', { error, dashboardId: dashboard.id });
    }
}
async function refreshWidgetData(dashboard) {
    try {
        let hasUpdates = false;
        for (const widget of dashboard.widgets) {
            const lastUpdated = new Date(widget.lastUpdated || 0);
            const refreshInterval = widget.configuration?.refreshInterval || 300;
            const shouldRefresh = Date.now() - lastUpdated.getTime() > refreshInterval * 1000;
            if (shouldRefresh) {
                widget.data = await getWidgetData(widget, dashboard.organizationId);
                widget.lastUpdated = new Date().toISOString();
                hasUpdates = true;
            }
        }
        if (hasUpdates) {
            await database_1.db.updateItem('dashboards', dashboard);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to refresh widget data', { error, dashboardId: dashboard.id });
    }
}
async function getWidgetData(widget, organizationId) {
    try {
        // Simplified widget data generation based on data source
        const dataSource = widget.configuration.dataSource;
        switch (dataSource) {
            case 'documents':
                return await getDocumentMetrics(organizationId);
            case 'users':
                return await getUserMetrics(organizationId);
            case 'workflows':
                return await getWorkflowMetrics(organizationId);
            case 'activities':
                return await getActivityMetrics(organizationId);
            default:
                return { message: 'No data available' };
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to get widget data', { error, widgetId: widget.id });
        return { error: 'Failed to load data' };
    }
}
async function getDocumentMetrics(organizationId) {
    // Simplified metrics - in production, this would query actual data
    return {
        totalDocuments: 1250,
        documentsThisMonth: 85,
        averageProcessingTime: 45,
        topCategories: [
            { name: 'Contracts', count: 320 },
            { name: 'Reports', count: 280 },
            { name: 'Invoices', count: 195 }
        ]
    };
}
async function getUserMetrics(organizationId) {
    return {
        totalUsers: 45,
        activeUsers: 38,
        newUsersThisMonth: 5,
        topContributors: [
            { name: 'John Smith', documents: 25 },
            { name: 'Jane Doe', documents: 18 },
            { name: 'Bob Johnson', documents: 15 }
        ]
    };
}
async function getWorkflowMetrics(organizationId) {
    return {
        totalWorkflows: 125,
        activeWorkflows: 23,
        completedThisMonth: 45,
        averageCompletionTime: 3.2
    };
}
async function getActivityMetrics(organizationId) {
    return {
        totalActivities: 2850,
        activitiesToday: 125,
        mostActiveHour: 14,
        activityTrend: [
            { date: '2024-01-01', count: 45 },
            { date: '2024-01-02', count: 52 },
            { date: '2024-01-03', count: 38 }
        ]
    };
}
// Register functions
functions_1.app.http('dashboard-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'dashboards',
    handler: createDashboard
});
functions_1.app.http('dashboard-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'dashboards/{dashboardId}',
    handler: getDashboard
});
//# sourceMappingURL=dashboard-management.js.map