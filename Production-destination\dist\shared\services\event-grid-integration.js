"use strict";
/**
 * Enhanced Event Grid Integration Service
 * Provides comprehensive Event Grid integration capabilities with production-ready features:
 * - Advanced filtering and routing
 * - Event batching and throttling
 * - Dead letter handling
 * - Custom retry policies
 * - Event validation and schema registry
 * - Performance monitoring
 * - Multi-topic publishing
 * - Event deduplication
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.eventGridIntegration = exports.EventGridIntegrationService = void 0;
exports.createEventGridIntegration = createEventGridIntegration;
const eventgrid_1 = require("@azure/eventgrid");
const core_auth_1 = require("@azure/core-auth");
const logger_1 = require("../utils/logger");
const redis_1 = require("./redis");
const database_1 = require("./database");
/**
 * Event Grid Integration Service Class
 */
class EventGridIntegrationService {
    constructor(config) {
        this.clients = new Map();
        this.eventQueue = [];
        this.schemas = new Map();
        this.isProcessing = false;
        this.lastBatchTime = 0;
        this.defaultRetryPolicy = {
            maxRetries: 3,
            retryDelay: 1000,
            exponentialBackoff: true,
            maxRetryDelay: 30000
        };
        this.config = {
            retryAttempts: 3,
            timeoutMs: 30000,
            batchSize: 10,
            throttleMs: 1000,
            ...config
        };
        this.metrics = {
            totalEvents: 0,
            successfulEvents: 0,
            failedEvents: 0,
            averageProcessingTime: 0,
            eventsFiltered: 0,
            eventsDeadLettered: 0,
            eventsBatched: 0,
            throttleCount: 0
        };
        this.initializeClient();
        this.startBatchProcessing();
        this.startPeriodicTasks();
    }
    /**
     * Initialize Event Grid client
     */
    initializeClient() {
        try {
            // Initialize default client
            const defaultClient = new eventgrid_1.EventGridPublisherClient(this.config.endpoint, "EventGrid", new core_auth_1.AzureKeyCredential(this.config.accessKey));
            this.clients.set('default', defaultClient);
            logger_1.logger.info('Event Grid client initialized successfully', {
                endpoint: this.config.endpoint
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Event Grid client', {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Register event schema for validation
     */
    registerEventSchema(schema) {
        this.schemas.set(schema.eventType, schema);
        logger_1.logger.info('Event schema registered', {
            eventType: schema.eventType,
            version: schema.version
        });
    }
    /**
     * Queue event for batch processing
     */
    async queueEvent(eventData) {
        // Validate event
        if (!await this.validateEventData(eventData)) {
            this.metrics.eventsFiltered++;
            return;
        }
        // Apply filters
        if (eventData.filters && !this.applyFilters(eventData, eventData.filters)) {
            this.metrics.eventsFiltered++;
            return;
        }
        // Add to queue
        this.eventQueue.push({
            ...eventData,
            id: eventData.id || this.generateEventId(),
            eventTime: eventData.eventTime || new Date()
        });
        logger_1.logger.debug('Event queued for batch processing', {
            eventType: eventData.eventType,
            queueSize: this.eventQueue.length
        });
        // Trigger batch processing if queue is full
        if (this.eventQueue.length >= this.config.batchSize) {
            await this.processBatch();
        }
    }
    /**
     * Publish a single event with enhanced features
     */
    async publishEvent(eventData, retryPolicy) {
        const startTime = Date.now();
        const eventId = eventData.id || this.generateEventId();
        try {
            // Validate event against schema
            if (!await this.validateEventData(eventData)) {
                logger_1.logger.warn('Event validation failed', { eventType: eventData.eventType, eventId });
                this.metrics.eventsFiltered++;
                return null;
            }
            // Apply filters
            if (eventData.filters && !this.applyFilters(eventData, eventData.filters)) {
                logger_1.logger.debug('Event filtered out', { eventType: eventData.eventType, eventId });
                this.metrics.eventsFiltered++;
                return null;
            }
            // Check for duplicate
            if (await this.isDuplicateEvent(eventId)) {
                logger_1.logger.info('Duplicate event detected, skipping', { eventId });
                return eventId;
            }
            const policy = retryPolicy || this.defaultRetryPolicy;
            let attempt = 0;
            while (attempt <= policy.maxRetries) {
                try {
                    const client = await this.getClient(eventData.topic ? this.getTopicEndpoint(eventData.topic) : this.config.endpoint, this.config.accessKey);
                    const event = {
                        id: eventId,
                        eventType: eventData.eventType,
                        subject: eventData.subject,
                        eventTime: eventData.eventTime || new Date(),
                        data: eventData.data,
                        dataVersion: eventData.dataVersion || '1.0'
                    };
                    await client.send([event]);
                    // Mark as published
                    await this.markEventAsPublished(eventId);
                    const processingTime = Date.now() - startTime;
                    this.updateMetrics(true, processingTime);
                    logger_1.logger.info('Event published successfully', {
                        eventId,
                        eventType: eventData.eventType,
                        subject: eventData.subject,
                        attempt,
                        processingTime
                    });
                    return eventId;
                }
                catch (error) {
                    attempt++;
                    this.metrics.failedEvents++;
                    logger_1.logger.error('Error publishing event', {
                        eventType: eventData.eventType,
                        eventId,
                        attempt,
                        error: error instanceof Error ? error.message : String(error)
                    });
                    if (attempt <= policy.maxRetries) {
                        const delay = this.calculateRetryDelay(attempt, policy);
                        await new Promise(resolve => setTimeout(resolve, delay));
                    }
                }
            }
            // Send to dead letter if all retries failed
            await this.sendToDeadLetter(eventData, 'max_retries_exceeded');
            return null;
        }
        catch (error) {
            this.metrics.failedEvents++;
            logger_1.logger.error('Unexpected error in publishEvent', {
                eventType: eventData.eventType,
                eventId,
                error: error instanceof Error ? error.message : String(error)
            });
            return null;
        }
    }
    /**
     * Publish multiple events in batch
     */
    async publishEvents(events) {
        const startTime = Date.now();
        const eventIds = [];
        try {
            const client = this.clients.get('default');
            if (!client) {
                throw new Error('Event Grid client not initialized');
            }
            const eventGridEvents = events.map(eventData => {
                const eventId = `${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
                eventIds.push(eventId);
                return {
                    id: eventId,
                    eventType: eventData.eventType,
                    subject: eventData.subject,
                    eventTime: eventData.eventTime || new Date(),
                    data: eventData.data,
                    dataVersion: eventData.dataVersion || '1.0'
                };
            });
            await client.send(eventGridEvents);
            const processingTime = Date.now() - startTime;
            this.updateMetrics(true, processingTime, events.length);
            logger_1.logger.info('Batch events published successfully', {
                eventCount: events.length,
                eventIds,
                processingTime
            });
            return eventIds;
        }
        catch (error) {
            const processingTime = Date.now() - startTime;
            this.updateMetrics(false, processingTime, events.length);
            logger_1.logger.error('Failed to publish batch events', {
                eventCount: events.length,
                error: error instanceof Error ? error.message : String(error),
                processingTime
            });
            throw error;
        }
    }
    /**
     * Publish event with retry logic
     */
    async publishEventWithRetry(eventData) {
        let lastError = null;
        for (let attempt = 1; attempt <= this.config.retryAttempts; attempt++) {
            try {
                const result = await this.publishEvent(eventData);
                if (result) {
                    return result;
                }
            }
            catch (error) {
                lastError = error instanceof Error ? error : new Error(String(error));
                if (attempt < this.config.retryAttempts) {
                    const delay = Math.pow(2, attempt) * 1000; // Exponential backoff
                    logger_1.logger.warn(`Event publish attempt ${attempt} failed, retrying in ${delay}ms`, {
                        eventType: eventData.eventType,
                        subject: eventData.subject,
                        error: lastError.message
                    });
                    await new Promise(resolve => setTimeout(resolve, delay));
                }
            }
        }
        if (lastError) {
            throw lastError;
        }
        return null;
    }
    /**
     * Enhanced event validation with schema support
     */
    async validateEventData(eventData) {
        if (!eventData) {
            logger_1.logger.warn('Event validation failed - eventData is null or undefined');
            return false;
        }
        const requiredFields = ['eventType', 'subject', 'data'];
        for (const field of requiredFields) {
            const value = eventData[field];
            if (!value || (typeof value === 'string' && value.trim() === '')) {
                logger_1.logger.warn('Event validation failed - missing or empty field', {
                    field,
                    eventType: eventData.eventType,
                    subject: eventData.subject
                });
                return false;
            }
        }
        // Validate event type format
        if (!/^[A-Za-z0-9]+\.[A-Za-z0-9]+$/.test(eventData.eventType)) {
            logger_1.logger.warn('Event validation failed - invalid eventType format', {
                eventType: eventData.eventType,
                expectedFormat: 'Category.Action (e.g., Document.Uploaded)'
            });
            return false;
        }
        // Validate data is an object
        if (typeof eventData.data !== 'object' || eventData.data === null) {
            logger_1.logger.warn('Event validation failed - data must be an object', {
                eventType: eventData.eventType,
                subject: eventData.subject,
                dataType: typeof eventData.data
            });
            return false;
        }
        // Schema validation
        const schema = this.schemas.get(eventData.eventType);
        if (schema) {
            try {
                for (const field of schema.required) {
                    if (!eventData.data || !(field in eventData.data)) {
                        logger_1.logger.warn('Event validation failed - missing required schema field', {
                            field,
                            eventType: eventData.eventType,
                            schema: schema.version
                        });
                        return false;
                    }
                }
            }
            catch (error) {
                logger_1.logger.warn('Event validation failed - schema validation error', {
                    eventType: eventData.eventType,
                    error: error instanceof Error ? error.message : String(error)
                });
                return false;
            }
        }
        return true;
    }
    /**
     * Apply event filters
     */
    applyFilters(eventData, filters) {
        return filters.every(filter => this.evaluateFilter(eventData, filter));
    }
    /**
     * Evaluate a single filter
     */
    evaluateFilter(eventData, filter) {
        // Get the field value from the event data
        const fieldValue = this.getFieldValue(eventData, filter.field);
        if (fieldValue === undefined || fieldValue === null) {
            return false;
        }
        const filterValue = filter.value;
        const fieldStr = String(fieldValue).toLowerCase();
        switch (filter.operator) {
            case 'equals':
                return fieldStr === String(filterValue).toLowerCase();
            case 'contains':
                return fieldStr.includes(String(filterValue).toLowerCase());
            case 'startsWith':
                return fieldStr.startsWith(String(filterValue).toLowerCase());
            case 'endsWith':
                return fieldStr.endsWith(String(filterValue).toLowerCase());
            case 'in':
                return Array.isArray(filterValue) &&
                    filterValue.some(v => String(v).toLowerCase() === fieldStr);
            case 'notIn':
                return Array.isArray(filterValue) &&
                    !filterValue.some(v => String(v).toLowerCase() === fieldStr);
            default:
                return true;
        }
    }
    /**
     * Get field value from event data using dot notation
     */
    getFieldValue(eventData, fieldPath) {
        const parts = fieldPath.split('.');
        let value = eventData;
        for (const part of parts) {
            if (value && typeof value === 'object' && part in value) {
                value = value[part];
            }
            else {
                return undefined;
            }
        }
        return value;
    }
    /**
     * Update metrics
     */
    updateMetrics(success, processingTime, eventCount = 1) {
        this.metrics.totalEvents += eventCount;
        if (success) {
            this.metrics.successfulEvents += eventCount;
        }
        else {
            this.metrics.failedEvents += eventCount;
        }
        // Update average processing time
        const totalProcessingTime = this.metrics.averageProcessingTime * (this.metrics.totalEvents - eventCount) + processingTime;
        this.metrics.averageProcessingTime = totalProcessingTime / this.metrics.totalEvents;
        this.metrics.lastEventTime = new Date();
    }
    /**
     * Get current metrics
     */
    getMetrics() {
        return { ...this.metrics };
    }
    /**
     * Reset metrics
     */
    resetMetrics() {
        this.metrics = {
            totalEvents: 0,
            successfulEvents: 0,
            failedEvents: 0,
            averageProcessingTime: 0,
            eventsFiltered: 0,
            eventsDeadLettered: 0,
            eventsBatched: 0,
            throttleCount: 0
        };
    }
    // Enhanced helper methods
    generateEventId() {
        return `evt-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`;
    }
    async getClient(endpoint, accessKey) {
        const clientKey = `${endpoint}:${accessKey}`;
        if (!this.clients.has(clientKey)) {
            const client = new eventgrid_1.EventGridPublisherClient(endpoint, "EventGrid", new core_auth_1.AzureKeyCredential(accessKey));
            this.clients.set(clientKey, client);
        }
        return this.clients.get(clientKey);
    }
    getTopicEndpoint(topic) {
        return this.config.endpoint; // Simplified - could be enhanced to support multiple topics
    }
    async isDuplicateEvent(eventId) {
        const key = `eventgrid:published:${eventId}`;
        return await redis_1.redis.exists(key);
    }
    async markEventAsPublished(eventId) {
        const key = `eventgrid:published:${eventId}`;
        await redis_1.redis.setex(key, 3600, 'published');
    }
    calculateRetryDelay(attempt, policy) {
        if (!policy.exponentialBackoff) {
            return policy.retryDelay;
        }
        const delay = policy.retryDelay * Math.pow(2, attempt - 1);
        return Math.min(delay, policy.maxRetryDelay);
    }
    async sendToDeadLetter(eventData, reason) {
        try {
            await database_1.db.createItem('dead-letter-events', {
                id: `dl-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                originalEventId: eventData.id,
                eventType: eventData.eventType,
                subject: eventData.subject,
                data: eventData.data,
                reason,
                timestamp: new Date().toISOString()
            });
            this.metrics.eventsDeadLettered++;
        }
        catch (error) {
            logger_1.logger.error('Failed to send event to dead letter', {
                eventType: eventData.eventType,
                eventId: eventData.id,
                error: error instanceof Error ? error.message : String(error)
            });
        }
    }
    startBatchProcessing() {
        setInterval(async () => {
            if (this.eventQueue.length > 0 && !this.isProcessing) {
                const now = Date.now();
                if (now - this.lastBatchTime >= this.config.throttleMs) {
                    await this.processBatch();
                }
            }
        }, this.config.throttleMs / 2);
    }
    async processBatch() {
        if (this.isProcessing || this.eventQueue.length === 0) {
            return;
        }
        this.isProcessing = true;
        this.lastBatchTime = Date.now();
        try {
            const batchSize = Math.min(this.eventQueue.length, this.config.batchSize);
            const batch = this.eventQueue.splice(0, batchSize);
            if (batch.length > 0) {
                await this.publishEvents(batch);
                this.metrics.eventsBatched += batch.length;
            }
        }
        catch (error) {
            logger_1.logger.error('Error processing event batch', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        finally {
            this.isProcessing = false;
        }
    }
    startPeriodicTasks() {
        // Periodic metrics publishing
        setInterval(async () => {
            try {
                await this.publishEvent({
                    eventType: 'EventGrid.Metrics',
                    subject: 'eventgrid/metrics',
                    data: this.getMetrics()
                });
            }
            catch (error) {
                // Don't log errors for metrics publishing to avoid recursion
            }
        }, 60000); // Every minute
    }
    /**
     * Health check
     */
    async healthCheck() {
        try {
            const testEvent = {
                eventType: 'Test.HealthCheck',
                subject: 'health/check',
                data: {
                    test: true,
                    timestamp: new Date().toISOString()
                }
            };
            await this.publishEvent(testEvent);
            return true;
        }
        catch (error) {
            logger_1.logger.error('Event Grid health check failed', {
                error: error instanceof Error ? error.message : String(error)
            });
            return false;
        }
    }
}
exports.EventGridIntegrationService = EventGridIntegrationService;
/**
 * Create Event Grid Integration Service instance
 */
function createEventGridIntegration() {
    const config = {
        endpoint: process.env.EVENT_GRID_TOPIC_ENDPOINT,
        accessKey: process.env.EVENT_GRID_TOPIC_KEY,
        retryAttempts: parseInt(process.env.EVENT_GRID_RETRY_ATTEMPTS || '3'),
        timeoutMs: parseInt(process.env.EVENT_GRID_TIMEOUT_MS || '30000')
    };
    if (!config.endpoint || !config.accessKey) {
        throw new Error('Event Grid configuration missing - check EVENT_GRID_TOPIC_ENDPOINT and EVENT_GRID_TOPIC_KEY');
    }
    return new EventGridIntegrationService(config);
}
// Export singleton instance
exports.eventGridIntegration = createEventGridIntegration();
//# sourceMappingURL=event-grid-integration.js.map