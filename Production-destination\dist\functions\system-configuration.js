"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getConfiguration = getConfiguration;
exports.updateConfiguration = updateConfiguration;
/**
 * System Configuration Function
 * Handles system-wide configuration management
 * Migrated from old-arch/src/admin-service/configuration/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Configuration types and enums
var ConfigurationType;
(function (ConfigurationType) {
    ConfigurationType["SYSTEM"] = "SYSTEM";
    ConfigurationType["ORGANIZATION"] = "ORGANIZATION";
    ConfigurationType["USER"] = "USER";
    ConfigurationType["FEATURE"] = "FEATURE";
    ConfigurationType["INTEGRATION"] = "INTEGRATION";
})(ConfigurationType || (ConfigurationType = {}));
var ConfigurationScope;
(function (ConfigurationScope) {
    ConfigurationScope["GLOBAL"] = "GLOBAL";
    ConfigurationScope["TENANT"] = "TENANT";
    ConfigurationScope["ORGANIZATION"] = "ORGANIZATION";
    ConfigurationScope["USER"] = "USER";
})(ConfigurationScope || (ConfigurationScope = {}));
// Validation schemas
const updateConfigurationSchema = Joi.object({
    key: Joi.string().min(2).max(100).required(),
    value: Joi.any().required(),
    type: Joi.string().valid(...Object.values(ConfigurationType)).required(),
    scope: Joi.string().valid(...Object.values(ConfigurationScope)).required(),
    organizationId: Joi.string().uuid().optional(),
    userId: Joi.string().uuid().optional(),
    description: Joi.string().max(500).optional(),
    encrypted: Joi.boolean().default(false),
    metadata: Joi.object().optional()
});
/**
 * Get configuration handler
 */
async function getConfiguration(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Get configuration started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const url = new URL(request.url);
        const key = url.searchParams.get('key');
        const scope = url.searchParams.get('scope') || ConfigurationScope.GLOBAL;
        const organizationId = url.searchParams.get('organizationId');
        if (!key) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Configuration key is required" }
            }, request);
        }
        // Check access permissions
        const hasAccess = await checkConfigurationAccess(user, scope, organizationId || undefined);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to configuration" }
            }, request);
        }
        // Get configuration
        const configuration = await getConfigurationValue(key, scope, organizationId || undefined, user.id);
        if (!configuration) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Configuration not found" }
            }, request);
        }
        logger_1.logger.info("Configuration retrieved successfully", {
            correlationId,
            key,
            scope,
            organizationId,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                key: configuration.key,
                value: configuration.value,
                type: configuration.type,
                scope: configuration.scope,
                description: configuration.description,
                version: configuration.version,
                updatedAt: configuration.updatedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get configuration failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update configuration handler
 */
async function updateConfiguration(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Update configuration started", { correlationId });
    try {
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkConfigurationAccess(user, ConfigurationScope.GLOBAL);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to configuration management" }
            }, request);
        }
        const body = await request.json();
        const { error, value } = updateConfigurationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const configRequest = value;
        // Get existing configuration or create new
        const existing = await getConfigurationValue(configRequest.key, configRequest.scope, configRequest.organizationId, configRequest.userId);
        const configId = existing?.id || (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const configuration = {
            id: configId,
            key: configRequest.key,
            value: configRequest.encrypted ? await encryptValue(configRequest.value) : configRequest.value,
            type: configRequest.type,
            scope: configRequest.scope,
            organizationId: configRequest.organizationId,
            userId: configRequest.userId,
            description: configRequest.description,
            encrypted: configRequest.encrypted,
            metadata: configRequest.metadata || {},
            version: existing ? existing.version + 1 : 1,
            createdBy: existing?.createdBy || user.id,
            createdAt: existing?.createdAt || now,
            updatedBy: user.id,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        if (existing) {
            await database_1.db.updateItem('configurations', configuration);
        }
        else {
            await database_1.db.createItem('configurations', configuration);
        }
        // Cache configuration for fast access
        await cacheConfiguration(configuration);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: existing ? "configuration_updated" : "configuration_created",
            userId: user.id,
            organizationId: configRequest.organizationId,
            timestamp: now,
            details: {
                configurationKey: configRequest.key,
                configurationType: configRequest.type,
                configurationScope: configRequest.scope,
                encrypted: configRequest.encrypted,
                version: configuration.version
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: existing ? 'ConfigurationUpdated' : 'ConfigurationCreated',
            aggregateId: configId,
            aggregateType: 'Configuration',
            version: configuration.version,
            data: {
                configuration: {
                    ...configuration,
                    value: configRequest.encrypted ? '[ENCRYPTED]' : configuration.value
                },
                updatedBy: user.id
            },
            userId: user.id,
            organizationId: configRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Configuration updated successfully", {
            correlationId,
            configurationKey: configRequest.key,
            configurationType: configRequest.type,
            configurationScope: configRequest.scope,
            version: configuration.version,
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: existing ? 200 : 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                key: configuration.key,
                type: configuration.type,
                scope: configuration.scope,
                version: configuration.version,
                updatedAt: configuration.updatedAt,
                message: existing ? "Configuration updated successfully" : "Configuration created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update configuration failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkConfigurationAccess(user, scope, organizationId) {
    try {
        // Check if user has admin role
        if (user.roles?.includes('admin') || user.roles?.includes('config_admin')) {
            return true;
        }
        // For organization scope, check organization access
        if (scope === ConfigurationScope.ORGANIZATION && organizationId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
            if (memberships.length > 0) {
                const membership = memberships[0];
                return membership.role === 'OWNER' || membership.role === 'ADMIN';
            }
        }
        // For user scope, check if it's the same user
        if (scope === ConfigurationScope.USER) {
            return true; // Users can access their own configurations
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check configuration access', { error, userId: user.id, scope, organizationId });
        return false;
    }
}
async function getConfigurationValue(key, scope, organizationId, userId) {
    try {
        const { cacheAside } = await Promise.resolve().then(() => __importStar(require('../shared/services/cache-aside')));
        // Build cache key
        const cacheKey = `${scope}:${organizationId || 'global'}:${userId || 'global'}:${key}`;
        // Build database query
        let query = 'SELECT * FROM c WHERE c.key = @key AND c.scope = @scope';
        const parameters = [key, scope];
        if (organizationId) {
            query += ' AND c.organizationId = @orgId';
            parameters.push(organizationId);
        }
        if (userId) {
            query += ' AND c.userId = @userId';
            parameters.push(userId);
        }
        const config = await cacheAside.get(cacheKey, {
            containerName: 'configurations',
            query,
            parameters
        }, {
            ttlSeconds: 300, // 5 minutes cache
            cachePrefix: 'config',
            enableFallback: true,
            enableWarming: true,
            warmingPriority: 'medium',
            eventDriven: true
        });
        if (config && config.encrypted) {
            // Decrypt if encrypted (this should be done after retrieval)
            config.value = await decryptValue(config.value);
        }
        return config;
    }
    catch (error) {
        logger_1.logger.error('Failed to get configuration value', { error, key, scope, organizationId, userId });
        return null;
    }
}
async function cacheConfiguration(configuration) {
    try {
        const cacheKey = `config:${configuration.scope}:${configuration.organizationId || 'global'}:${configuration.userId || 'global'}:${configuration.key}`;
        await redis_1.redis.setex(cacheKey, 300, JSON.stringify(configuration));
        // Also add to configuration list cache
        const listCacheKey = `configs:${configuration.scope}:${configuration.organizationId || 'global'}`;
        await redis_1.redis.sadd(listCacheKey, configuration.key);
        await redis_1.redis.expire(listCacheKey, 300);
    }
    catch (error) {
        logger_1.logger.error('Failed to cache configuration', { error, configId: configuration.id });
    }
}
async function encryptValue(value) {
    try {
        // Simplified encryption - in production use proper encryption service
        const crypto = require('crypto');
        const algorithm = 'aes-256-gcm';
        const key = crypto.scryptSync(process.env.CONFIG_ENCRYPTION_KEY || 'default-key', 'salt', 32);
        const iv = crypto.randomBytes(16);
        const cipher = crypto.createCipher(algorithm, key);
        let encrypted = cipher.update(JSON.stringify(value), 'utf8', 'hex');
        encrypted += cipher.final('hex');
        return `${iv.toString('hex')}:${encrypted}`;
    }
    catch (error) {
        logger_1.logger.error('Failed to encrypt configuration value', { error });
        return JSON.stringify(value); // Fallback to unencrypted
    }
}
async function decryptValue(encryptedValue) {
    try {
        // Simplified decryption - in production use proper encryption service
        const crypto = require('crypto');
        const algorithm = 'aes-256-gcm';
        const key = crypto.scryptSync(process.env.CONFIG_ENCRYPTION_KEY || 'default-key', 'salt', 32);
        const [_, encrypted] = encryptedValue.split(':');
        const decipher = crypto.createDecipher(algorithm, key);
        let decrypted = decipher.update(encrypted, 'hex', 'utf8');
        decrypted += decipher.final('utf8');
        return JSON.parse(decrypted);
    }
    catch (error) {
        logger_1.logger.error('Failed to decrypt configuration value', { error });
        return encryptedValue; // Fallback to encrypted value
    }
}
// Register functions
functions_1.app.http('config-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'config',
    handler: getConfiguration
});
functions_1.app.http('config-update', {
    methods: ['PUT', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/configuration',
    handler: updateConfiguration
});
//# sourceMappingURL=system-configuration.js.map