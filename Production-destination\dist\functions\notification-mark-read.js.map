{"version": 3, "file": "notification-mark-read.js", "sourceRoot": "", "sources": ["../../src/functions/notification-mark-read.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwBA,sDAgJC;AAKD,0CAiHC;AA9RD;;;GAGG;AACH,gDAAyF;AACzF,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,qBAAqB;AACrB,MAAM,cAAc,GAAG,GAAG,CAAC,MAAM,CAAC;IAChC,eAAe,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IACzE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;IACrC,MAAM,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IACnC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC1C,CAAC,CAAC,EAAE,CAAC,iBAAiB,EAAE,SAAS,CAAC,CAAC;AAEpC;;GAEG;AACI,KAAK,UAAU,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;IAC1F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,cAAc,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEvD,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,EAAE,eAAe,EAAE,OAAO,EAAE,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAE9E,IAAI,YAAY,GAAG,CAAC,CAAC;QACrB,IAAI,qBAAqB,GAAU,EAAE,CAAC;QAEtC,IAAI,OAAO,EAAE,CAAC;YACZ,8CAA8C;YAC9C,IAAI,SAAS,GAAG,uEAAuE,CAAC;YACxF,MAAM,UAAU,GAAU,CAAC,IAAI,CAAC,EAAE,EAAE,MAAM,CAAC,CAAC;YAE5C,uBAAuB;YACvB,IAAI,IAAI,CAAC,QAAQ,EAAE,CAAC;gBAClB,SAAS,IAAI,qDAAqD,CAAC;gBACnE,UAAU,CAAC,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YACjC,CAAC;YAED,qCAAqC;YACrC,IAAI,cAAc,EAAE,CAAC;gBACnB,SAAS,IAAI,yCAAyC,CAAC;gBACvD,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;YAClC,CAAC;YAED,gCAAgC;YAChC,IAAI,SAAS,EAAE,CAAC;gBACd,SAAS,IAAI,+BAA+B,CAAC;gBAC7C,UAAU,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;YAC7B,CAAC;YAED,yCAAyC;YACzC,SAAS,IAAI,kDAAkD,CAAC;YAChE,UAAU,CAAC,IAAI,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC;YAE1C,qBAAqB,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,eAAe,EAAE,SAAS,EAAE,UAAU,CAAC,CAAC;QAEtF,CAAC;aAAM,IAAI,eAAe,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzD,sCAAsC;YACtC,KAAK,MAAM,cAAc,IAAI,eAAe,EAAE,CAAC;gBAC7C,MAAM,YAAY,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;gBAExF,IAAI,YAAY,IAAK,YAAoB,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;oBAClE,qBAAqB,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC;gBAC3C,CAAC;YACH,CAAC;QACH,CAAC;QAED,uBAAuB;QACvB,KAAK,MAAM,YAAY,IAAI,qBAAqB,EAAE,CAAC;YACjD,MAAM,mBAAmB,GAAG;gBAC1B,GAAI,YAAoB;gBACxB,EAAE,EAAG,YAAoB,CAAC,EAAE;gBAC5B,MAAM;gBACN,MAAM,EAAE,MAAM,CAAC,CAAC,CAAC,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC,CAAC,CAAC,SAAS;aACtD,CAAC;YAEF,MAAM,aAAE,CAAC,UAAU,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;YAC1D,YAAY,EAAE,CAAC;QACjB,CAAC;QAED,2DAA2D;QAC3D,IAAI,YAAY,GAAG,CAAC,EAAE,CAAC;YACrB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;gBAChC,EAAE,EAAE,IAAA,SAAM,GAAE;gBACZ,IAAI,EAAE,2BAA2B;gBACjC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,cAAc;gBACd,SAAS;gBACT,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,OAAO,EAAE;oBACP,YAAY;oBACZ,MAAM;oBACN,OAAO;iBACR;gBACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;aACxB,CAAC,CAAC;QACL,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;YACpD,aAAa;YACb,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,YAAY;YACZ,MAAM;YACN,OAAO;SACR,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,YAAY;gBACZ,OAAO,EAAE,GAAG,YAAY,4BAA4B,MAAM,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ,EAAE;aACjF;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,OAAoB,EAAE,OAA0B;IACpF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,cAAc,GAAG,OAAO,CAAC,MAAM,CAAC,cAAc,CAAC;IAErD,IAAI,CAAC,cAAc,EAAE,CAAC;QACpB,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,6BAA6B,EAAE;SACnD,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,aAAa,EAAE,cAAc,EAAE,CAAC,CAAC;IAE3E,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,mBAAmB;QACnB,MAAM,YAAY,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,eAAe,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;QACxF,IAAI,CAAC,YAAY,EAAE,CAAC;YAClB,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,wBAAwB,EAAE;aAC9C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,iCAAiC;QACjC,IAAK,YAAoB,CAAC,WAAW,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;YAClD,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,IAAI,CAAE,YAAoB,CAAC,MAAM,EAAE,CAAC;YAClC,MAAM,mBAAmB,GAAG;gBAC1B,GAAI,YAAoB;gBACxB,EAAE,EAAE,cAAc;gBAClB,MAAM,EAAE,IAAI;gBACZ,MAAM,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACjC,CAAC;YAEF,MAAM,aAAE,CAAC,UAAU,CAAC,eAAe,EAAE,mBAAmB,CAAC,CAAC;QAC5D,CAAC;QAED,yBAAyB;QACzB,IAAI,UAAU,GAAG,QAAQ,CAAC;QAC1B,IAAI,WAAW,GAAG,EAAE,CAAC;QAErB,IAAK,YAAoB,CAAC,QAAQ,EAAE,CAAC;YACnC,IAAI,CAAC;gBACH,MAAM,MAAM,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,OAAO,EAAG,YAAoB,CAAC,QAAQ,EAAG,YAAoB,CAAC,QAAQ,CAAC,CAAC;gBAC1G,IAAI,MAAM,EAAE,CAAC;oBACX,UAAU,GAAI,MAAc,CAAC,IAAI,IAAK,MAAc,CAAC,KAAK,IAAI,cAAc,CAAC;oBAC7E,WAAW,GAAI,MAAc,CAAC,KAAK,IAAI,EAAE,CAAC;gBAC5C,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,iCAAiC;YACnC,CAAC;QACH,CAAC;QAED,MAAM,oBAAoB,GAAG;YAC3B,GAAI,YAAoB;YACxB,UAAU;YACV,WAAW;YACX,SAAS,EAAG,YAAoB,CAAC,SAAS,CAAC,CAAC,CAAC,IAAI,IAAI,CAAE,YAAoB,CAAC,SAAS,CAAC,GAAG,IAAI,IAAI,EAAE,CAAC,CAAC,CAAC,KAAK;SAC5G,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;YACjD,aAAa;YACb,cAAc;YACd,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,oBAAoB;SAC/B,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE;YACtC,aAAa;YACb,cAAc;YACd,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,wBAAwB,EAAE;IACjC,OAAO,EAAE,CAAC,OAAO,EAAE,SAAS,CAAC;IAC7B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,yBAAyB;IAChC,OAAO,EAAE,qBAAqB;CAC/B,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC3B,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;IAC3B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,gCAAgC;IACvC,OAAO,EAAE,eAAe;CACzB,CAAC,CAAC"}