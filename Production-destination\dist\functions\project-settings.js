"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getProjectSettings = getProjectSettings;
exports.updateProjectSettings = updateProjectSettings;
/**
 * Project Settings Function
 * Handles project settings, configuration, and policy management
 * Migrated from old-arch/src/project-service/settings/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Validation schemas
const updateProjectSettingsSchema = Joi.object({
    projectId: Joi.string().uuid().required(),
    general: Joi.object({
        visibility: Joi.string().valid('private', 'organization', 'public').optional(),
        allowGuestAccess: Joi.boolean().optional(),
        requireApprovalForDocuments: Joi.boolean().optional(),
        enableVersioning: Joi.boolean().optional(),
        autoArchiveInactiveDays: Joi.number().min(1).max(365).optional(),
        defaultDocumentPermissions: Joi.string().valid('view', 'comment', 'edit').optional()
    }).optional(),
    collaboration: Joi.object({
        enableComments: Joi.boolean().optional(),
        enableRealTimeEditing: Joi.boolean().optional(),
        allowExternalSharing: Joi.boolean().optional(),
        requireApprovalForSharing: Joi.boolean().optional(),
        maxSharesPerDocument: Joi.number().min(1).max(1000).optional(),
        enableMentions: Joi.boolean().optional(),
        enableNotifications: Joi.boolean().optional()
    }).optional(),
    workflow: Joi.object({
        enableWorkflows: Joi.boolean().optional(),
        allowCustomWorkflows: Joi.boolean().optional(),
        requireApprovalForWorkflowChanges: Joi.boolean().optional(),
        maxConcurrentWorkflows: Joi.number().min(1).max(100).optional(),
        defaultWorkflowTimeout: Joi.number().min(1).max(168).optional(), // Hours
        enableWorkflowNotifications: Joi.boolean().optional()
    }).optional(),
    security: Joi.object({
        enableEncryption: Joi.boolean().optional(),
        requireTwoFactorForSensitiveActions: Joi.boolean().optional(),
        allowedFileTypes: Joi.array().items(Joi.string()).optional(),
        maxFileSize: Joi.number().min(1).max(1000).optional(), // MB
        enableAuditLog: Joi.boolean().optional(),
        dataRetentionDays: Joi.number().min(1).max(2555).optional(),
        allowDownloads: Joi.boolean().optional()
    }).optional(),
    integrations: Joi.object({
        enabledIntegrations: Joi.array().items(Joi.string()).optional(),
        webhookEndpoints: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            url: Joi.string().uri().required(),
            events: Joi.array().items(Joi.string()).required(),
            secret: Joi.string().optional()
        })).optional(),
        apiAccess: Joi.boolean().optional(),
        allowThirdPartyApps: Joi.boolean().optional()
    }).optional(),
    notifications: Joi.object({
        enableEmailNotifications: Joi.boolean().optional(),
        enableSlackNotifications: Joi.boolean().optional(),
        enableTeamsNotifications: Joi.boolean().optional(),
        notificationEvents: Joi.object({
            documentUploaded: Joi.boolean().optional(),
            documentProcessed: Joi.boolean().optional(),
            commentAdded: Joi.boolean().optional(),
            workflowAssigned: Joi.boolean().optional(),
            memberAdded: Joi.boolean().optional(),
            projectUpdated: Joi.boolean().optional()
        }).optional(),
        digestFrequency: Joi.string().valid('immediate', 'hourly', 'daily', 'weekly').optional()
    }).optional(),
    customFields: Joi.object({
        enableCustomFields: Joi.boolean().optional(),
        fields: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().valid('text', 'number', 'date', 'boolean', 'select').required(),
            required: Joi.boolean().default(false),
            options: Joi.array().items(Joi.string()).optional()
        })).optional()
    }).optional()
});
/**
 * Get project settings handler
 */
async function getProjectSettings(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const projectId = request.params.projectId;
    logger_1.logger.info("Get project settings started", { correlationId, projectId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!projectId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project ID is required" }
            }, request);
        }
        // Check if user has permission to view project settings
        const hasPermission = await checkProjectSettingsPermission(projectId, user.id, 'VIEW');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to project settings" }
            }, request);
        }
        // Get project
        const project = await database_1.db.readItem('projects', projectId, projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        const projectData = project;
        // Get project settings or return defaults
        const settings = projectData.settings || getDefaultProjectSettings();
        logger_1.logger.info("Project settings retrieved successfully", {
            correlationId,
            projectId,
            userId: user.id,
            hasCustomSettings: !!projectData.settings
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                projectId,
                projectName: projectData.name,
                organizationId: projectData.organizationId,
                settings,
                lastUpdated: projectData.updatedAt,
                isDefault: !projectData.settings
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get project settings failed", {
            correlationId,
            projectId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update project settings handler
 */
async function updateProjectSettings(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update project settings started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateProjectSettingsSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const settingsUpdate = value;
        const projectId = settingsUpdate.projectId;
        // Check if user has permission to update project settings
        const hasPermission = await checkProjectSettingsPermission(projectId, user.id, 'UPDATE');
        if (!hasPermission) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to update project settings" }
            }, request);
        }
        // Get project
        const project = await database_1.db.readItem('projects', projectId, projectId);
        if (!project) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Project not found" }
            }, request);
        }
        const projectData = project;
        const now = new Date().toISOString();
        // Get current settings or defaults
        const currentSettings = projectData.settings || getDefaultProjectSettings();
        // Deep merge settings
        const updatedSettings = deepMergeSettings(currentSettings, settingsUpdate);
        // Update project with new settings
        const updatedProject = {
            ...projectData,
            settings: updatedSettings,
            updatedAt: now,
            updatedBy: user.id
        };
        await database_1.db.updateItem('projects', updatedProject);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "project_settings_updated",
            userId: user.id,
            organizationId: projectData.organizationId,
            projectId,
            timestamp: now,
            details: {
                updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'projectId'),
                projectName: projectData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ProjectSettingsUpdated',
            aggregateId: projectId,
            aggregateType: 'Project',
            version: 1,
            data: {
                project: projectData,
                previousSettings: currentSettings,
                newSettings: updatedSettings,
                updatedBy: user.id
            },
            userId: user.id,
            organizationId: projectData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Project settings updated successfully", {
            correlationId,
            projectId,
            updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'projectId'),
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                projectId,
                projectName: projectData.name,
                settings: updatedSettings,
                updatedSections: Object.keys(settingsUpdate).filter(key => key !== 'projectId'),
                updatedAt: now,
                updatedBy: user.id,
                message: "Project settings updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update project settings failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkProjectSettingsPermission(projectId, userId, action) {
    try {
        // Check if user is an owner or manager of the project
        const membershipQuery = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('project-members', membershipQuery, [projectId, userId, 'ACTIVE']);
        if (memberships.length === 0) {
            return false;
        }
        const membership = memberships[0];
        switch (action) {
            case 'VIEW':
                return ['OWNER', 'MANAGER', 'CONTRIBUTOR'].includes(membership.role);
            case 'UPDATE':
                return ['OWNER', 'MANAGER'].includes(membership.role);
            default:
                return false;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to check project settings permission', { error, projectId, userId, action });
        return false;
    }
}
function getDefaultProjectSettings() {
    return {
        general: {
            visibility: 'private',
            allowGuestAccess: false,
            requireApprovalForDocuments: false,
            enableVersioning: true,
            autoArchiveInactiveDays: 90,
            defaultDocumentPermissions: 'comment'
        },
        collaboration: {
            enableComments: true,
            enableRealTimeEditing: true,
            allowExternalSharing: false,
            requireApprovalForSharing: true,
            maxSharesPerDocument: 50,
            enableMentions: true,
            enableNotifications: true
        },
        workflow: {
            enableWorkflows: true,
            allowCustomWorkflows: false,
            requireApprovalForWorkflowChanges: true,
            maxConcurrentWorkflows: 10,
            defaultWorkflowTimeout: 24,
            enableWorkflowNotifications: true
        },
        security: {
            enableEncryption: true,
            requireTwoFactorForSensitiveActions: false,
            allowedFileTypes: ['pdf', 'doc', 'docx', 'txt', 'jpg', 'png', 'gif'],
            maxFileSize: 100,
            enableAuditLog: true,
            dataRetentionDays: 365,
            allowDownloads: true
        },
        integrations: {
            enabledIntegrations: [],
            webhookEndpoints: [],
            apiAccess: false,
            allowThirdPartyApps: false
        },
        notifications: {
            enableEmailNotifications: true,
            enableSlackNotifications: false,
            enableTeamsNotifications: false,
            notificationEvents: {
                documentUploaded: true,
                documentProcessed: true,
                commentAdded: true,
                workflowAssigned: true,
                memberAdded: true,
                projectUpdated: false
            },
            digestFrequency: 'daily'
        },
        customFields: {
            enableCustomFields: false,
            fields: []
        }
    };
}
function deepMergeSettings(current, updates) {
    const result = { ...current };
    for (const key in updates) {
        if (key === 'projectId')
            continue; // Skip projectId
        if (updates[key] !== null && typeof updates[key] === 'object' && !Array.isArray(updates[key])) {
            result[key] = deepMergeSettings(current[key] || {}, updates[key]);
        }
        else {
            result[key] = updates[key];
        }
    }
    return result;
}
// Register functions
functions_1.app.http('project-settings-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/{projectId}/settings',
    handler: getProjectSettings
});
functions_1.app.http('project-settings-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'projects/settings',
    handler: updateProjectSettings
});
//# sourceMappingURL=project-settings.js.map