"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkflowTemplate = createWorkflowTemplate;
/**
 * Workflow Template Create Function
 * Handles creation of workflow templates for reuse across projects
 * Migrated from old-arch/src/workflow-service/templates/create/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Validation schema
const createWorkflowTemplateSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(1000).optional(),
    category: Joi.string().max(50).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    definition: Joi.object({
        version: Joi.string().required(),
        steps: Joi.array().items(Joi.object({
            id: Joi.string().required(),
            name: Joi.string().required(),
            description: Joi.string().optional(),
            type: Joi.string().required(),
            action: Joi.object().required(),
            assignedTo: Joi.array().items(Joi.string()).optional(),
            dependencies: Joi.array().items(Joi.string()).optional(),
            conditions: Joi.array().optional(),
            timeout: Joi.number().optional(),
            position: Joi.object({
                x: Joi.number().required(),
                y: Joi.number().required()
            }).required()
        })).min(1).required(),
        triggers: Joi.array().items(Joi.object({
            id: Joi.string().required(),
            type: Joi.string().required(),
            conditions: Joi.array().required(),
            enabled: Joi.boolean().default(true)
        })).optional(),
        variables: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            type: Joi.string().required(),
            defaultValue: Joi.any().optional(),
            description: Joi.string().optional(),
            required: Joi.boolean().default(false)
        })).optional(),
        settings: Joi.object({
            allowParallelExecution: Joi.boolean().default(false),
            maxRetries: Joi.number().min(0).max(10).default(3),
            defaultTimeout: Joi.number().min(1).default(60),
            autoArchiveAfterDays: Joi.number().min(1).optional(),
            requireApprovalForChanges: Joi.boolean().default(false),
            enableAuditLog: Joi.boolean().default(true)
        }).optional()
    }).required(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([]),
    isPublic: Joi.boolean().default(false),
    isDefault: Joi.boolean().default(false)
});
/**
 * Create workflow template handler
 */
async function createWorkflowTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create workflow template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createWorkflowTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const templateRequest = value;
        // Verify organization access
        const organization = await database_1.db.readItem('organizations', templateRequest.organizationId, templateRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user is a member of the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, templateRequest.organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // If projectId is provided, verify project access
        if (templateRequest.projectId) {
            const project = await database_1.db.readItem('projects', templateRequest.projectId, templateRequest.projectId);
            if (!project) {
                return (0, cors_1.addCorsHeaders)({
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Project not found" }
                }, request);
            }
            if (project.organizationId !== templateRequest.organizationId) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Project does not belong to the specified organization" }
                }, request);
            }
        }
        // Check template limits for organization tier
        const orgData = organization;
        if (await isTemplateCountLimitReached(templateRequest.organizationId, orgData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Template limit reached for this organization tier",
                    tier: orgData.tier
                }
            }, request);
        }
        // Create workflow template
        const templateId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const workflowTemplate = {
            id: templateId,
            name: templateRequest.name,
            description: templateRequest.description || "",
            category: templateRequest.category,
            definition: templateRequest.definition,
            organizationId: templateRequest.organizationId,
            projectId: templateRequest.projectId,
            isPublic: templateRequest.isPublic,
            isDefault: templateRequest.isDefault,
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            version: "1.0.0",
            tags: templateRequest.tags,
            usageCount: 0,
            rating: 0,
            metadata: {
                stepCount: templateRequest.definition.steps?.length || 0,
                triggerCount: templateRequest.definition.triggers?.length || 0,
                variableCount: templateRequest.definition.variables?.length || 0,
                complexity: calculateTemplateComplexity(templateRequest.definition)
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('workflow-templates', workflowTemplate);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "workflow_template_created",
            userId: user.id,
            organizationId: templateRequest.organizationId,
            projectId: templateRequest.projectId,
            timestamp: now,
            details: {
                templateId,
                templateName: templateRequest.name,
                category: templateRequest.category,
                stepCount: workflowTemplate.metadata.stepCount,
                isPublic: templateRequest.isPublic,
                isDefault: templateRequest.isDefault
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'WorkflowTemplateCreated',
            aggregateId: templateId,
            aggregateType: 'WorkflowTemplate',
            version: 1,
            data: {
                template: workflowTemplate,
                createdBy: user.id,
                organizationName: orgData.name
            },
            userId: user.id,
            organizationId: templateRequest.organizationId,
            tenantId: user.tenantId
        });
        // Send notification for public templates
        if (templateRequest.isPublic) {
            await notification_1.notificationService.sendNotification({
                userId: user.id,
                type: 'WORKFLOW_TEMPLATE_CREATED',
                title: 'Public Workflow Template Created',
                message: `Your workflow template "${templateRequest.name}" has been created and is now available to the community.`,
                priority: 'normal',
                metadata: {
                    templateId,
                    templateName: templateRequest.name,
                    category: templateRequest.category,
                    organizationId: templateRequest.organizationId,
                    isPublic: true
                },
                organizationId: templateRequest.organizationId,
                projectId: templateRequest.projectId
            });
        }
        logger_1.logger.info("Workflow template created successfully", {
            correlationId,
            templateId,
            templateName: templateRequest.name,
            category: templateRequest.category,
            organizationId: templateRequest.organizationId,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: templateId,
                name: workflowTemplate.name,
                description: workflowTemplate.description,
                category: workflowTemplate.category,
                version: workflowTemplate.version,
                organizationId: templateRequest.organizationId,
                projectId: templateRequest.projectId,
                stepCount: workflowTemplate.metadata.stepCount,
                complexity: workflowTemplate.metadata.complexity,
                isPublic: workflowTemplate.isPublic,
                isDefault: workflowTemplate.isDefault,
                message: "Workflow template created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create workflow template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check if template count limit is reached for organization
 */
async function isTemplateCountLimitReached(organizationId, tier) {
    try {
        const templateCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const result = await database_1.db.queryItems('workflow-templates', templateCountQuery, [organizationId]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 5,
            'PROFESSIONAL': 25,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check template count limit', { error, organizationId });
        return false;
    }
}
/**
 * Calculate template complexity based on definition
 */
function calculateTemplateComplexity(definition) {
    const stepCount = definition.steps?.length || 0;
    const triggerCount = definition.triggers?.length || 0;
    const variableCount = definition.variables?.length || 0;
    // Calculate complexity score
    let complexityScore = 0;
    complexityScore += stepCount * 2;
    complexityScore += triggerCount * 3;
    complexityScore += variableCount * 1;
    // Check for advanced features
    const hasConditionalSteps = definition.steps?.some((step) => step.conditions?.length > 0);
    const hasParallelExecution = definition.settings?.allowParallelExecution;
    const hasDependencies = definition.steps?.some((step) => step.dependencies?.length > 0);
    if (hasConditionalSteps)
        complexityScore += 5;
    if (hasParallelExecution)
        complexityScore += 5;
    if (hasDependencies)
        complexityScore += 3;
    if (complexityScore <= 10)
        return 'LOW';
    if (complexityScore <= 25)
        return 'MEDIUM';
    return 'HIGH';
}
// Register functions
functions_1.app.http('workflow-template-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflow-templates/create',
    handler: createWorkflowTemplate
});
//# sourceMappingURL=workflow-template-create.js.map