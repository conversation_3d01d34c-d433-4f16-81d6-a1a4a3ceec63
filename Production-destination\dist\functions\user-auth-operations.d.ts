/**
 * User Auth Operations Functions
 * Handles logout, refresh token, and user registration operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * User logout handler
 */
export declare function userLogout(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Refresh token handler
 */
export declare function refreshToken(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * User registration handler
 */
export declare function userRegister(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
