/**
 * Data Encryption Function
 * Handles data encryption, decryption, and key management
 * Migrated from old-arch/src/security-service/encryption/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Encrypt data handler
 */
export declare function encryptData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Decrypt data handler
 */
export declare function decryptData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
