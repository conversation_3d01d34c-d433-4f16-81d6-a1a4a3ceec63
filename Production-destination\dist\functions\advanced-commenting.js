"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createComment = createComment;
exports.addReactionToComment = addReactionToComment;
/**
 * Advanced Commenting Function
 * Handles advanced commenting system with threading, mentions, and reactions
 * Migrated from old-arch/src/collaboration-service/comments/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Comment types and enums
var CommentType;
(function (CommentType) {
    CommentType["GENERAL"] = "GENERAL";
    CommentType["SUGGESTION"] = "SUGGESTION";
    CommentType["QUESTION"] = "QUESTION";
    CommentType["ISSUE"] = "ISSUE";
    CommentType["APPROVAL"] = "APPROVAL";
    CommentType["REJECTION"] = "REJECTION";
})(CommentType || (CommentType = {}));
var CommentStatus;
(function (CommentStatus) {
    CommentStatus["ACTIVE"] = "ACTIVE";
    CommentStatus["RESOLVED"] = "RESOLVED";
    CommentStatus["DELETED"] = "DELETED";
    CommentStatus["HIDDEN"] = "HIDDEN";
})(CommentStatus || (CommentStatus = {}));
var ReactionType;
(function (ReactionType) {
    ReactionType["LIKE"] = "LIKE";
    ReactionType["DISLIKE"] = "DISLIKE";
    ReactionType["LOVE"] = "LOVE";
    ReactionType["LAUGH"] = "LAUGH";
    ReactionType["CONFUSED"] = "CONFUSED";
    ReactionType["THUMBS_UP"] = "THUMBS_UP";
    ReactionType["THUMBS_DOWN"] = "THUMBS_DOWN";
})(ReactionType || (ReactionType = {}));
// Validation schemas
const createCommentSchema = joi_1.default.object({
    documentId: joi_1.default.string().uuid().required(),
    content: joi_1.default.string().min(1).max(5000).required(),
    type: joi_1.default.string().valid(...Object.values(CommentType)).default(CommentType.GENERAL),
    parentCommentId: joi_1.default.string().uuid().optional(),
    position: joi_1.default.object({
        page: joi_1.default.number().min(1).optional(),
        x: joi_1.default.number().min(0).optional(),
        y: joi_1.default.number().min(0).optional(),
        startOffset: joi_1.default.number().min(0).optional(),
        endOffset: joi_1.default.number().min(0).optional(),
        selectedText: joi_1.default.string().max(500).optional()
    }).optional(),
    mentions: joi_1.default.array().items(joi_1.default.string().uuid()).optional(),
    attachments: joi_1.default.array().items(joi_1.default.object({
        name: joi_1.default.string().required(),
        url: joi_1.default.string().uri().required(),
        type: joi_1.default.string().required(),
        size: joi_1.default.number().optional()
    })).optional(),
    metadata: joi_1.default.object().optional()
});
const updateCommentSchema = joi_1.default.object({
    commentId: joi_1.default.string().uuid().required(),
    content: joi_1.default.string().min(1).max(5000).optional(),
    status: joi_1.default.string().valid(...Object.values(CommentStatus)).optional(),
    metadata: joi_1.default.object().optional()
});
const addReactionSchema = joi_1.default.object({
    commentId: joi_1.default.string().uuid().required(),
    reactionType: joi_1.default.string().valid(...Object.values(ReactionType)).required()
});
/**
 * Create comment handler
 */
async function createComment(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create comment started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createCommentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const commentRequest = value;
        // Get document and check access
        const document = await database_1.db.readItem('documents', commentRequest.documentId, commentRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Validate parent comment if provided
        if (commentRequest.parentCommentId) {
            const parentComment = await database_1.db.readItem('comments', commentRequest.parentCommentId, commentRequest.parentCommentId);
            if (!parentComment) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Parent comment not found" }
                }, request);
            }
            const parentCommentData = parentComment;
            if (parentCommentData.documentId !== commentRequest.documentId) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Parent comment belongs to different document" }
                }, request);
            }
        }
        // Validate mentions
        if (commentRequest.mentions && commentRequest.mentions.length > 0) {
            const validMentions = await validateMentions(commentRequest.mentions, documentData.organizationId);
            if (validMentions.length !== commentRequest.mentions.length) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Some mentioned users are not valid" }
                }, request);
            }
        }
        // Create comment
        const commentId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const comment = {
            id: commentId,
            documentId: commentRequest.documentId,
            content: commentRequest.content,
            type: commentRequest.type || CommentType.GENERAL,
            status: CommentStatus.ACTIVE,
            parentCommentId: commentRequest.parentCommentId,
            position: commentRequest.position,
            mentions: commentRequest.mentions || [],
            attachments: commentRequest.attachments || [],
            metadata: commentRequest.metadata || {},
            createdBy: user.id,
            createdAt: now,
            reactions: [],
            replies: [],
            organizationId: documentData.organizationId,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('comments', comment);
        // Update document comment count
        await updateDocumentCommentCount(commentRequest.documentId, 1);
        // Send notifications for mentions
        if (commentRequest.mentions && commentRequest.mentions.length > 0) {
            await sendMentionNotifications(comment, commentRequest.mentions, user, documentData);
        }
        // Send notification to document owner if not the commenter
        if (documentData.createdBy !== user.id) {
            await sendCommentNotification(comment, documentData.createdBy, user, documentData);
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "comment_created",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: commentRequest.documentId,
            timestamp: now,
            details: {
                commentId,
                commentType: comment.type,
                documentName: documentData.name,
                isReply: !!commentRequest.parentCommentId,
                mentionCount: commentRequest.mentions?.length || 0,
                hasAttachments: (commentRequest.attachments?.length || 0) > 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'CommentCreated',
            aggregateId: commentId,
            aggregateType: 'Comment',
            version: 1,
            data: {
                comment,
                document: documentData,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Comment created successfully", {
            correlationId,
            commentId,
            documentId: commentRequest.documentId,
            documentName: documentData.name,
            commentType: comment.type,
            isReply: !!commentRequest.parentCommentId,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                comment: {
                    id: commentId,
                    content: comment.content,
                    type: comment.type,
                    status: comment.status,
                    position: comment.position,
                    mentions: comment.mentions,
                    attachments: comment.attachments,
                    createdBy: user.id,
                    createdAt: now,
                    reactions: [],
                    replyCount: 0
                },
                message: "Comment created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create comment failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Add reaction to comment handler
 */
async function addReactionToComment(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Add reaction to comment started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = addReactionSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const reactionRequest = value;
        // Get comment
        const comment = await database_1.db.readItem('comments', reactionRequest.commentId, reactionRequest.commentId);
        if (!comment) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Comment not found" }
            }, request);
        }
        const commentData = comment;
        // Check if comment is active
        if (commentData.status !== CommentStatus.ACTIVE) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Cannot react to inactive comment" }
            }, request);
        }
        // Check document access
        const document = await database_1.db.readItem('documents', commentData.documentId, commentData.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Check if user already reacted with this type
        const existingReaction = commentData.reactions.find((r) => r.userId === user.id && r.type === reactionRequest.reactionType);
        if (existingReaction) {
            // Remove existing reaction (toggle)
            commentData.reactions = commentData.reactions.filter((r) => !(r.userId === user.id && r.type === reactionRequest.reactionType));
        }
        else {
            // Remove any other reaction from this user and add new one
            commentData.reactions = commentData.reactions.filter((r) => r.userId !== user.id);
            commentData.reactions.push({
                type: reactionRequest.reactionType,
                userId: user.id,
                createdAt: new Date().toISOString()
            });
        }
        // Update comment
        const updatedComment = {
            ...commentData,
            id: reactionRequest.commentId,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('comments', updatedComment);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: existingReaction ? "comment_reaction_removed" : "comment_reaction_added",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: commentData.documentId,
            timestamp: new Date().toISOString(),
            details: {
                commentId: reactionRequest.commentId,
                reactionType: reactionRequest.reactionType,
                documentName: documentData.name
            },
            tenantId: user.tenantId
        });
        // Notify comment author if not the reactor
        if (commentData.createdBy !== user.id && !existingReaction) {
            await sendReactionNotification(commentData, reactionRequest.reactionType, user, documentData);
        }
        logger_1.logger.info("Comment reaction updated successfully", {
            correlationId,
            commentId: reactionRequest.commentId,
            reactionType: reactionRequest.reactionType,
            action: existingReaction ? 'removed' : 'added',
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                commentId: reactionRequest.commentId,
                reactionType: reactionRequest.reactionType,
                action: existingReaction ? 'removed' : 'added',
                reactions: updatedComment.reactions,
                message: `Reaction ${existingReaction ? 'removed' : 'added'} successfully`
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Add reaction to comment failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
async function validateMentions(mentions, organizationId) {
    try {
        const validMentions = [];
        for (const userId of mentions) {
            const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
            if (memberships.length > 0) {
                validMentions.push(userId);
            }
        }
        return validMentions;
    }
    catch (error) {
        logger_1.logger.error('Failed to validate mentions', { error, mentions, organizationId });
        return [];
    }
}
async function updateDocumentCommentCount(documentId, increment) {
    try {
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (document) {
            const documentData = document;
            const updatedDocument = {
                ...documentData,
                id: documentId,
                commentCount: (documentData.commentCount || 0) + increment,
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('documents', updatedDocument);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to update document comment count', { error, documentId, increment });
    }
}
async function sendMentionNotifications(comment, mentions, user, document) {
    try {
        for (const mentionedUserId of mentions) {
            await notification_1.notificationService.sendNotification({
                userId: mentionedUserId,
                type: 'comment_mention',
                title: `You were mentioned in a comment`,
                message: `${user.name || user.email} mentioned you in a comment on "${document.name}"`,
                resourceId: comment.documentId,
                resourceType: 'document',
                metadata: {
                    commentId: comment.id,
                    documentId: comment.documentId,
                    documentName: document.name,
                    commentContent: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : ''),
                    mentionedBy: user.id
                },
                channels: [notification_1.NotificationChannelType.EMAIL, notification_1.NotificationChannelType.IN_APP],
                priority: 'normal'
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to send mention notifications', { error, commentId: comment.id });
    }
}
async function sendCommentNotification(comment, recipientId, user, document) {
    try {
        await notification_1.notificationService.sendNotification({
            userId: recipientId,
            type: 'new_comment',
            title: `New comment on your document`,
            message: `${user.name || user.email} commented on "${document.name}"`,
            resourceId: comment.documentId,
            resourceType: 'document',
            metadata: {
                commentId: comment.id,
                documentId: comment.documentId,
                documentName: document.name,
                commentContent: comment.content.substring(0, 100) + (comment.content.length > 100 ? '...' : ''),
                commentedBy: user.id
            },
            channels: [notification_1.NotificationChannelType.EMAIL, notification_1.NotificationChannelType.IN_APP],
            priority: 'normal'
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to send comment notification', { error, commentId: comment.id });
    }
}
async function sendReactionNotification(comment, reactionType, user, document) {
    try {
        await notification_1.notificationService.sendNotification({
            userId: comment.createdBy,
            type: 'comment_reaction',
            title: `Someone reacted to your comment`,
            message: `${user.name || user.email} reacted with ${reactionType} to your comment on "${document.name}"`,
            resourceId: comment.documentId,
            resourceType: 'document',
            metadata: {
                commentId: comment.id,
                documentId: comment.documentId,
                documentName: document.name,
                reactionType,
                reactedBy: user.id
            },
            channels: [notification_1.NotificationChannelType.IN_APP],
            priority: 'low'
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to send reaction notification', { error, commentId: comment.id });
    }
}
// Register functions
functions_1.app.http('comment-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'comments',
    handler: createComment
});
functions_1.app.http('comment-reaction-add', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'comments/reactions',
    handler: addReactionToComment
});
//# sourceMappingURL=advanced-commenting.js.map