/**
 * Workflow Monitoring Function
 * Handles workflow monitoring, analytics, and performance tracking
 * Migrated from old-arch/src/workflow-service/monitoring/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get workflow analytics handler
 */
export declare function getWorkflowAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
