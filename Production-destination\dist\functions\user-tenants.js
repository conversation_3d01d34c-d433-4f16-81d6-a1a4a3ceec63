"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserTenants = getUserTenants;
exports.switchTenant = switchTenant;
exports.inviteUserToOrganization = inviteUserToOrganization;
/**
 * User Tenants Function
 * Handles user tenant management and switching between organizations
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const switchTenantSchema = Joi.object({
    organizationId: Joi.string().uuid().required()
});
const inviteUserSchema = Joi.object({
    email: Joi.string().email().required(),
    organizationId: Joi.string().uuid().required(),
    role: Joi.string().valid('ADMIN', 'MEMBER', 'VIEWER').default('MEMBER'),
    message: Joi.string().max(500).optional()
});
/**
 * Get user tenants handler
 */
async function getUserTenants(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const userId = request.params.userId;
    logger_1.logger.info("Get user tenants started", { correlationId, userId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // If userId is provided, check if user can access that user's tenants
        const targetUserId = userId || user.id;
        if (targetUserId !== user.id && !user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get user's organization memberships
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [targetUserId, 'active']);
        // Get organization details for each membership
        const tenants = await Promise.all(memberships.map(async (membership) => {
            try {
                const organization = await database_1.db.readItem('organizations', membership.organizationId, membership.organizationId);
                if (organization) {
                    // Get member count
                    const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status = @status';
                    const memberCountResult = await database_1.db.queryItems('organization-members', memberCountQuery, [membership.organizationId, 'active']);
                    const memberCount = Number(memberCountResult[0]) || 0;
                    // Get project count
                    const projectCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
                    const projectCountResult = await database_1.db.queryItems('projects', projectCountQuery, [membership.organizationId]);
                    const projectCount = Number(projectCountResult[0]) || 0;
                    return {
                        organizationId: membership.organizationId,
                        name: organization.name,
                        description: organization.description,
                        tier: organization.tier,
                        role: membership.role,
                        joinedAt: membership.joinedAt,
                        permissions: membership.permissions || [],
                        memberCount,
                        projectCount,
                        isCurrentTenant: membership.organizationId === user.tenantId
                    };
                }
            }
            catch (error) {
                // Organization might not exist or user might not have access
                return null;
            }
        }));
        // Filter out null results
        const validTenants = tenants.filter(tenant => tenant !== null);
        logger_1.logger.info("User tenants retrieved successfully", {
            correlationId,
            userId: targetUserId,
            tenantCount: validTenants.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: targetUserId,
                tenants: validTenants,
                currentTenantId: user.tenantId
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user tenants failed", {
            correlationId,
            userId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Switch tenant handler
 */
async function switchTenant(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Switch tenant started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = switchTenantSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { organizationId } = value;
        // Check if user is a member of the target organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You are not a member of this organization" }
            }, request);
        }
        // Get organization details
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Update user's current tenant
        const currentUser = await database_1.db.readItem('users', user.id, user.id);
        if (currentUser) {
            const updatedUser = {
                ...currentUser,
                id: user.id,
                tenantId: organizationId,
                currentOrganizationId: organizationId,
                lastTenantSwitch: new Date().toISOString(),
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('users', updatedUser);
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "tenant_switched",
            userId: user.id,
            organizationId,
            timestamp: new Date().toISOString(),
            details: {
                previousTenantId: user.tenantId,
                newTenantId: organizationId,
                organizationName: organization.name
            },
            tenantId: organizationId
        });
        logger_1.logger.info("Tenant switched successfully", {
            correlationId,
            userId: user.id,
            previousTenantId: user.tenantId,
            newTenantId: organizationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                organizationId,
                organizationName: organization.name,
                role: memberships[0].role,
                message: "Tenant switched successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Switch tenant failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Invite user to organization handler
 */
async function inviteUserToOrganization(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Invite user to organization started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = inviteUserSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { email, organizationId, role, message } = value;
        // Check if user has permission to invite to this organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'active']);
        if (memberships.length === 0 || memberships[0].role !== 'ADMIN') {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Only organization admins can invite users" }
            }, request);
        }
        // Get organization details
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user is already invited or a member
        const existingMemberQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND (c.email = @email OR c.userId IN (SELECT VALUE u.id FROM users u WHERE u.email = @email))';
        const existingMembers = await database_1.db.queryItems('organization-members', existingMemberQuery, [organizationId, email]);
        if (existingMembers.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User is already a member or has been invited" }
            }, request);
        }
        // Create invitation
        const invitationId = (0, uuid_1.v4)();
        const invitation = {
            id: invitationId,
            organizationId,
            email,
            role,
            invitedBy: user.id,
            invitedAt: new Date().toISOString(),
            status: 'PENDING',
            message,
            expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
            tenantId: user.tenantId
        };
        await database_1.db.createItem('organization-invitations', invitation);
        // Send invitation notification (simplified)
        // In production, this would send an email
        logger_1.logger.info("Organization invitation would be sent via email", {
            correlationId,
            email,
            organizationId,
            invitedBy: user.id
        });
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "user_invited_to_organization",
            userId: user.id,
            organizationId,
            timestamp: new Date().toISOString(),
            details: {
                invitedEmail: email,
                role,
                organizationName: organization.name,
                invitationId
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("User invited to organization successfully", {
            correlationId,
            email,
            organizationId,
            role,
            invitedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                invitationId,
                email,
                organizationId,
                organizationName: organization.name,
                role,
                expiresAt: invitation.expiresAt,
                message: "Invitation sent successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Invite user to organization failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('user-tenants-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/{userId?}/tenants',
    handler: getUserTenants
});
functions_1.app.http('user-tenants-switch', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/tenants/switch',
    handler: switchTenant
});
functions_1.app.http('user-tenants-invite', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'organizations/{organizationId}/invite',
    handler: inviteUserToOrganization
});
//# sourceMappingURL=user-tenants.js.map