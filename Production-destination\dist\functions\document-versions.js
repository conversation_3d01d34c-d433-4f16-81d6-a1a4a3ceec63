"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getDocumentVersions = getDocumentVersions;
exports.createDocumentVersion = createDocumentVersion;
/**
 * Document Versions Function
 * Handles document version history and management
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const getVersionsSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20)
});
/**
 * Get document versions handler
 */
async function getDocumentVersions(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.id;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Get document versions started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getVersionsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit } = value;
        // First, verify the document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Query for document versions
        const query = 'SELECT * FROM c WHERE c.documentId = @documentId ORDER BY c.versionNumber DESC';
        const parameters = [documentId];
        // Execute query with pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;
        const versions = await database_1.db.queryItems('documentVersions', paginatedQuery, parameters);
        // Get total count for pagination
        const countQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.documentId = @documentId';
        const countResult = await database_1.db.queryItems('documentVersions', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Format versions for response
        const formattedVersions = versions.map((version) => ({
            versionNumber: version.versionNumber,
            createdBy: version.createdBy,
            createdAt: version.createdAt,
            comment: version.comment,
            size: version.size,
            hash: version.hash,
            isActive: version.isActive,
            changes: version.changes || []
        }));
        logger_1.logger.info("Document versions retrieved successfully", {
            correlationId,
            documentId,
            userId: user.id,
            versionsCount: versions.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                versions: formattedVersions,
                pagination: {
                    page,
                    limit,
                    total,
                    hasMore: (page * limit) < total
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get document versions failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Create document version handler
 */
async function createDocumentVersion(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.id;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Create document version started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const body = await request.json();
        // Verify the document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get the current highest version number
        const latestVersionQuery = 'SELECT TOP 1 * FROM c WHERE c.documentId = @documentId ORDER BY c.versionNumber DESC';
        const latestVersions = await database_1.db.queryItems('documentVersions', latestVersionQuery, [documentId]);
        const latestVersion = latestVersions[0];
        const newVersionNumber = latestVersion ? latestVersion.versionNumber + 1 : 1;
        // Parse and validate body
        const bodyData = body;
        // Create new version record
        const newVersion = {
            id: `${documentId}_v${newVersionNumber}`,
            documentId,
            versionNumber: newVersionNumber,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            comment: bodyData.comment || `Version ${newVersionNumber}`,
            size: document.size || 0,
            hash: bodyData.hash || '',
            blobName: document.blobName || '',
            contentType: document.contentType || '',
            isActive: true,
            changes: bodyData.changes || []
        };
        // Deactivate previous version if it exists
        if (latestVersion) {
            const updatedPreviousVersion = {
                ...latestVersion,
                id: latestVersion.id,
                isActive: false
            };
            await database_1.db.updateItem('documentVersions', updatedPreviousVersion);
        }
        // Save new version
        await database_1.db.createItem('documentVersions', newVersion);
        // Update document with new version info
        const updatedDocument = {
            ...document,
            id: document.id,
            version: newVersionNumber,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        logger_1.logger.info("Document version created successfully", {
            correlationId,
            documentId,
            versionNumber: newVersionNumber,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                versionNumber: newVersionNumber,
                message: "Document version created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create document version failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined document versions handler
 */
async function handleDocumentVersions(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'GET':
            return await getDocumentVersions(request, context);
        case 'POST':
            return await createDocumentVersion(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('document-versions', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}/versions',
    handler: handleDocumentVersions
});
//# sourceMappingURL=document-versions.js.map