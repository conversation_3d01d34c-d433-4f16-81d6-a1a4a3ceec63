/**
 * User Management Functions
 * Handles user profile, preferences, and basic user operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get user profile handler
 */
export declare function getUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update user profile handler
 */
export declare function updateUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update user preferences handler
 */
export declare function updateUserPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
