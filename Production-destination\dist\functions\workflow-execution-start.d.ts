/**
 * Workflow Execution Start Function
 * Handles starting workflow executions with proper validation and orchestration
 * Migrated from old-arch/src/workflow-service/execution/start/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Start workflow execution handler
 */
export declare function startWorkflowExecution(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
