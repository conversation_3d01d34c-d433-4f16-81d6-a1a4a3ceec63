"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getAnalytics = getAnalytics;
/**
 * Analytics Function
 * Handles analytics and reporting operations
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const getAnalyticsSchema = Joi.object({
    type: Joi.string().valid('documents', 'workflows', 'users', 'activities', 'overview').required(),
    period: Joi.string().valid('day', 'week', 'month', 'quarter', 'year').default('month'),
    startDate: Joi.date().iso().optional(),
    endDate: Joi.date().iso().optional(),
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional(),
    groupBy: Joi.string().valid('day', 'week', 'month', 'type', 'status', 'user').optional()
});
/**
 * Get analytics data handler
 */
async function getAnalytics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get analytics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getAnalyticsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const analyticsRequest = value;
        // Calculate date range
        const dateRange = calculateDateRange(analyticsRequest.period, analyticsRequest.startDate, analyticsRequest.endDate);
        // Get analytics data based on type
        let analyticsData;
        switch (analyticsRequest.type) {
            case 'documents':
                analyticsData = await getDocumentAnalytics(dateRange, analyticsRequest, user);
                break;
            case 'workflows':
                analyticsData = await getWorkflowAnalytics(dateRange, analyticsRequest, user);
                break;
            case 'users':
                analyticsData = await getUserAnalytics(dateRange, analyticsRequest, user);
                break;
            case 'activities':
                analyticsData = await getActivityAnalytics(dateRange, analyticsRequest, user);
                break;
            case 'overview':
                analyticsData = await getOverviewAnalytics(dateRange, analyticsRequest, user);
                break;
            default:
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Invalid analytics type" }
                }, request);
        }
        logger_1.logger.info("Analytics data retrieved successfully", {
            correlationId,
            type: analyticsRequest.type,
            period: analyticsRequest.period,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                type: analyticsRequest.type,
                period: analyticsRequest.period,
                dateRange,
                data: analyticsData,
                generatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get analytics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Calculate date range based on period
 */
function calculateDateRange(period, startDate, endDate) {
    const now = new Date();
    const end = endDate ? new Date(endDate) : now;
    let start;
    if (startDate) {
        start = new Date(startDate);
    }
    else {
        switch (period) {
            case 'day':
                start = new Date(end.getTime() - 24 * 60 * 60 * 1000);
                break;
            case 'week':
                start = new Date(end.getTime() - 7 * 24 * 60 * 60 * 1000);
                break;
            case 'month':
                start = new Date(end.getFullYear(), end.getMonth() - 1, end.getDate());
                break;
            case 'quarter':
                start = new Date(end.getFullYear(), end.getMonth() - 3, end.getDate());
                break;
            case 'year':
                start = new Date(end.getFullYear() - 1, end.getMonth(), end.getDate());
                break;
            default:
                start = new Date(end.getTime() - 30 * 24 * 60 * 60 * 1000);
        }
    }
    return {
        start: start.toISOString(),
        end: end.toISOString()
    };
}
/**
 * Get document analytics
 */
async function getDocumentAnalytics(dateRange, request, user) {
    let query = 'SELECT * FROM c WHERE c.createdAt >= @startDate AND c.createdAt <= @endDate';
    const parameters = [dateRange.start, dateRange.end];
    // Add tenant isolation
    if (user.tenantId) {
        query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
        parameters.push(user.tenantId, user.id);
    }
    // Add filters
    if (request.organizationId) {
        query += ' AND c.organizationId = @organizationId';
        parameters.push(request.organizationId);
    }
    if (request.projectId) {
        query += ' AND c.projectId = @projectId';
        parameters.push(request.projectId);
    }
    const documents = await database_1.db.queryItems('documents', query, parameters);
    // Calculate metrics
    const totalDocuments = documents.length;
    const documentsByType = documents.reduce((acc, doc) => {
        const type = doc.contentType || 'unknown';
        acc[type] = (acc[type] || 0) + 1;
        return acc;
    }, {});
    const documentsByStatus = documents.reduce((acc, doc) => {
        const status = doc.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
    }, {});
    const averageSize = documents.length > 0
        ? documents.reduce((sum, doc) => sum + (doc.size || 0), 0) / documents.length
        : 0;
    return {
        totalDocuments,
        documentsByType,
        documentsByStatus,
        averageSize: Math.round(averageSize),
        recentDocuments: documents.slice(0, 10).map((doc) => ({
            id: doc.id,
            name: doc.name,
            type: doc.contentType,
            size: doc.size,
            createdAt: doc.createdAt
        }))
    };
}
/**
 * Get workflow analytics
 */
async function getWorkflowAnalytics(dateRange, request, user) {
    let query = 'SELECT * FROM c WHERE c.createdAt >= @startDate AND c.createdAt <= @endDate';
    const parameters = [dateRange.start, dateRange.end];
    // Add tenant isolation
    if (user.tenantId) {
        query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
        parameters.push(user.tenantId, user.id);
    }
    const workflows = await database_1.db.queryItems('workflows', query, parameters);
    const totalWorkflows = workflows.length;
    const workflowsByStatus = workflows.reduce((acc, workflow) => {
        const status = workflow.status || 'unknown';
        acc[status] = (acc[status] || 0) + 1;
        return acc;
    }, {});
    const completedWorkflows = workflows.filter((w) => w.status === 'COMPLETED');
    const averageCompletionTime = completedWorkflows.length > 0
        ? completedWorkflows.reduce((sum, w) => {
            const start = new Date(w.startedAt || w.createdAt);
            const end = new Date(w.completedAt);
            return sum + (end.getTime() - start.getTime());
        }, 0) / completedWorkflows.length
        : 0;
    return {
        totalWorkflows,
        workflowsByStatus,
        completedWorkflows: completedWorkflows.length,
        averageCompletionTimeHours: Math.round(averageCompletionTime / (1000 * 60 * 60)),
        activeWorkflows: workflows.filter((w) => w.status === 'ACTIVE').length
    };
}
/**
 * Get user analytics
 */
async function getUserAnalytics(dateRange, request, user) {
    // Get user activity from activities
    let query = 'SELECT * FROM c WHERE c.timestamp >= @startDate AND c.timestamp <= @endDate';
    const parameters = [dateRange.start, dateRange.end];
    // Add tenant isolation
    if (user.tenantId) {
        query += ' AND c.tenantId = @tenantId';
        parameters.push(user.tenantId);
    }
    const activities = await database_1.db.queryItems('activities', query, parameters);
    const userActivity = activities.reduce((acc, activity) => {
        const userId = activity.userId;
        if (!acc[userId]) {
            acc[userId] = { count: 0, types: {} };
        }
        acc[userId].count++;
        acc[userId].types[activity.type] = (acc[userId].types[activity.type] || 0) + 1;
        return acc;
    }, {});
    const mostActiveUsers = Object.entries(userActivity)
        .sort(([, a], [, b]) => b.count - a.count)
        .slice(0, 10)
        .map(([userId, data]) => ({ userId, ...data }));
    return {
        totalActivities: activities.length,
        uniqueActiveUsers: Object.keys(userActivity).length,
        mostActiveUsers,
        activityByType: activities.reduce((acc, activity) => {
            acc[activity.type] = (acc[activity.type] || 0) + 1;
            return acc;
        }, {})
    };
}
/**
 * Get activity analytics
 */
async function getActivityAnalytics(dateRange, request, user) {
    let query = 'SELECT * FROM c WHERE c.timestamp >= @startDate AND c.timestamp <= @endDate';
    const parameters = [dateRange.start, dateRange.end];
    // Add tenant isolation
    if (user.tenantId) {
        query += ' AND c.tenantId = @tenantId';
        parameters.push(user.tenantId);
    }
    const activities = await database_1.db.queryItems('activities', query, parameters);
    const activitiesByType = activities.reduce((acc, activity) => {
        acc[activity.type] = (acc[activity.type] || 0) + 1;
        return acc;
    }, {});
    const activitiesByDay = activities.reduce((acc, activity) => {
        const day = activity.timestamp.split('T')[0];
        acc[day] = (acc[day] || 0) + 1;
        return acc;
    }, {});
    return {
        totalActivities: activities.length,
        activitiesByType,
        activitiesByDay,
        recentActivities: activities.slice(0, 20).map((activity) => ({
            type: activity.type,
            userId: activity.userId,
            timestamp: activity.timestamp,
            details: activity.details
        }))
    };
}
/**
 * Get overview analytics
 */
async function getOverviewAnalytics(dateRange, request, user) {
    const [documents, workflows, activities] = await Promise.all([
        getDocumentAnalytics(dateRange, request, user),
        getWorkflowAnalytics(dateRange, request, user),
        getActivityAnalytics(dateRange, request, user)
    ]);
    return {
        summary: {
            totalDocuments: documents.totalDocuments,
            totalWorkflows: workflows.totalWorkflows,
            totalActivities: activities.totalActivities,
            activeWorkflows: workflows.activeWorkflows
        },
        documents: {
            byType: documents.documentsByType,
            byStatus: documents.documentsByStatus
        },
        workflows: {
            byStatus: workflows.workflowsByStatus,
            averageCompletionTimeHours: workflows.averageCompletionTimeHours
        },
        activities: {
            byType: activities.activitiesByType,
            trend: activities.activitiesByDay
        }
    };
}
// Register functions
functions_1.app.http('analytics-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'analytics',
    handler: getAnalytics
});
//# sourceMappingURL=analytics.js.map