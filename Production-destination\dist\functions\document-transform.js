"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.transformDocument = transformDocument;
/**
 * Document Transform Function
 * Handles document transformation operations like format conversion, merging, splitting
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Transformation types enum
var TransformationType;
(function (TransformationType) {
    TransformationType["FORMAT_CONVERSION"] = "FORMAT_CONVERSION";
    TransformationType["MERGE_DOCUMENTS"] = "MERGE_DOCUMENTS";
    TransformationType["SPLIT_DOCUMENT"] = "SPLIT_DOCUMENT";
    TransformationType["EXTRACT_PAGES"] = "EXTRACT_PAGES";
    TransformationType["ROTATE_PAGES"] = "ROTATE_PAGES";
    TransformationType["WATERMARK"] = "WATERMARK";
    TransformationType["COMPRESS"] = "COMPRESS";
})(TransformationType || (TransformationType = {}));
// Validation schemas
const transformDocumentSchema = Joi.object({
    transformationType: Joi.string().valid(...Object.values(TransformationType)).required(),
    sourceDocumentIds: Joi.array().items(Joi.string().uuid()).min(1).required(),
    targetFormat: Joi.string().optional(),
    options: Joi.object({
        pages: Joi.array().items(Joi.number().integer().min(1)).optional(),
        pageRanges: Joi.array().items(Joi.object({
            start: Joi.number().integer().min(1).required(),
            end: Joi.number().integer().min(1).required()
        })).optional(),
        rotation: Joi.number().valid(90, 180, 270).optional(),
        watermarkText: Joi.string().max(100).optional(),
        watermarkOpacity: Joi.number().min(0.1).max(1.0).optional(),
        compressionLevel: Joi.number().min(1).max(9).optional(),
        mergeOrder: Joi.array().items(Joi.string().uuid()).optional()
    }).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().required(),
    outputName: Joi.string().max(255).optional()
});
/**
 * Transform document handler
 */
async function transformDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Document transformation started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = transformDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { transformationType, sourceDocumentIds, targetFormat, options, organizationId, projectId, outputName } = value;
        const startTime = Date.now();
        // Get and validate source documents
        const sourceDocuments = [];
        let totalOriginalSize = 0;
        for (const documentId of sourceDocumentIds) {
            const document = await database_1.db.readItem('documents', documentId, documentId);
            if (!document) {
                return (0, cors_1.addCorsHeaders)({
                    status: 404,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: `Document not found: ${documentId}` }
                }, request);
            }
            // Check access permissions
            const hasAccess = (document.createdBy === user.id ||
                document.organizationId === user.tenantId ||
                user.roles?.includes('admin'));
            if (!hasAccess) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: `Access denied to document: ${documentId}` }
                }, request);
            }
            sourceDocuments.push(document);
            totalOriginalSize += document.size || 0;
        }
        // Initialize blob service client
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        // Download source documents
        const documentBuffers = [];
        for (const document of sourceDocuments) {
            const blobClient = containerClient.getBlockBlobClient(document.blobName);
            const downloadResponse = await blobClient.download(0);
            if (!downloadResponse.readableStreamBody) {
                return (0, cors_1.addCorsHeaders)({
                    status: 500,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: `Failed to download document: ${document.id}` }
                }, request);
            }
            const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
            documentBuffers.push(documentBuffer);
        }
        // Perform transformation
        const transformationResult = await performTransformation(documentBuffers, sourceDocuments, transformationType, targetFormat, options || {});
        // Save transformed document to blob storage
        const transformedDocumentId = (0, uuid_1.v4)();
        const fileExtension = getFileExtension(transformationResult.contentType);
        const transformedBlobName = `${organizationId}/${projectId}/${transformedDocumentId}_transformed.${fileExtension}`;
        const transformedBlobClient = containerClient.getBlockBlobClient(transformedBlobName);
        await transformedBlobClient.upload(transformationResult.transformedBuffer, transformationResult.transformedBuffer.length, {
            blobHTTPHeaders: { blobContentType: transformationResult.contentType }
        });
        // Generate output name
        const defaultName = sourceDocuments.length === 1
            ? `${sourceDocuments[0].name} (Transformed)`
            : `Merged Document (${sourceDocuments.length} files)`;
        // Create transformed document record
        const transformedDocument = {
            id: transformedDocumentId,
            sourceDocumentIds,
            name: outputName || defaultName,
            description: `Transformed document using ${transformationType}`,
            blobName: transformedBlobName,
            contentType: transformationResult.contentType,
            size: transformationResult.transformedBuffer.length,
            organizationId,
            projectId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            status: "TRANSFORMED",
            metadata: {
                transformationType,
                sourceDocumentIds,
                originalTotalSize: totalOriginalSize,
                transformedSize: transformationResult.transformedBuffer.length,
                pageCount: transformationResult.pageCount,
                transformedAt: new Date().toISOString(),
                transformedBy: user.id,
                options
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('documents', transformedDocument);
        // Create transformation record
        const transformation = {
            id: (0, uuid_1.v4)(),
            transformationType,
            sourceDocumentIds,
            transformedDocumentId,
            options,
            result: {
                originalTotalSize: totalOriginalSize,
                transformedSize: transformationResult.transformedBuffer.length,
                pageCount: transformationResult.pageCount,
                processingTime: Date.now() - startTime
            },
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            organizationId,
            projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-transformations', transformation);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_transformed",
            userId: user.id,
            organizationId,
            projectId,
            documentId: transformedDocumentId,
            timestamp: new Date().toISOString(),
            details: {
                transformationType,
                sourceDocumentCount: sourceDocumentIds.length,
                originalTotalSize: totalOriginalSize,
                transformedSize: transformationResult.transformedBuffer.length,
                pageCount: transformationResult.pageCount,
                processingTime: Date.now() - startTime
            },
            tenantId: user.tenantId
        });
        const response = {
            transformationType,
            sourceDocumentIds,
            transformedDocumentId,
            originalTotalSize: totalOriginalSize,
            transformedSize: transformationResult.transformedBuffer.length,
            pageCount: transformationResult.pageCount,
            processingTime: Date.now() - startTime,
            success: true
        };
        logger_1.logger.info("Document transformed successfully", {
            correlationId,
            transformationType,
            sourceDocumentCount: sourceDocumentIds.length,
            transformedDocumentId,
            userId: user.id,
            processingTime: response.processingTime
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document transformation failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Perform document transformation (simplified implementation)
 */
async function performTransformation(documentBuffers, sourceDocuments, transformationType, targetFormat, options = {}) {
    // This is a simplified implementation
    // In production, this would integrate with PDF processing libraries
    // like pdf-lib, PDFtk, or cloud services
    logger_1.logger.info("Performing document transformation", {
        transformationType,
        documentCount: documentBuffers.length,
        targetFormat
    });
    let transformedBuffer;
    let contentType;
    let pageCount = 1;
    switch (transformationType) {
        case TransformationType.MERGE_DOCUMENTS:
            // Simulate merging documents
            transformedBuffer = Buffer.concat(documentBuffers);
            contentType = targetFormat || 'application/pdf';
            pageCount = documentBuffers.length; // Simplified
            break;
        case TransformationType.FORMAT_CONVERSION:
            // Simulate format conversion
            transformedBuffer = documentBuffers[0];
            contentType = targetFormat || sourceDocuments[0].contentType;
            pageCount = 1;
            break;
        case TransformationType.SPLIT_DOCUMENT:
            // Simulate document splitting (return first part)
            transformedBuffer = documentBuffers[0];
            contentType = sourceDocuments[0].contentType;
            pageCount = Math.ceil((documentBuffers[0].length / documentBuffers[0].length) / 2);
            break;
        case TransformationType.EXTRACT_PAGES:
            // Simulate page extraction
            transformedBuffer = documentBuffers[0];
            contentType = sourceDocuments[0].contentType;
            pageCount = options.pages?.length || 1;
            break;
        case TransformationType.COMPRESS:
            // Simulate compression (reduce buffer size by 20%)
            const compressedSize = Math.floor(documentBuffers[0].length * 0.8);
            transformedBuffer = documentBuffers[0].slice(0, compressedSize);
            contentType = sourceDocuments[0].contentType;
            pageCount = 1;
            break;
        default:
            transformedBuffer = documentBuffers[0];
            contentType = sourceDocuments[0].contentType;
            pageCount = 1;
            break;
    }
    return {
        transformedBuffer,
        contentType,
        pageCount
    };
}
/**
 * Get file extension from content type
 */
function getFileExtension(contentType) {
    const extensions = {
        'application/pdf': 'pdf',
        'image/jpeg': 'jpg',
        'image/png': 'png',
        'image/tiff': 'tiff',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
    };
    return extensions[contentType] || 'bin';
}
/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on("data", (data) => {
            chunks.push(data instanceof Buffer ? data : Buffer.from(data));
        });
        readableStream.on("end", () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on("error", reject);
    });
}
// Register functions
functions_1.app.http('document-transform', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/transform',
    handler: transformDocument
});
//# sourceMappingURL=document-transform.js.map