"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.collectMetrics = collectMetrics;
exports.queryMetrics = queryMetrics;
exports.getMetricsSummary = getMetricsSummary;
/**
 * Metrics Collection Function
 * Handles comprehensive metrics collection and aggregation
 * Migrated from old-arch/src/monitoring-service/metrics/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
// Metrics types and enums
var MetricCategory;
(function (MetricCategory) {
    MetricCategory["PERFORMANCE"] = "PERFORMANCE";
    MetricCategory["BUSINESS"] = "BUSINESS";
    MetricCategory["SYSTEM"] = "SYSTEM";
    MetricCategory["USER"] = "USER";
    MetricCategory["SECURITY"] = "SECURITY";
    MetricCategory["CUSTOM"] = "CUSTOM";
})(MetricCategory || (MetricCategory = {}));
var AggregationType;
(function (AggregationType) {
    AggregationType["SUM"] = "SUM";
    AggregationType["AVERAGE"] = "AVERAGE";
    AggregationType["COUNT"] = "COUNT";
    AggregationType["MIN"] = "MIN";
    AggregationType["MAX"] = "MAX";
    AggregationType["PERCENTILE"] = "PERCENTILE";
})(AggregationType || (AggregationType = {}));
// Validation schemas
const collectMetricsSchema = Joi.object({
    metrics: Joi.array().items(Joi.object({
        name: Joi.string().min(2).max(100).required(),
        value: Joi.number().required(),
        category: Joi.string().valid(...Object.values(MetricCategory)).required(),
        unit: Joi.string().max(20).optional(),
        tags: Joi.object().optional(),
        timestamp: Joi.string().isoDate().optional()
    })).min(1).max(100).required(),
    organizationId: Joi.string().uuid().optional(),
    userId: Joi.string().uuid().optional(),
    source: Joi.string().max(100).required(),
    metadata: Joi.object().optional()
});
const queryMetricsSchema = Joi.object({
    metricNames: Joi.array().items(Joi.string()).optional(),
    category: Joi.string().valid(...Object.values(MetricCategory)).optional(),
    organizationId: Joi.string().uuid().optional(),
    userId: Joi.string().uuid().optional(),
    timeRange: Joi.object({
        startTime: Joi.string().isoDate().required(),
        endTime: Joi.string().isoDate().required()
    }).required(),
    aggregation: Joi.string().valid(...Object.values(AggregationType)).default(AggregationType.AVERAGE),
    interval: Joi.string().valid('1m', '5m', '15m', '1h', '1d').default('1h'),
    tags: Joi.object().optional(),
    limit: Joi.number().min(1).max(10000).default(1000)
});
/**
 * Collect metrics handler
 */
async function collectMetrics(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Collect metrics started", { correlationId });
    try {
        // Validate request body
        const body = await request.json();
        const { error, value } = collectMetricsSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const metricsRequest = value;
        // Store metrics
        const metricIds = await storeMetrics(metricsRequest);
        // Update real-time aggregations
        await updateRealTimeAggregations(metricsRequest.metrics);
        // Update metric statistics
        await updateMetricStatistics(metricsRequest.metrics);
        const duration = Date.now() - startTime;
        logger_1.logger.info("Metrics collected successfully", {
            correlationId,
            metricCount: metricsRequest.metrics.length,
            source: metricsRequest.source,
            organizationId: metricsRequest.organizationId,
            duration
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                metricIds,
                metricCount: metricsRequest.metrics.length,
                source: metricsRequest.source,
                collectedAt: new Date().toISOString(),
                processingTime: duration,
                message: "Metrics collected successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Collect metrics failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Query metrics handler
 */
async function queryMetrics(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Query metrics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            metricNames: url.searchParams.get('metricNames')?.split(','),
            category: url.searchParams.get('category'),
            organizationId: url.searchParams.get('organizationId'),
            userId: url.searchParams.get('userId'),
            timeRange: {
                startTime: url.searchParams.get('startTime') || new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
                endTime: url.searchParams.get('endTime') || new Date().toISOString()
            },
            aggregation: url.searchParams.get('aggregation') || AggregationType.AVERAGE,
            interval: url.searchParams.get('interval') || '1h',
            limit: parseInt(url.searchParams.get('limit') || '1000')
        };
        // Validate query parameters
        const { error, value } = queryMetricsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const queryRequest = value;
        // Check access permissions
        const hasAccess = await checkMetricsAccess(user, queryRequest.organizationId, queryRequest.userId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to metrics" }
            }, request);
        }
        // Query metrics
        const metricSeries = await executeMetricsQuery(queryRequest);
        const duration = Date.now() - startTime;
        logger_1.logger.info("Metrics queried successfully", {
            correlationId,
            seriesCount: metricSeries.length,
            timeRange: queryRequest.timeRange,
            aggregation: queryRequest.aggregation,
            duration,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                series: metricSeries,
                query: {
                    timeRange: queryRequest.timeRange,
                    aggregation: queryRequest.aggregation,
                    interval: queryRequest.interval
                },
                metadata: {
                    seriesCount: metricSeries.length,
                    totalDataPoints: metricSeries.reduce((sum, series) => sum + series.dataPoints.length, 0),
                    queryTime: duration
                },
                queriedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Query metrics failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get metrics summary handler
 */
async function getMetricsSummary(request, context) {
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse)
        return preflightResponse;
    const correlationId = context.invocationId;
    logger_1.logger.info("Get metrics summary started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkMetricsAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to metrics summary" }
            }, request);
        }
        // Get metrics summary
        const summary = await getSystemMetricsSummary();
        logger_1.logger.info("Metrics summary retrieved successfully", {
            correlationId,
            requestedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                summary,
                generatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get metrics summary failed", { correlationId, error: errorMessage });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkMetricsAccess(user, organizationId, userId) {
    try {
        // Check if user has admin or metrics role
        if (user.roles?.includes('admin') || user.roles?.includes('metrics_admin')) {
            return true;
        }
        // For organization metrics, check organization access
        if (organizationId) {
            const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
            const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
            return memberships.length > 0;
        }
        // For user metrics, check if it's the same user
        if (userId) {
            return user.id === userId;
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check metrics access', { error, userId: user.id, organizationId, targetUserId: userId });
        return false;
    }
}
async function storeMetrics(metricsRequest) {
    try {
        const metricIds = [];
        const batchSize = 25; // Cosmos DB batch limit
        // Process metrics in batches
        for (let i = 0; i < metricsRequest.metrics.length; i += batchSize) {
            const batch = metricsRequest.metrics.slice(i, i + batchSize);
            const batchPromises = batch.map(async (metric) => {
                const metricId = (0, uuid_1.v4)();
                const timestamp = metric.timestamp || new Date().toISOString();
                const metricRecord = {
                    id: metricId,
                    name: metric.name,
                    value: metric.value,
                    category: metric.category,
                    unit: metric.unit,
                    tags: metric.tags || {},
                    timestamp,
                    organizationId: metricsRequest.organizationId,
                    userId: metricsRequest.userId,
                    source: metricsRequest.source,
                    metadata: metricsRequest.metadata || {},
                    tenantId: metricsRequest.organizationId || 'system'
                };
                await database_1.db.createItem('metrics', metricRecord);
                return metricId;
            });
            const batchIds = await Promise.all(batchPromises);
            metricIds.push(...batchIds);
        }
        return metricIds;
    }
    catch (error) {
        logger_1.logger.error('Failed to store metrics', { error, metricCount: metricsRequest.metrics.length });
        throw error;
    }
}
async function updateRealTimeAggregations(metrics) {
    try {
        const now = new Date();
        const currentMinute = Math.floor(now.getTime() / 60000) * 60000;
        const currentHour = Math.floor(now.getTime() / 3600000) * 3600000;
        for (const metric of metrics) {
            // Update minute-level aggregations
            const minuteKey = `metrics:agg:${metric.name}:1m:${currentMinute}`;
            await redis_1.redis.hincrby(minuteKey, 'count', 1);
            await redis_1.redis.hincrbyfloat(minuteKey, 'sum', metric.value);
            await redis_1.redis.hset(minuteKey, 'last_value', metric.value.toString());
            await redis_1.redis.expire(minuteKey, 3600); // 1 hour
            // Update hour-level aggregations
            const hourKey = `metrics:agg:${metric.name}:1h:${currentHour}`;
            await redis_1.redis.hincrby(hourKey, 'count', 1);
            await redis_1.redis.hincrbyfloat(hourKey, 'sum', metric.value);
            await redis_1.redis.hset(hourKey, 'last_value', metric.value.toString());
            await redis_1.redis.expire(hourKey, 86400); // 24 hours
            // Update min/max values
            const currentMin = await redis_1.redis.hget(minuteKey, 'min');
            const currentMax = await redis_1.redis.hget(minuteKey, 'max');
            if (!currentMin || metric.value < parseFloat(currentMin)) {
                await redis_1.redis.hset(minuteKey, 'min', metric.value.toString());
            }
            if (!currentMax || metric.value > parseFloat(currentMax)) {
                await redis_1.redis.hset(minuteKey, 'max', metric.value.toString());
            }
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to update real-time aggregations', { error });
    }
}
async function updateMetricStatistics(metrics) {
    try {
        for (const metric of metrics) {
            const statsKey = `metrics:stats:${metric.name}`;
            await redis_1.redis.hincrby(statsKey, 'total_count', 1);
            await redis_1.redis.hincrbyfloat(statsKey, 'total_sum', metric.value);
            await redis_1.redis.hset(statsKey, 'last_value', metric.value.toString());
            await redis_1.redis.hset(statsKey, 'last_updated', new Date().toISOString());
            await redis_1.redis.expire(statsKey, 86400 * 30); // 30 days
            // Update category statistics
            const categoryStatsKey = `metrics:category_stats:${metric.category}`;
            await redis_1.redis.hincrby(categoryStatsKey, 'count', 1);
            await redis_1.redis.hset(categoryStatsKey, 'last_updated', new Date().toISOString());
            await redis_1.redis.expire(categoryStatsKey, 86400 * 7); // 7 days
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to update metric statistics', { error });
    }
}
async function executeMetricsQuery(queryRequest) {
    try {
        // Build query
        let query = 'SELECT * FROM c WHERE c.timestamp >= @startTime AND c.timestamp <= @endTime';
        const parameters = [queryRequest.timeRange.startTime, queryRequest.timeRange.endTime];
        if (queryRequest.metricNames && queryRequest.metricNames.length > 0) {
            query += ' AND c.name IN (@metricNames)';
            parameters.push(queryRequest.metricNames);
        }
        if (queryRequest.category) {
            query += ' AND c.category = @category';
            parameters.push(queryRequest.category);
        }
        if (queryRequest.organizationId) {
            query += ' AND c.organizationId = @orgId';
            parameters.push(queryRequest.organizationId);
        }
        if (queryRequest.userId) {
            query += ' AND c.userId = @userId';
            parameters.push(queryRequest.userId);
        }
        query += ' ORDER BY c.timestamp ASC';
        // Execute query
        const metrics = await database_1.db.queryItems('metrics', query, parameters);
        // Group by metric name and aggregate
        const groupedMetrics = groupMetricsByName(metrics);
        const aggregatedSeries = aggregateMetrics(groupedMetrics, queryRequest.aggregation, queryRequest.interval);
        return aggregatedSeries;
    }
    catch (error) {
        logger_1.logger.error('Failed to execute metrics query', { error, queryRequest });
        return [];
    }
}
function groupMetricsByName(metrics) {
    const grouped = {};
    for (const metric of metrics) {
        if (!grouped[metric.name]) {
            grouped[metric.name] = [];
        }
        grouped[metric.name].push(metric);
    }
    return grouped;
}
function aggregateMetrics(groupedMetrics, aggregation, interval) {
    const series = [];
    for (const [metricName, metrics] of Object.entries(groupedMetrics)) {
        if (metrics.length === 0)
            continue;
        const firstMetric = metrics[0];
        const intervalMs = parseInterval(interval);
        const buckets = {};
        // Group metrics into time buckets
        for (const metric of metrics) {
            const timestamp = new Date(metric.timestamp);
            const bucketTime = Math.floor(timestamp.getTime() / intervalMs) * intervalMs;
            const bucketKey = new Date(bucketTime).toISOString();
            if (!buckets[bucketKey]) {
                buckets[bucketKey] = [];
            }
            buckets[bucketKey].push(metric);
        }
        // Aggregate each bucket
        const dataPoints = [];
        let sum = 0;
        let count = 0;
        let min = Number.MAX_VALUE;
        let max = Number.MIN_VALUE;
        for (const [timestamp, bucketMetrics] of Object.entries(buckets)) {
            const values = bucketMetrics.map(m => m.value);
            let aggregatedValue;
            switch (aggregation) {
                case AggregationType.SUM:
                    aggregatedValue = values.reduce((sum, val) => sum + val, 0);
                    break;
                case AggregationType.AVERAGE:
                    aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
                    break;
                case AggregationType.COUNT:
                    aggregatedValue = values.length;
                    break;
                case AggregationType.MIN:
                    aggregatedValue = Math.min(...values);
                    break;
                case AggregationType.MAX:
                    aggregatedValue = Math.max(...values);
                    break;
                default:
                    aggregatedValue = values.reduce((sum, val) => sum + val, 0) / values.length;
            }
            dataPoints.push({
                timestamp,
                value: aggregatedValue,
                tags: bucketMetrics[0].tags
            });
            // Update overall statistics
            sum += aggregatedValue;
            count++;
            min = Math.min(min, aggregatedValue);
            max = Math.max(max, aggregatedValue);
        }
        series.push({
            name: metricName,
            category: firstMetric.category,
            unit: firstMetric.unit,
            dataPoints: dataPoints.sort((a, b) => new Date(a.timestamp).getTime() - new Date(b.timestamp).getTime()),
            aggregation,
            interval,
            statistics: {
                count,
                sum,
                average: count > 0 ? sum / count : 0,
                min: min === Number.MAX_VALUE ? 0 : min,
                max: max === Number.MIN_VALUE ? 0 : max
            }
        });
    }
    return series;
}
function parseInterval(interval) {
    const intervalMap = {
        '1m': 60 * 1000,
        '5m': 5 * 60 * 1000,
        '15m': 15 * 60 * 1000,
        '1h': 60 * 60 * 1000,
        '1d': 24 * 60 * 60 * 1000
    };
    return intervalMap[interval] || intervalMap['1h'];
}
async function getSystemMetricsSummary() {
    try {
        // Get summary statistics from Redis
        const categories = Object.values(MetricCategory);
        const summary = {
            totalMetrics: 0,
            categorySummary: {},
            topMetrics: [],
            recentActivity: {}
        };
        for (const category of categories) {
            const categoryStatsKey = `metrics:category_stats:${category}`;
            const categoryStats = await redis_1.redis.hgetall(categoryStatsKey);
            summary.categorySummary[category] = {
                count: parseInt(categoryStats.count || '0'),
                lastUpdated: categoryStats.last_updated
            };
            summary.totalMetrics += parseInt(categoryStats.count || '0');
        }
        // Get top metrics by activity
        const metricKeys = await redis_1.redis.keys('metrics:stats:*');
        const topMetrics = [];
        for (const key of metricKeys.slice(0, 10)) {
            const stats = await redis_1.redis.hgetall(key);
            const metricName = key.replace('metrics:stats:', '');
            topMetrics.push({
                name: metricName,
                count: parseInt(stats.total_count || '0'),
                lastValue: parseFloat(stats.last_value || '0'),
                lastUpdated: stats.last_updated
            });
        }
        summary.topMetrics = topMetrics.sort((a, b) => b.count - a.count).slice(0, 10);
        return summary;
    }
    catch (error) {
        logger_1.logger.error('Failed to get system metrics summary', { error });
        return {
            totalMetrics: 0,
            categorySummary: {},
            topMetrics: [],
            recentActivity: {}
        };
    }
}
// Register functions
functions_1.app.http('metrics-collect', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'metrics/collect',
    handler: collectMetrics
});
functions_1.app.http('metrics-query', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'metrics/query',
    handler: queryMetrics
});
functions_1.app.http('metrics-summary', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'metrics/summary',
    handler: getMetricsSummary
});
//# sourceMappingURL=metrics-collection.js.map