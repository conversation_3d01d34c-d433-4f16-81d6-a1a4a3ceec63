/**
 * Notification Preferences Management Function
 * Handles advanced notification preference management, templates, and delivery settings
 * Migrated from old-arch/src/notification-service/preferences/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get notification preferences handler
 */
export declare function getNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update notification preferences handler
 */
export declare function updateNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Reset notification preferences to defaults handler
 */
export declare function resetNotificationPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
