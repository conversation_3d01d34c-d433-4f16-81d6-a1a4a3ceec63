{"version": 3, "file": "document-archiving.js", "sourceRoot": "", "sources": ["../../src/functions/document-archiving.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiFA,0CA2LC;AAKD,0CAwKC;AAzbD;;;;GAIG;AACH,gDAAyF;AACzF,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AACjD,oDAAwD;AAExD,4BAA4B;AAC5B,IAAK,aAKJ;AALD,WAAK,aAAa;IAChB,kCAAiB,CAAA;IACjB,sCAAqB,CAAA;IACrB,sDAAqC,CAAA;IACrC,oCAAmB,CAAA;AACrB,CAAC,EALI,aAAa,KAAb,aAAa,QAKjB;AAED,IAAK,eAOJ;AAPD,WAAK,eAAe;IAClB,sCAAmB,CAAA;IACnB,sCAAmB,CAAA;IACnB,wCAAqB,CAAA;IACrB,sCAAmB,CAAA;IACnB,0CAAuB,CAAA;IACvB,oCAAiB,CAAA;AACnB,CAAC,EAPI,eAAe,KAAf,eAAe,QAOnB;AAED,qBAAqB;AACrB,MAAM,qBAAqB,GAAG,GAAG,CAAC,MAAM,CAAC;IACvC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,eAAe,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC,CAAC,OAAO,CAAC,eAAe,CAAC,OAAO,CAAC;IACvG,mBAAmB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IAC7D,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACxC,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;IAC7C,eAAe,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CAC7C,CAAC,CAAC;AAEH,MAAM,qBAAqB,GAAG,GAAG,CAAC,MAAM,CAAC;IACvC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACxC,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CACjD,CAAC,CAAC;AAiCH;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,OAAoB,EAAE,OAA0B;IACpF,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB;QAAE,OAAO,iBAAiB,CAAC;IAEhD,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE3D,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAA2B,KAAK,CAAC;QAErD,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QACtG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,QAAe,CAAC;QAErC,wBAAwB;QACxB,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;aACjD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,wCAAwC;QACxC,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACvC,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,8BAA8B,EAAE;aACpD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,4BAA4B;QAC5B,MAAM,SAAS,GAAG,uBAAuB,CACvC,cAAc,CAAC,eAAe,IAAI,eAAe,CAAC,OAAO,EACzD,cAAc,CAAC,mBAAmB,CACnC,CAAC;QAEF,wBAAwB;QACxB,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,MAAM,eAAe,GAAoB;YACvC,EAAE,EAAE,SAAS;YACb,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,gBAAgB,EAAE,YAAY;YAC9B,aAAa,EAAE,aAAa,CAAC,QAAQ;YACrC,eAAe,EAAE,cAAc,CAAC,eAAe,IAAI,eAAe,CAAC,OAAO;YAC1E,UAAU,EAAE,GAAG;YACf,SAAS;YACT,MAAM,EAAE,cAAc,CAAC,MAAM;YAC7B,QAAQ,EAAE;gBACR,YAAY,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;gBACpC,gBAAgB,EAAE,cAAc,CAAC,gBAAgB,KAAK,KAAK;gBAC3D,eAAe,EAAE,cAAc,CAAC,eAAe,KAAK,KAAK;gBACzD,gBAAgB,EAAE,gBAAgB,CAAC,YAAY,CAAC;aACjD;YACD,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ,IAAI,IAAI,CAAC,EAAE;SACnC,CAAC;QAEF,oBAAoB;QACpB,MAAM,aAAa,GAAG,MAAM,wBAAwB,CAAC,eAAe,CAAC,CAAC;QACtE,eAAe,CAAC,QAAQ,CAAC,cAAc,GAAG,aAAa,CAAC,cAAc,CAAC;QACvE,eAAe,CAAC,QAAQ,CAAC,gBAAgB,GAAG,aAAa,CAAC,QAAQ,CAAC;QAEnE,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;QAE1D,yBAAyB;QACzB,MAAM,eAAe,GAAG;YACtB,GAAG,YAAY;YACf,EAAE,EAAE,YAAY,CAAC,EAAE;YACnB,MAAM,EAAE,UAAU;YAClB,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,SAAS;YACT,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS,EAAE,GAAG;YACd,OAAO,EAAE;gBACP,SAAS;gBACT,eAAe,EAAE,eAAe,CAAC,eAAe;gBAChD,SAAS;gBACT,MAAM,EAAE,cAAc,CAAC,MAAM;gBAC7B,YAAY,EAAE,eAAe,CAAC,QAAQ,CAAC,YAAY;gBACnD,cAAc,EAAE,eAAe,CAAC,QAAQ,CAAC,cAAc;aACxD;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,oBAAY,CAAC,YAAY,CAAC;YAC9B,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,cAAc,CAAC,UAAU;YACtC,aAAa,EAAE,UAAU;YACzB,OAAO,EAAE,CAAC;YACV,IAAI,EAAE;gBACJ,QAAQ,EAAE,eAAe;gBACzB,OAAO,EAAE,eAAe;gBACxB,UAAU,EAAE,IAAI,CAAC,EAAE;aACpB;YACD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,aAAa;YACb,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS;YACT,eAAe,EAAE,eAAe,CAAC,eAAe;YAChD,SAAS;YACT,UAAU,EAAE,IAAI,CAAC,EAAE;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,SAAS;gBACT,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,MAAM,EAAE,aAAa,CAAC,QAAQ;gBAC9B,eAAe,EAAE,eAAe,CAAC,eAAe;gBAChD,UAAU,EAAE,GAAG;gBACf,SAAS;gBACT,gBAAgB,EAAE,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC;oBACzD,CAAC,eAAe,CAAC,QAAQ,CAAC,YAAY,GAAG,eAAe,CAAC,QAAQ,CAAC,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC,IAAI;gBACrG,OAAO,EAAE,gCAAgC;aAC1C;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QAEhF,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,eAAe,CAAC,OAAoB,EAAE,OAA0B;IACpF,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB;QAAE,OAAO,iBAAiB,CAAC;IAEhD,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,0BAA0B,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE3D,IAAI,CAAC;QACH,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,qBAAqB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE9D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAAG,KAAK,CAAC;QAE7B,wBAAwB;QACxB,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QACtG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,QAAe,CAAC;QAErC,IAAI,YAAY,CAAC,MAAM,KAAK,UAAU,EAAE,CAAC;YACvC,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE;aAChD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,wBAAwB;QACxB,MAAM,SAAS,GAAG,MAAM,mBAAmB,CAAC,YAAY,EAAE,IAAI,CAAC,EAAE,CAAC,CAAC;QACnE,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,2BAA2B,EAAE;aACjD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,qBAAqB;QACrB,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,mBAAmB,EAAE,YAAY,CAAC,SAAS,EAAE,YAAY,CAAC,SAAS,CAAC,CAAC;QACvG,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,0BAA0B,EAAE;aAChD,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,WAAW,GAAG,OAAc,CAAC;QAEnC,sBAAsB;QACtB,MAAM,aAAa,GAAG,MAAM,0BAA0B,CAAC,WAAW,CAAC,CAAC;QAEpE,yBAAyB;QACzB,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QACrC,MAAM,gBAAgB,GAAG;YACvB,GAAG,YAAY;YACf,EAAE,EAAE,YAAY,CAAC,EAAE;YACnB,MAAM,EAAE,QAAQ;YAChB,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,EAAE;YACnB,SAAS,EAAE,cAAc,CAAC,gBAAgB,IAAI,YAAY,CAAC,SAAS;YACpE,SAAS,EAAE,GAAG;SACf,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,gBAAgB,CAAC,CAAC;QAEnD,wBAAwB;QACxB,MAAM,cAAc,GAAG;YACrB,GAAG,WAAW;YACd,EAAE,EAAE,WAAW,CAAC,EAAE;YAClB,aAAa,EAAE,aAAa,CAAC,MAAM;YACnC,UAAU,EAAE,GAAG;YACf,UAAU,EAAE,IAAI,CAAC,EAAE;SACpB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,cAAc,CAAC,CAAC;QAEzD,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,mBAAmB;YACzB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,gBAAgB,CAAC,SAAS;YACrC,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS,EAAE,GAAG;YACd,OAAO,EAAE;gBACP,SAAS,EAAE,YAAY,CAAC,SAAS;gBACjC,MAAM,EAAE,cAAc,CAAC,MAAM;gBAC7B,iBAAiB,EAAE,cAAc,CAAC,gBAAgB;gBAClD,kBAAkB,EAAE,WAAW,CAAC,UAAU;aAC3C;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,oBAAY,CAAC,YAAY,CAAC;YAC9B,IAAI,EAAE,kBAAkB;YACxB,WAAW,EAAE,cAAc,CAAC,UAAU;YACtC,aAAa,EAAE,UAAU;YACzB,OAAO,EAAE,CAAC;YACV,IAAI,EAAE;gBACJ,QAAQ,EAAE,gBAAgB;gBAC1B,OAAO,EAAE,cAAc;gBACvB,UAAU,EAAE,IAAI,CAAC,EAAE;aACpB;YACD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,aAAa;YACb,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,UAAU,EAAE,IAAI,CAAC,EAAE;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,MAAM,EAAE,QAAQ;gBAChB,UAAU,EAAE,GAAG;gBACf,SAAS,EAAE,gBAAgB,CAAC,SAAS;gBACrC,OAAO,EAAE,gCAAgC;aAC1C;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,aAAa,EAAE,KAAK,EAAE,YAAY,EAAE,CAAC,CAAC;QAEhF,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AAEH,KAAK,UAAU,mBAAmB,CAAC,QAAa,EAAE,MAAc;IAC9D,IAAI,CAAC;QACH,IAAI,QAAQ,CAAC,SAAS,KAAK,MAAM;YAAE,OAAO,IAAI,CAAC;QAE/C,MAAM,eAAe,GAAG,+FAA+F,CAAC;QACxH,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAe,EAAE,CAAC,QAAQ,CAAC,cAAc,EAAE,MAAM,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE9H,OAAO,WAAW,CAAC,MAAM,GAAG,CAAC,CAAC;IAChC,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;QAC5F,OAAO,KAAK,CAAC;IACf,CAAC;AACH,CAAC;AAED,SAAS,uBAAuB,CAAC,eAAgC,EAAE,UAAmB;IACpF,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;IACvB,IAAI,cAAoB,CAAC;IAEzB,QAAQ,eAAe,EAAE,CAAC;QACxB,KAAK,eAAe,CAAC,OAAO;YAC1B,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACpE,MAAM;QACR,KAAK,eAAe,CAAC,OAAO;YAC1B,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACpE,MAAM;QACR,KAAK,eAAe,CAAC,QAAQ;YAC3B,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACrE,MAAM;QACR,KAAK,eAAe,CAAC,OAAO;YAC1B,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YACzE,MAAM;QACR,KAAK,eAAe,CAAC,MAAM;YACzB,IAAI,CAAC,UAAU;gBAAE,MAAM,IAAI,KAAK,CAAC,kDAAkD,CAAC,CAAC;YACrF,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,UAAU,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;YAC5E,MAAM;QACR,KAAK,eAAe,CAAC,SAAS;YAC5B,OAAO,SAAS,CAAC;QACnB;YACE,cAAc,GAAG,IAAI,IAAI,CAAC,GAAG,CAAC,OAAO,EAAE,GAAG,CAAC,GAAG,GAAG,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,GAAG,IAAI,CAAC,CAAC;IAC7E,CAAC;IAED,OAAO,cAAc,CAAC,WAAW,EAAE,CAAC;AACtC,CAAC;AAED,SAAS,gBAAgB,CAAC,QAAa;IACrC,iCAAiC;IACjC,MAAM,MAAM,GAAG,OAAO,CAAC,QAAQ,CAAC,CAAC;IACjC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,QAAQ,CAAC,CAAC;IACzC,OAAO,MAAM,CAAC,UAAU,CAAC,QAAQ,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;AACnE,CAAC;AAED,KAAK,UAAU,wBAAwB,CAAC,OAAwB;IAC9D,IAAI,CAAC;QACH,6CAA6C;QAC7C,MAAM,YAAY,GAAG,OAAO,CAAC,QAAQ,CAAC,YAAY,CAAC;QACnD,MAAM,gBAAgB,GAAG,OAAO,CAAC,QAAQ,CAAC,eAAe,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,GAAG,CAAC;QACtE,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,YAAY,GAAG,gBAAgB,CAAC,CAAC;QAEnE,yCAAyC;QACzC,MAAM,QAAQ,GAAG,gBAAgB,CAAC,OAAO,CAAC,gBAAgB,CAAC,CAAC;QAE5D,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE;YAC1C,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,YAAY;YACZ,cAAc;YACd,gBAAgB,EAAE,CAAC,YAAY,GAAG,cAAc,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;SAC7D,CAAC,CAAC;QAEH,OAAO;YACL,cAAc;YACd,QAAQ;YACR,OAAO,EAAE,IAAI;SACd,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACvF,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,KAAK,UAAU,0BAA0B,CAAC,OAAY;IACpD,IAAI,CAAC;QACH,+BAA+B;QAC/B,eAAM,CAAC,IAAI,CAAC,gCAAgC,EAAE;YAC5C,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,UAAU,EAAE,OAAO,CAAC,UAAU;SAC/B,CAAC,CAAC;QAEH,OAAO;YACL,OAAO,EAAE,IAAI;YACb,YAAY,EAAE,OAAO,CAAC,QAAQ,CAAC,YAAY;SAC5C,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,CAAC,EAAE,EAAE,CAAC,CAAC;QACzF,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,gCAAgC;IACvC,OAAO,EAAE,eAAe;CACzB,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC3B,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,gCAAgC;IACvC,OAAO,EAAE,eAAe;CACzB,CAAC,CAAC"}