/**
 * Document Comments Function
 * Handles document comment operations (create, read, update, delete)
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create comment handler
 */
export declare function createComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get comments handler
 */
export declare function getComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
