/**
 * API Rate Limiting Function
 * Handles API rate limiting, throttling, and quota management
 * Migrated from old-arch/src/api-service/rate-limiting/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Check rate limit handler
 */
export declare function checkRateLimit(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create rate limit handler
 */
export declare function createRateLimit(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
