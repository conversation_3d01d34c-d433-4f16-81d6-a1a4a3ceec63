/**
 * Database service for Cosmos DB operations
 * Provides centralized database access and common operations
 */
import { Container } from '@azure/cosmos';
export declare class DatabaseService {
    private static instance;
    private client;
    private database;
    private containers;
    private constructor();
    /**
     * Initialize the database connection lazily
     */
    private initializeDatabase;
    static getInstance(): DatabaseService;
    /**
     * Check if database is available
     */
    private isDatabaseAvailable;
    /**
     * Get container instance
     */
    getContainer(containerName: string): Container;
    /**
     * Create item in container
     */
    createItem<T extends Record<string, any>>(containerName: string, item: T): Promise<T>;
    /**
     * Read item by id and partition key
     */
    readItem<T extends Record<string, any>>(containerName: string, id: string, partitionKey: string): Promise<T | null>;
    /**
     * Update item
     */
    updateItem<T>(containerName: string, item: T & {
        id: string;
    }): Promise<T>;
    /**
     * Delete item
     */
    deleteItem(containerName: string, id: string, partitionKey: string): Promise<void>;
    /**
     * Query items with SQL
     */
    queryItems<T>(containerName: string, query: string, parameters?: any[]): Promise<T[]>;
    /**
     * Query items with pagination
     */
    queryItemsPaginated<T>(containerName: string, query: string, parameters?: any[], maxItemCount?: number, continuationToken?: string): Promise<{
        items: T[];
        continuationToken?: string;
    }>;
    /**
     * Check if container exists
     */
    containerExists(containerName: string): Promise<boolean>;
    /**
     * Upsert an item in a container (create or update)
     */
    upsertItem(containerName: string, item: any): Promise<any>;
    /**
     * Create container if it doesn't exist
     */
    createContainerIfNotExists(containerName: string, partitionKey: string): Promise<Container>;
}
export declare const db: DatabaseService;
export default db;
