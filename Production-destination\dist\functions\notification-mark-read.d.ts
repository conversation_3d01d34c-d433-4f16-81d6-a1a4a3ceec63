/**
 * Notification Mark Read Function
 * Handles marking notifications as read/unread
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Mark notifications as read handler
 */
export declare function markNotificationsRead(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get notification by ID handler
 */
export declare function getNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
