/**
 * System Configuration Function
 * Handles system-wide configuration management
 * Migrated from old-arch/src/admin-service/configuration/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get configuration handler
 */
export declare function getConfiguration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update configuration handler
 */
export declare function updateConfiguration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
