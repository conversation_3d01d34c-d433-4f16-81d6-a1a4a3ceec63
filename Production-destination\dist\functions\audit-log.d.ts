/**
 * Audit Log Function
 * Handles audit trail and compliance logging
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * List audit logs handler
 */
export declare function listAuditLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Export audit logs handler
 */
export declare function exportAuditLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get audit statistics handler
 */
export declare function getAuditStatistics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
