"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createMigration = createMigration;
exports.getMigrationStatus = getMigrationStatus;
/**
 * Data Migration Function
 * Handles data migration operations between systems and versions
 * Migrated from old-arch/src/admin-service/migration/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Migration types and enums
var MigrationType;
(function (MigrationType) {
    MigrationType["IMPORT"] = "IMPORT";
    MigrationType["EXPORT"] = "EXPORT";
    MigrationType["TRANSFORM"] = "TRANSFORM";
    MigrationType["SYNC"] = "SYNC";
    MigrationType["UPGRADE"] = "UPGRADE";
})(MigrationType || (MigrationType = {}));
var MigrationStatus;
(function (MigrationStatus) {
    MigrationStatus["PENDING"] = "PENDING";
    MigrationStatus["RUNNING"] = "RUNNING";
    MigrationStatus["COMPLETED"] = "COMPLETED";
    MigrationStatus["FAILED"] = "FAILED";
    MigrationStatus["CANCELLED"] = "CANCELLED";
    MigrationStatus["PAUSED"] = "PAUSED";
})(MigrationStatus || (MigrationStatus = {}));
var DataSource;
(function (DataSource) {
    DataSource["CSV"] = "CSV";
    DataSource["JSON"] = "JSON";
    DataSource["XML"] = "XML";
    DataSource["DATABASE"] = "DATABASE";
    DataSource["API"] = "API";
    DataSource["FILESYSTEM"] = "FILESYSTEM";
})(DataSource || (DataSource = {}));
// Validation schemas
const createMigrationSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(MigrationType)).required(),
    organizationId: Joi.string().uuid().required(),
    source: Joi.object({
        type: Joi.string().valid(...Object.values(DataSource)).required(),
        connection: Joi.object({
            url: Joi.string().uri().optional(),
            connectionString: Joi.string().optional(),
            filePath: Joi.string().optional(),
            apiKey: Joi.string().optional(),
            credentials: Joi.object().optional()
        }).required(),
        configuration: Joi.object({
            batchSize: Joi.number().min(1).max(10000).default(1000),
            delimiter: Joi.string().max(5).optional(),
            encoding: Joi.string().valid('utf8', 'utf16', 'ascii').default('utf8'),
            skipHeaders: Joi.boolean().default(false),
            dateFormat: Joi.string().optional()
        }).optional()
    }).required(),
    target: Joi.object({
        type: Joi.string().valid(...Object.values(DataSource)).required(),
        connection: Joi.object({
            url: Joi.string().uri().optional(),
            connectionString: Joi.string().optional(),
            filePath: Joi.string().optional(),
            apiKey: Joi.string().optional(),
            credentials: Joi.object().optional()
        }).required(),
        configuration: Joi.object().optional()
    }).required(),
    mapping: Joi.object({
        fieldMappings: Joi.array().items(Joi.object({
            sourceField: Joi.string().required(),
            targetField: Joi.string().required(),
            transformation: Joi.string().optional(),
            defaultValue: Joi.any().optional(),
            required: Joi.boolean().default(false)
        })).required(),
        filters: Joi.array().items(Joi.object({
            field: Joi.string().required(),
            operator: Joi.string().valid('equals', 'not_equals', 'contains', 'greater_than', 'less_than', 'exists').required(),
            value: Joi.any().optional()
        })).optional(),
        transformations: Joi.array().items(Joi.object({
            type: Joi.string().valid('uppercase', 'lowercase', 'trim', 'format_date', 'custom').required(),
            field: Joi.string().required(),
            parameters: Joi.object().optional()
        })).optional()
    }).required(),
    schedule: Joi.object({
        enabled: Joi.boolean().default(false),
        frequency: Joi.string().valid('once', 'daily', 'weekly', 'monthly').default('once'),
        time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
        dayOfWeek: Joi.number().min(0).max(6).optional(),
        dayOfMonth: Joi.number().min(1).max(31).optional()
    }).optional(),
    options: Joi.object({
        validateData: Joi.boolean().default(true),
        skipErrors: Joi.boolean().default(false),
        createBackup: Joi.boolean().default(true),
        dryRun: Joi.boolean().default(false),
        parallelProcessing: Joi.boolean().default(false),
        maxRetries: Joi.number().min(0).max(5).default(3)
    }).optional()
});
/**
 * Create migration handler
 */
async function createMigration(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create migration started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkMigrationAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to migration functions" }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = createMigrationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const migrationRequest = value;
        // Check migration limits
        const canCreate = await checkMigrationLimits(user.tenantId || user.id);
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Validate connections
        const sourceValidation = await validateConnection(migrationRequest.source);
        if (!sourceValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: `Source connection error: ${sourceValidation.reason}` }
            }, request);
        }
        const targetValidation = await validateConnection(migrationRequest.target);
        if (!targetValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: `Target connection error: ${targetValidation.reason}` }
            }, request);
        }
        // Create migration job
        const migrationId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const migration = {
            id: migrationId,
            name: migrationRequest.name,
            description: migrationRequest.description,
            type: migrationRequest.type,
            status: MigrationStatus.PENDING,
            organizationId: migrationRequest.organizationId,
            source: migrationRequest.source,
            target: migrationRequest.target,
            mapping: migrationRequest.mapping,
            schedule: migrationRequest.schedule,
            options: {
                validateData: true,
                skipErrors: false,
                createBackup: true,
                dryRun: false,
                parallelProcessing: false,
                maxRetries: 3,
                ...migrationRequest.options
            },
            progress: {
                percentage: 0,
                currentStep: 'Initializing',
                recordsProcessed: 0,
                totalRecords: 0,
                recordsSuccessful: 0,
                recordsFailed: 0
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('data-migrations', migration);
        // Start migration process if not scheduled
        if (!migrationRequest.schedule?.enabled) {
            await startMigrationProcess(migration);
        }
        // Create audit log entry
        await database_1.db.createItem('audit-logs', {
            id: (0, uuid_1.v4)(),
            eventType: 'MIGRATION_CREATED',
            userId: user.id,
            organizationId: migrationRequest.organizationId,
            description: `Data migration created: ${migrationRequest.name}`,
            severity: 'MEDIUM',
            resourceType: 'data-migration',
            resourceId: migrationId,
            details: {
                migrationType: migrationRequest.type,
                sourceType: migrationRequest.source.type,
                targetType: migrationRequest.target.type,
                fieldMappingCount: migrationRequest.mapping.fieldMappings.length,
                isScheduled: !!migrationRequest.schedule?.enabled,
                isDryRun: migrationRequest.options?.dryRun || false
            },
            timestamp: now,
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown',
            userAgent: request.headers.get('user-agent') || 'unknown',
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DataMigrationCreated',
            aggregateId: migrationId,
            aggregateType: 'DataMigration',
            version: 1,
            data: {
                migration,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: migrationRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Migration created successfully", {
            correlationId,
            migrationId,
            migrationName: migrationRequest.name,
            migrationType: migrationRequest.type,
            sourceType: migrationRequest.source.type,
            targetType: migrationRequest.target.type,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                migrationId,
                name: migrationRequest.name,
                type: migrationRequest.type,
                status: MigrationStatus.PENDING,
                sourceType: migrationRequest.source.type,
                targetType: migrationRequest.target.type,
                estimatedDuration: estimateMigrationDuration(migrationRequest),
                createdAt: now,
                message: "Migration created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create migration failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get migration status handler
 */
async function getMigrationStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const migrationId = request.params.migrationId;
    logger_1.logger.info("Get migration status started", { correlationId, migrationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkMigrationAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to migration functions" }
            }, request);
        }
        if (!migrationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Migration ID is required" }
            }, request);
        }
        // Get migration
        const migration = await database_1.db.readItem('data-migrations', migrationId, migrationId);
        if (!migration) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Migration not found" }
            }, request);
        }
        const migrationData = migration;
        logger_1.logger.info("Migration status retrieved successfully", {
            correlationId,
            migrationId,
            status: migrationData.status,
            progress: migrationData.progress?.percentage,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                migrationId: migrationData.id,
                name: migrationData.name,
                type: migrationData.type,
                status: migrationData.status,
                progress: migrationData.progress,
                results: migrationData.results,
                createdAt: migrationData.createdAt,
                updatedAt: migrationData.updatedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get migration status failed", {
            correlationId,
            migrationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkMigrationAccess(user) {
    try {
        // Check if user has admin or migration role
        return user.roles?.includes('admin') || user.roles?.includes('migration_admin');
    }
    catch (error) {
        logger_1.logger.error('Failed to check migration access', { error, userId: user.id });
        return false;
    }
}
async function checkMigrationLimits(tenantId) {
    try {
        // Check concurrent migration limit
        const activeMigrationsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.tenantId = @tenantId AND c.status IN (@running, @pending)';
        const activeMigrationsResult = await database_1.db.queryItems('data-migrations', activeMigrationsQuery, [tenantId, MigrationStatus.RUNNING, MigrationStatus.PENDING]);
        const activeMigrations = Number(activeMigrationsResult[0]) || 0;
        const maxConcurrentMigrations = 2; // Configurable limit
        if (activeMigrations >= maxConcurrentMigrations) {
            return {
                allowed: false,
                reason: `Maximum concurrent migrations limit reached (${maxConcurrentMigrations})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check migration limits', { error, tenantId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
async function validateConnection(connectionConfig) {
    try {
        // Validate connection configuration based on type
        switch (connectionConfig.type) {
            case DataSource.CSV:
            case DataSource.JSON:
            case DataSource.XML:
                if (!connectionConfig.connection.filePath && !connectionConfig.connection.url) {
                    return { valid: false, reason: 'File path or URL is required' };
                }
                break;
            case DataSource.DATABASE:
                if (!connectionConfig.connection.connectionString) {
                    return { valid: false, reason: 'Connection string is required' };
                }
                break;
            case DataSource.API:
                if (!connectionConfig.connection.url) {
                    return { valid: false, reason: 'API URL is required' };
                }
                break;
            default:
                return { valid: false, reason: 'Unsupported data source type' };
        }
        return { valid: true };
    }
    catch (error) {
        return { valid: false, reason: 'Connection validation failed' };
    }
}
function estimateMigrationDuration(migrationRequest) {
    // Simplified estimation - in production, use historical data
    let baseTimeMinutes = 15; // Base time for migration
    // Adjust based on type
    if (migrationRequest.type === MigrationType.TRANSFORM) {
        baseTimeMinutes *= 2;
    }
    else if (migrationRequest.type === MigrationType.SYNC) {
        baseTimeMinutes *= 1.5;
    }
    // Adjust based on complexity
    const mappingComplexity = migrationRequest.mapping.fieldMappings.length;
    if (mappingComplexity > 20) {
        baseTimeMinutes *= 1.5;
    }
    return `${baseTimeMinutes} minutes`;
}
async function startMigrationProcess(migration) {
    try {
        // Update status to running
        const updatedMigration = {
            ...migration,
            id: migration.id,
            status: MigrationStatus.RUNNING,
            progress: {
                ...migration.progress,
                percentage: 5,
                currentStep: 'Starting migration process',
                startedAt: new Date().toISOString()
            },
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('data-migrations', updatedMigration);
        // Simulate migration processing (in production, this would be actual migration logic)
        setTimeout(async () => {
            try {
                const migrationResult = await performMigration(migration);
                const completedMigration = {
                    ...updatedMigration,
                    id: migration.id,
                    status: MigrationStatus.COMPLETED,
                    progress: {
                        ...updatedMigration.progress,
                        percentage: 100,
                        currentStep: 'Completed'
                    },
                    results: {
                        totalRecords: migrationResult.totalRecords,
                        successfulRecords: migrationResult.successfulRecords,
                        failedRecords: migrationResult.failedRecords,
                        skippedRecords: migrationResult.skippedRecords,
                        completedAt: new Date().toISOString(),
                        logFile: migrationResult.logFile,
                        errorFile: migrationResult.errorFile
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('data-migrations', completedMigration);
                logger_1.logger.info('Migration completed successfully', { migrationId: migration.id });
            }
            catch (error) {
                const failedMigration = {
                    ...updatedMigration,
                    id: migration.id,
                    status: MigrationStatus.FAILED,
                    progress: {
                        ...updatedMigration.progress,
                        currentStep: 'Failed'
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('data-migrations', failedMigration);
                logger_1.logger.error('Migration failed', { migrationId: migration.id, error });
            }
        }, 20000); // 20 second delay for demo
    }
    catch (error) {
        logger_1.logger.error('Failed to start migration process', { migrationId: migration.id, error });
    }
}
async function performMigration(migration) {
    // Simplified migration result generation
    const mockResult = {
        totalRecords: Math.floor(Math.random() * 50000) + 10000,
        successfulRecords: 0,
        failedRecords: 0,
        skippedRecords: 0,
        logFile: `migrations/${migration.id}/migration.log`,
        errorFile: `migrations/${migration.id}/errors.log`
    };
    // Calculate success/failure rates
    mockResult.successfulRecords = Math.floor(mockResult.totalRecords * 0.95);
    mockResult.failedRecords = Math.floor(mockResult.totalRecords * 0.03);
    mockResult.skippedRecords = mockResult.totalRecords - mockResult.successfulRecords - mockResult.failedRecords;
    return mockResult;
}
// Register functions
functions_1.app.http('migration-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/data-migrations/create',
    handler: createMigration
});
functions_1.app.http('migration-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/data-migrations/{migrationId}/status',
    handler: getMigrationStatus
});
//# sourceMappingURL=data-migration.js.map