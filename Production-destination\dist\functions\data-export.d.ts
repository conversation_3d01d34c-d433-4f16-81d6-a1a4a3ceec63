/**
 * Data Export Function
 * Handles data export operations for various formats and sources
 * Migrated from old-arch/src/analytics-service/export/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create data export handler
 */
export declare function createDataExport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get export status handler
 */
export declare function getExportStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
