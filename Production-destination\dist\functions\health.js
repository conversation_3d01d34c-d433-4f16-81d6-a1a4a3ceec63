"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.healthCheck = healthCheck;
/**
 * Health check API for hepz backend services
 * Provides health status for services and dependencies
 */
const functions_1 = require("@azure/functions");
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const cosmos_1 = require("@azure/cosmos");
const storage_blob_1 = require("@azure/storage-blob");
/**
 * Check database health
 */
async function checkDatabaseHealth() {
    try {
        const cosmosClient = new cosmos_1.CosmosClient({
            endpoint: process.env.COSMOS_DB_ENDPOINT || "",
            key: process.env.COSMOS_DB_KEY || ""
        });
        // Try to get database info with timeout
        const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Database health check timeout')), 5000));
        const healthCheckPromise = cosmosClient.database(process.env.COSMOS_DB_DATABASE || "").read();
        const { database } = await Promise.race([healthCheckPromise, timeoutPromise]);
        return {
            name: "database",
            status: "healthy",
            message: `Connected to ${database.id}`,
            critical: true,
            timestamp: new Date().toISOString()
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error(`Database health check failed: ${errorMessage}`);
        return {
            name: "database",
            status: "unhealthy",
            message: `Failed to connect: ${errorMessage}`,
            critical: true,
            timestamp: new Date().toISOString()
        };
    }
}
/**
 * Check storage health
 */
async function checkStorageHealth() {
    try {
        const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
        if (!connectionString) {
            return {
                name: "storage",
                status: "disabled",
                message: "Storage connection string not configured",
                critical: true,
                timestamp: new Date().toISOString()
            };
        }
        const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(connectionString);
        // Try to list containers with timeout
        const timeoutPromise = new Promise((_, reject) => setTimeout(() => reject(new Error('Storage health check timeout')), 5000));
        const listContainersPromise = (async () => {
            const containerIterator = blobServiceClient.listContainers();
            const containerList = [];
            for await (const container of containerIterator) {
                containerList.push(container.name);
                if (containerList.length >= 5)
                    break; // Limit to first 5 containers
            }
            return containerList;
        })();
        const containers = await Promise.race([listContainersPromise, timeoutPromise]);
        return {
            name: "storage",
            status: "healthy",
            message: `Connected to storage with ${containers.length} containers`,
            critical: true,
            timestamp: new Date().toISOString()
        };
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error(`Storage health check failed: ${errorMessage}`);
        return {
            name: "storage",
            status: "unhealthy",
            message: `Failed to connect: ${errorMessage}`,
            critical: true,
            timestamp: new Date().toISOString()
        };
    }
}
/**
 * Health check HTTP trigger
 */
async function healthCheck(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    logger_1.logger.info("Health check requested", {
        correlationId: context.invocationId,
        query: Object.fromEntries(request.query.entries())
    });
    try {
        // Initialize health status
        const health = {
            status: "healthy",
            timestamp: new Date().toISOString(),
            version: process.env.VERSION || "1.0.0",
            environment: process.env.NODE_ENV || "development",
            services: []
        };
        // Check database connectivity
        const dbStatus = await checkDatabaseHealth();
        health.services.push(dbStatus);
        // Check blob storage connectivity
        const storageStatus = await checkStorageHealth();
        health.services.push(storageStatus);
        // Determine overall health status
        const unhealthyServices = health.services.filter(service => service.status === "unhealthy");
        if (unhealthyServices.length > 0) {
            health.status = "degraded";
            // If critical services are down, mark as unhealthy
            const criticalUnhealthyServices = unhealthyServices.filter(service => service.critical === true);
            if (criticalUnhealthyServices.length > 0) {
                health.status = "unhealthy";
            }
        }
        logger_1.logger.info(`Health check completed with status: ${health.status}`, {
            correlationId: context.invocationId
        });
        return (0, cors_1.addCorsHeaders)({
            status: health.status === "unhealthy" ? 503 : 200,
            headers: {
                "Content-Type": "application/json"
            },
            jsonBody: health
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error(`Health check failed: ${errorMessage}`, {
            correlationId: context.invocationId,
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: {
                "Content-Type": "application/json"
            },
            jsonBody: {
                status: "unhealthy",
                services: [],
                timestamp: new Date().toISOString(),
                error: errorMessage
            }
        }, request);
    }
}
functions_1.app.http('health', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'anonymous',
    route: 'health',
    handler: healthCheck
});
//# sourceMappingURL=health.js.map