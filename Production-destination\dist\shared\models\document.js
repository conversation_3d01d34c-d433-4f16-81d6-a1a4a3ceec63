"use strict";
/**
 * Document Models and Interfaces
 * Defines the structure for document-related data models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.SharePermission = exports.DocumentStatus = void 0;
var DocumentStatus;
(function (DocumentStatus) {
    DocumentStatus["UPLOADING"] = "UPLOADING";
    DocumentStatus["PROCESSING"] = "PROCESSING";
    DocumentStatus["PROCESSED"] = "PROCESSED";
    DocumentStatus["READY"] = "READY";
    DocumentStatus["ERROR"] = "ERROR";
    DocumentStatus["ARCHIVED"] = "ARCHIVED";
    DocumentStatus["DELETED"] = "DELETED";
    DocumentStatus["ENHANCED"] = "ENHANCED";
    DocumentStatus["SIGNED"] = "SIGNED";
})(DocumentStatus || (exports.DocumentStatus = DocumentStatus = {}));
var SharePermission;
(function (SharePermission) {
    SharePermission["VIEW"] = "VIEW";
    SharePermission["COMMENT"] = "COMMENT";
    SharePermission["EDIT"] = "EDIT";
    SharePermission["DOWNLOAD"] = "DOWNLOAD";
    SharePermission["SHARE"] = "SHARE";
    SharePermission["DELETE"] = "DELETE";
})(SharePermission || (exports.SharePermission = SharePermission = {}));
//# sourceMappingURL=document.js.map