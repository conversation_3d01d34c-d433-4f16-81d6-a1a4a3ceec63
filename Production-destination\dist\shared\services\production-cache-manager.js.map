{"version": 3, "file": "production-cache-manager.js", "sourceRoot": "", "sources": ["../../../src/shared/services/production-cache-manager.ts"], "names": [], "mappings": ";AAAA;;;;GAIG;;;AAEH,mCAAgC;AAChC,4CAAyC;AAezC,MAAa,sBAAsB;IAMjC;QAJQ,gBAAW,GAA0B,IAAI,GAAG,EAAE,CAAC;QAC/C,cAAS,GAA6B,IAAI,GAAG,EAAE,CAAC;QAChD,sBAAiB,GAAuC,IAAI,GAAG,EAAE,CAAC;QAGxE,IAAI,CAAC,iBAAiB,EAAE,CAAC;IAC3B,CAAC;IAEM,MAAM,CAAC,WAAW;QACvB,IAAI,CAAC,sBAAsB,CAAC,QAAQ,EAAE,CAAC;YACrC,sBAAsB,CAAC,QAAQ,GAAG,IAAI,sBAAsB,EAAE,CAAC;QACjE,CAAC;QACD,OAAO,sBAAsB,CAAC,QAAQ,CAAC;IACzC,CAAC;IAED;;OAEG;IACK,iBAAiB;QACvB,sCAAsC;QACtC,IAAI,CAAC,mBAAmB,CAAC,iBAAiB,EAAE;YAC1C,OAAO,EAAE,YAAY;YACrB,IAAI,EAAE,CAAC,UAAU,EAAE,SAAS,EAAE,eAAe,CAAC;YAC9C,WAAW,EAAE,CAAC,WAAW,EAAE,aAAa,CAAC;SAC1C,CAAC,CAAC;QAEH,kCAAkC;QAClC,IAAI,CAAC,mBAAmB,CAAC,aAAa,EAAE;YACtC,OAAO,EAAE,QAAQ;YACjB,IAAI,EAAE,CAAC,MAAM,EAAE,UAAU,EAAE,SAAS,CAAC;YACrC,WAAW,EAAE,CAAC,UAAU,EAAE,uBAAuB,CAAC;SACnD,CAAC,CAAC;QAEH,qCAAqC;QACrC,IAAI,CAAC,mBAAmB,CAAC,gBAAgB,EAAE;YACzC,OAAO,EAAE,WAAW;YACpB,IAAI,EAAE,CAAC,SAAS,EAAE,eAAe,CAAC;YAClC,WAAW,EAAE,CAAC,oBAAoB,CAAC;SACpC,CAAC,CAAC;QAEH,mCAAmC;QACnC,IAAI,CAAC,mBAAmB,CAAC,eAAe,EAAE;YACxC,OAAO,EAAE,UAAU;YACnB,IAAI,EAAE,CAAC,eAAe,EAAE,QAAQ,CAAC;YACjC,WAAW,EAAE,CAAC,gBAAgB,CAAC;SAChC,CAAC,CAAC;IACL,CAAC;IAED;;OAEG;IACI,mBAAmB,CAAC,MAAc,EAAE,IAA2B;QACpE,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,EAAE,IAAI,CAAC,CAAC;QACzC,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,MAAM,EAAE,IAAI,EAAE,CAAC,CAAC;IAClE,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,WAAW,CAAC,GAAW,EAAE,IAAc,EAAE,GAAY;QAChE,MAAM,QAAQ,GAAa;YACzB,GAAG;YACH,IAAI;YACJ,GAAG;YACH,SAAS,EAAE,IAAI,IAAI,EAAE;SACtB,CAAC;QAEF,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,EAAE,QAAQ,CAAC,CAAC;QAEpC,6BAA6B;QAC7B,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,IAAI,CAAC,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC;gBAC7B,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,EAAE,IAAI,GAAG,EAAE,CAAC,CAAC;YACrC,CAAC;YACD,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QACpC,CAAC;QAED,eAAM,CAAC,KAAK,CAAC,sBAAsB,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IAC3D,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,GAAG,CAAC,GAAW,EAAE,KAAU,EAAE,MAAc,IAAI,EAAE,OAAiB,EAAE;QAC/E,IAAI,CAAC;YACH,gCAAgC;YAChC,MAAM,IAAI,CAAC,WAAW,CAAC,GAAG,EAAE,IAAI,EAAE,GAAG,CAAC,CAAC;YAEvC,yBAAyB;YACzB,MAAM,OAAO,GAAG,MAAM,aAAK,CAAC,OAAO,CAAC,GAAG,EAAE,KAAK,EAAE,GAAG,CAAC,CAAC;YAErD,IAAI,OAAO,EAAE,CAAC;gBACZ,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE,EAAE,GAAG,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;YACrE,CAAC;YAED,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,GAAG;aACJ,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,GAAG,CAAI,GAAW;QAC7B,IAAI,CAAC;YACH,OAAO,MAAM,aAAK,CAAC,OAAO,CAAI,GAAG,CAAC,CAAC;QACrC,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,EAAE;gBACxC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,GAAG;aACJ,CAAC,CAAC;YACH,OAAO,IAAI,CAAC;QACd,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,MAAM,CAAC,GAAW;QAC7B,IAAI,CAAC;YACH,oBAAoB;YACpB,MAAM,OAAO,GAAG,MAAM,aAAK,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;YAExC,2BAA2B;YAC3B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAExB,OAAO,OAAO,CAAC;QACjB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;gBACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,GAAG;aACJ,CAAC,CAAC;YACH,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,IAAc;QAC1C,IAAI,CAAC;YACH,MAAM,YAAY,GAAG,IAAI,GAAG,EAAU,CAAC;YAEvC,+CAA+C;YAC/C,KAAK,MAAM,GAAG,IAAI,IAAI,EAAE,CAAC;gBACvB,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxC,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,EAAE,CAAC,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,CAAC;gBAChD,CAAC;YACH,CAAC;YAED,kBAAkB;YAClB,MAAM,gBAAgB,GAAG,KAAK,CAAC,IAAI,CAAC,YAAY,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YAC/E,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAEpC,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE;gBACvC,IAAI;gBACJ,WAAW,EAAE,YAAY,CAAC,IAAI;aAC/B,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACjD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,IAAI;aACL,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,gBAAgB,CAAC,MAAc,EAAE,UAA+B,EAAE;QAC7E,IAAI,CAAC;YACH,MAAM,IAAI,GAAG,IAAI,CAAC,iBAAiB,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;YAChD,IAAI,CAAC,IAAI,EAAE,CAAC;gBACV,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE,EAAE,MAAM,EAAE,CAAC,CAAC;gBAC7D,OAAO;YACT,CAAC;YAED,qBAAqB;YACrB,MAAM,IAAI,CAAC,gBAAgB,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAEvC,gDAAgD;YAChD,KAAK,MAAM,cAAc,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC9C,MAAM,IAAI,CAAC,qBAAqB,CAAC,cAAc,EAAE,OAAO,CAAC,CAAC;YAC5D,CAAC;YAED,eAAM,CAAC,IAAI,CAAC,2BAA2B,EAAE,EAAE,MAAM,EAAE,OAAO,EAAE,CAAC,CAAC;QAChE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;gBACjD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,MAAM;gBACN,OAAO;aACR,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,KAAK,CAAC,qBAAqB,CAAC,OAAe,EAAE,OAA4B;QAC/E,IAAI,CAAC;YACH,6DAA6D;YAC7D,MAAM,YAAY,GAAa,EAAE,CAAC;YAElC,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC/C,IAAI,IAAI,CAAC,cAAc,CAAC,GAAG,EAAE,OAAO,EAAE,OAAO,CAAC,EAAE,CAAC;oBAC/C,YAAY,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;gBACzB,CAAC;YACH,CAAC;YAED,2BAA2B;YAC3B,MAAM,gBAAgB,GAAG,YAAY,CAAC,GAAG,CAAC,GAAG,CAAC,EAAE,CAAC,IAAI,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACnE,MAAM,OAAO,CAAC,GAAG,CAAC,gBAAgB,CAAC,CAAC;YAEpC,eAAM,CAAC,KAAK,CAAC,0BAA0B,EAAE;gBACvC,OAAO;gBACP,OAAO;gBACP,WAAW,EAAE,YAAY,CAAC,MAAM;aACjC,CAAC,CAAC;QACL,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;gBAChD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;gBAC7D,OAAO;gBACP,OAAO;aACR,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACK,cAAc,CAAC,GAAW,EAAE,OAAe,EAAE,OAA4B;QAC/E,IAAI,CAAC;YACH,oDAAoD;YACpD,IAAI,gBAAgB,GAAG,OAAO,CAAC;YAE/B,uCAAuC;YACvC,KAAK,MAAM,CAAC,UAAU,EAAE,YAAY,CAAC,IAAI,MAAM,CAAC,OAAO,CAAC,OAAO,CAAC,EAAE,CAAC;gBACjE,gBAAgB,GAAG,gBAAgB,CAAC,OAAO,CAAC,IAAI,UAAU,GAAG,EAAE,MAAM,CAAC,YAAY,CAAC,CAAC,CAAC;YACvF,CAAC;YAED,2BAA2B;YAC3B,MAAM,YAAY,GAAG,gBAAgB;iBAClC,OAAO,CAAC,KAAK,EAAE,IAAI,CAAC;iBACpB,OAAO,CAAC,KAAK,EAAE,GAAG,CAAC,CAAC;YAEvB,MAAM,KAAK,GAAG,IAAI,MAAM,CAAC,IAAI,YAAY,GAAG,CAAC,CAAC;YAC9C,OAAO,KAAK,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;QACzB,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,GAAG,EAAE,OAAO,EAAE,OAAO,EAAE,KAAK,EAAE,CAAC,CAAC;YAC1E,OAAO,KAAK,CAAC;QACf,CAAC;IACH,CAAC;IAED;;OAEG;IACK,aAAa,CAAC,GAAW;QAC/B,MAAM,QAAQ,GAAG,IAAI,CAAC,WAAW,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;QAC3C,IAAI,QAAQ,EAAE,CAAC;YACb,2BAA2B;YAC3B,KAAK,MAAM,GAAG,IAAI,QAAQ,CAAC,IAAI,EAAE,CAAC;gBAChC,MAAM,OAAO,GAAG,IAAI,CAAC,SAAS,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC;gBACxC,IAAI,OAAO,EAAE,CAAC;oBACZ,OAAO,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBACpB,IAAI,OAAO,CAAC,IAAI,KAAK,CAAC,EAAE,CAAC;wBACvB,IAAI,CAAC,SAAS,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;oBAC7B,CAAC;gBACH,CAAC;YACH,CAAC;YAED,uBAAuB;YACvB,IAAI,CAAC,WAAW,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC;QAC/B,CAAC;IACH,CAAC;IAED;;OAEG;IACI,KAAK,CAAC,kBAAkB;QAC7B,IAAI,CAAC;YACH,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC;YACvB,MAAM,WAAW,GAAa,EAAE,CAAC;YAEjC,KAAK,MAAM,CAAC,GAAG,EAAE,QAAQ,CAAC,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;gBAC/C,IAAI,QAAQ,CAAC,GAAG,EAAE,CAAC;oBACjB,MAAM,UAAU,GAAG,IAAI,IAAI,CAAC,QAAQ,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,QAAQ,CAAC,GAAG,GAAG,IAAI,CAAC,CAAC,CAAC;oBAClF,IAAI,GAAG,GAAG,UAAU,EAAE,CAAC;wBACrB,WAAW,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;oBACxB,CAAC;gBACH,CAAC;YACH,CAAC;YAED,oCAAoC;YACpC,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;gBAC9B,IAAI,CAAC,aAAa,CAAC,GAAG,CAAC,CAAC;YAC1B,CAAC;YAED,eAAM,CAAC,KAAK,CAAC,yBAAyB,EAAE,EAAE,KAAK,EAAE,WAAW,CAAC,MAAM,EAAE,CAAC,CAAC;QACzE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;gBAC7C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;aAC9D,CAAC,CAAC;QACL,CAAC;IACH,CAAC;IAED;;OAEG;IACI,aAAa;QAMlB,MAAM,SAAS,GAA2B,EAAE,CAAC;QAE7C,KAAK,MAAM,CAAC,GAAG,EAAE,IAAI,CAAC,IAAI,IAAI,CAAC,SAAS,EAAE,CAAC;YACzC,SAAS,CAAC,GAAG,CAAC,GAAG,IAAI,CAAC,IAAI,CAAC;QAC7B,CAAC;QAED,OAAO;YACL,SAAS,EAAE,IAAI,CAAC,WAAW,CAAC,IAAI;YAChC,SAAS,EAAE,IAAI,CAAC,SAAS,CAAC,IAAI;YAC9B,UAAU,EAAE,IAAI,CAAC,iBAAiB,CAAC,IAAI;YACvC,SAAS;SACV,CAAC;IACJ,CAAC;CACF;AA/UD,wDA+UC;AAED,4BAA4B;AACf,QAAA,sBAAsB,GAAG,sBAAsB,CAAC,WAAW,EAAE,CAAC;AAC3E,kBAAe,8BAAsB,CAAC"}