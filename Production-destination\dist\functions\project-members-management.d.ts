/**
 * Project Members Management Function
 * Handles project member management, role assignments, and permissions
 * Migrated from old-arch/src/project-service/members/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get project members handler
 */
export declare function getProjectMembers(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Add project member handler
 */
export declare function addProjectMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update project member handler
 */
export declare function updateProjectMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
