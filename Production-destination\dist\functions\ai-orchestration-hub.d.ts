/**
 * AI Orchestration Hub Function
 * Central hub for coordinating AI operations across different services
 * Migrated from old-arch/src/ai-service/orchestration-hub/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create AI operation handler
 */
export declare function createAIOperation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get AI operation status handler
 */
export declare function getAIOperationStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
