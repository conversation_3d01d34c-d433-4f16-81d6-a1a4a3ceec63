/**
 * System Monitoring Function
 * Handles system health monitoring, performance metrics, and alerting
 * Enhanced from existing health.ts with comprehensive monitoring capabilities
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get system health handler
 */
export declare function getSystemHealth(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get performance metrics handler
 */
export declare function getPerformanceMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
