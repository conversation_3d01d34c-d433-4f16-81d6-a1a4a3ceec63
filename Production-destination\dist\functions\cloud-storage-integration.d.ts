/**
 * Cloud Storage Integration Function
 * Handles integration with multiple cloud storage providers (AWS S3, Azure Blob, Google Cloud)
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Configure storage provider handler
 */
export declare function configureStorage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Sync document handler
 */
export declare function syncDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Bulk sync documents handler
 */
export declare function bulkSyncDocuments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
