"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.advancedSearch = advancedSearch;
/**
 * Advanced Search Function
 * Handles advanced search capabilities with filtering, faceting, and analytics
 * Migrated from old-arch/src/search-service/advanced/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Search types and interfaces
var SearchScope;
(function (SearchScope) {
    SearchScope["ALL"] = "all";
    SearchScope["DOCUMENTS"] = "documents";
    SearchScope["WORKFLOWS"] = "workflows";
    SearchScope["PROJECTS"] = "projects";
    SearchScope["USERS"] = "users";
    SearchScope["ORGANIZATIONS"] = "organizations";
})(SearchScope || (SearchScope = {}));
var SortField;
(function (SortField) {
    SortField["RELEVANCE"] = "relevance";
    SortField["DATE"] = "date";
    SortField["NAME"] = "name";
    SortField["SIZE"] = "size";
    SortField["TYPE"] = "type";
})(SortField || (SortField = {}));
var SortDirection;
(function (SortDirection) {
    SortDirection["ASC"] = "asc";
    SortDirection["DESC"] = "desc";
})(SortDirection || (SortDirection = {}));
// Validation schema
const advancedSearchSchema = Joi.object({
    query: Joi.string().min(1).max(1000).required(),
    scope: Joi.string().valid(...Object.values(SearchScope)).default(SearchScope.ALL),
    filters: Joi.object({
        organizationId: Joi.string().uuid().optional(),
        projectId: Joi.string().uuid().optional(),
        contentType: Joi.string().optional(),
        createdBy: Joi.string().uuid().optional(),
        dateRange: Joi.object({
            start: Joi.string().isoDate().optional(),
            end: Joi.string().isoDate().optional()
        }).optional(),
        tags: Joi.array().items(Joi.string()).optional(),
        categories: Joi.array().items(Joi.string()).optional(),
        status: Joi.array().items(Joi.string()).optional(),
        sizeRange: Joi.object({
            min: Joi.number().min(0).optional(),
            max: Joi.number().min(0).optional()
        }).optional()
    }).optional(),
    sort: Joi.object({
        field: Joi.string().valid(...Object.values(SortField)).default(SortField.RELEVANCE),
        direction: Joi.string().valid(...Object.values(SortDirection)).default(SortDirection.DESC)
    }).optional(),
    pagination: Joi.object({
        page: Joi.number().min(1).default(1),
        limit: Joi.number().min(1).max(100).default(20)
    }).optional(),
    options: Joi.object({
        includeContent: Joi.boolean().default(false),
        highlightMatches: Joi.boolean().default(true),
        enableFacets: Joi.boolean().default(true),
        enableSuggestions: Joi.boolean().default(true),
        enableAnalytics: Joi.boolean().default(true)
    }).optional()
});
/**
 * Advanced search handler
 */
async function advancedSearch(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Advanced search started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse and validate request
        let searchRequest;
        if (request.method === 'GET') {
            // Handle GET request with query parameters
            const url = new URL(request.url);
            searchRequest = {
                query: url.searchParams.get('query') || '',
                scope: url.searchParams.get('scope') || SearchScope.ALL,
                filters: {},
                sort: {
                    field: url.searchParams.get('sortField') || SortField.RELEVANCE,
                    direction: url.searchParams.get('sortDirection') || SortDirection.DESC
                },
                pagination: {
                    page: parseInt(url.searchParams.get('page') || '1'),
                    limit: parseInt(url.searchParams.get('limit') || '20')
                },
                options: {
                    includeContent: url.searchParams.get('includeContent') === 'true',
                    highlightMatches: url.searchParams.get('highlightMatches') !== 'false',
                    enableFacets: url.searchParams.get('enableFacets') !== 'false',
                    enableSuggestions: url.searchParams.get('enableSuggestions') !== 'false',
                    enableAnalytics: url.searchParams.get('enableAnalytics') !== 'false'
                }
            };
        }
        else {
            // Handle POST request with JSON body
            const body = await request.json();
            const { error, value } = advancedSearchSchema.validate(body);
            if (error) {
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: {
                        error: 'Validation Error',
                        message: error.details.map(d => d.message).join(', ')
                    }
                }, request);
            }
            searchRequest = value;
        }
        if (!searchRequest.query.trim()) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Search query is required" }
            }, request);
        }
        const startTime = Date.now();
        // Perform advanced search
        const searchResults = await performAdvancedSearch(searchRequest, user);
        // Record search analytics if enabled
        if (searchRequest.options?.enableAnalytics) {
            await recordSearchAnalytics(searchRequest, user, searchResults);
        }
        const processingTime = Date.now() - startTime;
        const response = {
            ...searchResults,
            processingTime,
            success: true
        };
        logger_1.logger.info("Advanced search completed successfully", {
            correlationId,
            query: searchRequest.query,
            scope: searchRequest.scope,
            resultCount: searchResults.results.length,
            processingTime,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Advanced search failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Perform advanced search with filtering and faceting
 */
async function performAdvancedSearch(searchRequest, user) {
    const { query, scope, filters, sort, pagination, options } = searchRequest;
    const page = pagination?.page || 1;
    const limit = pagination?.limit || 20;
    // Get search containers based on scope
    const containers = getSearchContainers(scope);
    let allResults = [];
    // Search across relevant containers
    for (const containerName of containers) {
        try {
            const containerResults = await searchContainer(containerName, query, filters, user.tenantId);
            allResults = allResults.concat(containerResults.map(item => ({ ...item, _container: containerName })));
        }
        catch (error) {
            logger_1.logger.warn(`Failed to search in container ${containerName}`, { error });
        }
    }
    // Apply advanced filtering
    const filteredResults = applyAdvancedFilters(allResults, filters);
    // Apply sorting
    const sortedResults = applySorting(filteredResults, sort);
    // Apply pagination
    const offset = (page - 1) * limit;
    const paginatedResults = sortedResults.slice(offset, offset + limit);
    // Convert to search results format
    const searchResults = paginatedResults.map(item => ({
        id: item.id,
        type: item._container || item.type || 'unknown',
        title: item.name || item.title || 'Untitled',
        description: item.description || '',
        content: options?.includeContent ? (item.content || item.extractedText || '') : undefined,
        highlights: options?.highlightMatches ? generateHighlights(item, query) : undefined,
        score: calculateRelevanceScore(item, query),
        metadata: {
            createdAt: item.createdAt,
            createdBy: item.createdBy,
            organizationId: item.organizationId,
            projectId: item.projectId,
            tags: item.tags,
            categories: item.categories,
            size: item.size,
            contentType: item.contentType
        },
        url: generateItemUrl(item)
    }));
    // Generate facets if enabled
    const facets = options?.enableFacets ? generateFacets(filteredResults) : undefined;
    // Generate suggestions if enabled
    const suggestions = options?.enableSuggestions ? generateSuggestions(query, filteredResults) : undefined;
    // Generate analytics if enabled
    const analytics = options?.enableAnalytics ? {
        searchTime: Date.now(),
        totalIndexed: allResults.length,
        queryComplexity: determineQueryComplexity(query, filters)
    } : undefined;
    return {
        query,
        scope,
        results: searchResults,
        total: sortedResults.length,
        page,
        limit,
        hasMore: offset + limit < sortedResults.length,
        facets,
        suggestions,
        analytics
    };
}
/**
 * Get search containers based on scope
 */
function getSearchContainers(scope) {
    switch (scope) {
        case SearchScope.DOCUMENTS:
            return ['documents'];
        case SearchScope.WORKFLOWS:
            return ['workflows'];
        case SearchScope.PROJECTS:
            return ['projects'];
        case SearchScope.USERS:
            return ['users'];
        case SearchScope.ORGANIZATIONS:
            return ['organizations'];
        case SearchScope.ALL:
        default:
            return ['documents', 'workflows', 'projects', 'users', 'organizations'];
    }
}
/**
 * Search within a specific container
 */
async function searchContainer(containerName, query, filters, tenantId) {
    // Build search query
    let searchQuery = 'SELECT * FROM c WHERE c.tenantId = @tenantId';
    const parameters = [{ name: '@tenantId', value: tenantId }];
    // Add text search
    if (query.trim()) {
        searchQuery += ' AND (CONTAINS(LOWER(c.name), LOWER(@query)) OR CONTAINS(LOWER(c.description), LOWER(@query)) OR CONTAINS(LOWER(c.content), LOWER(@query)))';
        parameters.push({ name: '@query', value: query });
    }
    // Add basic filters
    if (filters?.organizationId) {
        searchQuery += ' AND c.organizationId = @organizationId';
        parameters.push({ name: '@organizationId', value: filters.organizationId });
    }
    if (filters?.projectId) {
        searchQuery += ' AND c.projectId = @projectId';
        parameters.push({ name: '@projectId', value: filters.projectId });
    }
    if (filters?.createdBy) {
        searchQuery += ' AND c.createdBy = @createdBy';
        parameters.push({ name: '@createdBy', value: filters.createdBy });
    }
    // Add date range filter
    if (filters?.dateRange?.start) {
        searchQuery += ' AND c.createdAt >= @startDate';
        parameters.push({ name: '@startDate', value: filters.dateRange.start });
    }
    if (filters?.dateRange?.end) {
        searchQuery += ' AND c.createdAt <= @endDate';
        parameters.push({ name: '@endDate', value: filters.dateRange.end });
    }
    try {
        const results = await database_1.db.queryItems(containerName, searchQuery, parameters);
        return results;
    }
    catch (error) {
        logger_1.logger.warn(`Failed to query container ${containerName}`, { error });
        return [];
    }
}
/**
 * Apply advanced filters to results
 */
function applyAdvancedFilters(results, filters) {
    let filteredResults = results;
    // Apply content type filter
    if (filters?.contentType) {
        filteredResults = filteredResults.filter(item => item.contentType && item.contentType.includes(filters.contentType));
    }
    // Apply tags filter
    if (filters?.tags && filters.tags.length > 0) {
        filteredResults = filteredResults.filter(item => item.tags && filters.tags.some((tag) => item.tags.includes(tag)));
    }
    // Apply categories filter
    if (filters?.categories && filters.categories.length > 0) {
        filteredResults = filteredResults.filter(item => item.categories && filters.categories.some((category) => item.categories.includes(category)));
    }
    // Apply status filter
    if (filters?.status && filters.status.length > 0) {
        filteredResults = filteredResults.filter(item => item.status && filters.status.includes(item.status));
    }
    // Apply size range filter
    if (filters?.sizeRange) {
        filteredResults = filteredResults.filter(item => {
            const size = item.size || 0;
            const min = filters.sizeRange.min || 0;
            const max = filters.sizeRange.max || Number.MAX_SAFE_INTEGER;
            return size >= min && size <= max;
        });
    }
    return filteredResults;
}
/**
 * Apply sorting to results
 */
function applySorting(results, sort) {
    if (!sort)
        return results;
    return results.sort((a, b) => {
        let comparison = 0;
        switch (sort.field) {
            case SortField.NAME:
                comparison = (a.name || '').localeCompare(b.name || '');
                break;
            case SortField.DATE:
                comparison = new Date(a.createdAt || 0).getTime() - new Date(b.createdAt || 0).getTime();
                break;
            case SortField.SIZE:
                comparison = (a.size || 0) - (b.size || 0);
                break;
            case SortField.TYPE:
                comparison = (a.contentType || '').localeCompare(b.contentType || '');
                break;
            case SortField.RELEVANCE:
            default:
                comparison = (b._score || 0) - (a._score || 0);
                break;
        }
        return sort.direction === SortDirection.ASC ? comparison : -comparison;
    });
}
/**
 * Calculate relevance score for search result
 */
function calculateRelevanceScore(item, query) {
    let score = 0.5; // Base score
    const text = `${item.name || ''} ${item.description || ''} ${item.content || ''}`.toLowerCase();
    const queryTerms = query.toLowerCase().split(/\s+/);
    for (const term of queryTerms) {
        if (text.includes(term)) {
            score += 0.2;
        }
    }
    // Boost for exact matches in title
    if ((item.name || '').toLowerCase().includes(query.toLowerCase())) {
        score += 0.3;
    }
    // Recency boost
    const daysSinceCreated = (Date.now() - new Date(item.createdAt).getTime()) / (1000 * 60 * 60 * 24);
    if (daysSinceCreated < 7) {
        score += 0.1;
    }
    return Math.min(score, 1.0);
}
/**
 * Generate highlights for search results
 */
function generateHighlights(item, query) {
    const highlights = [];
    const queryTerms = query.toLowerCase().split(/\s+/);
    const text = item.description || item.content || '';
    if (text) {
        for (const term of queryTerms) {
            const index = text.toLowerCase().indexOf(term);
            if (index !== -1) {
                const start = Math.max(0, index - 50);
                const end = Math.min(text.length, index + term.length + 50);
                const highlight = text.substring(start, end);
                highlights.push(`...${highlight}...`);
            }
        }
    }
    return highlights.slice(0, 3); // Limit to 3 highlights
}
/**
 * Generate facets for search results
 */
function generateFacets(results) {
    const facets = {};
    // Generate facets
    facets.type = {};
    facets.contentType = {};
    facets.status = {};
    facets.createdBy = {};
    for (const item of results) {
        // Type facet
        const type = item._container || item.type || 'unknown';
        facets.type[type] = (facets.type[type] || 0) + 1;
        // Content type facet
        if (item.contentType) {
            facets.contentType[item.contentType] = (facets.contentType[item.contentType] || 0) + 1;
        }
        // Status facet
        if (item.status) {
            facets.status[item.status] = (facets.status[item.status] || 0) + 1;
        }
        // Author facet
        if (item.createdBy) {
            facets.createdBy[item.createdBy] = (facets.createdBy[item.createdBy] || 0) + 1;
        }
    }
    // Convert to required format
    const formattedFacets = {};
    for (const [facetName, facetValues] of Object.entries(facets)) {
        formattedFacets[facetName] = Object.entries(facetValues)
            .map(([value, count]) => ({ value, count }))
            .sort((a, b) => b.count - a.count)
            .slice(0, 10); // Limit to top 10
    }
    return formattedFacets;
}
/**
 * Generate search suggestions
 */
function generateSuggestions(query, results) {
    // Simplified implementation
    const suggestions = [];
    // Extract common terms from results
    const terms = new Set();
    for (const item of results.slice(0, 10)) {
        const text = `${item.name || ''} ${item.description || ''}`.toLowerCase();
        const words = text.split(/\s+/).filter(word => word.length > 3);
        words.forEach(word => terms.add(word));
    }
    // Generate suggestions based on query
    const queryWords = query.toLowerCase().split(/\s+/);
    for (const term of Array.from(terms).slice(0, 5)) {
        if (!queryWords.includes(term)) {
            suggestions.push(`${query} ${term}`);
        }
    }
    return suggestions.slice(0, 3);
}
/**
 * Determine query complexity
 */
function determineQueryComplexity(query, filters) {
    let complexity = 'simple';
    if (query.split(/\s+/).length > 3)
        complexity = 'medium';
    if (filters && Object.keys(filters).length > 2)
        complexity = 'complex';
    if (query.includes('"') || query.includes('AND') || query.includes('OR'))
        complexity = 'complex';
    return complexity;
}
/**
 * Generate URL for search result item
 */
function generateItemUrl(item) {
    const baseUrl = process.env.FRONTEND_BASE_URL || 'https://app.docucontext.com';
    const type = item._container || item.type;
    switch (type) {
        case 'documents':
            return `${baseUrl}/documents/${item.id}`;
        case 'workflows':
            return `${baseUrl}/workflows/${item.id}`;
        case 'projects':
            return `${baseUrl}/projects/${item.id}`;
        case 'users':
            return `${baseUrl}/users/${item.id}`;
        case 'organizations':
            return `${baseUrl}/organizations/${item.id}`;
        default:
            return `${baseUrl}/search?id=${item.id}`;
    }
}
/**
 * Record search analytics
 */
async function recordSearchAnalytics(searchRequest, user, searchResults) {
    try {
        const analytics = {
            id: (0, uuid_1.v4)(),
            userId: user.id,
            query: searchRequest.query,
            scope: searchRequest.scope,
            filters: searchRequest.filters,
            resultCount: searchResults.results.length,
            timestamp: new Date().toISOString(),
            organizationId: user.tenantId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('search-analytics', analytics);
    }
    catch (error) {
        logger_1.logger.warn('Failed to record search analytics', { error });
    }
}
// Register functions
functions_1.app.http('search-advanced', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'search/advanced',
    handler: advancedSearch
});
//# sourceMappingURL=search-advanced.js.map