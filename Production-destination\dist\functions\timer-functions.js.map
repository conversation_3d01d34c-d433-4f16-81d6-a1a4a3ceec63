{"version": 3, "file": "timer-functions.js", "sourceRoot": "", "sources": ["../../src/functions/timer-functions.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,gDAAiE;AACjE,sDAAwD;AACxD,mDAAgD;AAChD,0DAAiD;AACjD,+DAAgE;AAEhE;;;GAGG;AACH,KAAK,UAAU,iBAAiB,CAAC,OAAc,EAAE,OAA0B;IACzE,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;QAC3C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,cAAc,GAAG;YACrB,gBAAgB,EAAE,CAAC;YACnB,OAAO,EAAE,CAAC;YACV,SAAS,EAAE,CAAC;YACZ,aAAa,EAAE,CAAC;SACjB,CAAC;QAEF,sDAAsD;QACtD,MAAM,WAAW,GAAG,IAAI,IAAI,EAAE,CAAC;QAC/B,WAAW,CAAC,OAAO,CAAC,WAAW,CAAC,OAAO,EAAE,GAAG,GAAG,CAAC,CAAC;QAEjD,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EACtD,iFAAiF,EACjF,CAAC,WAAW,CAAC,WAAW,EAAE,EAAE,SAAS,CAAC,CACvC,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,WAAW,EAAE,CAAC;YAC9B,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE;gBAC/B,GAAG,GAAG;gBACN,MAAM,EAAE,SAAS;gBACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YACH,cAAc,CAAC,gBAAgB,EAAE,CAAC;QACpC,CAAC;QAED,kDAAkD;QAClD,MAAM,UAAU,GAAG,IAAI,IAAI,EAAE,CAAC;QAC9B,UAAU,CAAC,OAAO,CAAC,UAAU,CAAC,OAAO,EAAE,GAAG,EAAE,CAAC,CAAC;QAE9C,MAAM,OAAO,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,YAAY,EACnD,8CAA8C,EAC9C,CAAC,UAAU,CAAC,WAAW,EAAE,CAAC,CAC3B,CAAC;QAEF,KAAK,MAAM,GAAG,IAAI,OAAO,EAAE,CAAC;YAC1B,6CAA6C;YAC7C,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE,GAAG,CAAC,EAAE,EAAE,GAAG,CAAC,EAAE,CAAC,CAAC;YAClD,cAAc,CAAC,OAAO,EAAE,CAAC;QAC3B,CAAC;QAED,8CAA8C;QAC9C,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QAEF,MAAM,aAAa,GAAG,iBAAiB,CAAC,kBAAkB,CAAC,MAAM,CAAC,CAAC;QACnE,MAAM,SAAS,GAAG,aAAa,CAAC,aAAa,EAAE,CAAC;QAEhD,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,SAAS,EAAE,CAAC;YACnC,MAAM,UAAU,GAAG,aAAa,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;YAC1D,MAAM,UAAU,GAAG,MAAM,UAAU,CAAC,aAAa,EAAE,CAAC;YAEpD,wCAAwC;YACxC,IAAI,UAAU,CAAC,YAAY,EAAE,CAAC;gBAC5B,MAAM,UAAU,GAAG,CAAC,IAAI,CAAC,GAAG,EAAE,GAAG,UAAU,CAAC,YAAY,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,IAAI,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;gBACvF,IAAI,UAAU,GAAG,EAAE,EAAE,CAAC;oBACpB,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;oBAC1B,cAAc,CAAC,SAAS,EAAE,CAAC;gBAC7B,CAAC;YACH,CAAC;QACH,CAAC;QAED,kFAAkF;QAClF,MAAM,kBAAkB,GAAG,iBAAiB,CAAC,kBAAkB,CAC7D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QAEF,MAAM,aAAa,GAAG,kBAAkB,CAAC,aAAa,EAAE,CAAC;QACzD,IAAI,KAAK,EAAE,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACvC,MAAM,IAAI,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EAC/C,8CAA8C,EAC9C,CAAC,IAAI,CAAC,IAAI,CAAC,CACZ,CAAC;YAEF,IAAI,IAAI,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;gBACtB,4BAA4B;gBAC5B,MAAM,UAAU,GAAG,kBAAkB,CAAC,aAAa,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC;gBAC/D,MAAM,UAAU,CAAC,MAAM,EAAE,CAAC;gBAC1B,cAAc,CAAC,aAAa,EAAE,CAAC;YACjC,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE,cAAc,CAAC,CAAC;QAEvD,wBAAwB;QACxB,MAAM,IAAA,kCAAY,EAChB,+BAAS,CAAC,mBAAmB,EAC7B,sBAAsB,EACtB;YACE,GAAG,cAAc;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,WAAW;SACpB,CACF,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,4BAA4B,EAAE;YACzC,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QAEH,MAAM,IAAA,kCAAY,EAChB,+BAAS,CAAC,mBAAmB,EAC7B,sBAAsB,EACtB;YACE,MAAM,EAAE,QAAQ;YAChB,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAC7D,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CACF,CAAC;IACJ,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,sBAAsB,CAAC,OAAc,EAAE,QAA2B;IAC/E,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;QACjD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,aAAa,GAAG;YACpB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,EAAE;YAChD,OAAO,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,EAAE;YAC/C,KAAK,EAAE,EAAE,MAAM,EAAE,SAAS,EAAE,YAAY,EAAE,CAAC,EAAE;YAC7C,aAAa,EAAE,CAAC;YAChB,eAAe,EAAE,CAAC;YAClB,UAAU,EAAE,CAAC;SACd,CAAC;QAEF,wBAAwB;QACxB,MAAM,OAAO,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC3B,IAAI,CAAC;YACH,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EAClC,8BAA8B,EAC9B,EAAE,CACH,CAAC;YACF,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,SAAS,CAAC;YAC1C,aAAa,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;QAC7D,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAa,CAAC,QAAQ,CAAC,MAAM,GAAG,WAAW,CAAC;YAC5C,aAAa,CAAC,QAAQ,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,OAAO,CAAC;QAC7D,CAAC;QAED,uBAAuB;QACvB,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAChC,IAAI,CAAC;YACH,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;YACF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;YACF,MAAM,eAAe,CAAC,MAAM,EAAE,CAAC;YAC/B,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,SAAS,CAAC;YACzC,aAAa,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC;QACjE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,aAAa,CAAC,OAAO,CAAC,MAAM,GAAG,WAAW,CAAC;YAC3C,aAAa,CAAC,OAAO,CAAC,YAAY,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,YAAY,CAAC;QACjE,CAAC;QAED,qBAAqB;QACrB,IAAI,CAAC;YACH,MAAM,cAAc,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EACzD,+DAA+D,EAC/D,CAAC,SAAS,CAAC,CACZ,CAAC;YACF,aAAa,CAAC,aAAa,GAAI,cAAc,CAAC,CAAC,CAAY,IAAI,CAAC,CAAC;QACnE,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAC1D,CAAC;QAED,6BAA6B;QAC7B,IAAI,CAAC;YACH,MAAM,mBAAmB,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EAC9D,6DAA6D,EAC7D,CAAC,QAAQ,CAAC,CACX,CAAC;YACF,aAAa,CAAC,eAAe,GAAI,mBAAmB,CAAC,CAAC,CAAY,IAAI,CAAC,CAAC;QAC1E,CAAC;QAAC,OAAO,KAAK,EAAE,CAAC;YACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,CAAC,CAAC;QAClE,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,wBAAwB,EAAE,aAAa,CAAC,CAAC;QAErD,uBAAuB;QACvB,MAAM,aAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE;YACpC,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;YAC1B,GAAG,aAAa;YAChB,IAAI,EAAE,cAAc;SACrB,CAAC,CAAC;QAEH,6BAA6B;QAC7B,MAAM,IAAA,kCAAY,EAChB,+BAAS,CAAC,mBAAmB,EAC7B,sBAAsB,EACtB,aAAa,CACd,CAAC;QAEF,mBAAmB;QACnB,IAAI,aAAa,CAAC,QAAQ,CAAC,MAAM,KAAK,WAAW;YAC7C,aAAa,CAAC,OAAO,CAAC,MAAM,KAAK,WAAW,EAAE,CAAC;YACjD,MAAM,IAAA,kCAAY,EAChB,+BAAS,CAAC,iBAAiB,EAC3B,qBAAqB,EACrB;gBACE,SAAS,EAAE,mBAAmB;gBAC9B,QAAQ,EAAE,MAAM;gBAChB,OAAO,EAAE,aAAa;gBACtB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CACF,CAAC;QACJ,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,kCAAkC,EAAE;YAC/C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED;;;GAGG;AACH,KAAK,UAAU,oBAAoB,CAAC,OAAc,EAAE,QAA2B;IAC7E,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE;QAC9C,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;QACnC,SAAS,EAAE,OAAO,CAAC,SAAS;KAC7B,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,OAAO,GAAG,IAAI,IAAI,EAAE,CAAC;QAC3B,MAAM,SAAS,GAAG,IAAI,IAAI,EAAE,CAAC;QAC7B,SAAS,CAAC,OAAO,CAAC,SAAS,CAAC,OAAO,EAAE,GAAG,CAAC,CAAC,CAAC;QAE3C,MAAM,SAAS,GAAG;YAChB,MAAM,EAAE;gBACN,KAAK,EAAE,SAAS,CAAC,WAAW,EAAE;gBAC9B,GAAG,EAAE,OAAO,CAAC,WAAW,EAAE;aAC3B;YACD,SAAS,EAAE;gBACT,QAAQ,EAAE,CAAC;gBACX,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,CAAC;gBACT,SAAS,EAAE,CAAC;aACb;YACD,SAAS,EAAE;gBACT,OAAO,EAAE,CAAC;gBACV,SAAS,EAAE,CAAC;gBACZ,MAAM,EAAE,CAAC;aACV;YACD,KAAK,EAAE;gBACL,MAAM,EAAE,CAAC;gBACT,gBAAgB,EAAE,CAAC;aACpB;YACD,WAAW,EAAE;gBACX,iBAAiB,EAAE,CAAC;gBACpB,SAAS,EAAE,CAAC;aACb;SACF,CAAC;QAEF,yBAAyB;QACzB,MAAM,aAAa,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EAAE;;;;;;OAMzD,EACD,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CACjD,CAAC;QAEF,IAAI,aAAa,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC7B,MAAM,KAAK,GAAG,aAAa,CAAC,CAAC,CAAQ,CAAC;YACtC,SAAS,CAAC,SAAS,CAAC,QAAQ,GAAG,KAAK,CAAC,KAAK,IAAI,CAAC,CAAC;YAChD,SAAS,CAAC,SAAS,CAAC,SAAS,GAAG,KAAK,CAAC,SAAS,IAAI,CAAC,CAAC;QACvD,CAAC;QAED,yBAAyB;QACzB,MAAM,aAAa,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EAAE;;;;;;;OAOzD,EACD,CAAC,SAAS,CAAC,WAAW,EAAE,EAAE,OAAO,CAAC,WAAW,EAAE,CAAC,CACjD,CAAC;QAEF,KAAK,MAAM,IAAI,IAAI,aAAa,EAAE,CAAC;YACjC,MAAM,YAAY,GAAG,IAAW,CAAC;YACjC,QAAQ,YAAY,CAAC,MAAM,EAAE,CAAC;gBAC5B,KAAK,QAAQ,CAAC;gBACd,KAAK,SAAS;oBACZ,SAAS,CAAC,SAAS,CAAC,OAAO,IAAI,YAAY,CAAC,KAAK,CAAC;oBAClD,MAAM;gBACR,KAAK,WAAW;oBACd,SAAS,CAAC,SAAS,CAAC,SAAS,IAAI,YAAY,CAAC,KAAK,CAAC;oBACpD,MAAM;gBACR,KAAK,QAAQ;oBACX,SAAS,CAAC,SAAS,CAAC,MAAM,IAAI,YAAY,CAAC,KAAK,CAAC;oBACjD,MAAM;YACV,CAAC;QACH,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,4BAA4B,EAAE,SAAS,CAAC,CAAC;QAErD,kBAAkB;QAClB,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE;YAC/B,EAAE,EAAE,UAAU,SAAS,CAAC,OAAO,EAAE,EAAE;YACnC,IAAI,EAAE,QAAQ;YACd,GAAG,SAAS;YACZ,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACtC,CAAC,CAAC;QAEH,0BAA0B;QAC1B,MAAM,IAAA,kCAAY,EAChB,+BAAS,CAAC,mBAAmB,EAC7B,yBAAyB,EACzB,SAAS,CACV,CAAC;IAEJ,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC5C,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;IACL,CAAC;AACH,CAAC;AAED,2BAA2B;AAC3B,eAAG,CAAC,KAAK,CAAC,mBAAmB,EAAE;IAC7B,QAAQ,EAAE,aAAa,EAAE,wBAAwB;IACjD,OAAO,EAAE,iBAAiB;CAC3B,CAAC,CAAC;AAEH,eAAG,CAAC,KAAK,CAAC,wBAAwB,EAAE;IAClC,QAAQ,EAAE,aAAa,EAAE,aAAa;IACtC,OAAO,EAAE,sBAAsB;CAChC,CAAC,CAAC;AAEH,eAAG,CAAC,KAAK,CAAC,sBAAsB,EAAE;IAChC,QAAQ,EAAE,aAAa,EAAE,2BAA2B;IACpD,OAAO,EAAE,oBAAoB;CAC9B,CAAC,CAAC"}