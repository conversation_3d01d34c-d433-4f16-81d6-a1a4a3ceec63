/**
 * Environment configuration for Azure Functions
 * Loads environment variables and validates required settings
 */
export declare const config: {
    cosmosDb: {
        endpoint: string;
        key: string;
        database: string;
    };
    storage: {
        connectionString: string;
    };
    ai: {
        azureOpenAI: {
            endpoint: string;
            key: string;
            deploymentName: string;
        };
        documentIntelligence: {
            endpoint: string;
            key: string;
        };
        search: {
            endpoint: string;
            key: string;
            indexName: string;
        };
    };
    serviceBus: {
        connectionString: string;
    };
    email: {
        postmarkServerToken: string;
    };
    redis: {
        connectionString: string;
    };
    app: {
        environment: string;
        version: string;
        logLevel: string;
    };
};
export default config;
