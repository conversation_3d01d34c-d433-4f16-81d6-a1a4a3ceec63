"use strict";
/**
 * LemonSqueezy Webhook Handler
 * Handles all LemonSqueezy webhook events with proper signature verification
 * Based on LemonSqueezy API documentation: https://docs.lemonsqueezy.com/api/webhooks
 */
Object.defineProperty(exports, "__esModule", { value: true });
const functions_1 = require("@azure/functions");
const crypto_1 = require("crypto");
const logger_1 = require("../shared/utils/logger");
const database_1 = require("../shared/services/database");
const event_grid_handlers_1 = require("./event-grid-handlers");
const cors_1 = require("../shared/middleware/cors");
const uuid_1 = require("uuid");
// LemonSqueezy Event Types
var LemonSqueezyEventType;
(function (LemonSqueezyEventType) {
    LemonSqueezyEventType["ORDER_CREATED"] = "order_created";
    LemonSqueezyEventType["ORDER_REFUNDED"] = "order_refunded";
    LemonSqueezyEventType["SUBSCRIPTION_CREATED"] = "subscription_created";
    LemonSqueezyEventType["SUBSCRIPTION_UPDATED"] = "subscription_updated";
    LemonSqueezyEventType["SUBSCRIPTION_CANCELLED"] = "subscription_cancelled";
    LemonSqueezyEventType["SUBSCRIPTION_RESUMED"] = "subscription_resumed";
    LemonSqueezyEventType["SUBSCRIPTION_EXPIRED"] = "subscription_expired";
    LemonSqueezyEventType["SUBSCRIPTION_PAUSED"] = "subscription_paused";
    LemonSqueezyEventType["SUBSCRIPTION_UNPAUSED"] = "subscription_unpaused";
    LemonSqueezyEventType["SUBSCRIPTION_PAYMENT_SUCCESS"] = "subscription_payment_success";
    LemonSqueezyEventType["SUBSCRIPTION_PAYMENT_FAILED"] = "subscription_payment_failed";
    LemonSqueezyEventType["SUBSCRIPTION_PAYMENT_RECOVERED"] = "subscription_payment_recovered";
    LemonSqueezyEventType["SUBSCRIPTION_PAYMENT_REFUNDED"] = "subscription_payment_refunded";
    LemonSqueezyEventType["LICENSE_KEY_CREATED"] = "license_key_created";
    LemonSqueezyEventType["LICENSE_KEY_UPDATED"] = "license_key_updated";
    LemonSqueezyEventType["AFFILIATE_ACTIVATED"] = "affiliate_activated";
})(LemonSqueezyEventType || (LemonSqueezyEventType = {}));
// Subscription status mapping
var SubscriptionStatus;
(function (SubscriptionStatus) {
    SubscriptionStatus["ON_TRIAL"] = "on_trial";
    SubscriptionStatus["ACTIVE"] = "active";
    SubscriptionStatus["PAUSED"] = "paused";
    SubscriptionStatus["PAST_DUE"] = "past_due";
    SubscriptionStatus["CANCELLED"] = "cancelled";
    SubscriptionStatus["EXPIRED"] = "expired";
})(SubscriptionStatus || (SubscriptionStatus = {}));
/**
 * Main LemonSqueezy webhook handler
 */
async function handleLemonSqueezyWebhook(request, _context) {
    const correlationId = (0, uuid_1.v4)();
    logger_1.logger.info('LemonSqueezy webhook received', {
        correlationId,
        method: request.method,
        url: request.url,
        headers: Object.fromEntries(request.headers.entries())
    });
    try {
        // Only accept POST requests
        if (request.method !== 'POST') {
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
        }
        // Get raw body for signature verification
        const rawBody = await request.text();
        const signature = request.headers.get('X-Signature');
        if (!signature) {
            logger_1.logger.warn('Missing X-Signature header', { correlationId });
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Missing signature header' }
            }, request);
        }
        // Verify webhook signature
        const isValidSignature = verifyWebhookSignature(rawBody, signature);
        if (!isValidSignature) {
            logger_1.logger.error('Invalid webhook signature', { correlationId, signature });
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Invalid signature' }
            }, request);
        }
        // Parse webhook payload
        let payload;
        try {
            payload = JSON.parse(rawBody);
        }
        catch (error) {
            logger_1.logger.error('Invalid JSON payload', { correlationId, error });
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Invalid JSON payload' }
            }, request);
        }
        // Validate payload structure
        if (!payload.meta?.event_name || !payload.data) {
            logger_1.logger.error('Invalid payload structure', { correlationId, payload });
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Invalid payload structure' }
            }, request);
        }
        // Check for duplicate webhook (idempotency)
        const webhookId = payload.meta.webhook_id;
        const eventId = `${webhookId}-${payload.data.id}-${payload.meta.event_name}`;
        const existingEvent = await checkDuplicateWebhook(eventId);
        if (existingEvent) {
            logger_1.logger.info('Duplicate webhook ignored', { correlationId, eventId });
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { message: 'Webhook already processed' }
            }, request);
        }
        // Store webhook event for audit and idempotency
        await storeWebhookEvent(eventId, payload, correlationId);
        // Process the webhook event
        await processWebhookEvent(payload, correlationId);
        logger_1.logger.info('LemonSqueezy webhook processed successfully', {
            correlationId,
            eventType: payload.meta.event_name,
            dataType: payload.data.type,
            dataId: payload.data.id,
            testMode: payload.meta.test_mode
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: 'Webhook processed successfully',
                correlationId,
                eventType: payload.meta.event_name
            }
        }, request);
    }
    catch (error) {
        logger_1.logger.error('LemonSqueezy webhook processing failed', {
            correlationId,
            error: error instanceof Error ? error.message : String(error),
            stack: error instanceof Error ? error.stack : undefined
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                error: 'Internal server error',
                correlationId
            }
        }, request);
    }
}
/**
 * Verify webhook signature using HMAC-SHA256
 */
function verifyWebhookSignature(rawBody, signature) {
    try {
        const secret = process.env.LEMONSQUEEZY_WEBHOOK_SECRET;
        if (!secret) {
            logger_1.logger.error('LemonSqueezy webhook secret not configured');
            return false;
        }
        // Create HMAC hash
        const hmac = (0, crypto_1.createHmac)('sha256', secret);
        const digest = Buffer.from(hmac.update(rawBody).digest('hex'), 'utf8');
        const providedSignature = Buffer.from(signature, 'utf8');
        // Use timing-safe comparison
        return (0, crypto_1.timingSafeEqual)(digest, providedSignature);
    }
    catch (error) {
        logger_1.logger.error('Error verifying webhook signature', { error });
        return false;
    }
}
/**
 * Check if webhook has already been processed (idempotency)
 */
async function checkDuplicateWebhook(eventId) {
    try {
        const existingEvents = await database_1.db.queryItems('lemonsqueezy-webhook-events', 'SELECT * FROM c WHERE c.eventId = @eventId', [eventId]);
        return existingEvents.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Error checking duplicate webhook', { eventId, error });
        return false; // Allow processing if check fails
    }
}
/**
 * Store webhook event for audit trail and idempotency
 */
async function storeWebhookEvent(eventId, payload, correlationId) {
    try {
        await database_1.db.createItem('lemonsqueezy-webhook-events', {
            id: (0, uuid_1.v4)(),
            eventId,
            eventType: payload.meta.event_name,
            dataType: payload.data.type,
            dataId: payload.data.id,
            testMode: payload.meta.test_mode,
            webhookId: payload.meta.webhook_id,
            payload,
            correlationId,
            processedAt: new Date().toISOString(),
            createdAt: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error('Error storing webhook event', { eventId, correlationId, error });
        // Don't throw - webhook processing should continue even if storage fails
    }
}
/**
 * Process webhook event based on event type
 */
async function processWebhookEvent(payload, correlationId) {
    const { meta, data } = payload;
    logger_1.logger.info('Processing LemonSqueezy webhook event', {
        correlationId,
        eventType: meta.event_name,
        dataType: data.type,
        dataId: data.id,
        testMode: meta.test_mode
    });
    try {
        switch (meta.event_name) {
            case LemonSqueezyEventType.ORDER_CREATED:
                await handleOrderCreated(data, correlationId);
                break;
            case LemonSqueezyEventType.ORDER_REFUNDED:
                await handleOrderRefunded(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_CREATED:
                await handleSubscriptionCreated(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_UPDATED:
                await handleSubscriptionUpdated(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_CANCELLED:
                await handleSubscriptionCancelled(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_RESUMED:
                await handleSubscriptionResumed(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_EXPIRED:
                await handleSubscriptionExpired(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_PAUSED:
                await handleSubscriptionPaused(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_UNPAUSED:
                await handleSubscriptionUnpaused(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_SUCCESS:
                await handleSubscriptionPaymentSuccess(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_FAILED:
                await handleSubscriptionPaymentFailed(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_RECOVERED:
                await handleSubscriptionPaymentRecovered(data, correlationId);
                break;
            case LemonSqueezyEventType.SUBSCRIPTION_PAYMENT_REFUNDED:
                await handleSubscriptionPaymentRefunded(data, correlationId);
                break;
            case LemonSqueezyEventType.LICENSE_KEY_CREATED:
                await handleLicenseKeyCreated(data, correlationId);
                break;
            case LemonSqueezyEventType.LICENSE_KEY_UPDATED:
                await handleLicenseKeyUpdated(data, correlationId);
                break;
            case LemonSqueezyEventType.AFFILIATE_ACTIVATED:
                await handleAffiliateActivated(data, correlationId);
                break;
            default:
                logger_1.logger.warn('Unknown LemonSqueezy event type', {
                    correlationId,
                    eventType: meta.event_name
                });
        }
        // Publish internal event for other systems to consume
        await publishInternalEvent(payload, correlationId);
    }
    catch (error) {
        logger_1.logger.error('Error processing webhook event', {
            correlationId,
            eventType: meta.event_name,
            error: error instanceof Error ? error.message : String(error)
        });
        throw error; // Re-throw to trigger retry logic
    }
}
/**
 * Handle order created event
 */
async function handleOrderCreated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing order created', {
        correlationId,
        orderId: data.id,
        customerEmail: attributes.user_email,
        total: attributes.total,
        currency: attributes.currency
    });
    try {
        // Store order in database
        await database_1.db.createItem('orders', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyOrderId: data.id,
            customerEmail: attributes.user_email,
            customerName: attributes.user_name,
            status: attributes.status,
            total: attributes.total,
            subtotal: attributes.subtotal,
            tax: attributes.tax,
            currency: attributes.currency,
            refunded: attributes.refunded,
            refundedAmount: attributes.refunded_amount,
            orderNumber: attributes.order_number,
            storeId: attributes.store_id,
            variantId: attributes.variant_id,
            productId: attributes.product_id,
            customData: attributes.custom_data,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        // Update user subscription status if applicable
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, 'active', correlationId);
        }
        logger_1.logger.info('Order created processed successfully', {
            correlationId,
            orderId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing order created', {
            correlationId,
            orderId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle order refunded event
 */
async function handleOrderRefunded(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing order refunded', {
        correlationId,
        orderId: data.id,
        refundedAmount: attributes.refunded_amount
    });
    try {
        // Update order in database
        await safeUpdateItem('orders', data.id, {
            refunded: attributes.refunded,
            refundedAmount: attributes.refunded_amount,
            status: attributes.status,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        // Update user subscription status if needed
        if (attributes.user_email && attributes.refunded) {
            await updateUserSubscriptionStatus(attributes.user_email, 'cancelled', correlationId);
        }
        logger_1.logger.info('Order refunded processed successfully', {
            correlationId,
            orderId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing order refunded', {
            correlationId,
            orderId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription created event
 */
async function handleSubscriptionCreated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription created', {
        correlationId,
        subscriptionId: data.id,
        customerEmail: attributes.user_email,
        status: attributes.status
    });
    try {
        // Store subscription in database
        await database_1.db.createItem('subscriptions', {
            id: (0, uuid_1.v4)(),
            lemonSqueezySubscriptionId: data.id,
            customerEmail: attributes.user_email,
            customerName: attributes.user_name,
            status: attributes.status,
            productId: attributes.product_id,
            variantId: attributes.variant_id,
            cardBrand: attributes.card_brand,
            cardLastFour: attributes.card_last_four,
            billingAnchor: attributes.billing_anchor,
            renewsAt: attributes.renews_at,
            endsAt: attributes.ends_at,
            trialEndsAt: attributes.trial_ends_at,
            price: attributes.price,
            isUsageBased: attributes.is_usage_based,
            subscriptionItemId: attributes.subscription_item_id,
            storeId: attributes.store_id,
            customData: attributes.custom_data,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        // Update user subscription status
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, attributes.status, correlationId);
        }
        logger_1.logger.info('Subscription created processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription created', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription updated event
 */
async function handleSubscriptionUpdated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription updated', {
        correlationId,
        subscriptionId: data.id,
        status: attributes.status
    });
    try {
        // Update subscription in database
        await safeUpdateItem('subscriptions', data.id, {
            status: attributes.status,
            cardBrand: attributes.card_brand,
            cardLastFour: attributes.card_last_four,
            billingAnchor: attributes.billing_anchor,
            renewsAt: attributes.renews_at,
            endsAt: attributes.ends_at,
            trialEndsAt: attributes.trial_ends_at,
            price: attributes.price,
            isUsageBased: attributes.is_usage_based,
            subscriptionItemId: attributes.subscription_item_id,
            customData: attributes.custom_data,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        // Update user subscription status
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, attributes.status, correlationId);
        }
        logger_1.logger.info('Subscription updated processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription updated', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription cancelled event
 */
async function handleSubscriptionCancelled(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription cancelled', {
        correlationId,
        subscriptionId: data.id,
        endsAt: attributes.ends_at
    });
    try {
        // Update subscription in database
        await safeUpdateItem('subscriptions', data.id, {
            status: SubscriptionStatus.CANCELLED,
            endsAt: attributes.ends_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        // Update user subscription status
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, SubscriptionStatus.CANCELLED, correlationId);
        }
        logger_1.logger.info('Subscription cancelled processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription cancelled', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription resumed event
 */
async function handleSubscriptionResumed(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription resumed', {
        correlationId,
        subscriptionId: data.id
    });
    try {
        await safeUpdateItem('subscriptions', data.id, {
            status: SubscriptionStatus.ACTIVE,
            endsAt: attributes.ends_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, SubscriptionStatus.ACTIVE, correlationId);
        }
        logger_1.logger.info('Subscription resumed processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription resumed', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription expired event
 */
async function handleSubscriptionExpired(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription expired', {
        correlationId,
        subscriptionId: data.id
    });
    try {
        await safeUpdateItem('subscriptions', data.id, {
            status: SubscriptionStatus.EXPIRED,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, SubscriptionStatus.EXPIRED, correlationId);
        }
        logger_1.logger.info('Subscription expired processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription expired', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription paused event
 */
async function handleSubscriptionPaused(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription paused', {
        correlationId,
        subscriptionId: data.id
    });
    try {
        await safeUpdateItem('subscriptions', data.id, {
            status: SubscriptionStatus.PAUSED,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, SubscriptionStatus.PAUSED, correlationId);
        }
        logger_1.logger.info('Subscription paused processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription paused', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription unpaused event
 */
async function handleSubscriptionUnpaused(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription unpaused', {
        correlationId,
        subscriptionId: data.id
    });
    try {
        await safeUpdateItem('subscriptions', data.id, {
            status: SubscriptionStatus.ACTIVE,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        if (attributes.user_email) {
            await updateUserSubscriptionStatus(attributes.user_email, SubscriptionStatus.ACTIVE, correlationId);
        }
        logger_1.logger.info('Subscription unpaused processed successfully', {
            correlationId,
            subscriptionId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription unpaused', {
            correlationId,
            subscriptionId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription payment success event
 */
async function handleSubscriptionPaymentSuccess(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription payment success', {
        correlationId,
        invoiceId: data.id,
        subscriptionId: attributes.subscription_id,
        total: attributes.total
    });
    try {
        // Store invoice/payment record
        await database_1.db.createItem('subscription-invoices', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyInvoiceId: data.id,
            subscriptionId: attributes.subscription_id,
            storeId: attributes.store_id,
            billingReason: attributes.billing_reason,
            cardBrand: attributes.card_brand,
            cardLastFour: attributes.card_last_four,
            currency: attributes.currency,
            currencyRate: attributes.currency_rate,
            status: attributes.status,
            statusFormatted: attributes.status_formatted,
            refunded: attributes.refunded,
            refundedAmount: attributes.refunded_amount,
            subtotal: attributes.subtotal,
            discountTotal: attributes.discount_total,
            tax: attributes.tax,
            total: attributes.total,
            subtotalUsd: attributes.subtotal_usd,
            discountTotalUsd: attributes.discount_total_usd,
            taxUsd: attributes.tax_usd,
            totalUsd: attributes.total_usd,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        logger_1.logger.info('Subscription payment success processed successfully', {
            correlationId,
            invoiceId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription payment success', {
            correlationId,
            invoiceId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription payment failed event
 */
async function handleSubscriptionPaymentFailed(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription payment failed', {
        correlationId,
        invoiceId: data.id,
        subscriptionId: attributes.subscription_id
    });
    try {
        // Store failed payment record
        await database_1.db.createItem('subscription-invoices', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyInvoiceId: data.id,
            subscriptionId: attributes.subscription_id,
            storeId: attributes.store_id,
            billingReason: attributes.billing_reason,
            status: 'failed',
            statusFormatted: attributes.status_formatted,
            currency: attributes.currency,
            total: attributes.total,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        // Update subscription status to past_due if needed
        if (attributes.subscription_id) {
            await safeUpdateItem('subscriptions', attributes.subscription_id, {
                status: SubscriptionStatus.PAST_DUE,
                updatedAt: attributes.updated_at,
                processedAt: new Date().toISOString()
            }, correlationId);
        }
        logger_1.logger.info('Subscription payment failed processed successfully', {
            correlationId,
            invoiceId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription payment failed', {
            correlationId,
            invoiceId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription payment recovered event
 */
async function handleSubscriptionPaymentRecovered(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription payment recovered', {
        correlationId,
        invoiceId: data.id,
        subscriptionId: attributes.subscription_id
    });
    try {
        // Store recovered payment record
        await database_1.db.createItem('subscription-invoices', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyInvoiceId: data.id,
            subscriptionId: attributes.subscription_id,
            storeId: attributes.store_id,
            billingReason: attributes.billing_reason,
            cardBrand: attributes.card_brand,
            cardLastFour: attributes.card_last_four,
            currency: attributes.currency,
            status: attributes.status,
            statusFormatted: attributes.status_formatted,
            total: attributes.total,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        // Update subscription status back to active
        if (attributes.subscription_id) {
            await safeUpdateItem('subscriptions', attributes.subscription_id, {
                status: SubscriptionStatus.ACTIVE,
                updatedAt: attributes.updated_at,
                processedAt: new Date().toISOString()
            }, correlationId);
        }
        logger_1.logger.info('Subscription payment recovered processed successfully', {
            correlationId,
            invoiceId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription payment recovered', {
            correlationId,
            invoiceId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle subscription payment refunded event
 */
async function handleSubscriptionPaymentRefunded(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing subscription payment refunded', {
        correlationId,
        invoiceId: data.id,
        refundedAmount: attributes.refunded_amount
    });
    try {
        // Update invoice record with refund information
        await safeUpdateItem('subscription-invoices', data.id, {
            refunded: attributes.refunded,
            refundedAmount: attributes.refunded_amount,
            status: attributes.status,
            statusFormatted: attributes.status_formatted,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        logger_1.logger.info('Subscription payment refunded processed successfully', {
            correlationId,
            invoiceId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing subscription payment refunded', {
            correlationId,
            invoiceId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle license key created event
 */
async function handleLicenseKeyCreated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing license key created', {
        correlationId,
        licenseKeyId: data.id,
        key: attributes.key
    });
    try {
        // Store license key
        await database_1.db.createItem('license-keys', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyLicenseKeyId: data.id,
            storeId: attributes.store_id,
            orderId: attributes.order_id,
            orderItemId: attributes.order_item_id,
            productId: attributes.product_id,
            userId: attributes.user_id,
            userName: attributes.user_name,
            userEmail: attributes.user_email,
            key: attributes.key,
            keyShort: attributes.key_short,
            activationLimit: attributes.activation_limit,
            instancesCount: attributes.instances_count,
            disabled: attributes.disabled,
            status: attributes.status,
            statusFormatted: attributes.status_formatted,
            expiresAt: attributes.expires_at,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        logger_1.logger.info('License key created processed successfully', {
            correlationId,
            licenseKeyId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing license key created', {
            correlationId,
            licenseKeyId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle license key updated event
 */
async function handleLicenseKeyUpdated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing license key updated', {
        correlationId,
        licenseKeyId: data.id,
        status: attributes.status
    });
    try {
        // Update license key
        await safeUpdateItem('license-keys', data.id, {
            activationLimit: attributes.activation_limit,
            instancesCount: attributes.instances_count,
            disabled: attributes.disabled,
            status: attributes.status,
            statusFormatted: attributes.status_formatted,
            expiresAt: attributes.expires_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        }, correlationId);
        logger_1.logger.info('License key updated processed successfully', {
            correlationId,
            licenseKeyId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing license key updated', {
            correlationId,
            licenseKeyId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Handle affiliate activated event
 */
async function handleAffiliateActivated(data, correlationId) {
    const { attributes } = data;
    logger_1.logger.info('Processing affiliate activated', {
        correlationId,
        affiliateId: data.id,
        email: attributes.email
    });
    try {
        // Store affiliate information
        await database_1.db.createItem('affiliates', {
            id: (0, uuid_1.v4)(),
            lemonSqueezyAffiliateId: data.id,
            storeId: attributes.store_id,
            email: attributes.email,
            firstName: attributes.first_name,
            lastName: attributes.last_name,
            url: attributes.url,
            commissionRate: attributes.commission_rate,
            commissionType: attributes.commission_type,
            status: attributes.status,
            createdAt: attributes.created_at,
            updatedAt: attributes.updated_at,
            processedAt: new Date().toISOString()
        });
        logger_1.logger.info('Affiliate activated processed successfully', {
            correlationId,
            affiliateId: data.id
        });
    }
    catch (error) {
        logger_1.logger.error('Error processing affiliate activated', {
            correlationId,
            affiliateId: data.id,
            error
        });
        throw error;
    }
}
/**
 * Helper function to safely update an item in the database
 */
async function safeUpdateItem(containerName, itemId, updates, correlationId) {
    try {
        const existingItem = await database_1.db.readItem(containerName, itemId, itemId);
        if (existingItem) {
            await database_1.db.updateItem(containerName, {
                ...existingItem,
                ...updates,
                id: itemId
            });
        }
        else {
            logger_1.logger.warn(`Item not found for update in ${containerName}`, {
                correlationId,
                itemId
            });
        }
    }
    catch (error) {
        logger_1.logger.error(`Error updating item in ${containerName}`, {
            correlationId,
            itemId,
            error
        });
        throw error;
    }
}
/**
 * Update user subscription status in the users table
 */
async function updateUserSubscriptionStatus(email, status, correlationId) {
    try {
        // Find user by email
        const users = await database_1.db.queryItems('users', 'SELECT * FROM c WHERE c.email = @email', [email]);
        if (users.length > 0) {
            const user = users[0];
            await database_1.db.updateItem('users', {
                ...user,
                subscriptionStatus: status,
                subscriptionUpdatedAt: new Date().toISOString()
            });
            logger_1.logger.info('User subscription status updated', {
                correlationId,
                userId: user.id,
                email,
                status
            });
        }
        else {
            logger_1.logger.warn('User not found for subscription status update', {
                correlationId,
                email,
                status
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Error updating user subscription status', {
            correlationId,
            email,
            status,
            error
        });
        // Don't throw - this is not critical for webhook processing
    }
}
/**
 * Publish internal event to Event Grid for other systems to consume
 */
async function publishInternalEvent(payload, correlationId) {
    try {
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.SYSTEM_HEALTH_CHECK, // Using existing event type since PAYMENT_PROCESSED doesn't exist
        `lemonsqueezy/${payload.meta.event_name}`, {
            eventType: payload.meta.event_name,
            dataType: payload.data.type,
            dataId: payload.data.id,
            testMode: payload.meta.test_mode,
            correlationId,
            timestamp: new Date().toISOString()
        });
        logger_1.logger.info('Internal event published', {
            correlationId,
            eventType: payload.meta.event_name
        });
    }
    catch (error) {
        logger_1.logger.error('Error publishing internal event', {
            correlationId,
            eventType: payload.meta.event_name,
            error
        });
        // Don't throw - webhook processing should continue even if event publishing fails
    }
}
// Register the function with Azure Functions
functions_1.app.http('lemonsqueezy-webhooks', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'anonymous',
    handler: handleLemonSqueezyWebhook
});
//# sourceMappingURL=lemonsqueezy-webhooks.js.map