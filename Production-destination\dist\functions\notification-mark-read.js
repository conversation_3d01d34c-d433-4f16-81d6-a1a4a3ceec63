"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.markNotificationsRead = markNotificationsRead;
exports.getNotification = getNotification;
/**
 * Notification Mark Read Function
 * Handles marking notifications as read/unread
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const markReadSchema = Joi.object({
    notificationIds: Joi.array().items(Joi.string().uuid()).min(1).optional(),
    markAll: Joi.boolean().default(false),
    isRead: Joi.boolean().default(true),
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional()
}).or('notificationIds', 'markAll');
/**
 * Mark notifications as read handler
 */
async function markNotificationsRead(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Mark notifications read started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = markReadSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { notificationIds, markAll, isRead, organizationId, projectId } = value;
        let updatedCount = 0;
        let notificationsToUpdate = [];
        if (markAll) {
            // Mark all notifications as read for the user
            let queryText = 'SELECT * FROM c WHERE c.recipientId = @userId AND c.isRead != @isRead';
            const parameters = [user.id, isRead];
            // Add tenant isolation
            if (user.tenantId) {
                queryText += ' AND (c.tenantId = @tenantId OR c.tenantId IS NULL)';
                parameters.push(user.tenantId);
            }
            // Filter by organization if provided
            if (organizationId) {
                queryText += ' AND c.organizationId = @organizationId';
                parameters.push(organizationId);
            }
            // Filter by project if provided
            if (projectId) {
                queryText += ' AND c.projectId = @projectId';
                parameters.push(projectId);
            }
            // Only include non-expired notifications
            queryText += ' AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
            parameters.push(new Date().toISOString());
            notificationsToUpdate = await database_1.db.queryItems('notifications', queryText, parameters);
        }
        else if (notificationIds && notificationIds.length > 0) {
            // Mark specific notifications as read
            for (const notificationId of notificationIds) {
                const notification = await database_1.db.readItem('notifications', notificationId, notificationId);
                if (notification && notification.recipientId === user.id) {
                    notificationsToUpdate.push(notification);
                }
            }
        }
        // Update notifications
        for (const notification of notificationsToUpdate) {
            const updatedNotification = {
                ...notification,
                id: notification.id,
                isRead,
                readAt: isRead ? new Date().toISOString() : undefined
            };
            await database_1.db.updateItem('notifications', updatedNotification);
            updatedCount++;
        }
        // Create activity record if any notifications were updated
        if (updatedCount > 0) {
            await database_1.db.createItem('activities', {
                id: (0, uuid_1.v4)(),
                type: "notifications_marked_read",
                userId: user.id,
                organizationId,
                projectId,
                timestamp: new Date().toISOString(),
                details: {
                    updatedCount,
                    isRead,
                    markAll
                },
                tenantId: user.tenantId
            });
        }
        logger_1.logger.info("Notifications marked read successfully", {
            correlationId,
            userId: user.id,
            updatedCount,
            isRead,
            markAll
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                updatedCount,
                message: `${updatedCount} notifications marked as ${isRead ? 'read' : 'unread'}`
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Mark notifications read failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get notification by ID handler
 */
async function getNotification(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const notificationId = request.params.notificationId;
    if (!notificationId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Notification ID is required' }
        }, request);
    }
    logger_1.logger.info("Get notification started", { correlationId, notificationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get notification
        const notification = await database_1.db.readItem('notifications', notificationId, notificationId);
        if (!notification) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Notification not found" }
            }, request);
        }
        // Check if user is the recipient
        if (notification.recipientId !== user.id) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Automatically mark as read when viewed
        if (!notification.isRead) {
            const updatedNotification = {
                ...notification,
                id: notificationId,
                isRead: true,
                readAt: new Date().toISOString()
            };
            await database_1.db.updateItem('notifications', updatedNotification);
        }
        // Get sender information
        let senderName = 'System';
        let senderEmail = '';
        if (notification.senderId) {
            try {
                const sender = await database_1.db.readItem('users', notification.senderId, notification.senderId);
                if (sender) {
                    senderName = sender.name || sender.email || 'Unknown User';
                    senderEmail = sender.email || '';
                }
            }
            catch (error) {
                // Sender might not exist anymore
            }
        }
        const enrichedNotification = {
            ...notification,
            senderName,
            senderEmail,
            isExpired: notification.expiresAt ? new Date(notification.expiresAt) < new Date() : false
        };
        logger_1.logger.info("Notification retrieved successfully", {
            correlationId,
            notificationId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: enrichedNotification
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get notification failed", {
            correlationId,
            notificationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Register functions
functions_1.app.http('notification-mark-read', {
    methods: ['PATCH', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/mark-read',
    handler: markNotificationsRead
});
functions_1.app.http('notification-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/{notificationId}',
    handler: getNotification
});
//# sourceMappingURL=notification-mark-read.js.map