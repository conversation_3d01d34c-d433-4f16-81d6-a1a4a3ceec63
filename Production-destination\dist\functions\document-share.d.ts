/**
 * Document Sharing Function
 * Handles document sharing operations (create, read, update, revoke)
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
export declare enum DocumentSharingStatus {
    ACTIVE = "ACTIVE",
    REVOKED = "REVOKED",
    EXPIRED = "EXPIRED"
}
export declare enum SharePermission {
    VIEW = "VIEW",
    COMMENT = "COMMENT",
    EDIT = "EDIT",
    DOWNLOAD = "DOWNLOAD"
}
/**
 * Create document share handler
 */
export declare function createDocumentShare(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get document shares handler
 */
export declare function getDocumentShares(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
