"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.listProjects = listProjects;
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schema
const listProjectsSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    organizationId: Joi.string().uuid().optional(),
    search: Joi.string().max(100).optional(),
    visibility: Joi.string().valid('PRIVATE', 'ORGANIZATION', 'PUBLIC').optional(),
    tags: Joi.string().optional() // Comma-separated tags
});
/**
 * List projects handler
 */
async function listProjects(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List projects started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listProjectsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, organizationId, search, visibility, tags } = value;
        // Build query to get projects where user is a member
        let queryText = 'SELECT * FROM c WHERE ARRAY_CONTAINS(c.memberIds, @userId)';
        const parameters = [user.id];
        // Add tenant isolation
        if (user.tenantId) {
            queryText += ' AND (c.tenantId = @tenantId OR c.createdBy = @userId2)';
            parameters.push(user.tenantId, user.id);
        }
        // Add organization filter if provided
        if (organizationId) {
            queryText += ' AND c.organizationId = @organizationId';
            parameters.push(organizationId);
        }
        // Add visibility filter if provided
        if (visibility) {
            queryText += ' AND c.visibility = @visibility';
            parameters.push(visibility);
        }
        // Add search filter if provided
        if (search) {
            queryText += ' AND (CONTAINS(LOWER(c.name), LOWER(@search)) OR CONTAINS(LOWER(c.description), LOWER(@search)))';
            parameters.push(search);
        }
        // Add tags filter if provided
        if (tags) {
            const tagList = tags.split(',').map((tag) => tag.trim());
            queryText += ' AND EXISTS(SELECT VALUE t FROM t IN c.tags WHERE t IN (@tagList))';
            parameters.push(tagList);
        }
        // Add ordering
        queryText += ' ORDER BY c.createdAt DESC';
        // Get total count for pagination
        const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
        const countResult = await database_1.db.queryItems('projects', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const projects = await database_1.db.queryItems('projects', paginatedQuery, parameters);
        // Enrich projects with computed fields
        const enrichedProjects = await Promise.all(projects.map(async (project) => {
            // Get document count
            const documentCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const documentCountResult = await database_1.db.queryItems('documents', documentCountQuery, [project.id]);
            const documentCount = Number(documentCountResult[0]) || 0;
            // Get workflow count
            const workflowCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const workflowCountResult = await database_1.db.queryItems('workflows', workflowCountQuery, [project.id]);
            const workflowCount = Number(workflowCountResult[0]) || 0;
            // Get member count
            const memberCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.projectId = @projectId';
            const memberCountResult = await database_1.db.queryItems('project-members', memberCountQuery, [project.id]);
            const memberCount = Number(memberCountResult[0]) || 0;
            // Get organization name
            let organizationName = 'Unknown';
            try {
                const organization = await database_1.db.readItem('organizations', project.organizationId, project.organizationId);
                if (organization) {
                    organizationName = organization.name;
                }
            }
            catch (error) {
                // Organization might not exist or user might not have access
            }
            return {
                ...project,
                documentCount,
                workflowCount,
                memberCount,
                organizationName,
                storageUsed: 0 // TODO: Calculate actual storage usage
            };
        }));
        logger_1.logger.info("Projects listed successfully", {
            correlationId,
            userId: user.id,
            count: projects.length,
            page,
            limit,
            organizationId
        });
        // Create response
        const response = {
            items: enrichedProjects,
            total,
            page,
            limit,
            hasMore: page * limit < total
        };
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List projects failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Note: Registration moved to project-create.ts for combined handling
// app.http('project-list', {
//   methods: ['GET', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'projects',
//   handler: listProjects
// });
//# sourceMappingURL=project-list.js.map