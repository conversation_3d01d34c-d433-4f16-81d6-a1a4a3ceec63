/**
 * CORS middleware for Azure Functions
 * Handles Cross-Origin Resource Sharing headers and preflight requests
 */
import { HttpRequest, HttpResponseInit } from '@azure/functions';
export interface CorsOptions {
    allowedOrigins?: string[];
    allowedMethods?: string[];
    allowedHeaders?: string[];
    allowCredentials?: boolean;
    maxAge?: number;
}
/**
 * Add CORS headers to response
 */
export declare function addCorsHeaders(response: HttpResponseInit, request: HttpRequest, options?: CorsOptions): HttpResponseInit;
/**
 * Handle preflight OPTIONS request
 */
export declare function handlePreflight(request: HttpRequest, options?: CorsOptions): HttpResponseInit | null;
/**
 * CORS middleware wrapper for Azure Functions
 */
export declare function withCors(handler: (request: HttpRequest, context: any) => Promise<HttpResponseInit>, options?: CorsOptions): (request: HttpRequest, context: any) => Promise<HttpResponseInit>;
