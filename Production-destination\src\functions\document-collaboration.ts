/**
 * Document Collaboration Function
 * Handles document sharing, commenting, and collaborative features
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import * as bcrypt from 'bcrypt';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';

// Share permission levels enum
enum SharePermission {
  VIEW = 'VIEW',
  COMMENT = 'COMMENT',
  EDIT = 'EDIT',
  ADMIN = 'ADMIN'
}

// Comment types enum
enum CommentType {
  GENERAL = 'GENERAL',
  SUGGESTION = 'SUGGESTION',
  APPROVAL = 'APPROVAL',
  QUESTION = 'QUESTION'
}

// Validation schemas
const shareDocumentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  shareWith: Joi.array().items(
    Joi.object({
      userId: Joi.string().uuid().optional(),
      email: Joi.string().email().optional(),
      permission: Joi.string().valid(...Object.values(SharePermission)).required()
    }).or('userId', 'email')
  ).min(1).required(),
  message: Joi.string().max(500).optional(),
  expiresAt: Joi.date().iso().optional(),
  allowDownload: Joi.boolean().default(true),
  allowPrint: Joi.boolean().default(true),
  requirePassword: Joi.boolean().default(false),
  password: Joi.string().min(6).optional()
});

const addCommentSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  content: Joi.string().required().max(2000),
  type: Joi.string().valid(...Object.values(CommentType)).default(CommentType.GENERAL),
  parentCommentId: Joi.string().uuid().optional(),
  position: Joi.object({
    page: Joi.number().integer().min(1).optional(),
    x: Joi.number().optional(),
    y: Joi.number().optional(),
    width: Joi.number().optional(),
    height: Joi.number().optional()
  }).optional(),
  mentions: Joi.array().items(Joi.string().uuid()).optional()
});

const listCommentsSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  page: Joi.number().integer().min(1).default(1),
  limit: Joi.number().integer().min(1).max(100).default(20),
  type: Joi.string().valid(...Object.values(CommentType)).optional(),
  resolved: Joi.boolean().optional()
});

/**
 * Share document handler
 */
export async function shareDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Share document started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = shareDocumentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentId, shareWith, message, expiresAt, allowDownload, allowPrint, requirePassword, password } = value;

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check if user has permission to share
    const canShare = (
      (document as any).createdBy === user.id ||
      (document as any).organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!canShare) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to share document" }
      }, request);
    }

    const shareResults = [];
    const shareId = uuidv4();

    // Process each share recipient
    for (const recipient of shareWith) {
      let targetUserId = recipient.userId;

      // If email provided, try to find user by email
      if (!targetUserId && recipient.email) {
        const userQuery = 'SELECT * FROM c WHERE c.email = @email';
        const users = await db.queryItems('users', userQuery, [recipient.email]);
        if (users.length > 0) {
          targetUserId = (users[0] as any).id;
        }
      }

      // Create share record
      const shareRecord = {
        id: uuidv4(),
        shareId,
        documentId,
        sharedBy: user.id,
        sharedWith: targetUserId,
        email: recipient.email,
        permission: recipient.permission,
        message,
        expiresAt,
        allowDownload,
        allowPrint,
        requirePassword,
        passwordHash: password ? await hashPassword(password) : undefined,
        isActive: true,
        createdAt: new Date().toISOString(),
        accessCount: 0,
        lastAccessedAt: null,
        organizationId: (document as any).organizationId,
        projectId: (document as any).projectId,
        tenantId: user.tenantId
      };

      await db.createItem('document-shares', shareRecord);

      // Send notification if user exists
      if (targetUserId) {
        await db.createItem('notifications', {
          id: uuidv4(),
          recipientId: targetUserId,
          senderId: user.id,
          type: 'DOCUMENT_SHARED',
          title: `Document shared: ${(document as any).name}`,
          message: message || `${(user as any).name || user.email} shared a document with you`,
          priority: 'MEDIUM',
          actionUrl: `/documents/${documentId}`,
          actionText: 'View Document',
          documentId,
          organizationId: (document as any).organizationId,
          projectId: (document as any).projectId,
          isRead: false,
          createdAt: new Date().toISOString(),
          tenantId: user.tenantId
        });
      }

      shareResults.push({
        email: recipient.email,
        userId: targetUserId,
        permission: recipient.permission,
        shareId: shareRecord.id,
        notificationSent: !!targetUserId
      });
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_shared",
      userId: user.id,
      organizationId: (document as any).organizationId,
      projectId: (document as any).projectId,
      documentId,
      timestamp: new Date().toISOString(),
      details: {
        documentName: (document as any).name,
        recipientCount: shareWith.length,
        permissions: shareWith.map((s: any) => s.permission),
        hasExpiration: !!expiresAt
      },
      tenantId: user.tenantId
    });

    logger.info("Document shared successfully", {
      correlationId,
      documentId,
      sharedBy: user.id,
      recipientCount: shareWith.length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        shareId,
        documentId,
        documentName: (document as any).name,
        shares: shareResults,
        expiresAt,
        message: "Document shared successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Share document failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Add comment handler
 */
export async function addComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Add comment started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = addCommentSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { documentId, content, type, parentCommentId, position, mentions } = value;

    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    // Check if user has permission to comment
    const canComment = await checkDocumentAccess(user.id, documentId, SharePermission.COMMENT);
    if (!canComment) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Insufficient permissions to comment on document" }
      }, request);
    }

    // Create comment
    const commentId = uuidv4();
    const comment = {
      id: commentId,
      documentId,
      content,
      type,
      parentCommentId,
      position,
      mentions: mentions || [],
      createdBy: user.id,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
      isResolved: false,
      resolvedBy: null,
      resolvedAt: null,
      likesCount: 0,
      repliesCount: 0,
      organizationId: (document as any).organizationId,
      projectId: (document as any).projectId,
      tenantId: user.tenantId
    };

    await db.createItem('document-comments', comment);

    // Update parent comment reply count if this is a reply
    if (parentCommentId) {
      const parentComment = await db.readItem('document-comments', parentCommentId, parentCommentId);
      if (parentComment) {
        const updatedParent = {
          ...(parentComment as any),
          id: parentCommentId,
          repliesCount: ((parentComment as any).repliesCount || 0) + 1,
          updatedAt: new Date().toISOString()
        };
        await db.updateItem('document-comments', updatedParent);
      }
    }

    // Send notifications to mentioned users
    if (mentions && mentions.length > 0) {
      for (const mentionedUserId of mentions) {
        await db.createItem('notifications', {
          id: uuidv4(),
          recipientId: mentionedUserId,
          senderId: user.id,
          type: 'DOCUMENT_COMMENTED',
          title: `You were mentioned in a comment`,
          message: `${(user as any).name || user.email} mentioned you in a comment on ${(document as any).name}`,
          priority: 'MEDIUM',
          actionUrl: `/documents/${documentId}#comment-${commentId}`,
          actionText: 'View Comment',
          documentId,
          organizationId: (document as any).organizationId,
          projectId: (document as any).projectId,
          isRead: false,
          createdAt: new Date().toISOString(),
          tenantId: user.tenantId
        });
      }
    }

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "document_commented",
      userId: user.id,
      organizationId: (document as any).organizationId,
      projectId: (document as any).projectId,
      documentId,
      commentId,
      timestamp: new Date().toISOString(),
      details: {
        documentName: (document as any).name,
        commentType: type,
        isReply: !!parentCommentId,
        mentionsCount: mentions?.length || 0
      },
      tenantId: user.tenantId
    });

    logger.info("Comment added successfully", {
      correlationId,
      commentId,
      documentId,
      userId: user.id,
      type,
      isReply: !!parentCommentId
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        id: commentId,
        documentId,
        content,
        type,
        createdBy: user.id,
        createdAt: comment.createdAt,
        message: "Comment added successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Add comment failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * List comments handler
 */
export async function listComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const documentId = request.params.documentId;

  if (!documentId) {
    return addCorsHeaders({
      status: 400,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: 'Document ID is required' }
    }, request);
  }

  logger.info("List comments started", { correlationId, documentId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate query parameters
    const queryParams = Object.fromEntries(request.query.entries());
    const { error, value } = listCommentsSchema.validate({ ...queryParams, documentId });

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const { page, limit, type, resolved } = value;

    // Check if user has access to document
    const canView = await checkDocumentAccess(user.id, documentId, SharePermission.VIEW);
    if (!canView) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Build query
    let queryText = 'SELECT * FROM c WHERE c.documentId = @documentId';
    const parameters: any[] = [documentId];

    if (type) {
      queryText += ' AND c.type = @type';
      parameters.push(type);
    }

    if (resolved !== undefined) {
      queryText += ' AND c.isResolved = @resolved';
      parameters.push(resolved);
    }

    // Order by creation date
    queryText += ' ORDER BY c.createdAt DESC';

    // Get total count
    const countQuery = queryText.replace('SELECT *', 'SELECT VALUE COUNT(1)');
    const countResult = await db.queryItems('document-comments', countQuery, parameters);
    const total = Number(countResult[0]) || 0;

    // Add pagination
    const offset = (page - 1) * limit;
    const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;

    // Execute query
    const comments = await db.queryItems('document-comments', paginatedQuery, parameters);

    // Enrich comments with user information
    const enrichedComments = await Promise.all(
      comments.map(async (comment: any) => {
        let authorName = 'Unknown User';
        try {
          const author = await db.readItem('users', comment.createdBy, comment.createdBy);
          if (author) {
            authorName = (author as any).name || (author as any).email;
          }
        } catch (error) {
          // Author might not exist
        }

        return {
          ...comment,
          authorName
        };
      })
    );

    logger.info("Comments listed successfully", {
      correlationId,
      documentId,
      userId: user.id,
      count: comments.length,
      page,
      limit
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        documentId,
        items: enrichedComments,
        total,
        page,
        limit,
        hasMore: page * limit < total
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("List comments failed", {
      correlationId,
      documentId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check document access
 */
async function checkDocumentAccess(userId: string, documentId: string, requiredPermission: SharePermission): Promise<boolean> {
  try {
    // Get document
    const document = await db.readItem('documents', documentId, documentId);
    if (!document) return false;

    // Check if user is owner
    if ((document as any).createdBy === userId) return true;

    // Check organization membership
    if ((document as any).organizationId) {
      const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
      const memberships = await db.queryItems('organization-members', membershipQuery, [userId, (document as any).organizationId, 'active']);

      if (memberships.length > 0) {
        const membership = memberships[0] as any;
        if (membership.role === 'ADMIN') return true;
        if (requiredPermission === SharePermission.VIEW && membership.role === 'MEMBER') return true;
      }
    }

    // Check direct shares
    const shareQuery = 'SELECT * FROM c WHERE c.documentId = @documentId AND c.sharedWith = @userId AND c.isActive = true AND (c.expiresAt IS NULL OR c.expiresAt > @now)';
    const shares = await db.queryItems('document-shares', shareQuery, [documentId, userId, new Date().toISOString()]);

    if (shares.length > 0) {
      const share = shares[0] as any;
      const permissionLevels = [SharePermission.VIEW, SharePermission.COMMENT, SharePermission.EDIT, SharePermission.ADMIN];
      const userLevel = permissionLevels.indexOf(share.permission);
      const requiredLevel = permissionLevels.indexOf(requiredPermission);
      return userLevel >= requiredLevel;
    }

    return false;
  } catch (error) {
    logger.error("Error checking document access", { userId, documentId, requiredPermission, error });
    return false;
  }
}

/**
 * Production password hashing using bcrypt
 */
async function hashPassword(password: string): Promise<string> {
  try {
    const saltRounds = 12; // High security salt rounds
    const hashedPassword = await bcrypt.hash(password, saltRounds);
    logger.info('Password hashed successfully');
    return hashedPassword;
  } catch (error) {
    logger.error('Password hashing failed', { error: error instanceof Error ? error.message : String(error) });
    throw new Error('Password hashing failed');
  }
}

/**
 * Verify password against hash
 */
async function verifyPassword(password: string, hash: string): Promise<boolean> {
  try {
    const isValid = await bcrypt.compare(password, hash);
    logger.info('Password verification completed', { isValid });
    return isValid;
  } catch (error) {
    logger.error('Password verification failed', { error: error instanceof Error ? error.message : String(error) });
    return false;
  }
}

// Register functions
app.http('document-share', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'documents/{id}/share',
  handler: shareDocument
});

// Note: Comment functionality is handled by document-comments.ts
// app.http('document-comment-add', {
//   methods: ['POST', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'documents/{id}/comments',
//   handler: addComment
// });

// app.http('document-comment-list', {
//   methods: ['GET', 'OPTIONS'],
//   authLevel: 'function',
//   route: 'documents/{documentId}/comments',
//   handler: listComments
// });
