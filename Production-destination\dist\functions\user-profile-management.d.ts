/**
 * User Profile Management Function
 * Handles user profile updates, avatar management, and profile settings
 * Migrated from old-arch/src/user-service/profile/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get user profile handler
 */
export declare function getUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update user profile handler
 */
export declare function updateUserProfile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Upload avatar handler
 */
export declare function uploadAvatar(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
