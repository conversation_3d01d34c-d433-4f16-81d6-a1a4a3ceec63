/**
 * Enhanced SignalR Service for production-ready real-time messaging
 * Features:
 * - Connection management and scaling
 * - Group management with Redis backing
 * - Authentication integration
 * - Message routing and filtering
 * - Performance monitoring
 * - Auto-reconnection and failover
 */
export interface SignalRConnection {
    connectionId: string;
    userId: string;
    groups: string[];
    connectedAt: Date;
    lastActivity: Date;
    metadata: Record<string, any>;
}
export interface SignalRMessage {
    target: string;
    arguments: any[];
    connectionId?: string;
    userId?: string;
    groupName?: string;
    excludeConnectionIds?: string[];
}
export interface SignalRGroup {
    name: string;
    connections: Set<string>;
    metadata: Record<string, any>;
    createdAt: Date;
    lastActivity: Date;
}
export interface SignalRMetrics {
    totalConnections: number;
    activeGroups: number;
    messagesSent: number;
    messagesReceived: number;
    errors: number;
    averageLatency: number;
}
export declare class SignalREnhancedService {
    private static instance;
    private connections;
    private groups;
    private metrics;
    private connectionString;
    private hubName;
    private isInitialized;
    private constructor();
    static getInstance(): SignalREnhancedService;
    /**
     * Initialize SignalR service
     */
    initialize(): Promise<void>;
    /**
     * Initialize Redis integration for scaling
     */
    private initializeRedisIntegration;
    /**
     * Handle Redis messages for cross-instance communication
     */
    private handleRedisMessage;
    /**
     * Handle Redis group management messages
     */
    private handleRedisGroupMessage;
    /**
     * Handle Redis connection management messages
     */
    private handleRedisConnectionMessage;
    /**
     * Register a new connection
     */
    registerConnection(connectionId: string, userId: string, metadata?: Record<string, any>): Promise<void>;
    /**
     * Unregister a connection
     */
    unregisterConnection(connectionId: string): Promise<void>;
    /**
     * Add connection to group
     */
    addToGroup(connectionId: string, groupName: string): Promise<boolean>;
    /**
     * Remove connection from group
     */
    removeFromGroup(connectionId: string, groupName: string): Promise<boolean>;
    /**
     * Send message to all connections
     */
    broadcast(message: SignalRMessage): Promise<boolean>;
    /**
     * Send message to specific user
     */
    sendToUser(userId: string, message: SignalRMessage): Promise<boolean>;
    /**
     * Send message to group
     */
    sendToGroup(groupName: string, message: SignalRMessage): Promise<boolean>;
    /**
     * Get service metrics
     */
    getMetrics(): SignalRMetrics;
    /**
     * Get connection information
     */
    getConnection(connectionId: string): SignalRConnection | undefined;
    /**
     * Get all connections for a user
     */
    getUserConnections(userId: string): SignalRConnection[];
    /**
     * Get group information
     */
    getGroup(groupName: string): SignalRGroup | undefined;
    private localAddToGroup;
    private localRemoveFromGroup;
    private localBroadcast;
    private localSendToUser;
    private localSendToGroup;
    private storeMessage;
    private startPeriodicTasks;
    private cleanupInactiveConnections;
}
export declare const signalREnhanced: SignalREnhancedService;
