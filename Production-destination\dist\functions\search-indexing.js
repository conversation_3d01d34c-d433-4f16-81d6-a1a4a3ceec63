"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.indexDocument = indexDocument;
exports.searchDocuments = searchDocuments;
/**
 * Search Indexing Function
 * Handles document indexing and search operations
 * Migrated from old-arch/src/search-service/document-indexer/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Search types and enums
var IndexStatus;
(function (IndexStatus) {
    IndexStatus["PENDING"] = "PENDING";
    IndexStatus["INDEXING"] = "INDEXING";
    IndexStatus["INDEXED"] = "INDEXED";
    IndexStatus["FAILED"] = "FAILED";
    IndexStatus["OUTDATED"] = "OUTDATED";
})(IndexStatus || (IndexStatus = {}));
var SearchType;
(function (SearchType) {
    SearchType["FULL_TEXT"] = "FULL_TEXT";
    SearchType["SEMANTIC"] = "SEMANTIC";
    SearchType["HYBRID"] = "HYBRID";
    SearchType["FACETED"] = "FACETED";
})(SearchType || (SearchType = {}));
// Validation schemas
const indexDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    forceReindex: Joi.boolean().default(false),
    extractContent: Joi.boolean().default(true),
    generateEmbeddings: Joi.boolean().default(true),
    updateMetadata: Joi.boolean().default(true)
});
const searchDocumentsSchema = Joi.object({
    query: Joi.string().min(1).max(500).required(),
    type: Joi.string().valid(...Object.values(SearchType)).default(SearchType.HYBRID),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    filters: Joi.object({
        documentTypes: Joi.array().items(Joi.string()).optional(),
        categories: Joi.array().items(Joi.string()).optional(),
        tags: Joi.array().items(Joi.string()).optional(),
        dateRange: Joi.object({
            startDate: Joi.string().isoDate().optional(),
            endDate: Joi.string().isoDate().optional()
        }).optional(),
        authors: Joi.array().items(Joi.string().uuid()).optional(),
        minScore: Joi.number().min(0).max(1).optional()
    }).optional(),
    options: Joi.object({
        page: Joi.number().min(1).default(1),
        limit: Joi.number().min(1).max(100).default(20),
        includeHighlights: Joi.boolean().default(true),
        includeFacets: Joi.boolean().default(false),
        includeContent: Joi.boolean().default(false),
        sortBy: Joi.string().valid('relevance', 'date', 'title', 'author').default('relevance'),
        sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    }).optional()
});
/**
 * Index document handler
 */
async function indexDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Index document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = indexDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const indexRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', indexRequest.documentId, indexRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Check if document is already indexed and not forcing reindex
        const existingIndex = await getExistingIndex(indexRequest.documentId);
        if (existingIndex && !indexRequest.forceReindex) {
            return (0, cors_1.addCorsHeaders)({
                status: 200,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    documentId: indexRequest.documentId,
                    status: existingIndex.status,
                    indexedAt: existingIndex.indexedAt,
                    message: "Document already indexed"
                }
            }, request);
        }
        // Start indexing process
        const indexResult = await performDocumentIndexing(documentData, indexRequest);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_indexed",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: indexRequest.documentId,
            timestamp: new Date().toISOString(),
            details: {
                documentName: documentData.name,
                indexStatus: indexResult.status,
                extractedContent: indexRequest.extractContent,
                generatedEmbeddings: indexRequest.generateEmbeddings,
                keywordCount: indexResult.keywords?.length || 0,
                entityCount: indexResult.extractedEntities?.length || 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentIndexed',
            aggregateId: indexRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentData,
                indexResult,
                indexedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document indexed successfully", {
            correlationId,
            documentId: indexRequest.documentId,
            documentName: documentData.name,
            indexStatus: indexResult.status,
            indexedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: indexRequest.documentId,
                status: indexResult.status,
                indexedAt: indexResult.indexedAt,
                keywordCount: indexResult.keywords?.length || 0,
                entityCount: indexResult.extractedEntities?.length || 0,
                message: "Document indexed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Index document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Search documents handler
 */
async function searchDocuments(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Search documents started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            query: url.searchParams.get('query') || '',
            type: url.searchParams.get('type') || SearchType.HYBRID,
            organizationId: url.searchParams.get('organizationId') || '',
            projectId: url.searchParams.get('projectId') || undefined,
            page: parseInt(url.searchParams.get('page') || '1'),
            limit: parseInt(url.searchParams.get('limit') || '20'),
            includeHighlights: url.searchParams.get('includeHighlights') === 'true',
            includeFacets: url.searchParams.get('includeFacets') === 'true',
            includeContent: url.searchParams.get('includeContent') === 'true',
            sortBy: url.searchParams.get('sortBy') || 'relevance',
            sortOrder: url.searchParams.get('sortOrder') || 'desc'
        };
        // Validate search request
        const { error, value } = searchDocumentsSchema.validate({
            ...queryParams,
            options: {
                page: queryParams.page,
                limit: queryParams.limit,
                includeHighlights: queryParams.includeHighlights,
                includeFacets: queryParams.includeFacets,
                includeContent: queryParams.includeContent,
                sortBy: queryParams.sortBy,
                sortOrder: queryParams.sortOrder
            }
        });
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const searchRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(searchRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Perform search
        const searchResults = await performDocumentSearch(searchRequest, user);
        // Store search in history
        await storeSearchHistory(searchRequest, user, searchResults.totalCount);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "documents_searched",
            userId: user.id,
            organizationId: searchRequest.organizationId,
            projectId: searchRequest.projectId,
            timestamp: new Date().toISOString(),
            details: {
                query: searchRequest.query,
                searchType: searchRequest.type,
                resultCount: searchResults.totalCount,
                page: searchRequest.options?.page || 1
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document search completed", {
            correlationId,
            query: searchRequest.query,
            searchType: searchRequest.type,
            resultCount: searchResults.totalCount,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                query: searchRequest.query,
                results: searchResults.results,
                pagination: {
                    page: searchRequest.options?.page || 1,
                    limit: searchRequest.options?.limit || 20,
                    totalCount: searchResults.totalCount,
                    totalPages: Math.ceil(searchResults.totalCount / (searchRequest.options?.limit || 20))
                },
                facets: searchResults.facets,
                searchTime: searchResults.searchTime,
                suggestions: searchResults.suggestions
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Search documents failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function getExistingIndex(documentId) {
    try {
        return await database_1.db.readItem('search-index', documentId, documentId);
    }
    catch (error) {
        logger_1.logger.error('Failed to get existing index', { error, documentId });
        return null;
    }
}
async function performDocumentIndexing(document, indexRequest) {
    try {
        const now = new Date().toISOString();
        // Extract content if requested
        let content = '';
        if (indexRequest.extractContent) {
            content = await extractDocumentContent(document);
        }
        // Generate embeddings if requested
        let contentVector;
        if (indexRequest.generateEmbeddings) {
            contentVector = await generateContentEmbeddings(content);
        }
        // Extract entities and keywords
        const extractedEntities = await extractEntities(content);
        const keywords = await extractKeywords(content);
        // Create search index
        const searchIndex = {
            id: document.id,
            documentId: document.id,
            title: document.name,
            content,
            contentVector,
            metadata: {
                fileName: document.name,
                contentType: document.contentType || 'application/octet-stream',
                size: document.size || 0,
                author: document.createdBy,
                createdAt: document.createdAt,
                updatedAt: document.updatedAt,
                category: document.category,
                tags: document.tags || [],
                organizationId: document.organizationId,
                projectId: document.projectId
            },
            extractedEntities,
            keywords,
            language: 'en', // Simplified - would detect language in production
            status: IndexStatus.INDEXED,
            indexedAt: now,
            tenantId: document.tenantId
        };
        // Store in search index
        await database_1.db.upsertItem('search-index', searchIndex);
        // Cache frequently accessed data in Redis
        await redis_1.redis.setex(`search:doc:${document.id}`, 3600, JSON.stringify({
            title: searchIndex.title,
            content: searchIndex.content.substring(0, 1000), // First 1000 chars
            keywords: searchIndex.keywords,
            metadata: searchIndex.metadata
        }));
        return searchIndex;
    }
    catch (error) {
        logger_1.logger.error('Failed to perform document indexing', { error, documentId: document.id });
        // Create failed index record
        const failedIndex = {
            id: document.id,
            documentId: document.id,
            title: document.name,
            content: '',
            metadata: {
                fileName: document.name,
                contentType: document.contentType || 'application/octet-stream',
                size: document.size || 0,
                author: document.createdBy,
                createdAt: document.createdAt,
                updatedAt: document.updatedAt,
                organizationId: document.organizationId,
                projectId: document.projectId,
                tags: []
            },
            extractedEntities: [],
            keywords: [],
            language: 'en',
            status: IndexStatus.FAILED,
            indexedAt: new Date().toISOString(),
            tenantId: document.tenantId
        };
        await database_1.db.upsertItem('search-index', failedIndex);
        return failedIndex;
    }
}
async function extractDocumentContent(document) {
    try {
        // In production, this would extract content from the actual document
        // For now, return mock content
        return `Mock extracted content from document: ${document.name}. This would contain the actual text content extracted from the document using appropriate text extraction libraries.`;
    }
    catch (error) {
        logger_1.logger.error('Failed to extract document content', { error, documentId: document.id });
        return '';
    }
}
async function generateContentEmbeddings(content) {
    try {
        // In production, this would generate actual embeddings using AI models
        // For now, return mock embeddings
        const mockEmbedding = Array.from({ length: 384 }, () => Math.random() * 2 - 1);
        return mockEmbedding;
    }
    catch (error) {
        logger_1.logger.error('Failed to generate content embeddings', { error });
        return [];
    }
}
async function extractEntities(content) {
    try {
        // In production, this would use NLP models to extract entities
        // For now, return mock entities
        return [
            { name: 'Acme Corporation', type: 'organization', confidence: 0.95 },
            { name: 'John Smith', type: 'person', confidence: 0.88 },
            { name: 'New York', type: 'location', confidence: 0.92 }
        ];
    }
    catch (error) {
        logger_1.logger.error('Failed to extract entities', { error });
        return [];
    }
}
async function extractKeywords(content) {
    try {
        // In production, this would use keyword extraction algorithms
        // For now, return mock keywords
        const words = content.toLowerCase().split(/\W+/).filter(word => word.length > 3);
        const uniqueWords = [...new Set(words)];
        return uniqueWords.slice(0, 20); // Top 20 keywords
    }
    catch (error) {
        logger_1.logger.error('Failed to extract keywords', { error });
        return [];
    }
}
async function performDocumentSearch(searchRequest, user) {
    try {
        const startTime = Date.now();
        // Production Azure Cognitive Search integration
        const searchEndpoint = process.env.AZURE_SEARCH_ENDPOINT;
        const searchKey = process.env.AZURE_SEARCH_KEY;
        const indexName = process.env.AZURE_SEARCH_INDEX_NAME || 'documents';
        if (!searchEndpoint || !searchKey) {
            logger_1.logger.warn('Azure Cognitive Search not configured, falling back to database search');
            return await performDatabaseSearch(searchRequest, user);
        }
        // Build Azure Cognitive Search query
        const searchOptions = {
            searchText: searchRequest.query || '*',
            searchMode: searchRequest.type === SearchType.SEMANTIC ? 'all' : 'any',
            queryType: searchRequest.type === SearchType.SEMANTIC ? 'semantic' : 'simple',
            top: searchRequest.options?.limit || 20,
            skip: ((searchRequest.options?.page || 1) - 1) * (searchRequest.options?.limit || 20),
            includeTotalCount: true,
            searchFields: ['title', 'content', 'keywords', 'extractedText'],
            select: ['id', 'documentId', 'title', 'content', 'metadata', 'keywords', 'extractedEntities', 'score'],
            filter: buildSearchFilter(searchRequest, user),
            orderBy: buildSearchOrderBy(searchRequest.options?.sortBy, searchRequest.options?.sortOrder),
            facets: searchRequest.options?.includeFacets ? ['metadata/contentType', 'metadata/tags', 'metadata/author'] : undefined,
            highlight: searchRequest.options?.includeHighlights ? 'title,content' : undefined,
            highlightPreTag: '<mark>',
            highlightPostTag: '</mark>'
        };
        // Execute search via Azure Cognitive Search REST API
        const searchResults = await executeAzureCognitiveSearch(searchEndpoint, searchKey, indexName, searchOptions);
        const processingTime = Date.now() - startTime;
        logger_1.logger.info('Azure Cognitive Search completed', {
            query: searchRequest.query,
            totalResults: searchResults.totalCount,
            returnedResults: searchResults.results.length,
            processingTime,
            searchMode: searchOptions.searchMode,
            queryType: searchOptions.queryType
        });
        return {
            results: searchResults.results,
            totalCount: searchResults.totalCount,
            page: searchRequest.options?.page || 1,
            limit: searchRequest.options?.limit || 20,
            totalPages: Math.ceil(searchResults.totalCount / (searchRequest.options?.limit || 20)),
            processingTime,
            facets: searchResults.facets,
            highlights: searchResults.highlights
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to perform document search', { error, query: searchRequest.query });
        return {
            results: [],
            totalCount: 0,
            searchTime: 0,
            suggestions: []
        };
    }
}
function generateHighlights(result, query) {
    if (!query)
        return [];
    const highlights = [];
    const queryLower = query.toLowerCase();
    // Title highlights
    if (result.title.toLowerCase().includes(queryLower)) {
        highlights.push({
            field: 'title',
            fragments: [result.title.replace(new RegExp(query, 'gi'), `<mark>$&</mark>`)]
        });
    }
    // Content highlights
    if (result.content.toLowerCase().includes(queryLower)) {
        const contentFragment = result.content.substring(0, 200);
        highlights.push({
            field: 'content',
            fragments: [contentFragment.replace(new RegExp(query, 'gi'), `<mark>$&</mark>`)]
        });
    }
    return highlights;
}
function generateFacets(searchResults) {
    const facets = {
        contentTypes: {},
        authors: {},
        tags: {}
    };
    searchResults.forEach(result => {
        // Content type facets
        const contentType = result.metadata.contentType;
        facets.contentTypes[contentType] = (facets.contentTypes[contentType] || 0) + 1;
        // Author facets
        const author = result.metadata.author;
        facets.authors[author] = (facets.authors[author] || 0) + 1;
        // Tag facets
        if (result.metadata.tags && Array.isArray(result.metadata.tags)) {
            result.metadata.tags.forEach((tag) => {
                facets.tags[tag] = (facets.tags[tag] || 0) + 1;
            });
        }
    });
    return facets;
}
function generateSearchSuggestions(query) {
    // In production, this would use search analytics and ML models
    // For now, return simple suggestions
    if (!query)
        return [];
    return [
        `${query} document`,
        `${query} report`,
        `${query} analysis`
    ];
}
async function storeSearchHistory(searchRequest, user, resultCount) {
    try {
        await database_1.db.createItem('search-history', {
            id: (0, uuid_1.v4)(),
            userId: user.id,
            organizationId: searchRequest.organizationId,
            projectId: searchRequest.projectId,
            query: searchRequest.query,
            searchType: searchRequest.type,
            resultCount,
            timestamp: new Date().toISOString(),
            tenantId: user.tenantId
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to store search history', { error, userId: user.id, query: searchRequest.query });
    }
}
/**
 * Execute Azure Cognitive Search via REST API
 */
async function executeAzureCognitiveSearch(endpoint, key, indexName, searchOptions) {
    try {
        const url = `${endpoint}/indexes/${indexName}/docs/search?api-version=2023-11-01`;
        const requestBody = {
            search: searchOptions.searchText,
            searchMode: searchOptions.searchMode,
            queryType: searchOptions.queryType,
            top: searchOptions.top,
            skip: searchOptions.skip,
            count: searchOptions.includeTotalCount,
            searchFields: searchOptions.searchFields?.join(','),
            select: searchOptions.select?.join(','),
            filter: searchOptions.filter,
            orderby: searchOptions.orderBy?.join(','),
            facets: searchOptions.facets,
            highlight: searchOptions.highlight,
            highlightPreTag: searchOptions.highlightPreTag,
            highlightPostTag: searchOptions.highlightPostTag
        };
        const response = await fetch(url, {
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'api-key': key
            },
            body: JSON.stringify(requestBody)
        });
        if (!response.ok) {
            const errorText = await response.text();
            throw new Error(`Azure Cognitive Search request failed: ${response.status} ${response.statusText} - ${errorText}`);
        }
        const result = await response.json();
        return {
            results: result.value || [],
            totalCount: result['@odata.count'] || 0,
            facets: result['@search.facets'],
            highlights: result['@search.highlights']
        };
    }
    catch (error) {
        logger_1.logger.error('Azure Cognitive Search execution failed', {
            error: error instanceof Error ? error.message : String(error),
            endpoint,
            indexName
        });
        throw error;
    }
}
/**
 * Build search filter for Azure Cognitive Search
 */
function buildSearchFilter(searchRequest, user) {
    const filters = [];
    // Add tenant isolation
    filters.push(`metadata/organizationId eq '${searchRequest.organizationId}'`);
    // Add project filter if specified
    if (searchRequest.projectId) {
        filters.push(`metadata/projectId eq '${searchRequest.projectId}'`);
    }
    // Add document type filters
    if (searchRequest.filters?.documentTypes && searchRequest.filters.documentTypes.length > 0) {
        const typeFilters = searchRequest.filters.documentTypes
            .map(type => `metadata/contentType eq '${type}'`)
            .join(' or ');
        filters.push(`(${typeFilters})`);
    }
    // Add tag filters
    if (searchRequest.filters?.tags && searchRequest.filters.tags.length > 0) {
        const tagFilters = searchRequest.filters.tags
            .map(tag => `metadata/tags/any(t: t eq '${tag}')`)
            .join(' or ');
        filters.push(`(${tagFilters})`);
    }
    // Add date range filters
    if (searchRequest.filters?.dateRange?.startDate) {
        filters.push(`metadata/createdAt ge ${searchRequest.filters.dateRange.startDate}`);
    }
    if (searchRequest.filters?.dateRange?.endDate) {
        filters.push(`metadata/createdAt le ${searchRequest.filters.dateRange.endDate}`);
    }
    // Add author filters
    if (searchRequest.filters?.authors && searchRequest.filters.authors.length > 0) {
        const authorFilters = searchRequest.filters.authors
            .map(author => `metadata/author eq '${author}'`)
            .join(' or ');
        filters.push(`(${authorFilters})`);
    }
    return filters.join(' and ');
}
/**
 * Build order by clause for Azure Cognitive Search
 */
function buildSearchOrderBy(sortBy, sortOrder) {
    if (!sortBy || sortBy === 'relevance') {
        return undefined; // Use default relevance scoring
    }
    const order = sortOrder === 'asc' ? 'asc' : 'desc';
    switch (sortBy) {
        case 'date':
            return [`metadata/createdAt ${order}`];
        case 'title':
            return [`title ${order}`];
        case 'author':
            return [`metadata/author ${order}`];
        default:
            return undefined;
    }
}
/**
 * Fallback database search when Azure Cognitive Search is not available
 */
async function performDatabaseSearch(searchRequest, user) {
    try {
        // Build basic database query
        let query = 'SELECT * FROM c WHERE c.metadata.organizationId = @orgId';
        const parameters = [searchRequest.organizationId];
        if (searchRequest.projectId) {
            query += ' AND c.metadata.projectId = @projectId';
            parameters.push(searchRequest.projectId);
        }
        // Add text search
        if (searchRequest.query) {
            query += ' AND (CONTAINS(LOWER(c.title), LOWER(@query)) OR CONTAINS(LOWER(c.content), LOWER(@query)))';
            parameters.push(searchRequest.query);
        }
        // Execute search
        const results = await database_1.db.queryItems('search-index', query, parameters);
        // Apply pagination
        const page = searchRequest.options?.page || 1;
        const limit = searchRequest.options?.limit || 20;
        const offset = (page - 1) * limit;
        const paginatedResults = results.slice(offset, offset + limit);
        return {
            results: paginatedResults,
            totalCount: results.length,
            page,
            limit,
            totalPages: Math.ceil(results.length / limit),
            processingTime: 0,
            facets: undefined,
            highlights: undefined
        };
    }
    catch (error) {
        logger_1.logger.error('Database search fallback failed', {
            error: error instanceof Error ? error.message : String(error),
            query: searchRequest.query
        });
        throw error;
    }
}
// Register functions
functions_1.app.http('search-index-document', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'search/index',
    handler: indexDocument
});
functions_1.app.http('search-documents', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'search/documents',
    handler: searchDocuments
});
//# sourceMappingURL=search-indexing.js.map