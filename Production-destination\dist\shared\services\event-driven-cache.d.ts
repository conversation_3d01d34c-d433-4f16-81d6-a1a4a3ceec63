/**
 * Event-Driven Cache Service
 * Integrates with Azure Event Grid for real-time cache invalidation
 * Provides automatic cache warming based on usage patterns
 */
export interface CacheInvalidationEvent {
    eventType: 'document.updated' | 'user.updated' | 'session.expired' | 'device.updated';
    resourceId: string;
    organizationId?: string;
    userId?: string;
    timestamp: Date;
    metadata?: any;
}
export interface CacheWarmingConfig {
    enabled: boolean;
    patterns: {
        documents: boolean;
        sessions: boolean;
        userActivities: boolean;
        deviceRegistrations: boolean;
        featureFlags: boolean;
        systemConfig: boolean;
    };
    priorities: {
        high: string[];
        medium: string[];
        low: string[];
    };
    frequencies: {
        high: number;
        medium: number;
        low: number;
    };
}
export declare class EventDrivenCacheService {
    private static instance;
    private warmingConfig;
    private isInitialized;
    private constructor();
    static getInstance(): EventDrivenCacheService;
    /**
     * Initialize event-driven cache system
     */
    initialize(): Promise<void>;
    /**
     * Setup cache warming rules based on configuration
     */
    private setupWarmingRules;
    /**
     * Add warming rule for specific pattern
     */
    private addWarmingRule;
    /**
     * Get database query configuration for cache pattern
     */
    private getDbQueryForPattern;
    /**
     * Get TTL based on priority
     */
    private getTtlForPriority;
    /**
     * Setup event listeners for cache invalidation
     */
    private setupEventListeners;
    /**
     * Handle cache events
     */
    private handleCacheEvent;
    /**
     * Publish cache event to Event Grid
     */
    private publishCacheEvent;
    /**
     * Handle cache warming event
     */
    private handleWarmingEvent;
    /**
     * Process cache invalidation event from external source
     */
    processInvalidationEvent(event: CacheInvalidationEvent): Promise<void>;
    /**
     * Get cache patterns to invalidate based on event type
     */
    private getInvalidationPatterns;
    /**
     * Get cache warming and event processing statistics
     */
    getStatistics(): any;
    /**
     * Update warming configuration
     */
    updateWarmingConfig(config: Partial<CacheWarmingConfig>): void;
}
export declare const eventDrivenCache: EventDrivenCacheService;
export default eventDrivenCache;
