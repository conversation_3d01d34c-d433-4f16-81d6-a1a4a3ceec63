"use strict";
/**
 * Organization Models and Interfaces
 * Defines the structure for organization-related data models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ProjectRole = exports.ProjectStatus = exports.MemberStatus = exports.OrganizationRole = exports.OrganizationStatus = exports.OrganizationTier = exports.OrganizationSize = void 0;
var OrganizationSize;
(function (OrganizationSize) {
    OrganizationSize["STARTUP"] = "STARTUP";
    OrganizationSize["SMALL"] = "SMALL";
    OrganizationSize["MEDIUM"] = "MEDIUM";
    OrganizationSize["LARGE"] = "LARGE";
    OrganizationSize["ENTERPRISE"] = "ENTERPRISE";
})(OrganizationSize || (exports.OrganizationSize = OrganizationSize = {}));
var OrganizationTier;
(function (OrganizationTier) {
    OrganizationTier["FREE"] = "FREE";
    OrganizationTier["BASIC"] = "BASIC";
    OrganizationTier["PROFESSIONAL"] = "PROFESSIONAL";
    OrganizationTier["ENTERPRISE"] = "ENTERPRISE";
    OrganizationTier["CUSTOM"] = "CUSTOM";
})(OrganizationTier || (exports.OrganizationTier = OrganizationTier = {}));
var OrganizationStatus;
(function (OrganizationStatus) {
    OrganizationStatus["ACTIVE"] = "ACTIVE";
    OrganizationStatus["INACTIVE"] = "INACTIVE";
    OrganizationStatus["SUSPENDED"] = "SUSPENDED";
    OrganizationStatus["TRIAL"] = "TRIAL";
    OrganizationStatus["EXPIRED"] = "EXPIRED";
    OrganizationStatus["DELETED"] = "DELETED";
})(OrganizationStatus || (exports.OrganizationStatus = OrganizationStatus = {}));
var OrganizationRole;
(function (OrganizationRole) {
    OrganizationRole["OWNER"] = "OWNER";
    OrganizationRole["ADMIN"] = "ADMIN";
    OrganizationRole["MEMBER"] = "MEMBER";
    OrganizationRole["VIEWER"] = "VIEWER";
    OrganizationRole["GUEST"] = "GUEST";
})(OrganizationRole || (exports.OrganizationRole = OrganizationRole = {}));
var MemberStatus;
(function (MemberStatus) {
    MemberStatus["ACTIVE"] = "ACTIVE";
    MemberStatus["INACTIVE"] = "INACTIVE";
    MemberStatus["PENDING"] = "PENDING";
    MemberStatus["SUSPENDED"] = "SUSPENDED";
    MemberStatus["LEFT"] = "LEFT";
    MemberStatus["REMOVED"] = "REMOVED";
})(MemberStatus || (exports.MemberStatus = MemberStatus = {}));
var ProjectStatus;
(function (ProjectStatus) {
    ProjectStatus["PLANNING"] = "PLANNING";
    ProjectStatus["ACTIVE"] = "ACTIVE";
    ProjectStatus["ON_HOLD"] = "ON_HOLD";
    ProjectStatus["COMPLETED"] = "COMPLETED";
    ProjectStatus["CANCELLED"] = "CANCELLED";
    ProjectStatus["ARCHIVED"] = "ARCHIVED";
})(ProjectStatus || (exports.ProjectStatus = ProjectStatus = {}));
var ProjectRole;
(function (ProjectRole) {
    ProjectRole["OWNER"] = "OWNER";
    ProjectRole["MANAGER"] = "MANAGER";
    ProjectRole["CONTRIBUTOR"] = "CONTRIBUTOR";
    ProjectRole["VIEWER"] = "VIEWER";
})(ProjectRole || (exports.ProjectRole = ProjectRole = {}));
//# sourceMappingURL=organization.js.map