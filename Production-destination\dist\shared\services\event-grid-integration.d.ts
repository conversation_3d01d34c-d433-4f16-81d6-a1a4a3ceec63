/**
 * Enhanced Event Grid Integration Service
 * Provides comprehensive Event Grid integration capabilities with production-ready features:
 * - Advanced filtering and routing
 * - Event batching and throttling
 * - Dead letter handling
 * - Custom retry policies
 * - Event validation and schema registry
 * - Performance monitoring
 * - Multi-topic publishing
 * - Event deduplication
 */
export interface EventGridConfig {
    endpoint: string;
    accessKey: string;
    retryAttempts?: number;
    timeoutMs?: number;
    batchSize?: number;
    throttleMs?: number;
}
export interface CustomEventData {
    eventType: string;
    subject: string;
    data: any;
    dataVersion?: string;
    eventTime?: Date;
    id?: string;
    topic?: string;
    filters?: EventFilter[];
}
export interface EventSubscription {
    name: string;
    eventTypes: string[];
    endpoint: string;
    filters?: EventFilter[];
}
export interface EventFilter {
    field: string;
    operator: 'equals' | 'contains' | 'startsWith' | 'endsWith' | 'in' | 'notIn';
    value: string | string[];
}
export interface EventSchema {
    eventType: string;
    version: string;
    schema: any;
    required: string[];
}
export interface EventMetrics {
    totalEvents: number;
    successfulEvents: number;
    failedEvents: number;
    averageProcessingTime: number;
    lastEventTime?: Date;
    eventsFiltered: number;
    eventsDeadLettered: number;
    eventsBatched: number;
    throttleCount: number;
}
export interface RetryPolicy {
    maxRetries: number;
    retryDelay: number;
    exponentialBackoff: boolean;
    maxRetryDelay: number;
}
/**
 * Event Grid Integration Service Class
 */
export declare class EventGridIntegrationService {
    private clients;
    private config;
    private metrics;
    private eventQueue;
    private schemas;
    private isProcessing;
    private lastBatchTime;
    private defaultRetryPolicy;
    constructor(config: EventGridConfig);
    /**
     * Initialize Event Grid client
     */
    private initializeClient;
    /**
     * Register event schema for validation
     */
    registerEventSchema(schema: EventSchema): void;
    /**
     * Queue event for batch processing
     */
    queueEvent(eventData: CustomEventData): Promise<void>;
    /**
     * Publish a single event with enhanced features
     */
    publishEvent(eventData: CustomEventData, retryPolicy?: RetryPolicy): Promise<string | null>;
    /**
     * Publish multiple events in batch
     */
    publishEvents(events: CustomEventData[]): Promise<string[]>;
    /**
     * Publish event with retry logic
     */
    publishEventWithRetry(eventData: CustomEventData): Promise<string | null>;
    /**
     * Enhanced event validation with schema support
     */
    validateEventData(eventData: CustomEventData): Promise<boolean>;
    /**
     * Apply event filters
     */
    private applyFilters;
    /**
     * Evaluate a single filter
     */
    private evaluateFilter;
    /**
     * Get field value from event data using dot notation
     */
    private getFieldValue;
    /**
     * Update metrics
     */
    private updateMetrics;
    /**
     * Get current metrics
     */
    getMetrics(): EventMetrics;
    /**
     * Reset metrics
     */
    resetMetrics(): void;
    private generateEventId;
    private getClient;
    private getTopicEndpoint;
    private isDuplicateEvent;
    private markEventAsPublished;
    private calculateRetryDelay;
    private sendToDeadLetter;
    private startBatchProcessing;
    private processBatch;
    private startPeriodicTasks;
    /**
     * Health check
     */
    healthCheck(): Promise<boolean>;
}
/**
 * Create Event Grid Integration Service instance
 */
export declare function createEventGridIntegration(): EventGridIntegrationService;
export declare const eventGridIntegration: EventGridIntegrationService;
