"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createFeatureFlag = createFeatureFlag;
exports.evaluateFeatureFlag = evaluateFeatureFlag;
/**
 * Feature Flags Function
 * Handles feature flag management and evaluation
 * Migrated from old-arch/src/admin-service/feature-flags/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
const event_1 = require("../shared/services/event");
// Feature flag types and enums
var FeatureFlagType;
(function (FeatureFlagType) {
    FeatureFlagType["BOOLEAN"] = "BOOLEAN";
    FeatureFlagType["STRING"] = "STRING";
    FeatureFlagType["NUMBER"] = "NUMBER";
    FeatureFlagType["JSON"] = "JSON";
    FeatureFlagType["PERCENTAGE"] = "PERCENTAGE";
})(FeatureFlagType || (FeatureFlagType = {}));
var FeatureFlagStatus;
(function (FeatureFlagStatus) {
    FeatureFlagStatus["ACTIVE"] = "ACTIVE";
    FeatureFlagStatus["INACTIVE"] = "INACTIVE";
    FeatureFlagStatus["ARCHIVED"] = "ARCHIVED";
})(FeatureFlagStatus || (FeatureFlagStatus = {}));
var TargetType;
(function (TargetType) {
    TargetType["ALL"] = "ALL";
    TargetType["USER"] = "USER";
    TargetType["ORGANIZATION"] = "ORGANIZATION";
    TargetType["TENANT"] = "TENANT";
    TargetType["PERCENTAGE"] = "PERCENTAGE";
})(TargetType || (TargetType = {}));
// Validation schemas
const createFeatureFlagSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    key: Joi.string().pattern(/^[a-zA-Z0-9_-]+$/).min(2).max(50).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(FeatureFlagType)).required(),
    defaultValue: Joi.any().required(),
    variations: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        value: Joi.any().required(),
        description: Joi.string().optional()
    })).optional(),
    targeting: Joi.object({
        enabled: Joi.boolean().default(false),
        rules: Joi.array().items(Joi.object({
            id: Joi.string().optional(),
            name: Joi.string().required(),
            targetType: Joi.string().valid(...Object.values(TargetType)).required(),
            targets: Joi.array().items(Joi.string()).optional(),
            percentage: Joi.number().min(0).max(100).optional(),
            value: Joi.any().required(),
            enabled: Joi.boolean().default(true)
        })).optional()
    }).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(20).optional(),
    metadata: Joi.object().optional()
});
const evaluateFeatureFlagSchema = Joi.object({
    flagKey: Joi.string().required(),
    context: Joi.object({
        userId: Joi.string().uuid().optional(),
        organizationId: Joi.string().uuid().optional(),
        tenantId: Joi.string().uuid().optional(),
        userAgent: Joi.string().optional(),
        ipAddress: Joi.string().ip().optional(),
        custom: Joi.object().optional()
    }).optional()
});
/**
 * Create feature flag handler
 */
async function createFeatureFlag(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create feature flag started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkFeatureFlagAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to feature flag management" }
            }, request);
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = createFeatureFlagSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const flagRequest = value;
        // Check if flag key already exists
        const keyExists = await checkFeatureFlagKeyExists(flagRequest.key);
        if (keyExists) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Feature flag key already exists" }
            }, request);
        }
        // Create feature flag
        const flagId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const featureFlag = {
            id: flagId,
            name: flagRequest.name,
            key: flagRequest.key,
            description: flagRequest.description,
            type: flagRequest.type,
            status: FeatureFlagStatus.ACTIVE,
            defaultValue: flagRequest.defaultValue,
            variations: (flagRequest.variations || []).map((variation) => ({
                id: (0, uuid_1.v4)(),
                name: variation.name,
                targetType: variation.targetType || 'USER',
                targets: variation.targets || [],
                percentage: variation.percentage || 0,
                value: variation.value,
                enabled: true
            })),
            targeting: {
                enabled: false,
                rules: (flagRequest.targeting?.rules || []),
                ...flagRequest.targeting
            },
            tags: flagRequest.tags || [],
            metadata: flagRequest.metadata || {},
            statistics: {
                evaluationCount: 0,
                variationCounts: {}
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        // Add IDs to targeting rules
        if (featureFlag.targeting.rules) {
            featureFlag.targeting.rules = featureFlag.targeting.rules.map(rule => ({
                ...rule,
                id: rule.id || (0, uuid_1.v4)(),
                enabled: rule.enabled !== false
            }));
        }
        await database_1.db.createItem('feature-flags', featureFlag);
        // Cache feature flag for fast evaluation
        await cacheFeatureFlag(featureFlag);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "feature_flag_created",
            userId: user.id,
            timestamp: now,
            details: {
                flagId,
                flagKey: flagRequest.key,
                flagName: flagRequest.name,
                flagType: flagRequest.type,
                targetingEnabled: featureFlag.targeting.enabled,
                ruleCount: featureFlag.targeting.rules?.length || 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'FeatureFlagCreated',
            aggregateId: flagId,
            aggregateType: 'FeatureFlag',
            version: 1,
            data: {
                featureFlag,
                createdBy: user.id
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Feature flag created successfully", {
            correlationId,
            flagId,
            flagKey: flagRequest.key,
            flagName: flagRequest.name,
            flagType: flagRequest.type,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: flagId,
                name: flagRequest.name,
                key: flagRequest.key,
                type: flagRequest.type,
                status: FeatureFlagStatus.ACTIVE,
                defaultValue: flagRequest.defaultValue,
                targeting: featureFlag.targeting,
                createdAt: now,
                message: "Feature flag created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create feature flag failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Evaluate feature flag handler
 */
async function evaluateFeatureFlag(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Evaluate feature flag started", { correlationId });
    try {
        // Parse query parameters or body
        let evaluationRequest;
        if (request.method === 'GET') {
            const url = new URL(request.url);
            evaluationRequest = {
                flagKey: url.searchParams.get('flagKey'),
                context: {
                    userId: url.searchParams.get('userId'),
                    organizationId: url.searchParams.get('organizationId'),
                    tenantId: url.searchParams.get('tenantId')
                }
            };
        }
        else {
            const body = await request.json();
            evaluationRequest = body;
        }
        // Validate request
        const { error, value } = evaluateFeatureFlagSchema.validate(evaluationRequest);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { flagKey, context } = value;
        // Get feature flag (try cache first)
        const featureFlag = await getFeatureFlag(flagKey);
        if (!featureFlag) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Feature flag not found" }
            }, request);
        }
        // Evaluate feature flag
        const evaluation = await evaluateFlag(featureFlag, context || {});
        // Update statistics
        await updateFlagStatistics(featureFlag.id, evaluation.value);
        logger_1.logger.info("Feature flag evaluated successfully", {
            correlationId,
            flagKey,
            flagValue: evaluation.value,
            reason: evaluation.reason,
            userId: context?.userId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                flagKey,
                value: evaluation.value,
                reason: evaluation.reason,
                ruleId: evaluation.ruleId,
                evaluatedAt: new Date().toISOString()
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Evaluate feature flag failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkFeatureFlagAccess(user) {
    try {
        // Check if user has admin or feature flag management role
        return user.roles?.includes('admin') || user.roles?.includes('feature_flag_admin');
    }
    catch (error) {
        logger_1.logger.error('Failed to check feature flag access', { error, userId: user.id });
        return false;
    }
}
async function checkFeatureFlagKeyExists(key) {
    try {
        const existingQuery = 'SELECT * FROM c WHERE c.key = @key';
        const existing = await database_1.db.queryItems('feature-flags', existingQuery, [key]);
        return existing.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check feature flag key exists', { error, key });
        return false;
    }
}
async function cacheFeatureFlag(featureFlag) {
    try {
        const cacheKey = `feature_flag:${featureFlag.key}`;
        const flagData = JSON.stringify(featureFlag);
        await redis_1.redis.setex(cacheKey, 300, flagData); // 5 minutes cache
        // Also cache in a set for bulk operations
        await redis_1.redis.sadd('feature_flags:active', featureFlag.key);
    }
    catch (error) {
        logger_1.logger.error('Failed to cache feature flag', { error, flagId: featureFlag.id });
    }
}
async function getFeatureFlag(key) {
    try {
        const { cacheAside } = await Promise.resolve().then(() => __importStar(require('../shared/services/cache-aside')));
        return await cacheAside.get(key, {
            containerName: 'feature-flags',
            query: 'SELECT * FROM c WHERE c.key = @key AND c.status = @active',
            parameters: [key, FeatureFlagStatus.ACTIVE]
        }, {
            ttlSeconds: 300, // 5 minutes cache
            cachePrefix: 'feature_flag',
            enableFallback: true,
            enableWarming: true,
            warmingPriority: 'high',
            eventDriven: true
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to get feature flag', { error, key });
        return null;
    }
}
async function evaluateFlag(featureFlag, context) {
    try {
        // If targeting is disabled, return default value
        if (!featureFlag.targeting.enabled) {
            return {
                value: featureFlag.defaultValue,
                reason: 'DEFAULT',
                ruleId: null
            };
        }
        // Evaluate targeting rules in order
        for (const rule of featureFlag.targeting.rules || []) {
            if (!rule.enabled)
                continue;
            const matches = await evaluateRule(rule, context);
            if (matches) {
                return {
                    value: rule.value,
                    reason: 'RULE_MATCH',
                    ruleId: rule.id
                };
            }
        }
        // No rules matched, return default
        return {
            value: featureFlag.defaultValue,
            reason: 'DEFAULT',
            ruleId: null
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to evaluate feature flag', { error, flagId: featureFlag.id });
        return {
            value: featureFlag.defaultValue,
            reason: 'ERROR',
            ruleId: null
        };
    }
}
async function evaluateRule(rule, context) {
    try {
        switch (rule.targetType) {
            case TargetType.ALL:
                return true;
            case TargetType.USER:
                return context.userId && rule.targets?.includes(context.userId);
            case TargetType.ORGANIZATION:
                return context.organizationId && rule.targets?.includes(context.organizationId);
            case TargetType.TENANT:
                return context.tenantId && rule.targets?.includes(context.tenantId);
            case TargetType.PERCENTAGE:
                if (rule.percentage !== undefined) {
                    // Use consistent hashing for percentage rollouts
                    const hash = hashString(context.userId || context.organizationId || 'anonymous');
                    const percentage = (hash % 100) + 1;
                    return percentage <= rule.percentage;
                }
                return false;
            default:
                return false;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to evaluate rule', { error, ruleId: rule.id });
        return false;
    }
}
function hashString(str) {
    let hash = 0;
    for (let i = 0; i < str.length; i++) {
        const char = str.charCodeAt(i);
        hash = ((hash << 5) - hash) + char;
        hash = hash & hash; // Convert to 32-bit integer
    }
    return Math.abs(hash);
}
async function updateFlagStatistics(flagId, value) {
    try {
        // Update statistics in Redis for performance
        const statsKey = `feature_flag_stats:${flagId}`;
        const valueKey = JSON.stringify(value);
        await redis_1.redis.hincrby(statsKey, 'evaluationCount', 1);
        await redis_1.redis.hincrby(statsKey, `variation:${valueKey}`, 1);
        await redis_1.redis.hset(statsKey, 'lastEvaluated', new Date().toISOString());
        await redis_1.redis.expire(statsKey, 86400); // 24 hours
    }
    catch (error) {
        logger_1.logger.error('Failed to update flag statistics', { error, flagId });
    }
}
// Register functions
functions_1.app.http('feature-flag-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'management/feature-flags/create',
    handler: createFeatureFlag
});
functions_1.app.http('feature-flag-evaluate', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'feature-flags/evaluate',
    handler: evaluateFeatureFlag
});
//# sourceMappingURL=feature-flags.js.map