/**
 * Project Settings Function
 * Handles project settings, configuration, and policy management
 * Migrated from old-arch/src/project-service/settings/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get project settings handler
 */
export declare function getProjectSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update project settings handler
 */
export declare function updateProjectSettings(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
