"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createAIOperation = createAIOperation;
exports.getAIOperationStatus = getAIOperationStatus;
/**
 * AI Orchestration Hub Function
 * Central hub for coordinating AI operations across different services
 * Migrated from old-arch/src/ai-service/orchestration-hub/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// AI operation types
var AIOperationType;
(function (AIOperationType) {
    AIOperationType["DOCUMENT_ANALYSIS"] = "DOCUMENT_ANALYSIS";
    AIOperationType["CONTENT_GENERATION"] = "CONTENT_GENERATION";
    AIOperationType["CONTENT_COMPLETION"] = "CONTENT_COMPLETION";
    AIOperationType["DOCUMENT_SUMMARIZATION"] = "DOCUMENT_SUMMARIZATION";
    AIOperationType["INTELLIGENT_SEARCH"] = "INTELLIGENT_SEARCH";
    AIOperationType["WORKFLOW_OPTIMIZATION"] = "WORKFLOW_OPTIMIZATION";
    AIOperationType["BATCH_PROCESSING"] = "BATCH_PROCESSING";
})(AIOperationType || (AIOperationType = {}));
var AIOperationStatus;
(function (AIOperationStatus) {
    AIOperationStatus["PENDING"] = "PENDING";
    AIOperationStatus["RUNNING"] = "RUNNING";
    AIOperationStatus["COMPLETED"] = "COMPLETED";
    AIOperationStatus["FAILED"] = "FAILED";
    AIOperationStatus["CANCELLED"] = "CANCELLED";
})(AIOperationStatus || (AIOperationStatus = {}));
var AIOperationPriority;
(function (AIOperationPriority) {
    AIOperationPriority["LOW"] = "LOW";
    AIOperationPriority["NORMAL"] = "NORMAL";
    AIOperationPriority["HIGH"] = "HIGH";
    AIOperationPriority["URGENT"] = "URGENT";
})(AIOperationPriority || (AIOperationPriority = {}));
// Validation schema
const aiOperationSchema = Joi.object({
    operationType: Joi.string().valid(...Object.values(AIOperationType)).required(),
    priority: Joi.string().valid(...Object.values(AIOperationPriority)).default(AIOperationPriority.NORMAL),
    parameters: Joi.object({
        documentIds: Joi.array().items(Joi.string().uuid()).optional(),
        workflowId: Joi.string().uuid().optional(),
        prompt: Joi.string().max(2000).optional(),
        options: Joi.object().optional(),
        targetFormat: Joi.string().optional(),
        customInstructions: Joi.string().max(1000).optional()
    }).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    callbackUrl: Joi.string().uri().optional(),
    metadata: Joi.object().optional()
});
/**
 * Create AI operation handler
 */
async function createAIOperation(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create AI operation started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = aiOperationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const operationRequest = value;
        // Validate organization access
        const organization = await database_1.db.readItem('organizations', operationRequest.organizationId, operationRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user has access to the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, operationRequest.organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check AI operation limits for organization tier
        const orgData = organization;
        if (await isAIOperationLimitReached(operationRequest.organizationId, orgData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "AI operation limit reached for this organization tier",
                    tier: orgData.tier
                }
            }, request);
        }
        // Create AI operation
        const operationId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const aiOperation = {
            id: operationId,
            operationType: operationRequest.operationType,
            status: AIOperationStatus.PENDING,
            priority: operationRequest.priority,
            parameters: operationRequest.parameters,
            organizationId: operationRequest.organizationId,
            projectId: operationRequest.projectId,
            callbackUrl: operationRequest.callbackUrl,
            metadata: operationRequest.metadata || {},
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            estimatedCompletionTime: calculateEstimatedCompletionTime(operationRequest),
            progress: {
                percentage: 0,
                currentStep: 'Initializing',
                totalSteps: getOperationSteps(operationRequest.operationType)
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('ai-operations', aiOperation);
        // Queue the operation for processing
        await queueAIOperation(aiOperation);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "ai_operation_created",
            userId: user.id,
            organizationId: operationRequest.organizationId,
            projectId: operationRequest.projectId,
            timestamp: now,
            details: {
                operationId,
                operationType: operationRequest.operationType,
                priority: operationRequest.priority,
                organizationName: orgData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'AIOperationCreated',
            aggregateId: operationId,
            aggregateType: 'AIOperation',
            version: 1,
            data: {
                operation: aiOperation,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: operationRequest.organizationId,
            tenantId: user.tenantId
        });
        // Send notification for high priority operations
        if (operationRequest.priority === AIOperationPriority.HIGH || operationRequest.priority === AIOperationPriority.URGENT) {
            await notification_1.notificationService.sendNotification({
                userId: user.id,
                type: 'AI_OPERATION_CREATED',
                title: 'High Priority AI Operation Started',
                message: `Your ${operationRequest.operationType} operation has been queued with ${operationRequest.priority} priority.`,
                priority: 'high',
                metadata: {
                    operationId,
                    operationType: operationRequest.operationType,
                    organizationId: operationRequest.organizationId
                },
                organizationId: operationRequest.organizationId,
                projectId: operationRequest.projectId
            });
        }
        const response = {
            operationId,
            operationType: aiOperation.operationType,
            status: aiOperation.status,
            priority: aiOperation.priority,
            estimatedCompletionTime: aiOperation.estimatedCompletionTime,
            progress: aiOperation.progress,
            createdAt: aiOperation.createdAt,
            updatedAt: aiOperation.updatedAt
        };
        logger_1.logger.info("AI operation created successfully", {
            correlationId,
            operationId,
            operationType: operationRequest.operationType,
            priority: operationRequest.priority,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create AI operation failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get AI operation status handler
 */
async function getAIOperationStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const operationId = request.params.operationId;
    logger_1.logger.info("Get AI operation status started", { correlationId, operationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!operationId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Operation ID is required" }
            }, request);
        }
        // Get AI operation
        const aiOperation = await database_1.db.readItem('ai-operations', operationId, operationId);
        if (!aiOperation) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "AI operation not found" }
            }, request);
        }
        const operationData = aiOperation;
        // Check access permissions
        const hasAccess = (operationData.createdBy === user.id ||
            operationData.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        const response = {
            operationId: operationData.id,
            operationType: operationData.operationType,
            status: operationData.status,
            priority: operationData.priority,
            estimatedCompletionTime: operationData.estimatedCompletionTime,
            result: operationData.result,
            progress: operationData.progress,
            error: operationData.error,
            createdAt: operationData.createdAt,
            updatedAt: operationData.updatedAt
        };
        logger_1.logger.info("AI operation status retrieved successfully", {
            correlationId,
            operationId,
            status: operationData.status,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get AI operation status failed", {
            correlationId,
            operationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check if AI operation limit is reached for organization
 */
async function isAIOperationLimitReached(organizationId, tier) {
    try {
        // Get current month's operations count
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const operationsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
        const result = await database_1.db.queryItems('ai-operations', operationsQuery, [organizationId, startOfMonth.toISOString()]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 10,
            'PROFESSIONAL': 100,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check AI operation limit', { error, organizationId });
        return false;
    }
}
/**
 * Calculate estimated completion time
 */
function calculateEstimatedCompletionTime(operationRequest) {
    // Simplified estimation based on operation type
    const estimationMinutes = {
        [AIOperationType.DOCUMENT_ANALYSIS]: 5,
        [AIOperationType.CONTENT_GENERATION]: 10,
        [AIOperationType.CONTENT_COMPLETION]: 3,
        [AIOperationType.DOCUMENT_SUMMARIZATION]: 2,
        [AIOperationType.INTELLIGENT_SEARCH]: 1,
        [AIOperationType.WORKFLOW_OPTIMIZATION]: 15,
        [AIOperationType.BATCH_PROCESSING]: 30
    };
    const baseMinutes = estimationMinutes[operationRequest.operationType] || 5;
    // Adjust based on priority
    const priorityMultiplier = operationRequest.priority === AIOperationPriority.URGENT ? 0.5 :
        operationRequest.priority === AIOperationPriority.HIGH ? 0.7 : 1.0;
    const estimatedMinutes = Math.ceil(baseMinutes * priorityMultiplier);
    const completionTime = new Date(Date.now() + estimatedMinutes * 60 * 1000);
    return completionTime.toISOString();
}
/**
 * Get number of steps for operation type
 */
function getOperationSteps(operationType) {
    const steps = {
        [AIOperationType.DOCUMENT_ANALYSIS]: 4,
        [AIOperationType.CONTENT_GENERATION]: 5,
        [AIOperationType.CONTENT_COMPLETION]: 3,
        [AIOperationType.DOCUMENT_SUMMARIZATION]: 3,
        [AIOperationType.INTELLIGENT_SEARCH]: 2,
        [AIOperationType.WORKFLOW_OPTIMIZATION]: 6,
        [AIOperationType.BATCH_PROCESSING]: 8
    };
    return steps[operationType] || 3;
}
/**
 * Queue AI operation for processing using shared Service Bus service (production implementation)
 */
async function queueAIOperation(aiOperation) {
    const { serviceBusEnhanced } = require('../shared/services/service-bus');
    try {
        // Ensure Service Bus is initialized
        await serviceBusEnhanced.initialize();
        const queueName = 'ai-operations';
        // Prepare message for Service Bus using the enhanced service
        const message = {
            body: {
                operationId: aiOperation.id,
                operationType: aiOperation.operationType,
                data: aiOperation,
                queuedAt: new Date().toISOString()
            },
            messageId: `ai-op-${aiOperation.id}-${Date.now()}`,
            subject: `AI Operation: ${aiOperation.operationType}`,
            correlationId: aiOperation.id,
            applicationProperties: {
                operationId: aiOperation.id,
                operationType: aiOperation.operationType,
                priority: aiOperation.priority || 'normal',
                organizationId: aiOperation.organizationId,
                userId: aiOperation.userId,
                source: 'ai-orchestration-hub'
            },
            // Set message TTL and scheduling based on priority
            timeToLive: aiOperation.priority === 'high' ? 300000 : 3600000, // 5 min for high, 1 hour for normal/low
            scheduledEnqueueTime: aiOperation.priority === 'low' ? new Date(Date.now() + 30000) : undefined // 30 sec delay for low priority
        };
        const success = await serviceBusEnhanced.sendToQueue(queueName, message);
        if (!success) {
            throw new Error('Failed to send message to Service Bus queue');
        }
        logger_1.logger.info("AI operation queued for processing via shared Service Bus", {
            operationId: aiOperation.id,
            operationType: aiOperation.operationType,
            priority: aiOperation.priority,
            queueName: queueName
        });
        // Update operation status to queued
        await updateAIOperationStatus(aiOperation.id, 'queued', {
            queuedAt: new Date().toISOString(),
            queueName: queueName
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to queue AI operation', {
            error: error instanceof Error ? error.message : String(error),
            operationId: aiOperation.id
        });
        // Fallback: Update status to failed
        await updateAIOperationStatus(aiOperation.id, 'failed', {
            error: 'Failed to queue operation',
            failedAt: new Date().toISOString()
        });
        throw error;
    }
}
/**
 * Update AI operation status in database
 */
async function updateAIOperationStatus(operationId, status, additionalData = {}) {
    try {
        const { db } = require('../shared/services/database');
        const operation = await db.readItem('ai-operations', operationId, operationId);
        if (operation) {
            const updatedOperation = {
                ...operation,
                status,
                ...additionalData,
                updatedAt: new Date().toISOString()
            };
            await db.updateItem('ai-operations', updatedOperation);
            logger_1.logger.info('AI operation status updated', { operationId, status });
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to update AI operation status', {
            operationId,
            status,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
/**
 * Simulate AI operation processing (for demo purposes)
 */
async function simulateAIOperationProcessing(aiOperation) {
    try {
        // Update status to running
        const updatedOperation = {
            ...aiOperation,
            status: AIOperationStatus.RUNNING,
            progress: {
                percentage: 25,
                currentStep: 'Processing',
                totalSteps: aiOperation.progress.totalSteps
            },
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('ai-operations', updatedOperation);
        // Simulate processing time
        setTimeout(async () => {
            // Complete the operation
            const completedOperation = {
                ...updatedOperation,
                status: AIOperationStatus.COMPLETED,
                progress: {
                    percentage: 100,
                    currentStep: 'Completed',
                    totalSteps: aiOperation.progress.totalSteps
                },
                result: {
                    success: true,
                    message: `${aiOperation.operationType} completed successfully`,
                    data: {
                        operationType: aiOperation.operationType,
                        processingTime: '2.5 seconds',
                        confidence: 0.95
                    }
                },
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('ai-operations', completedOperation);
            logger_1.logger.info("AI operation completed", {
                operationId: aiOperation.id,
                operationType: aiOperation.operationType
            });
        }, 3000); // 3 second processing simulation
    }
    catch (error) {
        logger_1.logger.error('Failed to process AI operation', { error, operationId: aiOperation.id });
    }
}
// Register functions
functions_1.app.http('ai-operation-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'ai/operations',
    handler: createAIOperation
});
functions_1.app.http('ai-operation-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'ai/operations/{operationId}',
    handler: getAIOperationStatus
});
//# sourceMappingURL=ai-orchestration-hub.js.map