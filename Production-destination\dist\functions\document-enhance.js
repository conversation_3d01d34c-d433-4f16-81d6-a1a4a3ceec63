"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.enhanceDocument = enhanceDocument;
/**
 * Document Enhancement Function
 * Handles document enhancement operations like quality improvement, format conversion, etc.
 * Migrated from old-arch/src/document-service/enhance/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Enhancement types enum
var EnhancementType;
(function (EnhancementType) {
    EnhancementType["QUALITY_IMPROVEMENT"] = "QUALITY_IMPROVEMENT";
    EnhancementType["FORMAT_CONVERSION"] = "FORMAT_CONVERSION";
    EnhancementType["OCR_ENHANCEMENT"] = "OCR_ENHANCEMENT";
    EnhancementType["IMAGE_OPTIMIZATION"] = "IMAGE_OPTIMIZATION";
    EnhancementType["TEXT_CLEANUP"] = "TEXT_CLEANUP";
    EnhancementType["LAYOUT_OPTIMIZATION"] = "LAYOUT_OPTIMIZATION";
})(EnhancementType || (EnhancementType = {}));
// Validation schema
const enhanceDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    enhancementType: Joi.string().valid(...Object.values(EnhancementType)).required(),
    options: Joi.object({
        targetFormat: Joi.string().optional(),
        quality: Joi.number().min(1).max(100).optional(),
        dpi: Joi.number().min(72).max(600).optional(),
        removeBackground: Joi.boolean().optional(),
        enhanceText: Joi.boolean().optional(),
        preserveLayout: Joi.boolean().optional(),
        colorMode: Joi.string().valid('color', 'grayscale', 'blackwhite').optional()
    }).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().required()
});
/**
 * Enhance document handler
 */
async function enhanceDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Document enhancement started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = enhanceDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { documentId, enhancementType, options, organizationId, projectId } = value;
        const startTime = Date.now();
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Initialize blob service client
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        // Download the document from blob storage
        const blobClient = containerClient.getBlockBlobClient(document.blobName);
        const downloadResponse = await blobClient.download(0);
        if (!downloadResponse.readableStreamBody) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Failed to download document" }
            }, request);
        }
        const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
        // Perform enhancement based on type
        const enhancementResult = await performEnhancement(documentBuffer, enhancementType, options || {}, document.contentType);
        // Save enhanced document to blob storage
        const enhancedDocumentId = (0, uuid_1.v4)();
        const enhancedBlobName = `${organizationId}/${projectId}/${enhancedDocumentId}_enhanced.${getFileExtension(enhancementResult.targetFormat || document.contentType)}`;
        const enhancedBlobClient = containerClient.getBlockBlobClient(enhancedBlobName);
        await enhancedBlobClient.upload(enhancementResult.enhancedBuffer, enhancementResult.enhancedBuffer.length, {
            blobHTTPHeaders: { blobContentType: enhancementResult.targetFormat || document.contentType }
        });
        // Create enhanced document record
        const enhancedDocument = {
            id: enhancedDocumentId,
            originalDocumentId: documentId,
            name: `${document.name} (Enhanced)`,
            description: `Enhanced version of ${document.name} using ${enhancementType}`,
            blobName: enhancedBlobName,
            contentType: enhancementResult.targetFormat || document.contentType,
            size: enhancementResult.enhancedBuffer.length,
            organizationId,
            projectId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            status: "ENHANCED",
            metadata: {
                enhancementType,
                originalSize: documentBuffer.length,
                enhancedSize: enhancementResult.enhancedBuffer.length,
                qualityScore: enhancementResult.qualityScore,
                improvements: enhancementResult.improvements,
                enhancedAt: new Date().toISOString(),
                enhancedBy: user.id,
                options
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('documents', enhancedDocument);
        // Create enhancement record
        const enhancement = {
            id: (0, uuid_1.v4)(),
            documentId,
            enhancedDocumentId,
            enhancementType,
            options,
            result: {
                originalSize: documentBuffer.length,
                enhancedSize: enhancementResult.enhancedBuffer.length,
                qualityScore: enhancementResult.qualityScore,
                improvements: enhancementResult.improvements,
                processingTime: Date.now() - startTime
            },
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            organizationId,
            projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('document-enhancements', enhancement);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_enhanced",
            userId: user.id,
            organizationId,
            projectId,
            documentId,
            enhancedDocumentId,
            timestamp: new Date().toISOString(),
            details: {
                enhancementType,
                originalSize: documentBuffer.length,
                enhancedSize: enhancementResult.enhancedBuffer.length,
                qualityImprovement: enhancementResult.qualityScore,
                processingTime: Date.now() - startTime
            },
            tenantId: user.tenantId
        });
        const response = {
            documentId,
            enhancedDocumentId,
            enhancementType,
            originalSize: documentBuffer.length,
            enhancedSize: enhancementResult.enhancedBuffer.length,
            qualityScore: enhancementResult.qualityScore,
            processingTime: Date.now() - startTime,
            improvements: enhancementResult.improvements,
            success: true
        };
        logger_1.logger.info("Document enhanced successfully", {
            correlationId,
            documentId,
            enhancedDocumentId,
            enhancementType,
            userId: user.id,
            processingTime: response.processingTime
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document enhancement failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Perform document enhancement (simplified implementation)
 */
async function performEnhancement(documentBuffer, enhancementType, options, contentType) {
    // This is a simplified implementation
    // In production, this would integrate with image processing libraries
    // and AI services for actual enhancement
    logger_1.logger.info("Performing document enhancement", {
        enhancementType,
        documentSize: documentBuffer.length,
        contentType
    });
    const improvements = [];
    let qualityScore = 85; // Base quality score
    switch (enhancementType) {
        case EnhancementType.QUALITY_IMPROVEMENT:
            improvements.push("Improved image resolution");
            improvements.push("Enhanced contrast and brightness");
            qualityScore = 92;
            break;
        case EnhancementType.OCR_ENHANCEMENT:
            improvements.push("Improved text recognition accuracy");
            improvements.push("Enhanced character clarity");
            qualityScore = 88;
            break;
        case EnhancementType.IMAGE_OPTIMIZATION:
            improvements.push("Optimized file size");
            improvements.push("Improved compression efficiency");
            qualityScore = 90;
            break;
        case EnhancementType.TEXT_CLEANUP:
            improvements.push("Removed noise and artifacts");
            improvements.push("Improved text readability");
            qualityScore = 87;
            break;
        default:
            improvements.push("General document enhancement applied");
            break;
    }
    // Simulate enhancement processing
    // In production, apply actual image processing algorithms
    const enhancedBuffer = documentBuffer; // For now, return original
    return {
        enhancedBuffer,
        targetFormat: options.targetFormat || contentType,
        qualityScore,
        improvements
    };
}
/**
 * Get file extension from content type
 */
function getFileExtension(contentType) {
    const extensions = {
        'application/pdf': 'pdf',
        'image/jpeg': 'jpg',
        'image/png': 'png',
        'image/tiff': 'tiff',
        'application/msword': 'doc',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'docx'
    };
    return extensions[contentType] || 'bin';
}
/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on("data", (data) => {
            chunks.push(data instanceof Buffer ? data : Buffer.from(data));
        });
        readableStream.on("end", () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on("error", reject);
    });
}
// Register functions
functions_1.app.http('document-enhance', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}/enhance',
    handler: enhanceDocument
});
//# sourceMappingURL=document-enhance.js.map