/**
 * Email Service Function
 * Handles email sending and management
 * Migrated from old-arch/src/email-service/send/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Send email handler
 */
export declare function sendEmail(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create email template handler
 */
export declare function createEmailTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
