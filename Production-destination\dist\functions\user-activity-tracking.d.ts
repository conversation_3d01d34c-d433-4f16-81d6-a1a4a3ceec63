/**
 * User Activity Tracking Function
 * Handles user activity tracking and analytics
 * Migrated from old-arch/src/user-service/activity/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Track user activity handler
 */
export declare function trackUserActivity(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get activity analytics handler
 */
export declare function getActivityAnalytics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
