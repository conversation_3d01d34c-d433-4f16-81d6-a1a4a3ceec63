"use strict";
/**
 * Generic Cache-Aside Service
 * Provides a reusable pattern for cache-aside operations with database fallback
 * Ensures data consistency and graceful degradation when Redis is unavailable
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.cacheAside = exports.CacheAsideService = void 0;
const redis_1 = require("./redis");
const database_1 = require("./database");
const logger_1 = require("../utils/logger");
const events_1 = require("events");
class CacheAsideService extends events_1.EventEmitter {
    constructor() {
        super();
        this.warmingRules = new Map();
        this.warmingQueue = [];
        this.isWarmingActive = false;
        this.warmingInterval = null;
        this.eventQueue = [];
        this.isProcessingEvents = false;
        this.setupEventProcessing();
        this.setupCacheWarming();
    }
    static getInstance() {
        if (!CacheAsideService.instance) {
            CacheAsideService.instance = new CacheAsideService();
        }
        return CacheAsideService.instance;
    }
    /**
     * Setup event-driven cache processing
     */
    setupEventProcessing() {
        // Process events every 100ms
        setInterval(() => {
            if (!this.isProcessingEvents && this.eventQueue.length > 0) {
                this.processEventQueue();
            }
        }, 100);
    }
    /**
     * Setup cache warming system
     */
    setupCacheWarming() {
        // Check for warming opportunities every 5 minutes
        this.warmingInterval = setInterval(() => {
            if (!this.isWarmingActive) {
                this.processWarmingRules();
            }
        }, 5 * 60 * 1000);
    }
    /**
     * Add cache warming rule
     */
    addWarmingRule(ruleId, rule) {
        this.warmingRules.set(ruleId, rule);
        logger_1.logger.info('Cache warming rule added', { ruleId, pattern: rule.pattern, priority: rule.priority });
    }
    /**
     * Remove cache warming rule
     */
    removeWarmingRule(ruleId) {
        this.warmingRules.delete(ruleId);
        logger_1.logger.info('Cache warming rule removed', { ruleId });
    }
    /**
     * Emit cache event for event-driven invalidation
     */
    emitCacheEvent(event) {
        this.eventQueue.push(event);
        this.emit('cacheEvent', event);
        logger_1.logger.debug('Cache event emitted', { type: event.type, key: event.key, source: event.source });
    }
    /**
     * Add event to warming queue
     */
    addToWarmingQueue(event) {
        this.warmingQueue.push(event);
        logger_1.logger.debug('Event added to warming queue', { type: event.type, key: event.key, priority: event.priority });
    }
    /**
     * Generic get operation with cache-aside pattern
     */
    async get(cacheKey, dbQuery, options = {}) {
        const { ttlSeconds = 3600, enableFallback = true, cachePrefix = '', enableWarming = false, warmingPriority = 'medium', eventDriven = false } = options;
        const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;
        try {
            // Try Redis first
            const cachedData = await redis_1.redis.getJson(fullCacheKey);
            if (cachedData) {
                logger_1.logger.debug('Data found in Redis cache', { cacheKey: fullCacheKey });
                // Emit cache hit event if event-driven
                if (eventDriven) {
                    this.emitCacheEvent({
                        type: 'update',
                        key: fullCacheKey,
                        timestamp: new Date(),
                        source: 'cache-hit'
                    });
                }
                return cachedData;
            }
            // Cache miss - emit event if event-driven
            if (eventDriven) {
                this.emitCacheEvent({
                    type: 'warm',
                    key: fullCacheKey,
                    priority: warmingPriority,
                    timestamp: new Date(),
                    source: 'cache-miss'
                });
            }
            // Fallback to database if enabled
            if (enableFallback) {
                const dbData = await this.queryDatabase(dbQuery);
                if (dbData) {
                    // Cache the data back to Redis for future requests
                    await redis_1.redis.setJson(fullCacheKey, dbData, ttlSeconds);
                    logger_1.logger.info('Data retrieved from database and cached', { cacheKey: fullCacheKey });
                    // Add to warming queue if warming is enabled
                    if (enableWarming) {
                        this.addToWarmingQueue({
                            type: 'warm',
                            key: fullCacheKey,
                            data: dbData,
                            priority: warmingPriority,
                            timestamp: new Date(),
                            source: 'database-fallback'
                        });
                    }
                    return dbData;
                }
            }
            logger_1.logger.debug('Data not found in cache or database', { cacheKey: fullCacheKey });
            return null;
        }
        catch (error) {
            logger_1.logger.error('Cache-aside get operation failed', {
                error: error instanceof Error ? error.message : String(error),
                cacheKey: fullCacheKey
            });
            // If Redis fails, try database directly
            if (enableFallback) {
                try {
                    return await this.queryDatabase(dbQuery);
                }
                catch (dbError) {
                    logger_1.logger.error('Database fallback also failed', {
                        error: dbError instanceof Error ? dbError.message : String(dbError),
                        cacheKey: fullCacheKey
                    });
                }
            }
            return null;
        }
    }
    /**
     * Generic set operation with cache invalidation
     */
    async set(cacheKey, data, options = {}) {
        const { ttlSeconds = 3600, cachePrefix = '', invalidatePatterns = [], eventDriven = false } = options;
        const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;
        try {
            // Set data in Redis
            const success = await redis_1.redis.setJson(fullCacheKey, data, ttlSeconds);
            if (success) {
                // Emit update event if event-driven
                if (eventDriven) {
                    this.emitCacheEvent({
                        type: 'update',
                        key: fullCacheKey,
                        data,
                        timestamp: new Date(),
                        source: 'cache-set'
                    });
                }
                // Invalidate related caches
                for (const pattern of invalidatePatterns) {
                    await this.invalidatePattern(pattern);
                    // Emit invalidation events if event-driven
                    if (eventDriven) {
                        this.emitCacheEvent({
                            type: 'invalidate',
                            key: fullCacheKey,
                            pattern,
                            timestamp: new Date(),
                            source: 'pattern-invalidation'
                        });
                    }
                }
                logger_1.logger.debug('Data cached and related caches invalidated', {
                    cacheKey: fullCacheKey,
                    invalidatedPatterns: invalidatePatterns
                });
            }
            return success;
        }
        catch (error) {
            logger_1.logger.error('Cache-aside set operation failed', {
                error: error instanceof Error ? error.message : String(error),
                cacheKey: fullCacheKey
            });
            return false;
        }
    }
    /**
     * Get list of items with cache-aside pattern
     */
    async getList(cacheKey, dbQuery, options = {}) {
        const { ttlSeconds = 3600, enableFallback = true, cachePrefix = '' } = options;
        const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;
        try {
            // Try Redis first
            const cachedList = await redis_1.redis.getJson(fullCacheKey);
            if (cachedList && Array.isArray(cachedList)) {
                logger_1.logger.debug('List found in Redis cache', { cacheKey: fullCacheKey, count: cachedList.length });
                return cachedList;
            }
            // Fallback to database if enabled
            if (enableFallback) {
                const dbList = await this.queryDatabaseList(dbQuery);
                if (dbList && dbList.length > 0) {
                    // Cache the list back to Redis for future requests
                    await redis_1.redis.setJson(fullCacheKey, dbList, ttlSeconds);
                    logger_1.logger.info('List retrieved from database and cached', {
                        cacheKey: fullCacheKey,
                        count: dbList.length
                    });
                    return dbList;
                }
            }
            logger_1.logger.debug('List not found in cache or database', { cacheKey: fullCacheKey });
            return [];
        }
        catch (error) {
            logger_1.logger.error('Cache-aside getList operation failed', {
                error: error instanceof Error ? error.message : String(error),
                cacheKey: fullCacheKey
            });
            // If Redis fails, try database directly
            if (enableFallback) {
                try {
                    return await this.queryDatabaseList(dbQuery);
                }
                catch (dbError) {
                    logger_1.logger.error('Database fallback also failed for list', {
                        error: dbError instanceof Error ? dbError.message : String(dbError),
                        cacheKey: fullCacheKey
                    });
                }
            }
            return [];
        }
    }
    /**
     * Delete from cache and invalidate related patterns
     */
    async delete(cacheKey, options = {}) {
        const { cachePrefix = '', invalidatePatterns = [] } = options;
        const fullCacheKey = cachePrefix ? `${cachePrefix}:${cacheKey}` : cacheKey;
        try {
            const success = await redis_1.redis.delete(fullCacheKey);
            // Invalidate related caches
            for (const pattern of invalidatePatterns) {
                await this.invalidatePattern(pattern);
            }
            logger_1.logger.debug('Cache deleted and related caches invalidated', {
                cacheKey: fullCacheKey,
                invalidatedPatterns: invalidatePatterns
            });
            return success;
        }
        catch (error) {
            logger_1.logger.error('Cache-aside delete operation failed', {
                error: error instanceof Error ? error.message : String(error),
                cacheKey: fullCacheKey
            });
            return false;
        }
    }
    /**
     * Query database for single item
     */
    async queryDatabase(dbQuery) {
        try {
            if (dbQuery.itemId && dbQuery.partitionKey) {
                // Direct item read
                return await database_1.db.readItem(dbQuery.containerName, dbQuery.itemId, dbQuery.partitionKey);
            }
            else if (dbQuery.query && dbQuery.parameters) {
                // Query with parameters
                const results = await database_1.db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
                return results.length > 0 ? results[0] : null;
            }
            else {
                throw new Error('Invalid database query configuration');
            }
        }
        catch (error) {
            logger_1.logger.error('Database query failed', {
                error: error instanceof Error ? error.message : String(error),
                containerName: dbQuery.containerName
            });
            throw error;
        }
    }
    /**
     * Query database for list of items
     */
    async queryDatabaseList(dbQuery) {
        try {
            if (dbQuery.query && dbQuery.parameters) {
                const results = await database_1.db.queryItems(dbQuery.containerName, dbQuery.query, dbQuery.parameters);
                return results;
            }
            else {
                throw new Error('Invalid database query configuration for list');
            }
        }
        catch (error) {
            logger_1.logger.error('Database list query failed', {
                error: error instanceof Error ? error.message : String(error),
                containerName: dbQuery.containerName
            });
            throw error;
        }
    }
    /**
     * Invalidate cache pattern using production-safe approach
     */
    async invalidatePattern(pattern) {
        try {
            // Use production cache manager for safe invalidation
            const { productionCacheManager } = await Promise.resolve().then(() => __importStar(require('./production-cache-manager')));
            // Convert pattern to tags for efficient invalidation
            const tags = this.extractTagsFromPattern(pattern);
            if (tags.length > 0) {
                await productionCacheManager.invalidateByTags(tags);
                logger_1.logger.debug('Cache invalidated by tags', { pattern, tags });
            }
            else {
                // Fallback to Redis pattern deletion for non-tagged patterns
                logger_1.logger.warn('Using fallback pattern deletion', { pattern });
                if (redis_1.redis.isAvailable()) {
                    await redis_1.redis.deleteByPattern(pattern);
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to invalidate cache pattern', {
                error: error instanceof Error ? error.message : String(error),
                pattern
            });
        }
    }
    /**
     * Extract cache tags from pattern for efficient invalidation
     */
    extractTagsFromPattern(pattern) {
        const tags = [];
        if (pattern.startsWith('document:')) {
            tags.push('document', 'content');
        }
        else if (pattern.startsWith('user:')) {
            tags.push('user', 'activity');
        }
        else if (pattern.startsWith('session:')) {
            tags.push('session', 'collaboration');
        }
        else if (pattern.startsWith('config:')) {
            tags.push('configuration', 'system');
        }
        else if (pattern.startsWith('feature_flag:')) {
            tags.push('feature', 'configuration');
        }
        else if (pattern.startsWith('device:')) {
            tags.push('device', 'user');
        }
        else if (pattern.startsWith('bi_report:')) {
            tags.push('analytics', 'report');
        }
        return tags;
    }
    /**
     * Process event queue for event-driven cache operations
     */
    async processEventQueue() {
        if (this.isProcessingEvents || this.eventQueue.length === 0) {
            return;
        }
        this.isProcessingEvents = true;
        try {
            const events = this.eventQueue.splice(0, 10); // Process up to 10 events at a time
            for (const event of events) {
                await this.processEvent(event);
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to process event queue', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        finally {
            this.isProcessingEvents = false;
        }
    }
    /**
     * Process individual cache event
     */
    async processEvent(event) {
        try {
            switch (event.type) {
                case 'invalidate':
                    if (event.pattern) {
                        await this.invalidatePattern(event.pattern);
                    }
                    else {
                        await redis_1.redis.delete(event.key);
                    }
                    break;
                case 'warm':
                    await this.warmCache(event);
                    break;
                case 'update':
                    // Event already processed during set operation
                    break;
                case 'delete':
                    await redis_1.redis.delete(event.key);
                    break;
            }
            logger_1.logger.debug('Cache event processed', {
                type: event.type,
                key: event.key,
                source: event.source
            });
        }
        catch (error) {
            logger_1.logger.error('Failed to process cache event', {
                error: error instanceof Error ? error.message : String(error),
                event
            });
        }
    }
    /**
     * Process cache warming rules
     */
    async processWarmingRules() {
        if (this.isWarmingActive || this.warmingRules.size === 0) {
            return;
        }
        this.isWarmingActive = true;
        try {
            const now = new Date();
            for (const [ruleId, rule] of this.warmingRules) {
                const shouldWarm = !rule.lastWarmed ||
                    (now.getTime() - rule.lastWarmed.getTime()) >= (rule.frequency * 60 * 1000);
                if (shouldWarm) {
                    await this.executeWarmingRule(ruleId, rule);
                    rule.lastWarmed = now;
                }
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to process warming rules', {
                error: error instanceof Error ? error.message : String(error)
            });
        }
        finally {
            this.isWarmingActive = false;
        }
    }
    /**
     * Execute cache warming rule
     */
    async executeWarmingRule(ruleId, rule) {
        try {
            logger_1.logger.info('Executing cache warming rule', { ruleId, pattern: rule.pattern });
            const data = await this.queryDatabase(rule.dbQuery);
            if (data) {
                const cacheKey = rule.pattern.replace('*', 'warmed');
                await redis_1.redis.setJson(cacheKey, data, rule.options.ttlSeconds || 3600);
                logger_1.logger.info('Cache warming completed', {
                    ruleId,
                    cacheKey,
                    priority: rule.priority
                });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to execute warming rule', {
                error: error instanceof Error ? error.message : String(error),
                ruleId
            });
        }
    }
    /**
     * Warm specific cache entry
     */
    async warmCache(event) {
        try {
            if (event.data) {
                await redis_1.redis.setJson(event.key, event.data, 3600);
                logger_1.logger.debug('Cache warmed from event', { key: event.key, priority: event.priority });
            }
        }
        catch (error) {
            logger_1.logger.error('Failed to warm cache', {
                error: error instanceof Error ? error.message : String(error),
                key: event.key
            });
        }
    }
    /**
     * Get cache warming statistics
     */
    getWarmingStats() {
        return {
            rulesCount: this.warmingRules.size,
            queueSize: this.warmingQueue.length,
            isActive: this.isWarmingActive
        };
    }
    /**
     * Get event processing statistics
     */
    getEventStats() {
        return {
            queueSize: this.eventQueue.length,
            isProcessing: this.isProcessingEvents
        };
    }
    /**
     * Cleanup resources
     */
    cleanup() {
        if (this.warmingInterval) {
            clearInterval(this.warmingInterval);
            this.warmingInterval = null;
        }
        this.warmingRules.clear();
        this.warmingQueue.length = 0;
        this.eventQueue.length = 0;
    }
}
exports.CacheAsideService = CacheAsideService;
// Export singleton instance
exports.cacheAside = CacheAsideService.getInstance();
exports.default = exports.cacheAside;
//# sourceMappingURL=cache-aside.js.map