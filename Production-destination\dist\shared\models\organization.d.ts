/**
 * Organization Models and Interfaces
 * Defines the structure for organization-related data models
 */
export interface Organization {
    id: string;
    name: string;
    description?: string;
    logo?: string;
    website?: string;
    industry?: string;
    size: OrganizationSize;
    tier: OrganizationTier;
    status: OrganizationStatus;
    settings: OrganizationSettings;
    billing: BillingInfo;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    metadata?: OrganizationMetadata;
    tenantId: string;
}
export declare enum OrganizationSize {
    STARTUP = "STARTUP",
    SMALL = "SMALL",
    MEDIUM = "MEDIUM",
    LARGE = "LARGE",
    ENTERPRISE = "ENTERPRISE"
}
export declare enum OrganizationTier {
    FREE = "FREE",
    BASIC = "BASIC",
    PROFESSIONAL = "PROFESSIONAL",
    ENTERPRISE = "ENTERPRISE",
    CUSTOM = "CUSTOM"
}
export declare enum OrganizationStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    SUSPENDED = "SUSPENDED",
    TRIAL = "TRIAL",
    EXPIRED = "EXPIRED",
    DELETED = "DELETED"
}
export interface OrganizationSettings {
    allowPublicProjects: boolean;
    requireApprovalForNewMembers: boolean;
    enableGuestAccess: boolean;
    defaultProjectVisibility: 'private' | 'organization' | 'public';
    defaultDocumentRetentionDays: number;
    enableAuditLog: boolean;
    enableSSOLogin: boolean;
    ssoProvider?: string;
    ssoConfiguration?: any;
    securitySettings: SecuritySettings;
    integrationSettings: IntegrationSettings;
    notificationSettings: OrganizationNotificationSettings;
}
export interface SecuritySettings {
    enforcePasswordPolicy: boolean;
    passwordPolicy?: PasswordPolicy;
    requireTwoFactor: boolean;
    sessionTimeoutMinutes: number;
    allowedIpRanges?: string[];
    enableDeviceTracking: boolean;
    requireApprovalForSensitiveActions: boolean;
}
export interface PasswordPolicy {
    minLength: number;
    requireUppercase: boolean;
    requireLowercase: boolean;
    requireNumbers: boolean;
    requireSpecialChars: boolean;
    preventReuse: number;
    maxAge: number;
}
export interface IntegrationSettings {
    enabledIntegrations: string[];
    webhookEndpoints: WebhookEndpoint[];
    apiKeys: ApiKeyInfo[];
    externalConnections: ExternalConnection[];
}
export interface WebhookEndpoint {
    id: string;
    name: string;
    url: string;
    events: string[];
    secret: string;
    enabled: boolean;
    createdAt: string;
}
export interface ApiKeyInfo {
    id: string;
    name: string;
    keyPrefix: string;
    permissions: string[];
    createdBy: string;
    createdAt: string;
    lastUsedAt?: string;
    expiresAt?: string;
    enabled: boolean;
}
export interface ExternalConnection {
    id: string;
    type: string;
    name: string;
    configuration: any;
    status: 'connected' | 'disconnected' | 'error';
    lastSyncAt?: string;
    createdAt: string;
}
export interface OrganizationNotificationSettings {
    enableEmailNotifications: boolean;
    enableSlackIntegration: boolean;
    slackWebhookUrl?: string;
    enableTeamsIntegration: boolean;
    teamsWebhookUrl?: string;
    notificationChannels: NotificationChannel[];
}
export interface NotificationChannel {
    id: string;
    type: 'email' | 'slack' | 'teams' | 'webhook';
    name: string;
    configuration: any;
    events: string[];
    enabled: boolean;
}
export interface BillingInfo {
    subscriptionId?: string;
    planId: string;
    planName: string;
    billingCycle: 'monthly' | 'yearly';
    nextBillingDate?: string;
    paymentMethod?: PaymentMethod;
    billingAddress?: BillingAddress;
    usage: UsageInfo;
    limits: UsageLimits;
}
export interface PaymentMethod {
    type: 'card' | 'bank' | 'invoice';
    last4?: string;
    brand?: string;
    expiryMonth?: number;
    expiryYear?: number;
}
export interface BillingAddress {
    company?: string;
    line1: string;
    line2?: string;
    city: string;
    state?: string;
    postalCode: string;
    country: string;
    taxId?: string;
}
export interface UsageInfo {
    currentPeriodStart: string;
    currentPeriodEnd: string;
    documentsProcessed: number;
    storageUsedGB: number;
    apiCallsMade: number;
    activeUsers: number;
    workflowsExecuted: number;
}
export interface UsageLimits {
    maxDocumentsPerMonth: number;
    maxStorageGB: number;
    maxApiCallsPerMonth: number;
    maxActiveUsers: number;
    maxWorkflowsPerMonth: number;
    maxProjectsPerOrganization: number;
}
export interface OrganizationMetadata {
    memberCount: number;
    projectCount: number;
    documentCount: number;
    workflowCount: number;
    lastActivityAt?: string;
    creationSource?: string;
    referralCode?: string;
    [key: string]: any;
}
export interface OrganizationMember {
    id: string;
    organizationId: string;
    userId?: string;
    email: string;
    role: OrganizationRole;
    permissions: string[];
    status: MemberStatus;
    invitedBy?: string;
    invitedAt?: string;
    joinedAt?: string;
    lastActiveAt?: string;
    metadata?: MemberMetadata;
    tenantId: string;
}
export declare enum OrganizationRole {
    OWNER = "OWNER",
    ADMIN = "ADMIN",
    MEMBER = "MEMBER",
    VIEWER = "VIEWER",
    GUEST = "GUEST"
}
export declare enum MemberStatus {
    ACTIVE = "ACTIVE",
    INACTIVE = "INACTIVE",
    PENDING = "PENDING",
    SUSPENDED = "SUSPENDED",
    LEFT = "LEFT",
    REMOVED = "REMOVED"
}
export interface MemberMetadata {
    department?: string;
    title?: string;
    location?: string;
    startDate?: string;
    manager?: string;
    directReports?: string[];
    skills?: string[];
    [key: string]: any;
}
export interface OrganizationInvitation {
    id: string;
    organizationId: string;
    email: string;
    role: OrganizationRole;
    permissions?: string[];
    invitedBy: string;
    invitedAt: string;
    expiresAt: string;
    status: 'PENDING' | 'ACCEPTED' | 'DECLINED' | 'EXPIRED' | 'CANCELLED';
    message?: string;
    acceptedAt?: string;
    declinedAt?: string;
    tenantId: string;
}
export interface OrganizationTeam {
    id: string;
    organizationId: string;
    name: string;
    description?: string;
    members: string[];
    permissions: string[];
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    metadata?: TeamMetadata;
    tenantId: string;
}
export interface TeamMetadata {
    purpose?: string;
    lead?: string;
    budget?: number;
    goals?: string[];
    [key: string]: any;
}
export interface OrganizationProject {
    id: string;
    organizationId: string;
    teamId?: string;
    name: string;
    description?: string;
    status: ProjectStatus;
    visibility: 'private' | 'organization' | 'public';
    members: ProjectMember[];
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    dueDate?: string;
    budget?: number;
    metadata?: ProjectMetadata;
    tenantId: string;
}
export declare enum ProjectStatus {
    PLANNING = "PLANNING",
    ACTIVE = "ACTIVE",
    ON_HOLD = "ON_HOLD",
    COMPLETED = "COMPLETED",
    CANCELLED = "CANCELLED",
    ARCHIVED = "ARCHIVED"
}
export interface ProjectMember {
    userId: string;
    role: ProjectRole;
    permissions: string[];
    joinedAt: string;
}
export declare enum ProjectRole {
    OWNER = "OWNER",
    MANAGER = "MANAGER",
    CONTRIBUTOR = "CONTRIBUTOR",
    VIEWER = "VIEWER"
}
export interface ProjectMetadata {
    category?: string;
    priority?: 'LOW' | 'MEDIUM' | 'HIGH' | 'URGENT';
    tags?: string[];
    estimatedHours?: number;
    actualHours?: number;
    progress?: number;
    [key: string]: any;
}
export interface OrganizationCreateRequest {
    name: string;
    description?: string;
    industry?: string;
    size: OrganizationSize;
    website?: string;
}
export interface OrganizationUpdateRequest {
    name?: string;
    description?: string;
    logo?: string;
    website?: string;
    industry?: string;
    size?: OrganizationSize;
    settings?: Partial<OrganizationSettings>;
}
export interface OrganizationInviteRequest {
    email: string;
    role: OrganizationRole;
    permissions?: string[];
    message?: string;
}
export interface TeamCreateRequest {
    name: string;
    description?: string;
    members?: string[];
    permissions?: string[];
}
export interface TeamUpdateRequest {
    name?: string;
    description?: string;
    members?: string[];
    permissions?: string[];
}
