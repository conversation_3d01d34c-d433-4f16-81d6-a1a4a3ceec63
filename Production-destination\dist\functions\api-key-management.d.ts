/**
 * API Key Management Function
 * Handles API key creation, management, and authentication
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create API key handler
 */
export declare function createApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List API keys handler
 */
export declare function listApiKeys(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Revoke API key handler
 */
export declare function revokeApiKey(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Validate API key (for authentication middleware)
 */
export declare function validateApiKey(apiKey: string): Promise<any>;
