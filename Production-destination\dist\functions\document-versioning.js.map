{"version": 3, "file": "document-versioning.js", "sourceRoot": "", "sources": ["../../src/functions/document-versioning.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqFA,sDAmNC;AAKD,wDA+OC;AA5hBD;;;;GAIG;AACH,gDAAyF;AACzF,sDAAwD;AACxD,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AACjD,kEAAsE;AACtE,oDAAwD;AAExD,0BAA0B;AAC1B,IAAK,WAKJ;AALD,WAAK,WAAW;IACd,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,8BAAe,CAAA;IACf,4BAAa,CAAA;AACf,CAAC,EALI,WAAW,KAAX,WAAW,QAKf;AAED,IAAK,aAIJ;AAJD,WAAK,aAAa;IAChB,kCAAiB,CAAA;IACjB,sCAAqB,CAAA;IACrB,oCAAmB,CAAA;AACrB,CAAC,EAJI,aAAa,KAAb,aAAa,QAIjB;AAED,qBAAqB;AACrB,MAAM,mBAAmB,GAAG,GAAG,CAAC,MAAM,CAAC;IACrC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,WAAW,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,OAAO,CAAC,WAAW,CAAC,IAAI,CAAC;IACxF,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACzC,IAAI,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,CAAC,GAAG,CAAC,EAAE,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;IACjE,OAAO,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;CACtC,CAAC,CAAC;AAEH,MAAM,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC;IACtC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACzC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;IACzC,YAAY,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;CAC1C,CAAC,CAAC;AAsCH;;GAEG;AACI,KAAK,UAAU,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;IAC1F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,mBAAmB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE5D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAAyB,KAAK,CAAC;QAEnD,iCAAiC;QACjC,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC,CAAC;QACtG,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,QAAe,CAAC;QAErC,yBAAyB;QACzB,MAAM,iBAAiB,GAAG,MAAM,2BAA2B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,sDAAsD,EAAE;aAC5E,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,6BAA6B;QAC7B,MAAM,oBAAoB,GAAG,MAAM,uBAAuB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,gBAAgB,GAAG,0BAA0B,CAAC,oBAAoB,EAAE,cAAc,CAAC,WAAW,CAAC,CAAC;QAEtG,uCAAuC;QACvC,MAAM,SAAS,GAAG,IAAA,SAAM,GAAE,CAAC;QAC3B,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,gDAAgD;QAChD,MAAM,eAAe,GAAG,MAAM,iBAAiB,CAAC,YAAY,EAAE,SAAS,CAAC,CAAC;QAEzE,uCAAuC;QACvC,MAAM,WAAW,GAAG,MAAM,oBAAoB,CAAC,YAAY,CAAC,QAAQ,CAAC,CAAC;QAEtE,uCAAuC;QACvC,MAAM,OAAO,GAAG,MAAM,qBAAqB,CAAC,cAAc,CAAC,UAAU,EAAE,YAAY,CAAC,CAAC;QAErF,MAAM,eAAe,GAAoB;YACvC,EAAE,EAAE,SAAS;YACb,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,aAAa,EAAE,gBAAgB;YAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,OAAO,EAAE,cAAc,CAAC,OAAO;YAC/B,IAAI,EAAE,cAAc,CAAC,IAAI;YACzB,QAAQ,EAAE,YAAY,CAAC,IAAI,IAAI,CAAC;YAChC,WAAW;YACX,QAAQ,EAAE,eAAe;YACzB,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE;gBACR,OAAO;gBACP,iBAAiB,EAAE,MAAM,oBAAoB,CAAC,cAAc,CAAC,UAAU,CAAC;gBACxE,UAAU,EAAE,KAAK;aAClB;SACF,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,eAAe,CAAC,CAAC;QAE1D,2CAA2C;QAC3C,MAAM,eAAe,GAAG;YACtB,GAAG,YAAY;YACf,gBAAgB,EAAE,SAAS;YAC3B,oBAAoB,EAAE,gBAAgB;YACtC,YAAY,EAAE,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC;YAClD,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,EAAE;SACnB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,0BAA0B;YAChC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS,EAAE,GAAG;YACd,OAAO,EAAE;gBACP,SAAS;gBACT,aAAa,EAAE,gBAAgB;gBAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,WAAW,EAAE,OAAO,CAAC,MAAM;gBAC3B,OAAO,EAAE,cAAc,CAAC,OAAO;aAChC;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,oBAAY,CAAC,YAAY,CAAC;YAC9B,IAAI,EAAE,wBAAwB;YAC9B,WAAW,EAAE,SAAS;YACtB,aAAa,EAAE,iBAAiB;YAChC,OAAO,EAAE,CAAC;YACV,IAAI,EAAE;gBACJ,OAAO,EAAE,eAAe;gBACxB,QAAQ,EAAE,YAAY;gBACtB,SAAS,EAAE,IAAI,CAAC,EAAE;aACnB;YACD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,uCAAuC;QACvC,IAAI,cAAc,CAAC,WAAW,KAAK,WAAW,CAAC,KAAK,EAAE,CAAC;YACrD,MAAM,kCAAmB,CAAC,gBAAgB,CAAC;gBACzC,MAAM,EAAE,IAAI,CAAC,EAAE;gBACf,IAAI,EAAE,0BAA0B;gBAChC,KAAK,EAAE,gCAAgC;gBACvC,OAAO,EAAE,WAAW,gBAAgB,QAAQ,YAAY,CAAC,IAAI,qBAAqB;gBAClF,QAAQ,EAAE,QAAQ;gBAClB,QAAQ,EAAE;oBACR,UAAU,EAAE,cAAc,CAAC,UAAU;oBACrC,YAAY,EAAE,YAAY,CAAC,IAAI;oBAC/B,SAAS;oBACT,aAAa,EAAE,gBAAgB;oBAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;oBACvC,cAAc,EAAE,YAAY,CAAC,cAAc;iBAC5C;gBACD,cAAc,EAAE,YAAY,CAAC,cAAc;gBAC3C,SAAS,EAAE,YAAY,CAAC,SAAS;aAClC,CAAC,CAAC;QACL,CAAC;QAED,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACnD,aAAa;YACb,SAAS;YACT,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,aAAa,EAAE,gBAAgB;YAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;YACvC,WAAW,EAAE,OAAO,CAAC,MAAM;YAC3B,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,EAAE,EAAE,SAAS;gBACb,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,aAAa,EAAE,gBAAgB;gBAC/B,WAAW,EAAE,cAAc,CAAC,WAAW;gBACvC,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,IAAI,EAAE,cAAc,CAAC,IAAI;gBACzB,QAAQ,EAAE,eAAe,CAAC,QAAQ;gBAClC,OAAO,EAAE,OAAO;gBAChB,SAAS,EAAE,GAAG;gBACd,OAAO,EAAE,uCAAuC;aACjD;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,sBAAsB,CAAC,OAAoB,EAAE,OAA0B;IAC3F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAEnE,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,oBAAoB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAE7D,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAA0B,KAAK,CAAC;QAEpD,2BAA2B;QAC3B,MAAM,CAAC,QAAQ,EAAE,OAAO,CAAC,GAAG,MAAM,OAAO,CAAC,GAAG,CAAC;YAC5C,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,cAAc,CAAC,UAAU,EAAE,cAAc,CAAC,UAAU,CAAC;YAC9E,aAAE,CAAC,QAAQ,CAAC,mBAAmB,EAAE,cAAc,CAAC,SAAS,EAAE,cAAc,CAAC,SAAS,CAAC;SACrF,CAAC,CAAC;QAEH,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,IAAI,CAAC,OAAO,EAAE,CAAC;YACb,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,mBAAmB,EAAE;aACzC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,YAAY,GAAG,QAAe,CAAC;QACrC,MAAM,WAAW,GAAG,OAAc,CAAC;QAEnC,qCAAqC;QACrC,IAAI,WAAW,CAAC,UAAU,KAAK,cAAc,CAAC,UAAU,EAAE,CAAC;YACzD,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,mDAAmD,EAAE;aACzE,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,yBAAyB;QACzB,MAAM,iBAAiB,GAAG,MAAM,2BAA2B,CAAC,YAAY,EAAE,IAAI,CAAC,CAAC;QAChF,IAAI,CAAC,iBAAiB,EAAE,CAAC;YACvB,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uDAAuD,EAAE;aAC7E,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,GAAG,GAAG,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE,CAAC;QAErC,gDAAgD;QAChD,IAAI,cAAc,CAAC,YAAY,EAAE,CAAC;YAChC,MAAM,qBAAqB,CAAC;gBAC1B,GAAG,OAAO;gBACV,IAAI,EAAE,KAAK,IAAI,EAAE,CAAC,CAAC;oBACjB,UAAU,EAAE,cAAc,CAAC,UAAU;oBACrC,WAAW,EAAE,WAAW,CAAC,IAAI;oBAC7B,OAAO,EAAE,sCAAsC,WAAW,CAAC,aAAa,EAAE;oBAC1E,IAAI,EAAE,CAAC,QAAQ,EAAE,aAAa,CAAC;oBAC/B,OAAO,EAAE,IAAI;iBACd,CAAC;aACI,EAAE,OAAO,CAAC,CAAC;QACrB,CAAC;QAED,sCAAsC;QACtC,MAAM,qBAAqB,CAAC,YAAY,EAAE,WAAW,CAAC,CAAC;QAEvD,gDAAgD;QAChD,MAAM,oBAAoB,GAAG,IAAA,SAAM,GAAE,CAAC;QACtC,MAAM,oBAAoB,GAAG,MAAM,uBAAuB,CAAC,cAAc,CAAC,UAAU,CAAC,CAAC;QACtF,MAAM,gBAAgB,GAAG,0BAA0B,CAAC,oBAAoB,EAAE,WAAW,CAAC,KAAK,CAAC,CAAC;QAE7F,MAAM,kBAAkB,GAAoB;YAC1C,EAAE,EAAE,oBAAoB;YACxB,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,aAAa,EAAE,gBAAgB;YAC/B,WAAW,EAAE,WAAW,CAAC,KAAK;YAC9B,MAAM,EAAE,aAAa,CAAC,MAAM;YAC5B,OAAO,EAAE,cAAc,CAAC,OAAO,IAAI,yBAAyB,WAAW,CAAC,aAAa,EAAE;YACvF,IAAI,EAAE,CAAC,UAAU,CAAC;YAClB,QAAQ,EAAE,WAAW,CAAC,QAAQ;YAC9B,WAAW,EAAE,WAAW,CAAC,WAAW;YACpC,QAAQ,EAAE,YAAY,CAAC,QAAQ,EAAE,wBAAwB;YACzD,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,GAAG;YACd,QAAQ,EAAE;gBACR,OAAO,EAAE,CAAC,yBAAyB,WAAW,CAAC,aAAa,EAAE,CAAC;gBAC/D,iBAAiB,EAAE,YAAY,CAAC,gBAAgB;gBAChD,UAAU,EAAE,IAAI;gBAChB,YAAY,EAAE,cAAc,CAAC,SAAS;aACvC;SACF,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,kBAAkB,CAAC,CAAC;QAE7D,kBAAkB;QAClB,MAAM,eAAe,GAAG;YACtB,GAAG,YAAY;YACf,gBAAgB,EAAE,oBAAoB;YACtC,oBAAoB,EAAE,gBAAgB;YACtC,YAAY,EAAE,CAAC,YAAY,CAAC,YAAY,IAAI,CAAC,CAAC,GAAG,CAAC;YAClD,SAAS,EAAE,GAAG;YACd,SAAS,EAAE,IAAI,CAAC,EAAE;SACnB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,2BAA2B;YACjC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,YAAY,CAAC,SAAS;YACjC,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,SAAS,EAAE,GAAG;YACd,OAAO,EAAE;gBACP,iBAAiB,EAAE,cAAc,CAAC,SAAS;gBAC3C,qBAAqB,EAAE,WAAW,CAAC,aAAa;gBAChD,YAAY,EAAE,oBAAoB;gBAClC,gBAAgB;gBAChB,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,OAAO,EAAE,cAAc,CAAC,OAAO;gBAC/B,aAAa,EAAE,cAAc,CAAC,YAAY;aAC3C;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,uBAAuB;QACvB,MAAM,oBAAY,CAAC,YAAY,CAAC;YAC9B,IAAI,EAAE,yBAAyB;YAC/B,WAAW,EAAE,oBAAoB;YACjC,aAAa,EAAE,iBAAiB;YAChC,OAAO,EAAE,CAAC;YACV,IAAI,EAAE;gBACJ,QAAQ,EAAE,YAAY;gBACtB,eAAe,EAAE,WAAW;gBAC5B,UAAU,EAAE,kBAAkB;gBAC9B,UAAU,EAAE,IAAI,CAAC,EAAE;aACpB;YACD,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,oBAAoB;QACpB,MAAM,kCAAmB,CAAC,gBAAgB,CAAC;YACzC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,IAAI,EAAE,2BAA2B;YACjC,KAAK,EAAE,2BAA2B;YAClC,OAAO,EAAE,aAAa,YAAY,CAAC,IAAI,kCAAkC,WAAW,CAAC,aAAa,GAAG;YACrG,QAAQ,EAAE,QAAQ;YAClB,QAAQ,EAAE;gBACR,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,iBAAiB,EAAE,cAAc,CAAC,SAAS;gBAC3C,qBAAqB,EAAE,WAAW,CAAC,aAAa;gBAChD,YAAY,EAAE,oBAAoB;gBAClC,gBAAgB;gBAChB,cAAc,EAAE,YAAY,CAAC,cAAc;aAC5C;YACD,cAAc,EAAE,YAAY,CAAC,cAAc;YAC3C,SAAS,EAAE,YAAY,CAAC,SAAS;SAClC,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,wCAAwC,EAAE;YACpD,aAAa;YACb,UAAU,EAAE,cAAc,CAAC,UAAU;YACrC,iBAAiB,EAAE,cAAc,CAAC,SAAS;YAC3C,qBAAqB,EAAE,WAAW,CAAC,aAAa;YAChD,YAAY,EAAE,oBAAoB;YAClC,gBAAgB;YAChB,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,UAAU,EAAE,cAAc,CAAC,UAAU;gBACrC,YAAY,EAAE,YAAY,CAAC,IAAI;gBAC/B,iBAAiB,EAAE,cAAc,CAAC,SAAS;gBAC3C,qBAAqB,EAAE,WAAW,CAAC,aAAa;gBAChD,YAAY,EAAE,oBAAoB;gBAClC,gBAAgB;gBAChB,aAAa,EAAE,cAAc,CAAC,YAAY;gBAC1C,OAAO,EAAE,wCAAwC;aAClD;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,iCAAiC,EAAE;YAC9C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AAEH,KAAK,UAAU,2BAA2B,CAAC,QAAa,EAAE,IAAS;IACjE,qCAAqC;IACrC,IAAI,QAAQ,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE,EAAE,CAAC;QACnC,OAAO,IAAI,CAAC;IACd,CAAC;IAED,gCAAgC;IAChC,IAAI,QAAQ,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ,EAAE,CAAC;QAC9C,MAAM,eAAe,GAAG,+FAA+F,CAAC;QACxH,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,sBAAsB,EAAE,eAAe,EAAE,CAAC,IAAI,CAAC,EAAE,EAAE,QAAQ,CAAC,cAAc,EAAE,QAAQ,CAAC,CAAC,CAAC;QAE/H,IAAI,WAAW,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YAC3B,MAAM,UAAU,GAAG,WAAW,CAAC,CAAC,CAAQ,CAAC;YACzC,OAAO,UAAU,CAAC,IAAI,KAAK,OAAO,IAAI,UAAU,CAAC,IAAI,KAAK,OAAO,IAAI,UAAU,CAAC,IAAI,KAAK,QAAQ,CAAC;QACpG,CAAC;IACH,CAAC;IAED,OAAO,KAAK,CAAC;AACf,CAAC;AAED,KAAK,UAAU,uBAAuB,CAAC,UAAkB;IACvD,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,uEAAuE,CAAC;QAC7F,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QAEtF,IAAI,QAAQ,CAAC,MAAM,KAAK,CAAC,EAAE,CAAC;YAC1B,OAAO,OAAO,CAAC;QACjB,CAAC;QAED,OAAQ,QAAQ,CAAC,CAAC,CAAS,CAAC,aAAa,CAAC;IAC5C,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,sCAAsC,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;QAC5E,OAAO,OAAO,CAAC;IACjB,CAAC;AACH,CAAC;AAED,SAAS,0BAA0B,CAAC,cAAsB,EAAE,WAAwB;IAClF,MAAM,CAAC,KAAK,EAAE,KAAK,EAAE,KAAK,CAAC,GAAG,cAAc,CAAC,KAAK,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,MAAM,CAAC,CAAC;IAEpE,QAAQ,WAAW,EAAE,CAAC;QACpB,KAAK,WAAW,CAAC,KAAK;YACpB,OAAO,GAAG,KAAK,GAAG,CAAC,MAAM,CAAC;QAC5B,KAAK,WAAW,CAAC,KAAK;YACpB,OAAO,GAAG,KAAK,IAAI,KAAK,GAAG,CAAC,IAAI,CAAC;QACnC,KAAK,WAAW,CAAC,KAAK;YACpB,OAAO,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;QAC1C,KAAK,WAAW,CAAC,IAAI,CAAC;QACtB;YACE,OAAO,GAAG,KAAK,IAAI,KAAK,IAAI,KAAK,GAAG,CAAC,EAAE,CAAC;IAC5C,CAAC;AACH,CAAC;AAED,KAAK,UAAU,iBAAiB,CAAC,QAAa,EAAE,SAAiB;IAC/D,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QACF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QAEF,6CAA6C;QAC7C,MAAM,gBAAgB,GAAG,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,eAAe,GAAG,YAAY,QAAQ,CAAC,cAAc,IAAI,QAAQ,CAAC,EAAE,IAAI,SAAS,EAAE,CAAC;QAC1F,MAAM,iBAAiB,GAAG,eAAe,CAAC,aAAa,CAAC,eAAe,CAAC,CAAC;QAEzE,MAAM,iBAAiB,CAAC,gBAAgB,CAAC,gBAAgB,CAAC,GAAG,CAAC,CAAC;QAE/D,OAAO,eAAe,CAAC;IACzB,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,QAAQ,CAAC,EAAE,EAAE,SAAS,EAAE,CAAC,CAAC;QAC7F,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,QAAgB;IAClD,0EAA0E;IAC1E,OAAO,QAAQ,IAAI,CAAC,GAAG,EAAE,IAAI,IAAI,CAAC,MAAM,EAAE,CAAC,QAAQ,CAAC,EAAE,CAAC,CAAC,SAAS,CAAC,CAAC,CAAC,EAAE,CAAC;AACzE,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,UAAkB,EAAE,QAAa;IACpE,8EAA8E;IAC9E,OAAO;QACL,iBAAiB;QACjB,mBAAmB;QACnB,mBAAmB;KACpB,CAAC;AACJ,CAAC;AAED,KAAK,UAAU,oBAAoB,CAAC,UAAkB;IACpD,IAAI,CAAC;QACH,MAAM,YAAY,GAAG,wFAAwF,CAAC;QAC9G,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,mBAAmB,EAAE,YAAY,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QAEtF,OAAO,QAAQ,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,CAAE,QAAQ,CAAC,CAAC,CAAS,CAAC,EAAE,CAAC,CAAC,CAAC,SAAS,CAAC;IACnE,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;QACzE,OAAO,SAAS,CAAC;IACnB,CAAC;AACH,CAAC;AAED,KAAK,UAAU,qBAAqB,CAAC,QAAa,EAAE,OAAY;IAC9D,IAAI,CAAC;QACH,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QACF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QAEF,kDAAkD;QAClD,MAAM,iBAAiB,GAAG,eAAe,CAAC,aAAa,CAAC,OAAO,CAAC,QAAQ,CAAC,CAAC;QAC1E,MAAM,kBAAkB,GAAG,eAAe,CAAC,aAAa,CAAC,QAAQ,CAAC,QAAQ,CAAC,CAAC;QAE5E,MAAM,kBAAkB,CAAC,gBAAgB,CAAC,iBAAiB,CAAC,GAAG,CAAC,CAAC;QAEjE,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACnD,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,SAAS,EAAE,OAAO,CAAC,EAAE;YACrB,aAAa,EAAE,OAAO,CAAC,aAAa;SACrC,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,mCAAmC,EAAE;YAChD,KAAK;YACL,UAAU,EAAE,QAAQ,CAAC,EAAE;YACvB,SAAS,EAAE,OAAO,CAAC,EAAE;SACtB,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,yBAAyB,EAAE;IAClC,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,iCAAiC;IACxC,OAAO,EAAE,qBAAqB;CAC/B,CAAC,CAAC;AAEH,eAAG,CAAC,IAAI,CAAC,0BAA0B,EAAE;IACnC,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,qDAAqD;IAC5D,OAAO,EAAE,sBAAsB;CAChC,CAAC,CAAC"}