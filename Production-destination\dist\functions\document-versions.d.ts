/**
 * Document Versions Function
 * Handles document version history and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get document versions handler
 */
export declare function getDocumentVersions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create document version handler
 */
export declare function createDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
