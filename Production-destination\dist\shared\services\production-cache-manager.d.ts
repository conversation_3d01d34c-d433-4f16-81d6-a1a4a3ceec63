/**
 * Production Cache Manager
 * Implements production-standard cache invalidation without relying on Redis pattern operations
 * Uses application-level tracking for better performance and reliability
 */
export interface CacheKey {
    key: string;
    tags: string[];
    ttl?: number;
    createdAt: Date;
}
export interface CacheInvalidationRule {
    pattern: string;
    tags: string[];
    relatedKeys: string[];
}
export declare class ProductionCacheManager {
    private static instance;
    private keyRegistry;
    private tagToKeys;
    private invalidationRules;
    private constructor();
    static getInstance(): ProductionCacheManager;
    /**
     * Setup default cache invalidation rules
     */
    private setupDefaultRules;
    /**
     * Add cache invalidation rule
     */
    addInvalidationRule(ruleId: string, rule: CacheInvalidationRule): void;
    /**
     * Register a cache key with tags for tracking
     */
    registerKey(key: string, tags: string[], ttl?: number): Promise<void>;
    /**
     * Set cache value with automatic registration
     */
    set(key: string, value: any, ttl?: number, tags?: string[]): Promise<boolean>;
    /**
     * Get cache value
     */
    get<T>(key: string): Promise<T | null>;
    /**
     * Delete cache key and unregister
     */
    delete(key: string): Promise<boolean>;
    /**
     * Invalidate cache by tags (production-safe approach)
     */
    invalidateByTags(tags: string[]): Promise<void>;
    /**
     * Invalidate cache by rule ID
     */
    invalidateByRule(ruleId: string, context?: Record<string, any>): Promise<void>;
    /**
     * Invalidate related keys with context substitution
     */
    private invalidateRelatedKeys;
    /**
     * Check if a key matches a pattern with context substitution
     */
    private matchesPattern;
    /**
     * Unregister a key from tracking
     */
    private unregisterKey;
    /**
     * Clean up expired keys from registry
     */
    cleanupExpiredKeys(): Promise<void>;
    /**
     * Get cache statistics
     */
    getStatistics(): {
        totalKeys: number;
        totalTags: number;
        totalRules: number;
        keysByTag: Record<string, number>;
    };
}
export declare const productionCacheManager: ProductionCacheManager;
export default productionCacheManager;
