/**
 * Document Archiving Function
 * Handles document archiving, retention, and lifecycle management
 * Migrated from old-arch/src/document-service/archiving/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Archive document handler
 */
export declare function archiveDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Restore document handler
 */
export declare function restoreDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
