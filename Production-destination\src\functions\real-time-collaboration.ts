/**
 * Real-Time Collaboration Function
 * Handles real-time document collaboration and editing
 * Migrated from old-arch/src/collaboration-service/session/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Joi from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { redis } from '../shared/services/redis';
import { eventService } from '../shared/services/event';
import { eventDrivenCache } from '../shared/services/event-driven-cache';

// Collaboration types and enums
enum SessionStatus {
  ACTIVE = 'ACTIVE',
  INACTIVE = 'INACTIVE',
  ENDED = 'ENDED'
}

enum OperationType {
  INSERT = 'INSERT',
  DELETE = 'DELETE',
  RETAIN = 'RETAIN',
  FORMAT = 'FORMAT'
}

enum CursorType {
  SELECTION = 'SELECTION',
  CARET = 'CARET'
}

// Validation schemas
const createSessionSchema = Joi.object({
  documentId: Joi.string().uuid().required(),
  sessionName: Joi.string().min(1).max(100).optional(),
  maxParticipants: Joi.number().min(1).max(50).default(10),
  allowAnonymous: Joi.boolean().default(false),
  permissions: Joi.object({
    canEdit: Joi.boolean().default(true),
    canComment: Joi.boolean().default(true),
    canShare: Joi.boolean().default(false)
  }).optional()
});

const joinSessionSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  displayName: Joi.string().min(1).max(50).optional()
});

const sendOperationSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  operations: Joi.array().items(Joi.object({
    type: Joi.string().valid(...Object.values(OperationType)).required(),
    position: Joi.number().min(0).required(),
    content: Joi.string().optional(),
    length: Joi.number().min(0).optional(),
    attributes: Joi.object().optional()
  })).min(1).required(),
  revision: Joi.number().min(0).required(),
  clientId: Joi.string().required()
});

const updateCursorSchema = Joi.object({
  sessionId: Joi.string().uuid().required(),
  cursor: Joi.object({
    type: Joi.string().valid(...Object.values(CursorType)).required(),
    position: Joi.number().min(0).required(),
    length: Joi.number().min(0).optional(),
    userId: Joi.string().uuid().required()
  }).required()
});

interface CreateSessionRequest {
  documentId: string;
  sessionName?: string;
  maxParticipants?: number;
  allowAnonymous?: boolean;
  permissions?: {
    canEdit?: boolean;
    canComment?: boolean;
    canShare?: boolean;
  };
}

interface CollaborationSession {
  id: string;
  documentId: string;
  sessionName: string;
  status: SessionStatus;
  createdBy: string;
  createdAt: string;
  lastActivity: string;
  maxParticipants: number;
  allowAnonymous: boolean;
  permissions: {
    canEdit: boolean;
    canComment: boolean;
    canShare: boolean;
  };
  participants: Array<{
    userId: string;
    displayName: string;
    joinedAt: string;
    isActive: boolean;
    cursor?: any;
  }>;
  currentRevision: number;
  organizationId: string;
  tenantId: string;
}

interface Operation {
  type: OperationType;
  position: number;
  content?: string;
  length?: number;
  attributes?: any;
}

interface OperationMessage {
  id: string;
  sessionId: string;
  userId: string;
  operations: Operation[];
  revision: number;
  clientId: string;
  timestamp: string;
}

/**
 * Create collaboration session handler
 */
export async function createCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create collaboration session started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = createSessionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const sessionRequest: CreateSessionRequest = value;

    // Get document and check access
    const document = await db.readItem('documents', sessionRequest.documentId, sessionRequest.documentId);
    if (!document) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Document not found" }
      }, request);
    }

    const documentData = document as any;

    // Check document access
    const hasAccess = await checkDocumentAccess(documentData, user.id);
    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to document" }
      }, request);
    }

    // Check if there's already an active session for this document
    const existingSession = await getActiveSessionForDocument(sessionRequest.documentId);
    if (existingSession) {
      return addCorsHeaders({
        status: 200,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          session: existingSession,
          message: "Active session already exists for this document"
        }
      }, request);
    }

    // Create collaboration session
    const sessionId = uuidv4();
    const now = new Date().toISOString();

    const session: CollaborationSession = {
      id: sessionId,
      documentId: sessionRequest.documentId,
      sessionName: sessionRequest.sessionName || `${documentData.name} - Collaboration`,
      status: SessionStatus.ACTIVE,
      createdBy: user.id,
      createdAt: now,
      lastActivity: now,
      maxParticipants: sessionRequest.maxParticipants || 10,
      allowAnonymous: sessionRequest.allowAnonymous || false,
      permissions: {
        canEdit: sessionRequest.permissions?.canEdit ?? true,
        canComment: sessionRequest.permissions?.canComment ?? true,
        canShare: sessionRequest.permissions?.canShare ?? false
      },
      participants: [{
        userId: user.id,
        displayName: user.name || user.email,
        joinedAt: now,
        isActive: true
      }],
      currentRevision: 0,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId || user.id
    };

    await db.createItem('collaboration-sessions', session);

    // Store session in Redis for real-time access
    await redis.setex(`session:${sessionId}`, 3600, JSON.stringify(session)); // 1 hour TTL

    // Initialize document state in Redis using enhanced service
    await redis.setDocumentContent(sessionRequest.documentId, documentData.content || '', 3600);
    await redis.set(`document:${sessionRequest.documentId}:revision`, '0');

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "collaboration_session_created",
      userId: user.id,
      organizationId: documentData.organizationId,
      projectId: documentData.projectId,
      documentId: sessionRequest.documentId,
      timestamp: now,
      details: {
        sessionId,
        sessionName: session.sessionName,
        documentName: documentData.name,
        maxParticipants: session.maxParticipants
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'CollaborationSessionCreated',
      aggregateId: sessionId,
      aggregateType: 'CollaborationSession',
      version: 1,
      data: {
        session,
        document: documentData,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: documentData.organizationId,
      tenantId: user.tenantId
    });

    // Emit cache invalidation event for document-related caches
    await eventDrivenCache.processInvalidationEvent({
      eventType: 'document.updated',
      resourceId: sessionRequest.documentId,
      organizationId: documentData.organizationId,
      userId: user.id,
      timestamp: new Date(),
      metadata: { sessionId, action: 'session_created' }
    });

    logger.info("Collaboration session created successfully", {
      correlationId,
      sessionId,
      documentId: sessionRequest.documentId,
      documentName: documentData.name,
      createdBy: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        session: {
          id: sessionId,
          documentId: sessionRequest.documentId,
          sessionName: session.sessionName,
          status: SessionStatus.ACTIVE,
          participants: session.participants,
          permissions: session.permissions,
          currentRevision: 0,
          createdAt: now
        },
        message: "Collaboration session created successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create collaboration session failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Join collaboration session handler
 */
export async function joinCollaborationSession(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Join collaboration session started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = joinSessionSchema.validate(body);

    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: {
          error: 'Validation Error',
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const joinRequest = value;

    // Get session from Redis first, then database
    let session = await getSessionFromRedis(joinRequest.sessionId);
    if (!session) {
      const sessionDoc = await db.readItem('collaboration-sessions', joinRequest.sessionId, joinRequest.sessionId);
      if (!sessionDoc) {
        return addCorsHeaders({
          status: 404,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Collaboration session not found" }
        }, request);
      }
      session = sessionDoc as any;
    }

    // Check if session is active
    if (session.status !== SessionStatus.ACTIVE) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Collaboration session is not active" }
      }, request);
    }

    // Check if user is already in session
    const existingParticipant = session.participants.find((p: any) => p.userId === user.id);
    if (existingParticipant) {
      // Update participant as active
      existingParticipant.isActive = true;
      existingParticipant.displayName = joinRequest.displayName || existingParticipant.displayName;
    } else {
      // Check participant limit
      const activeParticipants = session.participants.filter((p: any) => p.isActive);
      if (activeParticipants.length >= session.maxParticipants) {
        return addCorsHeaders({
          status: 400,
          headers: { 'Content-Type': 'application/json' },
          jsonBody: { error: "Session has reached maximum participants" }
        }, request);
      }

      // Add new participant
      session.participants.push({
        userId: user.id,
        displayName: joinRequest.displayName || user.name || user.email,
        joinedAt: new Date().toISOString(),
        isActive: true
      });
    }

    // Update session
    session.lastActivity = new Date().toISOString();
    await db.updateItem('collaboration-sessions', session);
    await redis.setex(`session:${joinRequest.sessionId}`, 3600, JSON.stringify(session));

    // Get current document content and revision with database fallback
    const currentContent = await redis.getDocumentContent(session.documentId, true) || '';
    const currentRevision = parseInt(await redis.get(`document:${session.documentId}:revision`) || '0');

    // Broadcast participant joined event
    await broadcastToSession(joinRequest.sessionId, {
      type: 'participant_joined',
      participant: {
        userId: user.id,
        displayName: joinRequest.displayName || user.name || user.email
      },
      timestamp: new Date().toISOString()
    });

    // Emit cache invalidation event for session-related caches
    await eventDrivenCache.processInvalidationEvent({
      eventType: 'user.updated',
      resourceId: user.id,
      organizationId: session.organizationId,
      userId: user.id,
      timestamp: new Date(),
      metadata: { sessionId: joinRequest.sessionId, action: 'session_joined' }
    });

    logger.info("User joined collaboration session successfully", {
      correlationId,
      sessionId: joinRequest.sessionId,
      userId: user.id,
      participantCount: session.participants.filter((p: any) => p.isActive).length
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: {
        session: {
          id: session.id,
          documentId: session.documentId,
          sessionName: session.sessionName,
          participants: session.participants.filter((p: any) => p.isActive),
          permissions: session.permissions,
          currentRevision,
          currentContent
        },
        message: "Joined collaboration session successfully"
      }
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Join collaboration session failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Helper functions
 */

async function checkDocumentAccess(document: any, userId: string): Promise<boolean> {
  try {
    // Check if user is the owner
    if (document.createdBy === userId) {
      return true;
    }

    // Check organization membership
    const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);

    return memberships.length > 0;
  } catch (error) {
    logger.error('Failed to check document access', { error, documentId: document.id, userId });
    return false;
  }
}

async function getActiveSessionForDocument(documentId: string): Promise<any> {
  try {
    const query = 'SELECT * FROM c WHERE c.documentId = @docId AND c.status = @status';
    const sessions = await db.queryItems('collaboration-sessions', query, [documentId, SessionStatus.ACTIVE]);
    return sessions.length > 0 ? sessions[0] : null;
  } catch (error) {
    logger.error('Failed to get active session for document', { error, documentId });
    return null;
  }
}

async function getSessionFromRedis(sessionId: string): Promise<any> {
  try {
    // Use enhanced Redis service with database fallback
    return await redis.getSession(sessionId, true);
  } catch (error) {
    logger.error('Failed to get session from Redis with fallback', { error, sessionId });
    return null;
  }
}

async function broadcastToSession(sessionId: string, message: any): Promise<void> {
  try {
    // In a real implementation, this would use WebSockets or SignalR
    // For now, we'll store the message in Redis for polling
    const messageId = uuidv4();
    await redis.lpush(`session:${sessionId}:messages`, JSON.stringify({
      id: messageId,
      ...message,
      timestamp: new Date().toISOString()
    }));

    // Keep only last 100 messages
    await redis.ltrim(`session:${sessionId}:messages`, 0, 99);

  } catch (error) {
    logger.error('Failed to broadcast to session', { error, sessionId });
  }
}

// Register functions
app.http('collaboration-session-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions',
  handler: createCollaborationSession
});

app.http('collaboration-session-join', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'collaboration/sessions/join',
  handler: joinCollaborationSession
});
