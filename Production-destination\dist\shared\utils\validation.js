"use strict";
/**
 * Validation utilities for Azure Functions
 * Provides common validation functions and schemas
 */
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.schemas = void 0;
exports.validateBody = validateBody;
exports.validateQuery = validateQuery;
exports.validateParams = validateParams;
exports.withValidation = withValidation;
const Joi = __importStar(require("joi"));
/**
 * Common validation schemas
 */
exports.schemas = {
    email: Joi.string().email().required(),
    password: Joi.string().min(8).required(),
    uuid: Joi.string().uuid().required(),
    tenantId: Joi.string().uuid().required(),
    userId: Joi.string().uuid().required(),
    documentId: Joi.string().uuid().required(),
    workflowId: Joi.string().uuid().required(),
    templateId: Joi.string().uuid().required(),
    pagination: Joi.object({
        page: Joi.number().integer().min(1).default(1),
        limit: Joi.number().integer().min(1).max(100).default(20),
        sortBy: Joi.string().optional(),
        sortOrder: Joi.string().valid('asc', 'desc').default('desc')
    }),
    dateRange: Joi.object({
        startDate: Joi.date().iso().optional(),
        endDate: Joi.date().iso().min(Joi.ref('startDate')).optional()
    })
};
/**
 * Validate request body against schema
 */
async function validateBody(request, schema) {
    try {
        const body = await request.json();
        const { error, value } = schema.validate(body, { abortEarly: false });
        if (error) {
            return {
                isValid: false,
                error: error.details.map(detail => detail.message).join(', ')
            };
        }
        return {
            isValid: true,
            value
        };
    }
    catch (error) {
        return {
            isValid: false,
            error: 'Invalid JSON in request body'
        };
    }
}
/**
 * Validate query parameters against schema
 */
function validateQuery(request, schema) {
    try {
        const query = Object.fromEntries(request.query.entries());
        const { error, value } = schema.validate(query, { abortEarly: false });
        if (error) {
            return {
                isValid: false,
                error: error.details.map(detail => detail.message).join(', ')
            };
        }
        return {
            isValid: true,
            value
        };
    }
    catch (error) {
        return {
            isValid: false,
            error: 'Invalid query parameters'
        };
    }
}
/**
 * Validate path parameters against schema
 */
function validateParams(params, schema) {
    try {
        const { error, value } = schema.validate(params, { abortEarly: false });
        if (error) {
            return {
                isValid: false,
                error: error.details.map(detail => detail.message).join(', ')
            };
        }
        return {
            isValid: true,
            value
        };
    }
    catch (error) {
        return {
            isValid: false,
            error: 'Invalid path parameters'
        };
    }
}
/**
 * Create validation middleware
 */
function withValidation(bodySchema, querySchema, paramsSchema) {
    return function (handler) {
        return async (request, context) => {
            const validated = {};
            // Validate body if schema provided
            if (bodySchema) {
                const bodyResult = await validateBody(request, bodySchema);
                if (!bodyResult.isValid) {
                    return {
                        status: 400,
                        headers: { 'Content-Type': 'application/json' },
                        jsonBody: {
                            error: 'Validation Error',
                            message: bodyResult.error
                        }
                    };
                }
                validated.body = bodyResult.value;
            }
            // Validate query if schema provided
            if (querySchema) {
                const queryResult = validateQuery(request, querySchema);
                if (!queryResult.isValid) {
                    return {
                        status: 400,
                        headers: { 'Content-Type': 'application/json' },
                        jsonBody: {
                            error: 'Validation Error',
                            message: queryResult.error
                        }
                    };
                }
                validated.query = queryResult.value;
            }
            // Validate params if schema provided
            if (paramsSchema) {
                const params = request.params || {};
                const paramsResult = validateParams(params, paramsSchema);
                if (!paramsResult.isValid) {
                    return {
                        status: 400,
                        headers: { 'Content-Type': 'application/json' },
                        jsonBody: {
                            error: 'Validation Error',
                            message: paramsResult.error
                        }
                    };
                }
                validated.params = paramsResult.value;
            }
            return await handler(request, context, validated);
        };
    };
}
//# sourceMappingURL=validation.js.map