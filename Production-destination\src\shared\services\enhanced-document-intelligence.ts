/**
 * Enhanced Document Intelligence Service
 * Production-ready implementation for comprehensive document analysis and storage
 */

import { DocumentAnalysisClient, AzureKeyCredential } from '@azure/ai-form-recognizer';
import { logger } from '../utils/logger';
import { db } from './database';
import { ragService } from './rag-service';
import { aiServices } from './ai-services';

export interface DocumentAnalysisResult {
  documentId: string;
  extractedText: string;
  layout: DocumentLayout;
  tables: DocumentTable[];
  keyValuePairs: KeyValuePair[];
  entities: DocumentEntity[];
  signatures: DocumentSignature[];
  barcodes: DocumentBarcode[];
  formulas: DocumentFormula[];
  metadata: DocumentMetadata;
  confidence: number;
  processingTime: number;
  modelUsed: string;
}

export interface DocumentLayout {
  pages: DocumentPage[];
  readingOrder: ReadingOrderElement[];
  styles: DocumentStyle[];
  languages: DetectedLanguage[];
}

export interface DocumentPage {
  pageNumber: number;
  width: number;
  height: number;
  angle: number;
  unit: string;
  lines: DocumentLine[];
  words: DocumentWord[];
  paragraphs: DocumentParagraph[];
  sections: DocumentSection[];
}

export interface DocumentLine {
  content: string;
  boundingBox: number[];
  confidence: number;
  words: DocumentWord[];
}

export interface DocumentWord {
  content: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentParagraph {
  content: string;
  boundingBox: number[];
  role?: string;
  spans: TextSpan[];
}

export interface DocumentSection {
  spans: TextSpan[];
  elements: string[];
}

export interface DocumentTable {
  rowCount: number;
  columnCount: number;
  cells: DocumentTableCell[];
  boundingBox: number[];
  confidence: number;
}

export interface DocumentTableCell {
  content: string;
  rowIndex: number;
  columnIndex: number;
  rowSpan?: number;
  columnSpan?: number;
  boundingBox: number[];
  confidence: number;
  kind?: string;
}

export interface KeyValuePair {
  key: string;
  value: string;
  keyConfidence: number;
  valueConfidence: number;
  keyBoundingBox?: number[];
  valueBoundingBox?: number[];
}

export interface DocumentEntity {
  category: string;
  subCategory?: string;
  content: string;
  boundingBox: number[];
  confidence: number;
  offset: number;
  length: number;
}

export interface DocumentSignature {
  type: 'handwritten' | 'digital';
  confidence: number;
  boundingBox: number[];
  verified?: boolean;
}

export interface DocumentBarcode {
  kind: string;
  value: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentFormula {
  kind: string;
  value: string;
  boundingBox: number[];
  confidence: number;
}

export interface DocumentMetadata {
  pageCount: number;
  documentSize: number;
  creationDate?: string;
  modificationDate?: string;
  author?: string;
  title?: string;
  subject?: string;
  keywords?: string[];
  producer?: string;
  creator?: string;
}

export interface ReadingOrderElement {
  content: string;
  boundingBox: number[];
  kind: string;
}

export interface DocumentStyle {
  isHandwritten?: boolean;
  spans: TextSpan[];
  confidence?: number;
}

export interface DetectedLanguage {
  locale: string;
  confidence: number;
  spans: TextSpan[];
}

export interface TextSpan {
  offset: number;
  length: number;
}

export class EnhancedDocumentIntelligenceService {
  private client: DocumentAnalysisClient;
  private initialized: boolean = false;

  constructor() {
    const endpoint = process.env.AI_DOCUMENT_INTELLIGENCE_ENDPOINT || '';
    const key = process.env.AI_DOCUMENT_INTELLIGENCE_KEY || '';
    
    if (!endpoint || !key) {
      throw new Error('Document Intelligence endpoint and key must be configured');
    }

    this.client = new DocumentAnalysisClient(endpoint, new AzureKeyCredential(key));
  }

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      this.initialized = true;
      logger.info('Enhanced Document Intelligence Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize Enhanced Document Intelligence Service', { error });
      throw error;
    }
  }

  /**
   * Perform comprehensive document analysis
   */
  async analyzeDocument(
    documentBuffer: Buffer,
    documentId: string,
    modelId?: string,
    features?: string[]
  ): Promise<DocumentAnalysisResult> {
    await this.initialize();

    const startTime = Date.now();
    const model = modelId || process.env.AI_DOCUMENT_INTELLIGENCE_DEFAULT_MODEL_ID || 'prebuilt-layout';

    try {
      logger.info('Starting comprehensive document analysis', {
        documentId,
        modelId: model,
        documentSize: documentBuffer.length,
        features
      });

      // Analyze document with Azure Document Intelligence
      const poller = await this.client.beginAnalyzeDocument(model, documentBuffer, {
        features: features as any[]
      });
      const result = await poller.pollUntilDone();

      if (!result) {
        throw new Error('No analysis result received from Document Intelligence');
      }

      // Extract comprehensive information
      const analysisResult: DocumentAnalysisResult = {
        documentId,
        extractedText: result.content || '',
        layout: this.extractLayout(result),
        tables: this.extractTables(result),
        keyValuePairs: this.extractKeyValuePairs(result),
        entities: this.extractEntities(result),
        signatures: this.extractSignatures(result),
        barcodes: this.extractBarcodes(result),
        formulas: this.extractFormulas(result),
        metadata: this.extractMetadata(result),
        confidence: this.calculateOverallConfidence(result),
        processingTime: Date.now() - startTime,
        modelUsed: model
      };

      // Store analysis results in database
      await this.storeAnalysisResults(analysisResult);

      // Index document for RAG if text content is available
      if (analysisResult.extractedText) {
        await this.indexDocumentForRAG(analysisResult);
      }

      // Generate AI-powered insights
      await this.generateDocumentInsights(analysisResult);

      logger.info('Document analysis completed successfully', {
        documentId,
        extractedTextLength: analysisResult.extractedText.length,
        tablesFound: analysisResult.tables.length,
        keyValuePairsFound: analysisResult.keyValuePairs.length,
        entitiesFound: analysisResult.entities.length,
        confidence: analysisResult.confidence,
        processingTime: analysisResult.processingTime
      });

      return analysisResult;

    } catch (error) {
      logger.error('Document analysis failed', {
        documentId,
        modelId: model,
        error
      });
      throw error;
    }
  }

  /**
   * Extract layout information
   */
  private extractLayout(result: any): DocumentLayout {
    const layout: DocumentLayout = {
      pages: [],
      readingOrder: [],
      styles: [],
      languages: []
    };

    // Extract pages
    if (result.pages) {
      layout.pages = result.pages.map((page: any, index: number) => ({
        pageNumber: index + 1,
        width: page.width || 0,
        height: page.height || 0,
        angle: page.angle || 0,
        unit: page.unit || 'pixel',
        lines: page.lines?.map((line: any) => ({
          content: line.content || '',
          boundingBox: line.boundingBox || [],
          confidence: line.confidence || 0,
          words: line.words?.map((word: any) => ({
            content: word.content || '',
            boundingBox: word.boundingBox || [],
            confidence: word.confidence || 0
          })) || []
        })) || [],
        words: page.words?.map((word: any) => ({
          content: word.content || '',
          boundingBox: word.boundingBox || [],
          confidence: word.confidence || 0
        })) || [],
        paragraphs: page.paragraphs?.map((para: any) => ({
          content: para.content || '',
          boundingBox: para.boundingBox || [],
          role: para.role,
          spans: para.spans || []
        })) || [],
        sections: page.sections?.map((section: any) => ({
          spans: section.spans || [],
          elements: section.elements || []
        })) || []
      }));
    }

    // Extract reading order
    if (result.paragraphs) {
      layout.readingOrder = result.paragraphs.map((para: any) => ({
        content: para.content || '',
        boundingBox: para.boundingBox || [],
        kind: para.role || 'paragraph'
      }));
    }

    // Extract styles
    if (result.styles) {
      layout.styles = result.styles.map((style: any) => ({
        isHandwritten: style.isHandwritten,
        spans: style.spans || [],
        confidence: style.confidence
      }));
    }

    // Extract languages
    if (result.languages) {
      layout.languages = result.languages.map((lang: any) => ({
        locale: lang.locale || 'en',
        confidence: lang.confidence || 0,
        spans: lang.spans || []
      }));
    }

    return layout;
  }

  /**
   * Extract tables
   */
  private extractTables(result: any): DocumentTable[] {
    if (!result.tables) return [];

    return result.tables.map((table: any) => ({
      rowCount: table.rowCount || 0,
      columnCount: table.columnCount || 0,
      cells: table.cells?.map((cell: any) => ({
        content: cell.content || '',
        rowIndex: cell.rowIndex || 0,
        columnIndex: cell.columnIndex || 0,
        rowSpan: cell.rowSpan,
        columnSpan: cell.columnSpan,
        boundingBox: cell.boundingBox || [],
        confidence: cell.confidence || 0,
        kind: cell.kind
      })) || [],
      boundingBox: table.boundingBox || [],
      confidence: table.confidence || 0
    }));
  }

  /**
   * Extract key-value pairs
   */
  private extractKeyValuePairs(result: any): KeyValuePair[] {
    if (!result.keyValuePairs) return [];

    return result.keyValuePairs.map((kvp: any) => ({
      key: kvp.key?.content || '',
      value: kvp.value?.content || '',
      keyConfidence: kvp.key?.confidence || 0,
      valueConfidence: kvp.value?.confidence || 0,
      keyBoundingBox: kvp.key?.boundingBox,
      valueBoundingBox: kvp.value?.boundingBox
    }));
  }

  /**
   * Extract entities
   */
  private extractEntities(result: any): DocumentEntity[] {
    if (!result.entities) return [];

    return result.entities.map((entity: any) => ({
      category: entity.category || '',
      subCategory: entity.subCategory,
      content: entity.content || '',
      boundingBox: entity.boundingBox || [],
      confidence: entity.confidence || 0,
      offset: entity.offset || 0,
      length: entity.length || 0
    }));
  }

  /**
   * Extract signatures (placeholder - would need specific model)
   */
  private extractSignatures(result: any): DocumentSignature[] {
    // This would require a specific signature detection model
    return [];
  }

  /**
   * Extract barcodes (placeholder - would need specific model)
   */
  private extractBarcodes(result: any): DocumentBarcode[] {
    // This would require a specific barcode detection model
    return [];
  }

  /**
   * Extract formulas (placeholder - would need specific model)
   */
  private extractFormulas(result: any): DocumentFormula[] {
    // This would require a specific formula detection model
    return [];
  }

  /**
   * Extract metadata
   */
  private extractMetadata(result: any): DocumentMetadata {
    return {
      pageCount: result.pages?.length || 0,
      documentSize: 0, // Would need to be passed from caller
      creationDate: result.metadata?.creationDate,
      modificationDate: result.metadata?.modificationDate,
      author: result.metadata?.author,
      title: result.metadata?.title,
      subject: result.metadata?.subject,
      keywords: result.metadata?.keywords,
      producer: result.metadata?.producer,
      creator: result.metadata?.creator
    };
  }

  /**
   * Calculate overall confidence
   */
  private calculateOverallConfidence(result: any): number {
    const confidenceValues: number[] = [];

    // Collect confidence values from various elements
    if (result.pages) {
      result.pages.forEach((page: any) => {
        page.lines?.forEach((line: any) => {
          if (line.confidence) confidenceValues.push(line.confidence);
        });
      });
    }

    if (result.tables) {
      result.tables.forEach((table: any) => {
        if (table.confidence) confidenceValues.push(table.confidence);
      });
    }

    if (result.keyValuePairs) {
      result.keyValuePairs.forEach((kvp: any) => {
        if (kvp.key?.confidence) confidenceValues.push(kvp.key.confidence);
        if (kvp.value?.confidence) confidenceValues.push(kvp.value.confidence);
      });
    }

    if (confidenceValues.length === 0) return 0.5;

    return confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length;
  }

  /**
   * Store analysis results in database
   */
  private async storeAnalysisResults(result: DocumentAnalysisResult): Promise<void> {
    try {
      const analysisRecord = {
        id: `analysis-${result.documentId}-${Date.now()}`,
        documentId: result.documentId,
        analysisType: 'comprehensive',
        results: result,
        createdAt: new Date().toISOString(),
        modelUsed: result.modelUsed,
        confidence: result.confidence,
        processingTime: result.processingTime
      };

      await db.createItem('document-analyses', analysisRecord);

      // Update document with latest analysis
      const document = await db.readItem('documents', result.documentId, result.documentId);
      if (document) {
        const updatedDocument = {
          ...document,
          extractedText: result.extractedText,
          documentIntelligence: {
            lastAnalysisId: analysisRecord.id,
            lastAnalyzedAt: new Date().toISOString(),
            tablesCount: result.tables.length,
            keyValuePairsCount: result.keyValuePairs.length,
            entitiesCount: result.entities.length,
            confidence: result.confidence,
            hasLayout: result.layout.pages.length > 0,
            hasStructuredData: result.tables.length > 0 || result.keyValuePairs.length > 0
          },
          updatedAt: new Date().toISOString()
        };

        await db.updateItem('documents', updatedDocument);
      }

    } catch (error) {
      logger.error('Failed to store analysis results', {
        documentId: result.documentId,
        error
      });
    }
  }

  /**
   * Index document for RAG
   */
  private async indexDocumentForRAG(result: DocumentAnalysisResult): Promise<void> {
    try {
      await ragService.indexDocument({
        documentId: result.documentId,
        content: result.extractedText,
        metadata: {
          hasLayout: result.layout.pages.length > 0,
          tablesCount: result.tables.length,
          keyValuePairsCount: result.keyValuePairs.length,
          entitiesCount: result.entities.length,
          confidence: result.confidence,
          modelUsed: result.modelUsed
        }
      });

      logger.info('Document indexed for RAG', {
        documentId: result.documentId,
        contentLength: result.extractedText.length
      });

    } catch (error) {
      logger.error('Failed to index document for RAG', {
        documentId: result.documentId,
        error
      });
    }
  }

  /**
   * Generate AI-powered insights
   */
  private async generateDocumentInsights(result: DocumentAnalysisResult): Promise<void> {
    try {
      if (!result.extractedText || result.extractedText.length < 100) {
        return; // Skip insights for very short documents
      }

      const prompt = `Analyze this document and provide insights about its content, structure, and potential use cases:

Document Type: Based on structure and content
Key Information: Important data points found
Business Value: How this document could be used
Data Quality: Assessment of the extracted information
Recommendations: Suggestions for processing or categorization

Document Content:
${result.extractedText.substring(0, 2000)}...

Tables Found: ${result.tables.length}
Key-Value Pairs: ${result.keyValuePairs.length}
Entities: ${result.entities.length}`;

      const insights = await aiServices.reason(prompt, [], {
        systemPrompt: 'You are a document analysis expert. Provide structured insights about documents.',
        maxTokens: 1000,
        temperature: 0.3
      });

      // Store insights
      const insightsRecord = {
        id: `insights-${result.documentId}-${Date.now()}`,
        documentId: result.documentId,
        insights: insights.content,
        reasoning: insights.reasoning,
        confidence: insights.confidence,
        generatedAt: new Date().toISOString(),
        tokensUsed: insights.tokensUsed
      };

      await db.createItem('document-insights', insightsRecord);

      logger.info('Document insights generated', {
        documentId: result.documentId,
        confidence: insights.confidence
      });

    } catch (error) {
      logger.error('Failed to generate document insights', {
        documentId: result.documentId,
        error
      });
    }
  }
}

// Export singleton instance
export const enhancedDocumentIntelligence = new EnhancedDocumentIntelligenceService();
