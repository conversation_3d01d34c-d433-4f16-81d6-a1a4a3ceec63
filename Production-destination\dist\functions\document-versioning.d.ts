/**
 * Document Versioning Function
 * Handles document version management, comparison, and restoration
 * Migrated from old-arch/src/document-service/versioning/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create document version handler
 */
export declare function createDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Restore document version handler
 */
export declare function restoreDocumentVersion(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
