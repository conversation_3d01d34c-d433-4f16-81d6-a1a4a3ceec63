/**
 * User Personalization Function
 * Handles user personalization settings for search and content preferences
 * Migrated from old-arch/src/user-service/personalization/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get user personalization settings handler
 */
export declare function getUserPersonalization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update user personalization settings handler
 */
export declare function updateUserPersonalization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
