/**
 * File Processing Function
 * Handles file processing, transformation, and analysis
 * Migrated from old-arch/src/file-service/processing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Process file handler
 */
export declare function processFile(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get processing status handler
 */
export declare function getProcessingStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
