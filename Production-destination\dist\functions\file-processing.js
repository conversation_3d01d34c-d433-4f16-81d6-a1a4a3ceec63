"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.processFile = processFile;
exports.getProcessingStatus = getProcessingStatus;
/**
 * File Processing Function
 * Handles file processing, transformation, and analysis
 * Migrated from old-arch/src/file-service/processing/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Processing types and enums
var ProcessingType;
(function (ProcessingType) {
    ProcessingType["TEXT_EXTRACTION"] = "TEXT_EXTRACTION";
    ProcessingType["IMAGE_ANALYSIS"] = "IMAGE_ANALYSIS";
    ProcessingType["DOCUMENT_CONVERSION"] = "DOCUMENT_CONVERSION";
    ProcessingType["THUMBNAIL_GENERATION"] = "THUMBNAIL_GENERATION";
    ProcessingType["METADATA_EXTRACTION"] = "METADATA_EXTRACTION";
    ProcessingType["VIRUS_SCAN"] = "VIRUS_SCAN";
    ProcessingType["CONTENT_ANALYSIS"] = "CONTENT_ANALYSIS";
})(ProcessingType || (ProcessingType = {}));
var ProcessingStatus;
(function (ProcessingStatus) {
    ProcessingStatus["PENDING"] = "PENDING";
    ProcessingStatus["PROCESSING"] = "PROCESSING";
    ProcessingStatus["COMPLETED"] = "COMPLETED";
    ProcessingStatus["FAILED"] = "FAILED";
    ProcessingStatus["CANCELLED"] = "CANCELLED";
})(ProcessingStatus || (ProcessingStatus = {}));
// Validation schemas
const processFileSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    processingTypes: Joi.array().items(Joi.string().valid(...Object.values(ProcessingType))).min(1).required(),
    options: Joi.object({
        extractImages: Joi.boolean().default(false),
        generateThumbnails: Joi.boolean().default(true),
        ocrLanguage: Joi.string().max(10).default('en'),
        outputFormat: Joi.string().valid('pdf', 'docx', 'txt', 'html').optional(),
        quality: Joi.string().valid('low', 'medium', 'high').default('medium'),
        priority: Joi.string().valid('low', 'normal', 'high').default('normal')
    }).optional()
});
/**
 * Process file handler
 */
async function processFile(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Process file started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = processFileSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const processRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', processRequest.documentId, processRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Check processing limits
        const canProcess = await checkProcessingLimits(documentData.organizationId);
        if (!canProcess.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canProcess.reason }
            }, request);
        }
        // Create processing job
        const jobId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const processingJob = {
            id: jobId,
            documentId: processRequest.documentId,
            processingTypes: processRequest.processingTypes,
            status: ProcessingStatus.PENDING,
            options: {
                extractImages: false,
                generateThumbnails: true,
                ocrLanguage: 'en',
                quality: 'medium',
                priority: 'normal',
                ...processRequest.options
            },
            progress: {
                percentage: 0,
                currentStep: 'Initializing',
                completedSteps: [],
                totalSteps: processRequest.processingTypes.length,
            },
            results: {},
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('processing-jobs', processingJob);
        // Start processing asynchronously
        await startFileProcessing(processingJob, documentData);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "file_processing_started",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: processRequest.documentId,
            timestamp: now,
            details: {
                jobId,
                documentName: documentData.name,
                processingTypes: processRequest.processingTypes,
                stepCount: processRequest.processingTypes.length,
                priority: processingJob.options.priority
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'FileProcessingStarted',
            aggregateId: processRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentData,
                processingJob,
                startedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("File processing started successfully", {
            correlationId,
            jobId,
            documentId: processRequest.documentId,
            documentName: documentData.name,
            processingTypes: processRequest.processingTypes,
            startedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                jobId,
                documentId: processRequest.documentId,
                documentName: documentData.name,
                processingTypes: processRequest.processingTypes,
                status: ProcessingStatus.PENDING,
                totalSteps: processRequest.processingTypes.length,
                estimatedDuration: estimateProcessingDuration(processRequest.processingTypes, documentData),
                createdAt: now,
                message: "File processing started successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Process file failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get processing status handler
 */
async function getProcessingStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const jobId = request.params.jobId;
    logger_1.logger.info("Get processing status started", { correlationId, jobId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!jobId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Job ID is required" }
            }, request);
        }
        // Get processing job
        const processingJob = await database_1.db.readItem('processing-jobs', jobId, jobId);
        if (!processingJob) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Processing job not found" }
            }, request);
        }
        const jobData = processingJob;
        // Get document to check access
        const document = await database_1.db.readItem('documents', jobData.documentId, jobData.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to processing job" }
            }, request);
        }
        logger_1.logger.info("Processing status retrieved successfully", {
            correlationId,
            jobId,
            status: jobData.status,
            progress: jobData.progress?.percentage,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                jobId: jobData.id,
                documentId: jobData.documentId,
                documentName: documentData.name,
                processingTypes: jobData.processingTypes,
                status: jobData.status,
                progress: jobData.progress,
                results: jobData.results,
                createdAt: jobData.createdAt,
                updatedAt: jobData.updatedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get processing status failed", {
            correlationId,
            jobId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
async function checkProcessingLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxConcurrentJobs: 2, maxDailyProcessing: 10 },
            'PROFESSIONAL': { maxConcurrentJobs: 10, maxDailyProcessing: 500 },
            'ENTERPRISE': { maxConcurrentJobs: -1, maxDailyProcessing: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxConcurrentJobs === -1) {
            return { allowed: true };
        }
        // Check concurrent jobs
        const activeJobsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@processing, @pending)';
        const activeJobsResult = await database_1.db.queryItems('processing-jobs', activeJobsQuery, [organizationId, ProcessingStatus.PROCESSING, ProcessingStatus.PENDING]);
        const activeJobs = Number(activeJobsResult[0]) || 0;
        if (activeJobs >= limit.maxConcurrentJobs) {
            return {
                allowed: false,
                reason: `Maximum concurrent processing jobs limit reached (${limit.maxConcurrentJobs})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check processing limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function estimateProcessingDuration(processingTypes, document) {
    // Simplified estimation - in production, use file size and type
    let baseTimeMinutes = 2; // Base time per processing type
    // Adjust based on processing types
    processingTypes.forEach(type => {
        switch (type) {
            case ProcessingType.TEXT_EXTRACTION:
                baseTimeMinutes += 1;
                break;
            case ProcessingType.IMAGE_ANALYSIS:
                baseTimeMinutes += 3;
                break;
            case ProcessingType.DOCUMENT_CONVERSION:
                baseTimeMinutes += 2;
                break;
            case ProcessingType.CONTENT_ANALYSIS:
                baseTimeMinutes += 4;
                break;
            default:
                baseTimeMinutes += 1;
        }
    });
    // Adjust based on file size (simplified)
    const fileSizeMB = (document.size || 1024) / 1024 / 1024;
    if (fileSizeMB > 10) {
        baseTimeMinutes *= 2;
    }
    else if (fileSizeMB > 1) {
        baseTimeMinutes *= 1.5;
    }
    return `${Math.ceil(baseTimeMinutes)} minutes`;
}
async function startFileProcessing(processingJob, document) {
    try {
        // Update status to processing
        const updatedJob = {
            ...processingJob,
            id: processingJob.id,
            status: ProcessingStatus.PROCESSING,
            progress: {
                ...processingJob.progress,
                percentage: 10,
                currentStep: 'Starting processing',
                startedAt: new Date().toISOString()
            },
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('processing-jobs', updatedJob);
        // Simulate processing (in production, this would be actual processing logic)
        setTimeout(async () => {
            try {
                const results = await performFileProcessing(processingJob, document);
                const completedJob = {
                    ...updatedJob,
                    id: processingJob.id,
                    status: ProcessingStatus.COMPLETED,
                    progress: {
                        ...updatedJob.progress,
                        percentage: 100,
                        currentStep: 'Completed',
                        completedSteps: processingJob.processingTypes
                    },
                    results,
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('processing-jobs', completedJob);
                logger_1.logger.info('File processing completed successfully', { jobId: processingJob.id });
            }
            catch (error) {
                const failedJob = {
                    ...updatedJob,
                    id: processingJob.id,
                    status: ProcessingStatus.FAILED,
                    progress: {
                        ...updatedJob.progress,
                        currentStep: 'Failed'
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('processing-jobs', failedJob);
                logger_1.logger.error('File processing failed', { jobId: processingJob.id, error });
            }
        }, 10000); // 10 second delay for demo
    }
    catch (error) {
        logger_1.logger.error('Failed to start file processing', { jobId: processingJob.id, error });
    }
}
async function performFileProcessing(processingJob, document) {
    const results = {};
    for (const processingType of processingJob.processingTypes) {
        try {
            switch (processingType) {
                case ProcessingType.TEXT_EXTRACTION:
                    results[processingType] = {
                        success: true,
                        data: {
                            extractedText: `Mock extracted text from ${document.name}`,
                            wordCount: Math.floor(Math.random() * 5000) + 100,
                            language: 'en'
                        }
                    };
                    break;
                case ProcessingType.THUMBNAIL_GENERATION:
                    results[processingType] = {
                        success: true,
                        outputFiles: [{
                                type: 'thumbnail',
                                url: `thumbnails/${processingJob.id}/thumb.jpg`,
                                size: 15360
                            }]
                    };
                    break;
                case ProcessingType.METADATA_EXTRACTION:
                    results[processingType] = {
                        success: true,
                        data: {
                            title: document.name,
                            author: 'Unknown',
                            createdDate: document.createdAt,
                            pageCount: Math.floor(Math.random() * 50) + 1,
                            keywords: ['document', 'processing', 'analysis']
                        }
                    };
                    break;
                case ProcessingType.VIRUS_SCAN:
                    results[processingType] = {
                        success: true,
                        data: {
                            clean: true,
                            scanEngine: 'MockAV',
                            scanDate: new Date().toISOString(),
                            threats: []
                        }
                    };
                    break;
                default:
                    results[processingType] = {
                        success: true,
                        data: {
                            message: `Mock processing completed for ${processingType}`
                        }
                    };
            }
        }
        catch (error) {
            results[processingType] = {
                success: false,
                error: error instanceof Error ? error.message : 'Processing failed'
            };
        }
    }
    return results;
}
// Register functions
functions_1.app.http('file-process', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'files/process',
    handler: processFile
});
functions_1.app.http('file-processing-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'files/processing/{jobId}/status',
    handler: getProcessingStatus
});
//# sourceMappingURL=file-processing.js.map