/**
 * Health Monitoring Function
 * Handles system health monitoring, metrics collection, and alerting
 * Migrated from old-arch/src/monitoring-service/health/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get system health handler
 */
export declare function getSystemHealth(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Record metric handler
 */
export declare function recordMetric(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create health check handler
 */
export declare function createHealthCheck(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
