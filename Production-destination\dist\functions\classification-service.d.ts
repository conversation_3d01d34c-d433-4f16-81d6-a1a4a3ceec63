/**
 * Classification Service Function
 * Handles document classification and categorization
 * Migrated from old-arch/src/classification-service/classify/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Classify document handler
 */
export declare function classifyDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create classification category handler
 */
export declare function createClassificationCategory(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
