/**
 * Real-time Messaging Function
 * Handles real-time chat, messaging, and communication features
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Send message handler
 */
export declare function sendMessage(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create channel handler
 */
export declare function createChannel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List messages handler
 */
export declare function listMessages(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get SignalR metrics from enhanced shared service
 */
export declare function getSignalRMetrics(): import("../shared/services/signalr").SignalRMetrics;
