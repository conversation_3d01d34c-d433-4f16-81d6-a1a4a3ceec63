/**
 * Template Management Function
 * Handles document and workflow template operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create template handler
 */
export declare function createTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List templates handler
 */
export declare function listTemplates(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Apply template handler
 */
export declare function applyTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
