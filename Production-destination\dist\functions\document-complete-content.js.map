{"version": 3, "file": "document-complete-content.js", "sourceRoot": "", "sources": ["../../src/functions/document-complete-content.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAiEA,0DA0LC;AA3PD;;;;GAIG;AACH,gDAAyF;AACzF,sDAAwD;AACxD,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,2BAA2B;AAC3B,IAAK,cAKJ;AALD,WAAK,cAAc;IACjB,uCAAqB,CAAA;IACrB,mCAAiB,CAAA;IACjB,yCAAuB,CAAA;IACvB,qCAAmB,CAAA;AACrB,CAAC,EALI,cAAc,KAAd,cAAc,QAKlB;AAED,IAAK,QAKJ;AALD,WAAK,QAAQ;IACX,yCAA6B,CAAA;IAC7B,6BAAiB,CAAA;IACjB,iCAAqB,CAAA;IACrB,iCAAqB,CAAA;AACvB,CAAC,EALI,QAAQ,KAAR,QAAQ,QAKZ;AAED,oBAAoB;AACpB,MAAM,uBAAuB,GAAG,GAAG,CAAC,MAAM,CAAC;IACzC,MAAM,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACzC,OAAO,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IAC1C,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,cAAc,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC/E,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,IAAI,CAAC,CAAC,QAAQ,EAAE;IACrD,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,QAAQ,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC/D,aAAa,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,QAAQ,EAAE;CACxC,CAAC,CAAC;AAyBH;;GAEG;AACI,KAAK,UAAU,uBAAuB,CAAC,OAAoB,EAAE,OAA0B;IAC5F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;IAErC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;IAElF,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE;aAC/C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAA6B,KAAK,CAAC;QAC1D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,qDAAqD;QACrD,IAAI,eAAe,GAAI,QAAgB,CAAC,aAAa,IAAK,QAAgB,CAAC,OAAO,IAAI,EAAE,CAAC;QAEzF,0DAA0D;QAC1D,IAAI,CAAC,eAAe,EAAE,CAAC;YACrB,IAAI,CAAC;gBACH,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;gBACF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;gBACF,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAE,QAAgB,CAAC,QAAQ,CAAC,CAAC;gBAE7E,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;gBACrD,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;oBACxC,MAAM,MAAM,GAAa,EAAE,CAAC;oBAC5B,IAAI,KAAK,EAAE,MAAM,KAAK,IAAI,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;wBAC9D,MAAM,CAAC,IAAI,CAAC,MAAM,CAAC,QAAQ,CAAC,KAAK,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,KAAK,CAAC,CAAC,CAAC;oBACnE,CAAC;oBACD,eAAe,GAAG,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,QAAQ,CAAC,OAAO,CAAC,CAAC;gBAC5D,CAAC;YACH,CAAC;YAAC,OAAO,KAAK,EAAE,CAAC;gBACf,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAAC;gBAC1E,OAAO,IAAA,qBAAc,EAAC;oBACpB,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE;iBAC3D,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;QACH,CAAC;QAED,IAAI,CAAC,eAAe,CAAC,IAAI,EAAE,EAAE,CAAC;YAC5B,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE;aAC3D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,gCAAgC;QAChC,MAAM,gBAAgB,GAAG,MAAM,wBAAwB,CACrD,eAAe,EACf,iBAAiB,CAClB,CAAC;QAEF,MAAM,cAAc,GAAG,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS,CAAC;QAE9C,kBAAkB;QAClB,MAAM,QAAQ,GAA8B;YAC1C,UAAU;YACV,eAAe;YACf,gBAAgB,EAAE,gBAAgB,CAAC,gBAAgB;YACnD,WAAW,EAAE,gBAAgB,CAAC,WAAW,IAAI,EAAE;YAC/C,QAAQ,EAAE;gBACR,cAAc,EAAE,iBAAiB,CAAC,cAAc;gBAChD,UAAU,EAAE,gBAAgB,CAAC,UAAU,IAAI,CAAC;gBAC5C,cAAc;gBACd,UAAU,EAAE,gBAAgB,CAAC,UAAU,IAAI,GAAG;aAC/C;YACD,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,4BAA4B;YAClC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAG,QAAgB,CAAC,cAAc;YAChD,SAAS,EAAG,QAAgB,CAAC,SAAS;YACtC,UAAU;YACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE;gBACP,cAAc,EAAE,iBAAiB,CAAC,cAAc;gBAChD,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,UAAU;gBACxC,cAAc;gBACd,IAAI,EAAE,iBAAiB,CAAC,IAAI;aAC7B;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,oDAAoD,EAAE;YAChE,aAAa;YACb,UAAU;YACV,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,UAAU,EAAE,QAAQ,CAAC,QAAQ,CAAC,UAAU;YACxC,cAAc;YACd,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,QAAQ;SACnB,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,oCAAoC,EAAE;YACjD,aAAa;YACb,UAAU;YACV,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,wBAAwB,CACrC,eAAuB,EACvB,OAAiC;IAOjC,sCAAsC;IACtC,8FAA8F;IAE9F,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE;QAC3C,cAAc,EAAE,OAAO,CAAC,cAAc;QACtC,aAAa,EAAE,eAAe,CAAC,MAAM;QACrC,IAAI,EAAE,OAAO,CAAC,IAAI;KACnB,CAAC,CAAC;IAEH,MAAM,iBAAiB,GAAG,eAAe,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC9D,IAAI,gBAAgB,GAAG,eAAe,CAAC;IACvC,IAAI,WAAW,GAAa,EAAE,CAAC;IAC/B,IAAI,UAAU,GAAG,CAAC,CAAC;IAEnB,QAAQ,OAAO,CAAC,cAAc,EAAE,CAAC;QAC/B,KAAK,cAAc,CAAC,QAAQ;YAC1B,MAAM,YAAY,GAAG,oBAAoB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YACpE,gBAAgB,GAAG,eAAe,GAAG,MAAM,GAAG,YAAY,CAAC;YAC3D,UAAU,GAAG,YAAY,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;YAC9C,WAAW,GAAG;gBACZ,wCAAwC;gBACxC,2CAA2C;gBAC3C,yDAAyD;aAC1D,CAAC;YACF,MAAM;QAER,KAAK,cAAc,CAAC,MAAM;YACxB,gBAAgB,GAAG,iBAAiB,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAC/D,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,iBAAiB,CAAC;YACtE,WAAW,GAAG;gBACZ,wDAAwD;gBACxD,8CAA8C;gBAC9C,uDAAuD;aACxD,CAAC;YACF,MAAM;QAER,KAAK,cAAc,CAAC,SAAS;YAC3B,gBAAgB,GAAG,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAC7D,UAAU,GAAG,CAAC,CAAC,CAAC,oCAAoC;YACpD,WAAW,GAAG;gBACZ,gCAAgC;gBAChC,6BAA6B;gBAC7B,gDAAgD;aACjD,CAAC;YACF,MAAM;QAER,KAAK,cAAc,CAAC,OAAO;YACzB,gBAAgB,GAAG,eAAe,CAAC,eAAe,EAAE,OAAO,CAAC,CAAC;YAC7D,UAAU,GAAG,gBAAgB,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,GAAG,iBAAiB,CAAC;YACtE,WAAW,GAAG;gBACZ,+CAA+C;gBAC/C,wCAAwC;gBACxC,mDAAmD;aACpD,CAAC;YACF,MAAM;IACV,CAAC;IAED,OAAO;QACL,gBAAgB;QAChB,WAAW;QACX,UAAU,EAAE,IAAI,CAAC,GAAG,CAAC,CAAC,EAAE,UAAU,CAAC;QACnC,UAAU,EAAE,IAAI,CAAC,6BAA6B;KAC/C,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,oBAAoB,CAAC,OAAe,EAAE,OAAiC;IAC9E,4DAA4D;IAC5D,MAAM,IAAI,GAAG,OAAO,CAAC,IAAI,IAAI,QAAQ,CAAC,YAAY,CAAC;IACnD,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,IAAI,EAAE,CAAC;IAEpC,OAAO,gCAAgC,IAAI;;EAE3C,MAAM,CAAC,CAAC,CAAC,4BAA4B,MAAM,GAAG,CAAC,CAAC,CAAC,sCAAsC;;;;;;;qBAOpE,IAAI;;iKAEwI,CAAC;AAClK,CAAC;AAED;;GAEG;AACH,SAAS,iBAAiB,CAAC,OAAe,EAAE,OAAiC;IAC3E,4DAA4D;IAC5D,MAAM,gBAAgB,GAAG,OAAO,CAAC,KAAK,CAAC,MAAM,CAAC,CAAC,GAAG,CAAC,SAAS,CAAC,EAAE;QAC7D,IAAI,SAAS,CAAC,IAAI,EAAE,EAAE,CAAC;YACrB,OAAO,SAAS,GAAG,+DAA+D,CAAC;QACrF,CAAC;QACD,OAAO,SAAS,CAAC;IACnB,CAAC,CAAC,CAAC;IAEH,OAAO,gBAAgB,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;AACvC,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,OAAe,EAAE,OAAiC;IACzE,4DAA4D;IAC5D,MAAM,SAAS,GAAG,OAAO,CAAC,KAAK,CAAC,KAAK,CAAC,CAAC,MAAM,CAAC;IAC9C,MAAM,YAAY,GAAG,IAAI,CAAC,GAAG,CAAC,SAAS,GAAG,GAAG,EAAE,OAAO,CAAC,SAAS,IAAI,GAAG,CAAC,CAAC;IAEzE,OAAO,2BAA2B,OAAO,CAAC,IAAI,IAAI,cAAc;;;;;;;qBAO7C,OAAO,CAAC,IAAI,IAAI,cAAc;;;;mBAIhC,SAAS;mBACT,IAAI,CAAC,KAAK,CAAC,YAAY,CAAC,QAAQ,CAAC;AACpD,CAAC;AAED;;GAEG;AACH,SAAS,eAAe,CAAC,OAAe,EAAE,OAAiC;IACzE,4DAA4D;IAC5D,OAAO,wCAAwC,OAAO,CAAC,IAAI,IAAI,cAAc;;EAE7E,OAAO,CAAC,MAAM,CAAC,CAAC,CAAC,8BAA8B,OAAO,CAAC,MAAM,GAAG,CAAC,CAAC,CAAC,EAAE;;;;;;;uBAOhD,OAAO,CAAC,IAAI,IAAI,cAAc;;sHAEiE,CAAC;AACvH,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,2BAA2B,EAAE;IACpC,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,iCAAiC;IACxC,OAAO,EAAE,uBAAuB;CACjC,CAAC,CAAC"}