"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createIntegration = createIntegration;
/**
 * Integration Create Function
 * Handles creation and management of external integrations
 * Migrated from old-arch/src/integration-service/create/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Integration types and providers
var IntegrationType;
(function (IntegrationType) {
    IntegrationType["WEBHOOK"] = "webhook";
    IntegrationType["API"] = "api";
    IntegrationType["DATABASE"] = "database";
    IntegrationType["STORAGE"] = "storage";
    IntegrationType["NOTIFICATION"] = "notification";
    IntegrationType["AUTHENTICATION"] = "authentication";
    IntegrationType["ANALYTICS"] = "analytics";
})(IntegrationType || (IntegrationType = {}));
var IntegrationProvider;
(function (IntegrationProvider) {
    IntegrationProvider["SLACK"] = "slack";
    IntegrationProvider["TEAMS"] = "teams";
    IntegrationProvider["DISCORD"] = "discord";
    IntegrationProvider["ZAPIER"] = "zapier";
    IntegrationProvider["SALESFORCE"] = "salesforce";
    IntegrationProvider["HUBSPOT"] = "hubspot";
    IntegrationProvider["GOOGLE_DRIVE"] = "google_drive";
    IntegrationProvider["DROPBOX"] = "dropbox";
    IntegrationProvider["ONEDRIVE"] = "onedrive";
    IntegrationProvider["AWS_S3"] = "aws_s3";
    IntegrationProvider["AZURE_BLOB"] = "azure_blob";
    IntegrationProvider["CUSTOM"] = "custom";
})(IntegrationProvider || (IntegrationProvider = {}));
var IntegrationStatus;
(function (IntegrationStatus) {
    IntegrationStatus["PENDING"] = "pending";
    IntegrationStatus["CONNECTED"] = "connected";
    IntegrationStatus["DISCONNECTED"] = "disconnected";
    IntegrationStatus["ERROR"] = "error";
    IntegrationStatus["EXPIRED"] = "expired";
})(IntegrationStatus || (IntegrationStatus = {}));
// Validation schema
const createIntegrationSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(IntegrationType)).required(),
    provider: Joi.string().valid(...Object.values(IntegrationProvider)).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    configuration: Joi.object({
        apiKey: Joi.string().optional(),
        apiSecret: Joi.string().optional(),
        accessToken: Joi.string().optional(),
        refreshToken: Joi.string().optional(),
        webhookUrl: Joi.string().uri().optional(),
        baseUrl: Joi.string().uri().optional(),
        scopes: Joi.array().items(Joi.string()).optional(),
        customFields: Joi.object().optional()
    }).required(),
    settings: Joi.object({
        syncEnabled: Joi.boolean().default(true),
        syncInterval: Joi.number().min(60).max(86400).default(3600), // 1 hour default
        retryAttempts: Joi.number().min(0).max(10).default(3),
        timeout: Joi.number().min(5000).max(300000).default(30000), // 30 seconds
        enableLogging: Joi.boolean().default(true),
        enableNotifications: Joi.boolean().default(true)
    }).optional(),
    tags: Joi.array().items(Joi.string().max(50)).max(10).default([])
});
/**
 * Create integration handler
 */
async function createIntegration(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create integration started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createIntegrationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const integrationRequest = value;
        // Verify organization access
        const organization = await database_1.db.readItem('organizations', integrationRequest.organizationId, integrationRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check if user has integration management permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, integrationRequest.organizationId, 'ACTIVE']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        const userMembership = memberships[0];
        const canManageIntegrations = userMembership.role === 'OWNER' || userMembership.role === 'ADMIN';
        if (!canManageIntegrations) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Insufficient permissions to manage integrations" }
            }, request);
        }
        // Check integration limits for organization tier
        const orgData = organization;
        if (await isIntegrationLimitReached(integrationRequest.organizationId, orgData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Integration limit reached for this organization tier",
                    tier: orgData.tier
                }
            }, request);
        }
        // Validate integration configuration
        const configValidation = await validateIntegrationConfiguration(integrationRequest.type, integrationRequest.provider, integrationRequest.configuration);
        if (!configValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Invalid integration configuration",
                    details: configValidation.errors
                }
            }, request);
        }
        // Create integration
        const integrationId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        // Encrypt sensitive configuration data
        const encryptedConfiguration = await encryptSensitiveData(integrationRequest.configuration);
        const integration = {
            id: integrationId,
            name: integrationRequest.name,
            description: integrationRequest.description || "",
            type: integrationRequest.type,
            provider: integrationRequest.provider,
            status: IntegrationStatus.PENDING,
            organizationId: integrationRequest.organizationId,
            projectId: integrationRequest.projectId,
            configuration: encryptedConfiguration,
            settings: {
                syncEnabled: integrationRequest.settings?.syncEnabled ?? true,
                syncInterval: integrationRequest.settings?.syncInterval ?? 3600,
                retryAttempts: integrationRequest.settings?.retryAttempts ?? 3,
                timeout: integrationRequest.settings?.timeout ?? 30000,
                enableLogging: integrationRequest.settings?.enableLogging ?? true,
                enableNotifications: integrationRequest.settings?.enableNotifications ?? true
            },
            tags: integrationRequest.tags,
            createdBy: user.id,
            createdAt: now,
            updatedBy: user.id,
            updatedAt: now,
            lastSyncAt: null,
            syncStatus: null,
            errorMessage: null,
            metadata: {
                connectionAttempts: 0,
                lastConnectionAttempt: null,
                successfulSyncs: 0,
                failedSyncs: 0,
                totalDataSynced: 0
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('integrations', integration);
        // Test the integration connection
        const connectionResult = await testIntegrationConnection(integration);
        // Update integration status based on connection test
        const updatedIntegration = {
            ...integration,
            status: connectionResult.success ? IntegrationStatus.CONNECTED : IntegrationStatus.ERROR,
            errorMessage: connectionResult.error || null,
            metadata: {
                ...integration.metadata,
                connectionAttempts: 1,
                lastConnectionAttempt: now
            },
            updatedAt: now
        };
        await database_1.db.updateItem('integrations', updatedIntegration);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "integration_created",
            userId: user.id,
            organizationId: integrationRequest.organizationId,
            projectId: integrationRequest.projectId,
            timestamp: now,
            details: {
                integrationId,
                integrationName: integrationRequest.name,
                type: integrationRequest.type,
                provider: integrationRequest.provider,
                status: updatedIntegration.status,
                connectionSuccess: connectionResult.success,
                organizationName: orgData.name
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'IntegrationCreated',
            aggregateId: integrationId,
            aggregateType: 'Integration',
            version: 1,
            data: {
                integration: {
                    ...updatedIntegration,
                    configuration: '[ENCRYPTED]' // Don't include sensitive data in events
                },
                createdBy: user.id,
                connectionResult
            },
            userId: user.id,
            organizationId: integrationRequest.organizationId,
            tenantId: user.tenantId
        });
        // Send notification
        const notificationMessage = connectionResult.success
            ? `Integration "${integrationRequest.name}" has been created and connected successfully.`
            : `Integration "${integrationRequest.name}" has been created but connection failed. Please check the configuration.`;
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'INTEGRATION_CREATED',
            title: connectionResult.success ? 'Integration connected successfully' : 'Integration created with connection issues',
            message: notificationMessage,
            priority: connectionResult.success ? 'normal' : 'high',
            metadata: {
                integrationId,
                integrationName: integrationRequest.name,
                type: integrationRequest.type,
                provider: integrationRequest.provider,
                status: updatedIntegration.status,
                organizationId: integrationRequest.organizationId,
                organizationName: orgData.name
            },
            organizationId: integrationRequest.organizationId,
            projectId: integrationRequest.projectId
        });
        logger_1.logger.info("Integration created successfully", {
            correlationId,
            integrationId,
            integrationName: integrationRequest.name,
            type: integrationRequest.type,
            provider: integrationRequest.provider,
            status: updatedIntegration.status,
            organizationId: integrationRequest.organizationId,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: integrationId,
                name: updatedIntegration.name,
                type: updatedIntegration.type,
                provider: updatedIntegration.provider,
                status: updatedIntegration.status,
                organizationId: integrationRequest.organizationId,
                organizationName: orgData.name,
                connectionSuccess: connectionResult.success,
                errorMessage: connectionResult.error,
                message: "Integration created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create integration failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check if integration limit is reached for organization
 */
async function isIntegrationLimitReached(organizationId, tier) {
    try {
        const integrationCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId';
        const result = await database_1.db.queryItems('integrations', integrationCountQuery, [organizationId]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 3,
            'PROFESSIONAL': 15,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check integration limit', { error, organizationId });
        return false;
    }
}
/**
 * Validate integration configuration
 */
async function validateIntegrationConfiguration(type, provider, configuration) {
    const errors = [];
    // Provider-specific validation
    switch (provider) {
        case IntegrationProvider.SLACK:
            if (!configuration.accessToken && !configuration.webhookUrl) {
                errors.push('Slack integration requires either accessToken or webhookUrl');
            }
            break;
        case IntegrationProvider.SALESFORCE:
            if (!configuration.apiKey || !configuration.apiSecret) {
                errors.push('Salesforce integration requires apiKey and apiSecret');
            }
            break;
        case IntegrationProvider.AWS_S3:
            if (!configuration.accessToken || !configuration.apiSecret) {
                errors.push('AWS S3 integration requires accessToken (Access Key ID) and apiSecret (Secret Access Key)');
            }
            break;
        case IntegrationProvider.CUSTOM:
            if (type === IntegrationType.WEBHOOK && !configuration.webhookUrl) {
                errors.push('Custom webhook integration requires webhookUrl');
            }
            if (type === IntegrationType.API && !configuration.baseUrl) {
                errors.push('Custom API integration requires baseUrl');
            }
            break;
    }
    return {
        valid: errors.length === 0,
        errors
    };
}
/**
 * Encrypt sensitive configuration data
 */
async function encryptSensitiveData(configuration) {
    // In production, implement proper encryption
    // For now, we'll just mask sensitive fields
    const sensitiveFields = ['apiKey', 'apiSecret', 'accessToken', 'refreshToken'];
    const encrypted = { ...configuration };
    for (const field of sensitiveFields) {
        if (encrypted[field]) {
            // In production, use proper encryption like Azure Key Vault
            encrypted[field] = `[ENCRYPTED:${encrypted[field].substring(0, 4)}...]`;
        }
    }
    return encrypted;
}
/**
 * Test integration connection
 */
async function testIntegrationConnection(integration) {
    try {
        // Simplified connection test - in production, implement actual API calls
        logger_1.logger.info("Testing integration connection", {
            integrationId: integration.id,
            type: integration.type,
            provider: integration.provider
        });
        // Simulate connection test based on provider
        switch (integration.provider) {
            case IntegrationProvider.SLACK:
                // In production, test Slack API connection
                return { success: true };
            case IntegrationProvider.SALESFORCE:
                // In production, test Salesforce API connection
                return { success: true };
            case IntegrationProvider.CUSTOM:
                // In production, test custom endpoint
                if (integration.configuration.webhookUrl || integration.configuration.baseUrl) {
                    return { success: true };
                }
                return { success: false, error: 'No valid endpoint configured' };
            default:
                return { success: true };
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error('Integration connection test failed', {
            error: errorMessage,
            integrationId: integration.id
        });
        return {
            success: false,
            error: errorMessage
        };
    }
}
// Register functions
functions_1.app.http('integration-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'integrations',
    handler: createIntegration
});
//# sourceMappingURL=integration-create.js.map