/**
 * User Permissions Function
 * Handles user permission retrieval and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
export declare enum SystemRole {
    SYSTEM_ADMIN = "SYSTEM_ADMIN",
    ORGANIZATION_ADMIN = "ORGANIZATION_ADMIN",
    ORGANIZATION_MEMBER = "ORGANIZATION_MEMBER",
    ORGANIZATION_VIEWER = "ORGANIZATION_VIEWER",
    PROJECT_ADMIN = "PROJECT_ADMIN",
    PROJECT_MEMBER = "PROJECT_MEMBER",
    PROJECT_VIEWER = "PROJECT_VIEWER",
    GUEST = "GUEST"
}
export declare enum PermissionAction {
    VIEW_ORGANIZATION = "VIEW_ORGANIZATION",
    UPDATE_ORGANIZATION = "UPDATE_ORGANIZATION",
    MANAGE_ORGANIZATION = "MANAGE_ORGANIZATION",
    VIEW_ORGANIZATION_MEMBERS = "VIEW_ORGANIZATION_MEMBERS",
    INVITE_ORGANIZATION_MEMBER = "INVITE_ORGANIZATION_MEMBER",
    REMOVE_ORGANIZATION_MEMBER = "REMOVE_ORGANIZATION_MEMBER",
    MANAGE_ORGANIZATION_MEMBERS = "MANAGE_ORGANIZATION_MEMBERS",
    CREATE_PROJECT = "CREATE_PROJECT",
    VIEW_PROJECT = "VIEW_PROJECT",
    UPDATE_PROJECT = "UPDATE_PROJECT",
    DELETE_PROJECT = "DELETE_PROJECT",
    MANAGE_PROJECT = "MANAGE_PROJECT",
    ADD_PROJECT_MEMBER = "ADD_PROJECT_MEMBER",
    REMOVE_PROJECT_MEMBER = "REMOVE_PROJECT_MEMBER",
    MANAGE_PROJECT_MEMBERS = "MANAGE_PROJECT_MEMBERS",
    UPLOAD_DOCUMENT = "UPLOAD_DOCUMENT",
    VIEW_DOCUMENT = "VIEW_DOCUMENT",
    UPDATE_DOCUMENT = "UPDATE_DOCUMENT",
    DELETE_DOCUMENT = "DELETE_DOCUMENT",
    SHARE_DOCUMENT = "SHARE_DOCUMENT",
    COMMENT_DOCUMENT = "COMMENT_DOCUMENT",
    EDIT_ANY_COMMENT = "EDIT_ANY_COMMENT",
    DELETE_ANY_COMMENT = "DELETE_ANY_COMMENT",
    VIEW_ANALYTICS = "VIEW_ANALYTICS",
    VIEW_USER_PERMISSIONS = "VIEW_USER_PERMISSIONS",
    GRANT_PERMISSION = "GRANT_PERMISSION"
}
/**
 * Get user permissions handler
 */
export declare function getUserPermissions(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
