"use strict";
var __importDefault = (this && this.__importDefault) || function (mod) {
    return (mod && mod.__esModule) ? mod : { "default": mod };
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.createBatchJob = createBatchJob;
exports.getBatchJobStatus = getBatchJobStatus;
/**
 * AI Batch Processing Function
 * Handles batch document processing with AI models
 * Migrated from old-arch/src/ai-service/batch-processing/index.ts
 */
const functions_1 = require("@azure/functions");
// import { BlobServiceClient } from "@azure/storage-blob"; // Unused for now
const uuid_1 = require("uuid");
const joi_1 = __importDefault(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// import { notificationService } from '../shared/services/notification'; // Unused for now
const event_1 = require("../shared/services/event");
// Batch processing types and enums
var BatchStatus;
(function (BatchStatus) {
    BatchStatus["PENDING"] = "PENDING";
    BatchStatus["PROCESSING"] = "PROCESSING";
    BatchStatus["COMPLETED"] = "COMPLETED";
    BatchStatus["FAILED"] = "FAILED";
    BatchStatus["CANCELLED"] = "CANCELLED";
})(BatchStatus || (BatchStatus = {}));
var ProcessingType;
(function (ProcessingType) {
    ProcessingType["DOCUMENT_ANALYSIS"] = "DOCUMENT_ANALYSIS";
    ProcessingType["TEXT_EXTRACTION"] = "TEXT_EXTRACTION";
    ProcessingType["FORM_PROCESSING"] = "FORM_PROCESSING";
    ProcessingType["CLASSIFICATION"] = "CLASSIFICATION";
    ProcessingType["ENTITY_EXTRACTION"] = "ENTITY_EXTRACTION";
})(ProcessingType || (ProcessingType = {}));
// Validation schemas
const createBatchJobSchema = joi_1.default.object({
    name: joi_1.default.string().min(2).max(100).required(),
    description: joi_1.default.string().max(500).optional(),
    organizationId: joi_1.default.string().uuid().required(),
    projectId: joi_1.default.string().uuid().optional(),
    processingType: joi_1.default.string().valid(...Object.values(ProcessingType)).required(),
    documentIds: joi_1.default.array().items(joi_1.default.string().uuid()).min(1).max(1000).required(),
    configuration: joi_1.default.object({
        modelId: joi_1.default.string().optional(),
        extractTables: joi_1.default.boolean().default(false),
        extractKeyValuePairs: joi_1.default.boolean().default(true),
        extractEntities: joi_1.default.boolean().default(false),
        detectLanguages: joi_1.default.boolean().default(false),
        customPrompt: joi_1.default.string().max(2000).optional(),
        outputFormat: joi_1.default.string().valid('json', 'csv', 'xlsx').default('json'),
        batchSize: joi_1.default.number().min(1).max(100).default(10),
        maxRetries: joi_1.default.number().min(0).max(5).default(3)
    }).optional(),
    priority: joi_1.default.string().valid('low', 'normal', 'high').default('normal'),
    scheduledAt: joi_1.default.string().isoDate().optional(),
    notifyOnCompletion: joi_1.default.boolean().default(true)
});
/**
 * Create batch processing job handler
 */
async function createBatchJob(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create batch job started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createBatchJobSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const batchRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(batchRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Validate document access
        const validDocuments = await validateDocumentAccess(batchRequest.documentIds, user.id, batchRequest.organizationId);
        if (validDocuments.length !== batchRequest.documentIds.length) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Some documents are not accessible",
                    invalidDocuments: batchRequest.documentIds.filter(id => !validDocuments.includes(id))
                }
            }, request);
        }
        // Check batch processing limits
        const canCreateBatch = await checkBatchProcessingLimits(batchRequest.organizationId, batchRequest.documentIds.length);
        if (!canCreateBatch.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreateBatch.reason }
            }, request);
        }
        // Create batch job
        const batchJobId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const batchJob = {
            id: batchJobId,
            name: batchRequest.name,
            description: batchRequest.description,
            organizationId: batchRequest.organizationId,
            projectId: batchRequest.projectId,
            processingType: batchRequest.processingType,
            status: batchRequest.scheduledAt ? BatchStatus.PENDING : BatchStatus.PROCESSING,
            documentIds: batchRequest.documentIds,
            configuration: {
                ...batchRequest.configuration,
                batchSize: batchRequest.configuration?.batchSize || 10,
                maxRetries: batchRequest.configuration?.maxRetries || 3,
                outputFormat: batchRequest.configuration?.outputFormat || 'json'
            },
            priority: batchRequest.priority || 'normal',
            scheduledAt: batchRequest.scheduledAt,
            startedAt: batchRequest.scheduledAt ? undefined : now,
            progress: {
                total: batchRequest.documentIds.length,
                processed: 0,
                successful: 0,
                failed: 0,
                percentage: 0
            },
            results: {
                successfulDocuments: [],
                failedDocuments: [],
                summary: {}
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('batch-jobs', batchJob);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "batch_job_created",
            userId: user.id,
            organizationId: batchRequest.organizationId,
            projectId: batchRequest.projectId,
            timestamp: now,
            details: {
                batchJobId,
                batchJobName: batchRequest.name,
                processingType: batchRequest.processingType,
                documentCount: batchRequest.documentIds.length,
                priority: batchRequest.priority,
                isScheduled: !!batchRequest.scheduledAt
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'BatchJobCreated',
            aggregateId: batchJobId,
            aggregateType: 'BatchJob',
            version: 1,
            data: {
                batchJob,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: batchRequest.organizationId,
            tenantId: user.tenantId
        });
        // Start processing if not scheduled
        if (!batchRequest.scheduledAt) {
            // Process asynchronously
            processBatchJobAsync(batchJob);
        }
        logger_1.logger.info("Batch job created successfully", {
            correlationId,
            batchJobId,
            batchJobName: batchRequest.name,
            processingType: batchRequest.processingType,
            documentCount: batchRequest.documentIds.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: batchJobId,
                name: batchRequest.name,
                processingType: batchRequest.processingType,
                status: batchJob.status,
                documentCount: batchRequest.documentIds.length,
                priority: batchRequest.priority,
                scheduledAt: batchRequest.scheduledAt,
                estimatedDuration: estimateProcessingDuration(batchRequest.documentIds.length, batchRequest.processingType),
                createdAt: now,
                message: "Batch job created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create batch job failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get batch job status handler
 */
async function getBatchJobStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const batchJobId = request.params.batchJobId;
    logger_1.logger.info("Get batch job status started", { correlationId, batchJobId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!batchJobId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Batch job ID is required" }
            }, request);
        }
        // Get batch job
        const batchJob = await database_1.db.readItem('batch-jobs', batchJobId, batchJobId);
        if (!batchJob) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Batch job not found" }
            }, request);
        }
        const batchJobData = batchJob;
        // Check access
        const hasAccess = await checkOrganizationAccess(batchJobData.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to batch job" }
            }, request);
        }
        logger_1.logger.info("Batch job status retrieved successfully", {
            correlationId,
            batchJobId,
            status: batchJobData.status,
            progress: batchJobData.progress.percentage,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: batchJobData.id,
                name: batchJobData.name,
                processingType: batchJobData.processingType,
                status: batchJobData.status,
                progress: batchJobData.progress,
                results: batchJobData.results,
                createdAt: batchJobData.createdAt,
                startedAt: batchJobData.startedAt,
                completedAt: batchJobData.completedAt,
                estimatedCompletion: estimateCompletionTime(batchJobData)
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get batch job status failed", {
            correlationId,
            batchJobId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function validateDocumentAccess(documentIds, userId, organizationId) {
    try {
        const validDocuments = [];
        for (const documentId of documentIds) {
            const document = await database_1.db.readItem('documents', documentId, documentId);
            if (document && document.organizationId === organizationId) {
                validDocuments.push(documentId);
            }
        }
        return validDocuments;
    }
    catch (error) {
        logger_1.logger.error('Failed to validate document access', { error, documentIds, userId });
        return [];
    }
}
async function checkBatchProcessingLimits(organizationId, documentCount) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxDocuments: 10, maxConcurrentJobs: 1 },
            'PROFESSIONAL': { maxDocuments: 100, maxConcurrentJobs: 5 },
            'ENTERPRISE': { maxDocuments: 1000, maxConcurrentJobs: 20 }
        };
        const limit = limits[tier] || limits['FREE'];
        // Check document count limit
        if (documentCount > limit.maxDocuments) {
            return {
                allowed: false,
                reason: `Document count exceeds tier limit of ${limit.maxDocuments}`
            };
        }
        // Check concurrent jobs limit
        const activeJobsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status IN (@processing, @pending)';
        const activeJobsResult = await database_1.db.queryItems('batch-jobs', activeJobsQuery, [organizationId, BatchStatus.PROCESSING, BatchStatus.PENDING]);
        const activeJobs = Number(activeJobsResult[0]) || 0;
        if (activeJobs >= limit.maxConcurrentJobs) {
            return {
                allowed: false,
                reason: `Maximum concurrent jobs limit reached (${limit.maxConcurrentJobs})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check batch processing limits', { error, organizationId, documentCount });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function estimateProcessingDuration(documentCount, processingType) {
    // Simplified estimation - in production, use historical data
    const baseTimePerDocument = {
        [ProcessingType.TEXT_EXTRACTION]: 30, // seconds
        [ProcessingType.DOCUMENT_ANALYSIS]: 60,
        [ProcessingType.FORM_PROCESSING]: 45,
        [ProcessingType.CLASSIFICATION]: 20,
        [ProcessingType.ENTITY_EXTRACTION]: 40
    };
    const totalSeconds = documentCount * (baseTimePerDocument[processingType] || 30);
    const minutes = Math.ceil(totalSeconds / 60);
    if (minutes < 60) {
        return `${minutes} minutes`;
    }
    else {
        const hours = Math.ceil(minutes / 60);
        return `${hours} hours`;
    }
}
function estimateCompletionTime(batchJob) {
    if (batchJob.status === BatchStatus.COMPLETED || batchJob.status === BatchStatus.FAILED) {
        return null;
    }
    if (batchJob.progress.processed === 0) {
        return estimateProcessingDuration(batchJob.progress.total, batchJob.processingType);
    }
    // Calculate based on current progress
    const startTime = new Date(batchJob.startedAt).getTime();
    const now = Date.now();
    const elapsedMs = now - startTime;
    const progressRate = batchJob.progress.processed / (elapsedMs / 1000); // documents per second
    const remainingDocuments = batchJob.progress.total - batchJob.progress.processed;
    const estimatedRemainingSeconds = remainingDocuments / progressRate;
    const minutes = Math.ceil(estimatedRemainingSeconds / 60);
    return `${minutes} minutes`;
}
async function processBatchJobAsync(batchJob) {
    // This would be implemented as a separate background process
    // For now, we'll just log that processing has started
    logger_1.logger.info('Batch job processing started asynchronously', {
        batchJobId: batchJob.id,
        documentCount: batchJob.documentIds.length,
        processingType: batchJob.processingType
    });
}
// Register functions
functions_1.app.http('batch-job-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'ai/batch-jobs',
    handler: createBatchJob
});
functions_1.app.http('batch-job-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'ai/batch-jobs/{batchJobId}/status',
    handler: getBatchJobStatus
});
//# sourceMappingURL=ai-batch-processing.js.map