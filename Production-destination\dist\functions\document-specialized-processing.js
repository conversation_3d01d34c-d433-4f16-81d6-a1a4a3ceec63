"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.processSpecializedDocument = processSpecializedDocument;
/**
 * Document Specialized Processing Function
 * Handles advanced AI processing for specific document types
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const specializedProcessingSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    documentType: Joi.string().valid('invoice', 'receipt', 'contract', 'businessCard', 'idDocument', 'tax', 'healthInsurance').required(),
    workflowOptions: Joi.object({
        autoValidation: Joi.boolean().default(true),
        extractLineItems: Joi.boolean().default(true),
        detectAnomalies: Joi.boolean().default(true),
        generateSummary: Joi.boolean().default(false),
        enableCompliance: Joi.boolean().default(false),
        createWorkflowTasks: Joi.boolean().default(false)
    }).optional(),
    businessRules: Joi.object({
        requiredFields: Joi.array().items(Joi.string()).optional(),
        validationRules: Joi.array().optional(),
        approvalWorkflow: Joi.boolean().default(false),
        notificationSettings: Joi.object().optional()
    }).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().required()
});
/**
 * Process specialized document handler
 */
async function processSpecializedDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Specialized document processing started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = specializedProcessingSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const processingRequest = value;
        const startTime = Date.now();
        const processingId = (0, uuid_1.v4)();
        // Get document
        const document = await database_1.db.readItem('documents', processingRequest.documentId, processingRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Get document content from blob storage
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlobClient(document.blobName);
        const downloadResponse = await blobClient.download();
        if (!downloadResponse.readableStreamBody) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Failed to download document content" }
            }, request);
        }
        const documentBuffer = await streamToBuffer(downloadResponse.readableStreamBody);
        // Perform specialized analysis (simplified implementation)
        const comprehensiveResult = await performComprehensiveAnalysis(documentBuffer, processingRequest.documentType);
        // Process document type specific logic
        const specializedData = await processDocumentTypeSpecificLogic(comprehensiveResult, processingRequest.documentType, processingRequest.workflowOptions || {}, processingRequest.businessRules || {});
        // Generate workflow actions
        const workflowActions = await generateWorkflowActions(specializedData, processingRequest.documentType, processingRequest.workflowOptions || {}, user);
        // Create specialized processing response
        const response = {
            documentId: processingRequest.documentId,
            processingId,
            documentType: processingRequest.documentType,
            extractedData: {
                structuredFields: specializedData.structuredFields,
                lineItems: specializedData.lineItems,
                entities: comprehensiveResult.entities || [],
                signatures: comprehensiveResult.signatures || [],
                barcodes: comprehensiveResult.barcodes || [],
                formulas: comprehensiveResult.formulas || []
            },
            businessIntelligence: {
                classification: specializedData.classification,
                validation: specializedData.validation,
                anomalies: specializedData.anomalies,
                compliance: specializedData.compliance,
                insights: specializedData.insights
            },
            workflowActions,
            processingTime: Date.now() - startTime,
            success: true
        };
        // Save specialized processing results
        await database_1.db.createItem('specialized-processing-results', {
            id: processingId,
            documentId: processingRequest.documentId,
            userId: user.id,
            organizationId: processingRequest.organizationId,
            projectId: processingRequest.projectId,
            documentType: processingRequest.documentType,
            results: response,
            createdAt: new Date().toISOString(),
            tenantId: user.tenantId
        });
        // Update document with specialized processing info
        const updatedDocument = {
            ...document,
            id: document.id,
            specializedProcessing: {
                type: processingRequest.documentType,
                processingId,
                extractedData: response.extractedData,
                businessIntelligence: response.businessIntelligence,
                processedAt: new Date().toISOString()
            },
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "specialized_document_processing",
            userId: user.id,
            organizationId: processingRequest.organizationId,
            projectId: processingRequest.projectId,
            documentId: processingRequest.documentId,
            processingId,
            timestamp: new Date().toISOString(),
            details: {
                documentType: processingRequest.documentType,
                structuredFieldsExtracted: Object.keys(specializedData.structuredFields).length,
                lineItemsExtracted: specializedData.lineItems.length,
                anomaliesDetected: specializedData.anomalies.length,
                workflowTasksCreated: workflowActions.tasksCreated.length,
                processingTime: response.processingTime
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Specialized document processing completed", {
            correlationId,
            documentId: processingRequest.documentId,
            processingId,
            documentType: processingRequest.documentType,
            processingTime: response.processingTime,
            fieldsExtracted: Object.keys(specializedData.structuredFields).length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Specialized document processing failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Perform comprehensive analysis (simplified implementation)
 */
async function performComprehensiveAnalysis(documentBuffer, documentType) {
    // This is a simplified implementation
    // In production, this would integrate with Azure Document Intelligence
    logger_1.logger.info("Performing comprehensive analysis", {
        documentSize: documentBuffer.length,
        documentType
    });
    // Simulate analysis results
    return {
        confidence: 0.95,
        keyValuePairs: {
            'Invoice Number': { value: 'INV-2024-001', confidence: 0.98 },
            'Invoice Date': { value: '2024-01-15', confidence: 0.97 },
            'Total Amount': { value: '1,250.00', confidence: 0.99 },
            'Vendor Name': { value: 'Acme Corporation', confidence: 0.96 }
        },
        tables: [],
        entities: [],
        signatures: [],
        barcodes: [],
        formulas: []
    };
}
/**
 * Process document type specific logic
 */
async function processDocumentTypeSpecificLogic(comprehensiveResult, documentType, workflowOptions, businessRules) {
    const specializedData = {
        structuredFields: {},
        lineItems: [],
        classification: { type: documentType, confidence: comprehensiveResult.confidence },
        validation: { isValid: true, errors: [], warnings: [] },
        anomalies: [],
        compliance: {},
        insights: []
    };
    // Extract structured fields based on document type
    if (documentType === 'invoice') {
        specializedData.structuredFields = comprehensiveResult.keyValuePairs;
        // Validate required fields
        const requiredFields = businessRules.requiredFields || ['Invoice Number', 'Invoice Date', 'Total Amount'];
        for (const field of requiredFields) {
            if (!specializedData.structuredFields[field]) {
                specializedData.validation.errors.push(`Missing required field: ${field}`);
                specializedData.validation.isValid = false;
            }
        }
        // Detect anomalies
        if (workflowOptions.detectAnomalies) {
            const totalAmount = parseFloat(specializedData.structuredFields['Total Amount']?.value || '0');
            if (totalAmount > 10000) {
                specializedData.anomalies.push({
                    type: 'high_amount',
                    severity: 'warning',
                    message: `Unusually high invoice amount: $${totalAmount}`,
                    recommendation: 'Verify amount and consider additional approval'
                });
            }
        }
    }
    return specializedData;
}
/**
 * Generate workflow actions
 */
async function generateWorkflowActions(specializedData, documentType, options, user) {
    const workflowActions = {
        tasksCreated: [],
        approvalRequired: false,
        notifications: [],
        nextSteps: []
    };
    if (options.createWorkflowTasks) {
        if (specializedData.validation.errors.length > 0) {
            workflowActions.tasksCreated.push({
                type: 'validation_review',
                title: `Review ${documentType} Validation Errors`,
                description: `Document has ${specializedData.validation.errors.length} validation errors`,
                assignedTo: user.id,
                priority: 'high'
            });
        }
        if (specializedData.anomalies.length > 0) {
            workflowActions.approvalRequired = true;
            workflowActions.nextSteps.push('Requires manager approval due to detected anomalies');
        }
    }
    return workflowActions;
}
/**
 * Convert stream to buffer
 */
async function streamToBuffer(readableStream) {
    return new Promise((resolve, reject) => {
        const chunks = [];
        readableStream.on("data", (data) => {
            chunks.push(data instanceof Buffer ? data : Buffer.from(data));
        });
        readableStream.on("end", () => {
            resolve(Buffer.concat(chunks));
        });
        readableStream.on("error", reject);
    });
}
// Register functions
functions_1.app.http('document-specialized-processing', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{id}/specialized-processing',
    handler: processSpecializedDocument
});
//# sourceMappingURL=document-specialized-processing.js.map