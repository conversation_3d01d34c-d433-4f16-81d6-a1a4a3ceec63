/**
 * Workflow Management Functions
 * Handles workflow creation, retrieval, and basic operations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
export declare enum WorkflowStatus {
    DRAFT = "draft",
    ACTIVE = "active",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    CANCELLED = "cancelled",
    PAUSED = "paused"
}
export declare enum StepStatus {
    PENDING = "pending",
    IN_PROGRESS = "in_progress",
    COMPLETED = "completed",
    SKIPPED = "skipped",
    FAILED = "failed"
}
export declare enum StepType {
    REVIEW = "review",
    APPROVAL = "approval",
    PROCESSING = "processing",
    NOTIFICATION = "notification",
    CUSTOM = "custom"
}
/**
 * Create workflow handler
 */
export declare function createWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get workflow handler
 */
export declare function getWorkflow(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List workflows handler
 */
export declare function listWorkflows(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
