"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.PermissionAction = exports.SystemRole = void 0;
exports.getUserPermissions = getUserPermissions;
/**
 * User Permissions Function
 * Handles user permission retrieval and management
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Permission enums
var SystemRole;
(function (SystemRole) {
    SystemRole["SYSTEM_ADMIN"] = "SYSTEM_ADMIN";
    SystemRole["ORGANIZATION_ADMIN"] = "ORGANIZATION_ADMIN";
    SystemRole["ORGANIZATION_MEMBER"] = "ORGANIZATION_MEMBER";
    SystemRole["ORGANIZATION_VIEWER"] = "ORGANIZATION_VIEWER";
    SystemRole["PROJECT_ADMIN"] = "PROJECT_ADMIN";
    SystemRole["PROJECT_MEMBER"] = "PROJECT_MEMBER";
    SystemRole["PROJECT_VIEWER"] = "PROJECT_VIEWER";
    SystemRole["GUEST"] = "GUEST";
})(SystemRole || (exports.SystemRole = SystemRole = {}));
var PermissionAction;
(function (PermissionAction) {
    // Organization permissions
    PermissionAction["VIEW_ORGANIZATION"] = "VIEW_ORGANIZATION";
    PermissionAction["UPDATE_ORGANIZATION"] = "UPDATE_ORGANIZATION";
    PermissionAction["MANAGE_ORGANIZATION"] = "MANAGE_ORGANIZATION";
    PermissionAction["VIEW_ORGANIZATION_MEMBERS"] = "VIEW_ORGANIZATION_MEMBERS";
    PermissionAction["INVITE_ORGANIZATION_MEMBER"] = "INVITE_ORGANIZATION_MEMBER";
    PermissionAction["REMOVE_ORGANIZATION_MEMBER"] = "REMOVE_ORGANIZATION_MEMBER";
    PermissionAction["MANAGE_ORGANIZATION_MEMBERS"] = "MANAGE_ORGANIZATION_MEMBERS";
    // Project permissions
    PermissionAction["CREATE_PROJECT"] = "CREATE_PROJECT";
    PermissionAction["VIEW_PROJECT"] = "VIEW_PROJECT";
    PermissionAction["UPDATE_PROJECT"] = "UPDATE_PROJECT";
    PermissionAction["DELETE_PROJECT"] = "DELETE_PROJECT";
    PermissionAction["MANAGE_PROJECT"] = "MANAGE_PROJECT";
    PermissionAction["ADD_PROJECT_MEMBER"] = "ADD_PROJECT_MEMBER";
    PermissionAction["REMOVE_PROJECT_MEMBER"] = "REMOVE_PROJECT_MEMBER";
    PermissionAction["MANAGE_PROJECT_MEMBERS"] = "MANAGE_PROJECT_MEMBERS";
    // Document permissions
    PermissionAction["UPLOAD_DOCUMENT"] = "UPLOAD_DOCUMENT";
    PermissionAction["VIEW_DOCUMENT"] = "VIEW_DOCUMENT";
    PermissionAction["UPDATE_DOCUMENT"] = "UPDATE_DOCUMENT";
    PermissionAction["DELETE_DOCUMENT"] = "DELETE_DOCUMENT";
    PermissionAction["SHARE_DOCUMENT"] = "SHARE_DOCUMENT";
    PermissionAction["COMMENT_DOCUMENT"] = "COMMENT_DOCUMENT";
    PermissionAction["EDIT_ANY_COMMENT"] = "EDIT_ANY_COMMENT";
    PermissionAction["DELETE_ANY_COMMENT"] = "DELETE_ANY_COMMENT";
    // Analytics permissions
    PermissionAction["VIEW_ANALYTICS"] = "VIEW_ANALYTICS";
    // User permissions
    PermissionAction["VIEW_USER_PERMISSIONS"] = "VIEW_USER_PERMISSIONS";
    PermissionAction["GRANT_PERMISSION"] = "GRANT_PERMISSION";
})(PermissionAction || (exports.PermissionAction = PermissionAction = {}));
// Validation schemas
const getPermissionsSchema = Joi.object({
    organizationId: Joi.string().uuid().optional(),
    projectId: Joi.string().uuid().optional()
});
/**
 * Get user permissions handler
 */
async function getUserPermissions(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const userId = request.params.userId;
    logger_1.logger.info("Get user permissions started", { correlationId, userId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const targetUserId = userId || user.id;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getPermissionsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { organizationId, projectId } = value;
        // Users can only view their own permissions unless they're admins
        const isViewingSelf = targetUserId === user.id;
        const isSystemAdmin = user.roles?.includes('admin') || user.systemRoles?.includes(SystemRole.SYSTEM_ADMIN);
        const isOrgAdmin = user.systemRoles?.includes(SystemRole.ORGANIZATION_ADMIN) &&
            organizationId &&
            user.organizationIds?.includes(organizationId);
        if (!isViewingSelf && !isSystemAdmin && !isOrgAdmin) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "You don't have permission to view this user's permissions" }
            }, request);
        }
        // Get target user
        const targetUser = await database_1.db.readItem('users', targetUserId, targetUserId);
        if (!targetUser) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User not found" }
            }, request);
        }
        // Get user's system roles
        const systemRoles = targetUser.systemRoles || [];
        // Get all permissions from system roles
        let allPermissions = [];
        // System admin has all permissions
        if (systemRoles.includes(SystemRole.SYSTEM_ADMIN)) {
            allPermissions = Object.values(PermissionAction);
        }
        else {
            // Get role assignments for the user
            let query = "SELECT * FROM c WHERE c.userId = @userId AND c.isActive = true";
            const parameters = [targetUserId];
            // Add organization filter if provided
            if (organizationId) {
                query += " AND (c.scope = 'SYSTEM' OR (c.scope = 'ORGANIZATION' AND c.organizationId = @organizationId)";
                // Add project filter if provided
                if (projectId) {
                    query += " OR (c.scope = 'PROJECT' AND c.organizationId = @organizationId AND c.projectId = @projectId)";
                    parameters.push(organizationId, projectId);
                }
                else {
                    parameters.push(organizationId);
                }
                query += ")";
            }
            const roleAssignments = await database_1.db.queryItems('role-assignments', query, parameters);
            // Get permissions from assigned roles
            if (roleAssignments.length > 0) {
                // Get all role IDs
                const roleIds = roleAssignments.map((assignment) => assignment.roleId);
                // Get roles
                const roleQuery = "SELECT * FROM c WHERE ARRAY_CONTAINS(@roleIds, c.id)";
                const roles = await database_1.db.queryItems('roles', roleQuery, [roleIds]);
                // Collect all permissions
                roles.forEach((role) => {
                    if (role.permissions) {
                        allPermissions = [...allPermissions, ...role.permissions];
                    }
                });
            }
            // Add permissions from system roles
            systemRoles.forEach((roleName) => {
                switch (roleName) {
                    case SystemRole.ORGANIZATION_ADMIN:
                        allPermissions = [...allPermissions, ...getOrganizationAdminPermissions()];
                        break;
                    case SystemRole.ORGANIZATION_MEMBER:
                        allPermissions = [...allPermissions, ...getOrganizationMemberPermissions()];
                        break;
                    case SystemRole.ORGANIZATION_VIEWER:
                        allPermissions = [...allPermissions, ...getOrganizationViewerPermissions()];
                        break;
                    case SystemRole.PROJECT_ADMIN:
                        allPermissions = [...allPermissions, ...getProjectAdminPermissions()];
                        break;
                    case SystemRole.PROJECT_MEMBER:
                        allPermissions = [...allPermissions, ...getProjectMemberPermissions()];
                        break;
                    case SystemRole.PROJECT_VIEWER:
                        allPermissions = [...allPermissions, ...getProjectViewerPermissions()];
                        break;
                    case SystemRole.GUEST:
                        allPermissions = [...allPermissions, ...getGuestPermissions()];
                        break;
                }
            });
        }
        // Remove duplicates
        const uniquePermissions = [...new Set(allPermissions)];
        logger_1.logger.info("User permissions retrieved successfully", {
            correlationId,
            targetUserId,
            permissionsCount: uniquePermissions.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: targetUserId,
                systemRoles,
                permissions: uniquePermissions,
                organizationId,
                projectId
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user permissions failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
// Helper functions to get permissions for each role
function getOrganizationAdminPermissions() {
    return [
        PermissionAction.UPDATE_ORGANIZATION,
        PermissionAction.VIEW_ORGANIZATION,
        PermissionAction.MANAGE_ORGANIZATION,
        PermissionAction.VIEW_ORGANIZATION_MEMBERS,
        PermissionAction.INVITE_ORGANIZATION_MEMBER,
        PermissionAction.REMOVE_ORGANIZATION_MEMBER,
        PermissionAction.MANAGE_ORGANIZATION_MEMBERS,
        PermissionAction.CREATE_PROJECT,
        PermissionAction.UPDATE_PROJECT,
        PermissionAction.DELETE_PROJECT,
        PermissionAction.VIEW_PROJECT,
        PermissionAction.MANAGE_PROJECT,
        PermissionAction.UPLOAD_DOCUMENT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.UPDATE_DOCUMENT,
        PermissionAction.DELETE_DOCUMENT,
        PermissionAction.SHARE_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT,
        PermissionAction.VIEW_ANALYTICS,
        PermissionAction.VIEW_USER_PERMISSIONS,
        PermissionAction.GRANT_PERMISSION
    ];
}
function getOrganizationMemberPermissions() {
    return [
        PermissionAction.VIEW_ORGANIZATION,
        PermissionAction.VIEW_ORGANIZATION_MEMBERS,
        PermissionAction.CREATE_PROJECT,
        PermissionAction.VIEW_PROJECT,
        PermissionAction.UPLOAD_DOCUMENT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.UPDATE_DOCUMENT,
        PermissionAction.SHARE_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT,
        PermissionAction.VIEW_ANALYTICS
    ];
}
function getOrganizationViewerPermissions() {
    return [
        PermissionAction.VIEW_ORGANIZATION,
        PermissionAction.VIEW_ORGANIZATION_MEMBERS,
        PermissionAction.VIEW_PROJECT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT,
        PermissionAction.VIEW_ANALYTICS
    ];
}
function getProjectAdminPermissions() {
    return [
        PermissionAction.UPDATE_PROJECT,
        PermissionAction.VIEW_PROJECT,
        PermissionAction.MANAGE_PROJECT,
        PermissionAction.ADD_PROJECT_MEMBER,
        PermissionAction.REMOVE_PROJECT_MEMBER,
        PermissionAction.MANAGE_PROJECT_MEMBERS,
        PermissionAction.UPLOAD_DOCUMENT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.UPDATE_DOCUMENT,
        PermissionAction.DELETE_DOCUMENT,
        PermissionAction.SHARE_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT,
        PermissionAction.EDIT_ANY_COMMENT,
        PermissionAction.DELETE_ANY_COMMENT
    ];
}
function getProjectMemberPermissions() {
    return [
        PermissionAction.VIEW_PROJECT,
        PermissionAction.UPLOAD_DOCUMENT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.UPDATE_DOCUMENT,
        PermissionAction.SHARE_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT
    ];
}
function getProjectViewerPermissions() {
    return [
        PermissionAction.VIEW_PROJECT,
        PermissionAction.VIEW_DOCUMENT,
        PermissionAction.COMMENT_DOCUMENT
    ];
}
function getGuestPermissions() {
    return [
        PermissionAction.VIEW_DOCUMENT
    ];
}
// Register functions
functions_1.app.http('user-permissions', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/{userId?}/permissions',
    handler: getUserPermissions
});
//# sourceMappingURL=user-permissions.js.map