/**
 * Data Migration Function
 * Handles data migration operations between systems and versions
 * Migrated from old-arch/src/admin-service/migration/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create migration handler
 */
export declare function createMigration(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get migration status handler
 */
export declare function getMigrationStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
