"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getSystemHealth = getSystemHealth;
exports.getPerformanceMetrics = getPerformanceMetrics;
/**
 * System Monitoring Function
 * Handles system health monitoring, performance metrics, and alerting
 * Enhanced from existing health.ts with comprehensive monitoring capabilities
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const redis_1 = require("../shared/services/redis");
// Monitoring types and enums
var ServiceStatus;
(function (ServiceStatus) {
    ServiceStatus["HEALTHY"] = "HEALTHY";
    ServiceStatus["DEGRADED"] = "DEGRADED";
    ServiceStatus["UNHEALTHY"] = "UNHEALTHY";
    ServiceStatus["UNKNOWN"] = "UNKNOWN";
})(ServiceStatus || (ServiceStatus = {}));
var AlertSeverity;
(function (AlertSeverity) {
    AlertSeverity["INFO"] = "INFO";
    AlertSeverity["WARNING"] = "WARNING";
    AlertSeverity["ERROR"] = "ERROR";
    AlertSeverity["CRITICAL"] = "CRITICAL";
})(AlertSeverity || (AlertSeverity = {}));
var MetricType;
(function (MetricType) {
    MetricType["COUNTER"] = "COUNTER";
    MetricType["GAUGE"] = "GAUGE";
    MetricType["HISTOGRAM"] = "HISTOGRAM";
    MetricType["TIMER"] = "TIMER";
})(MetricType || (MetricType = {}));
// Validation schemas
const getSystemHealthSchema = Joi.object({
    includeDetails: Joi.boolean().default(false),
    includeMetrics: Joi.boolean().default(false),
    includeAlerts: Joi.boolean().default(false)
});
const recordMetricSchema = Joi.object({
    name: Joi.string().min(1).max(100).required(),
    type: Joi.string().valid(...Object.values(MetricType)).required(),
    value: Joi.number().required(),
    tags: Joi.object().optional(),
    timestamp: Joi.string().isoDate().optional()
});
/**
 * Get system health handler
 */
async function getSystemHealth(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get system health started", { correlationId });
    try {
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            includeDetails: url.searchParams.get('includeDetails') === 'true',
            includeMetrics: url.searchParams.get('includeMetrics') === 'true',
            includeAlerts: url.searchParams.get('includeAlerts') === 'true'
        };
        // Validate query parameters
        const { error, value } = getSystemHealthSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const options = value;
        // Check service health
        const services = await checkAllServices(options.includeDetails);
        // Determine overall system status
        const overallStatus = determineOverallStatus(services);
        // Build response
        const healthResponse = {
            status: overallStatus,
            timestamp: new Date().toISOString(),
            uptime: process.uptime(),
            version: process.env.APP_VERSION || '1.0.0',
            services
        };
        // Include metrics if requested
        if (options.includeMetrics) {
            healthResponse.metrics = await getSystemMetrics();
        }
        // Include alerts if requested
        if (options.includeAlerts) {
            healthResponse.alerts = await getActiveAlerts();
        }
        // Store health check result
        await storeHealthCheckResult(healthResponse);
        logger_1.logger.info("System health check completed", {
            correlationId,
            status: overallStatus,
            serviceCount: services.length,
            unhealthyServices: services.filter(s => s.status === ServiceStatus.UNHEALTHY).length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: healthResponse
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get system health failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                status: ServiceStatus.UNHEALTHY,
                error: "Health check failed",
                timestamp: new Date().toISOString()
            }
        }, request);
    }
}
/**
 * Get performance metrics handler
 */
async function getPerformanceMetrics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get performance metrics started", { correlationId });
    try {
        // Authenticate user (admin only)
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Check admin access
        const hasAccess = await checkAdminAccess(user);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to system metrics" }
            }, request);
        }
        // Get performance metrics
        const metrics = await collectPerformanceMetrics();
        // Get historical metrics (last 24 hours)
        const historicalMetrics = await getHistoricalMetrics(24);
        logger_1.logger.info("Performance metrics retrieved successfully", {
            correlationId,
            userId: user.id,
            metricsTimestamp: metrics.timestamp
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                current: metrics,
                historical: historicalMetrics,
                summary: {
                    averageResponseTime: calculateAverageResponseTime(historicalMetrics),
                    errorRate: calculateErrorRate(historicalMetrics),
                    uptime: calculateUptime(historicalMetrics),
                    throughput: calculateThroughput(historicalMetrics)
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get performance metrics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkAllServices(includeDetails) {
    const services = [];
    // Check database
    const dbHealth = await checkDatabaseHealth(includeDetails);
    services.push(dbHealth);
    // Check Redis cache
    const redisHealth = await checkRedisHealth(includeDetails);
    services.push(redisHealth);
    // Check blob storage
    const storageHealth = await checkStorageHealth(includeDetails);
    services.push(storageHealth);
    // Check external services
    const externalHealth = await checkExternalServices(includeDetails);
    services.push(...externalHealth);
    return services;
}
async function checkDatabaseHealth(includeDetails) {
    const startTime = Date.now();
    try {
        // Simple health check query
        await database_1.db.queryItems('health-check', 'SELECT VALUE 1', []);
        const responseTime = Date.now() - startTime;
        return {
            name: 'database',
            status: responseTime < 1000 ? ServiceStatus.HEALTHY : ServiceStatus.DEGRADED,
            responseTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                provider: 'CosmosDB',
                region: process.env.COSMOS_DB_REGION || 'unknown',
                consistency: 'Session'
            } : undefined
        };
    }
    catch (error) {
        return {
            name: 'database',
            status: ServiceStatus.UNHEALTHY,
            responseTime: Date.now() - startTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                error: error instanceof Error ? error.message : String(error)
            } : undefined
        };
    }
}
async function checkRedisHealth(includeDetails) {
    const startTime = Date.now();
    try {
        await redis_1.redis.ping();
        const responseTime = Date.now() - startTime;
        return {
            name: 'cache',
            status: responseTime < 500 ? ServiceStatus.HEALTHY : ServiceStatus.DEGRADED,
            responseTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                provider: 'Redis',
                version: await redis_1.redis.info('server').then(info => {
                    const match = info.match(/redis_version:([^\r\n]+)/);
                    return match ? match[1] : 'unknown';
                }).catch(() => 'unknown')
            } : undefined
        };
    }
    catch (error) {
        return {
            name: 'cache',
            status: ServiceStatus.UNHEALTHY,
            responseTime: Date.now() - startTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                error: error instanceof Error ? error.message : String(error)
            } : undefined
        };
    }
}
async function checkStorageHealth(includeDetails) {
    const startTime = Date.now();
    try {
        // This would check blob storage connectivity
        // For now, we'll simulate a successful check
        await new Promise(resolve => setTimeout(resolve, 50));
        const responseTime = Date.now() - startTime;
        return {
            name: 'storage',
            status: ServiceStatus.HEALTHY,
            responseTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                provider: 'Azure Blob Storage',
                redundancy: 'LRS'
            } : undefined
        };
    }
    catch (error) {
        return {
            name: 'storage',
            status: ServiceStatus.UNHEALTHY,
            responseTime: Date.now() - startTime,
            lastCheck: new Date().toISOString(),
            details: includeDetails ? {
                error: error instanceof Error ? error.message : String(error)
            } : undefined
        };
    }
}
async function checkExternalServices(includeDetails) {
    const services = [];
    // Check AI services
    services.push({
        name: 'ai_services',
        status: ServiceStatus.HEALTHY, // Simulated
        responseTime: 200,
        lastCheck: new Date().toISOString(),
        details: includeDetails ? {
            provider: 'Azure AI Services',
            models: ['gpt-4', 'document-intelligence']
        } : undefined
    });
    return services;
}
function determineOverallStatus(services) {
    const unhealthyServices = services.filter(s => s.status === ServiceStatus.UNHEALTHY);
    const degradedServices = services.filter(s => s.status === ServiceStatus.DEGRADED);
    if (unhealthyServices.length > 0) {
        return ServiceStatus.UNHEALTHY;
    }
    else if (degradedServices.length > 0) {
        return ServiceStatus.DEGRADED;
    }
    else {
        return ServiceStatus.HEALTHY;
    }
}
async function getSystemMetrics() {
    try {
        // Get metrics from Redis or calculate them
        const metrics = await redis_1.redis.hgetall('system:metrics') || {};
        return {
            requests: {
                total: parseInt(metrics.requests_total || '0'),
                successful: parseInt(metrics.requests_successful || '0'),
                failed: parseInt(metrics.requests_failed || '0'),
                averageResponseTime: parseFloat(metrics.avg_response_time || '0')
            },
            resources: {
                memoryUsage: parseFloat(metrics.memory_usage || '0'),
                cpuUsage: parseFloat(metrics.cpu_usage || '0'),
                diskUsage: parseFloat(metrics.disk_usage || '0')
            },
            database: {
                connections: parseInt(metrics.db_connections || '0'),
                queryTime: parseFloat(metrics.db_query_time || '0'),
                errorRate: parseFloat(metrics.db_error_rate || '0')
            }
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to get system metrics', { error });
        return {
            requests: { total: 0, successful: 0, failed: 0, averageResponseTime: 0 },
            resources: { memoryUsage: 0, cpuUsage: 0, diskUsage: 0 },
            database: { connections: 0, queryTime: 0, errorRate: 0 }
        };
    }
}
async function getActiveAlerts() {
    try {
        const query = 'SELECT * FROM c WHERE c.resolved = false ORDER BY c.timestamp DESC';
        return await database_1.db.queryItems('system-alerts', query, []);
    }
    catch (error) {
        logger_1.logger.error('Failed to get active alerts', { error });
        return [];
    }
}
async function storeHealthCheckResult(healthResponse) {
    try {
        await database_1.db.createItem('health-checks', {
            id: (0, uuid_1.v4)(),
            ...healthResponse,
            type: 'system-health'
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to store health check result', { error });
    }
}
async function checkAdminAccess(user) {
    try {
        return user.roles?.includes('admin') || user.roles?.includes('system_admin');
    }
    catch (error) {
        logger_1.logger.error('Failed to check admin access', { error, userId: user.id });
        return false;
    }
}
async function collectPerformanceMetrics() {
    // This would collect real performance metrics
    // For now, we'll return simulated data
    return {
        timestamp: new Date().toISOString(),
        requests: {
            total: 1000,
            successful: 950,
            failed: 50,
            averageResponseTime: 250,
            requestsPerSecond: 10
        },
        resources: {
            memoryUsage: 65.5,
            cpuUsage: 45.2,
            diskUsage: 78.9,
            networkIO: 1024
        },
        database: {
            connections: 25,
            activeQueries: 5,
            queryTime: 150,
            errorRate: 0.02,
            throughput: 500
        },
        storage: {
            blobOperations: 200,
            storageUsage: 85.6,
            transferRate: 2048
        },
        cache: {
            hitRate: 92.5,
            missRate: 7.5,
            evictions: 10,
            connections: 15
        }
    };
}
async function getHistoricalMetrics(hours) {
    try {
        const since = new Date(Date.now() - hours * 60 * 60 * 1000).toISOString();
        const query = 'SELECT * FROM c WHERE c.timestamp >= @since ORDER BY c.timestamp DESC';
        return await database_1.db.queryItems('performance-metrics', query, [since]);
    }
    catch (error) {
        logger_1.logger.error('Failed to get historical metrics', { error, hours });
        return [];
    }
}
function calculateAverageResponseTime(metrics) {
    if (metrics.length === 0)
        return 0;
    const sum = metrics.reduce((acc, m) => acc + (m.requests?.averageResponseTime || 0), 0);
    return sum / metrics.length;
}
function calculateErrorRate(metrics) {
    if (metrics.length === 0)
        return 0;
    const totalRequests = metrics.reduce((acc, m) => acc + (m.requests?.total || 0), 0);
    const totalErrors = metrics.reduce((acc, m) => acc + (m.requests?.failed || 0), 0);
    return totalRequests > 0 ? (totalErrors / totalRequests) * 100 : 0;
}
function calculateUptime(_metrics) {
    // Simplified uptime calculation
    return 99.9; // Would be calculated based on actual downtime
}
function calculateThroughput(metrics) {
    if (metrics.length === 0)
        return 0;
    const sum = metrics.reduce((acc, m) => acc + (m.requests?.requestsPerSecond || 0), 0);
    return sum / metrics.length;
}
// Register functions
functions_1.app.http('system-health', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'anonymous', // Health checks should be accessible
    route: 'system/health',
    handler: getSystemHealth
});
functions_1.app.http('system-metrics', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'system/metrics',
    handler: getPerformanceMetrics
});
//# sourceMappingURL=system-monitoring.js.map