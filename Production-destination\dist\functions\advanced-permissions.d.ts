/**
 * Advanced Permissions Function
 * Handles sophisticated role-based access control and fine-grained permissions
 * Migrated from old-arch/src/shared/middleware/authorization.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create advanced role handler
 */
export declare function createAdvancedRole(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Check advanced permission handler
 */
export declare function checkAdvancedPermission(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
