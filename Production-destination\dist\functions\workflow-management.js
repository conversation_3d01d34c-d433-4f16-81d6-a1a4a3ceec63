"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.StepType = exports.StepStatus = exports.WorkflowStatus = void 0;
exports.createWorkflow = createWorkflow;
exports.getWorkflow = getWorkflow;
exports.listWorkflows = listWorkflows;
/**
 * Workflow Management Functions
 * Handles workflow creation, retrieval, and basic operations
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Workflow enums
var WorkflowStatus;
(function (WorkflowStatus) {
    WorkflowStatus["DRAFT"] = "draft";
    WorkflowStatus["ACTIVE"] = "active";
    WorkflowStatus["IN_PROGRESS"] = "in_progress";
    WorkflowStatus["COMPLETED"] = "completed";
    WorkflowStatus["CANCELLED"] = "cancelled";
    WorkflowStatus["PAUSED"] = "paused";
})(WorkflowStatus || (exports.WorkflowStatus = WorkflowStatus = {}));
var StepStatus;
(function (StepStatus) {
    StepStatus["PENDING"] = "pending";
    StepStatus["IN_PROGRESS"] = "in_progress";
    StepStatus["COMPLETED"] = "completed";
    StepStatus["SKIPPED"] = "skipped";
    StepStatus["FAILED"] = "failed";
})(StepStatus || (exports.StepStatus = StepStatus = {}));
var StepType;
(function (StepType) {
    StepType["REVIEW"] = "review";
    StepType["APPROVAL"] = "approval";
    StepType["PROCESSING"] = "processing";
    StepType["NOTIFICATION"] = "notification";
    StepType["CUSTOM"] = "custom";
})(StepType || (exports.StepType = StepType = {}));
// Validation schemas
const createWorkflowSchema = Joi.object({
    name: Joi.string().required().max(255),
    description: Joi.string().optional().max(1000),
    projectId: Joi.string().uuid().required(),
    organizationId: Joi.string().uuid().required(),
    documentId: Joi.string().uuid().optional(),
    templateId: Joi.string().uuid().optional(),
    steps: Joi.array().items(Joi.object({
        name: Joi.string().required(),
        description: Joi.string().optional(),
        type: Joi.string().valid(...Object.values(StepType)).required(),
        order: Joi.number().integer().min(1).required(),
        assigneeId: Joi.string().uuid().optional(),
        dueDate: Joi.date().iso().optional(),
        required: Joi.boolean().default(true)
    })).min(1).required(),
    priority: Joi.string().valid('low', 'medium', 'high', 'urgent').default('medium'),
    dueDate: Joi.date().iso().optional()
});
const listWorkflowsSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    projectId: Joi.string().uuid().optional(),
    status: Joi.string().valid(...Object.values(WorkflowStatus)).optional(),
    assigneeId: Joi.string().uuid().optional()
});
/**
 * Create workflow handler
 */
async function createWorkflow(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Workflow creation started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createWorkflowSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const workflowData = value;
        // Create workflow steps with IDs and default status
        const steps = workflowData.steps.map((step) => ({
            id: (0, uuid_1.v4)(),
            name: step.name,
            description: step.description,
            type: step.type,
            order: step.order,
            status: StepStatus.PENDING,
            assigneeId: step.assigneeId,
            dueDate: step.dueDate,
            required: step.required,
            createdAt: new Date().toISOString()
        }));
        // Sort steps by order
        steps.sort((a, b) => a.order - b.order);
        // Create workflow object
        const workflow = {
            id: (0, uuid_1.v4)(),
            name: workflowData.name,
            description: workflowData.description,
            status: WorkflowStatus.DRAFT,
            projectId: workflowData.projectId,
            organizationId: workflowData.organizationId,
            documentId: workflowData.documentId,
            templateId: workflowData.templateId,
            steps,
            currentStepId: steps.length > 0 ? steps[0].id : undefined,
            priority: workflowData.priority,
            dueDate: workflowData.dueDate,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            tenantId: user.tenantId
        };
        // Save to database
        await database_1.db.createItem('workflows', workflow);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "workflow_created",
            userId: user.id,
            projectId: workflow.projectId,
            workflowId: workflow.id,
            timestamp: new Date().toISOString(),
            details: {
                workflowName: workflow.name,
                documentId: workflow.documentId
            }
        });
        logger_1.logger.info("Workflow created successfully", {
            correlationId,
            workflowId: workflow.id,
            userId: user.id,
            stepsCount: steps.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: workflow.id,
                name: workflow.name,
                status: workflow.status,
                message: "Workflow created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Workflow creation failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Failed to create workflow" }
        }, request);
    }
}
/**
 * Get workflow handler
 */
async function getWorkflow(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const workflowId = request.params.id;
    if (!workflowId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Workflow ID is required' }
        }, request);
    }
    logger_1.logger.info("Workflow retrieval started", { correlationId, workflowId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get workflow from database
        const workflow = await database_1.db.readItem('workflows', workflowId, workflowId);
        if (!workflow) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Workflow not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (workflow.createdBy === user.id ||
            workflow.organizationId === user.tenantId ||
            workflow.steps.some((step) => step.assigneeId === user.id) ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Add HATEOAS links
        const workflowWithLinks = {
            ...workflow,
            _links: {
                self: { href: `/workflows/${workflow.id}` },
                start: { href: `/workflows/${workflow.id}/start` },
                steps: { href: `/workflows/${workflow.id}/steps` }
            }
        };
        logger_1.logger.info("Workflow retrieved successfully", {
            correlationId,
            workflowId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: workflowWithLinks
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Workflow retrieval failed", {
            correlationId,
            workflowId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List workflows handler
 */
async function listWorkflows(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Workflow list started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listWorkflowsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, projectId, status, assigneeId } = value;
        // Build query
        let query = 'SELECT * FROM c WHERE 1=1';
        const parameters = [];
        // Add user access filter
        if (user.tenantId) {
            query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
            parameters.push(user.tenantId, user.id);
        }
        else {
            query += ' AND c.createdBy = @userId';
            parameters.push(user.id);
        }
        // Add filters
        if (projectId) {
            query += ' AND c.projectId = @projectId';
            parameters.push(projectId);
        }
        if (status) {
            query += ' AND c.status = @status';
            parameters.push(status);
        }
        if (assigneeId) {
            query += ' AND ARRAY_CONTAINS(c.steps, {"assigneeId": @assigneeId}, true)';
            parameters.push(assigneeId);
        }
        query += ' ORDER BY c.createdAt DESC';
        // Execute query with pagination
        const offset = (page - 1) * limit;
        query += ` OFFSET ${offset} LIMIT ${limit}`;
        const workflows = await database_1.db.queryItems('workflows', query, parameters);
        logger_1.logger.info("Workflows listed successfully", {
            correlationId,
            userId: user.id,
            count: workflows.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                workflows,
                pagination: {
                    page,
                    limit,
                    hasMore: workflows.length === limit
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Workflow list failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined workflows handler
 */
async function handleWorkflows(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createWorkflow(request, context);
        case 'GET':
            return await listWorkflows(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('workflows', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflows',
    handler: handleWorkflows
});
functions_1.app.http('workflow-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflows/{id}',
    handler: getWorkflow
});
//# sourceMappingURL=workflow-management.js.map