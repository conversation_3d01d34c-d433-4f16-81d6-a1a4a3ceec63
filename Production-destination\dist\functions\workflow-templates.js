"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createWorkflowTemplate = createWorkflowTemplate;
exports.getWorkflowTemplate = getWorkflowTemplate;
exports.listWorkflowTemplates = listWorkflowTemplates;
/**
 * Workflow Templates Function
 * Handles workflow template management (create, read, update, delete)
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const createTemplateSchema = Joi.object({
    name: Joi.string().required().max(100),
    description: Joi.string().max(500).optional(),
    projectId: Joi.string().uuid().required(),
    definition: Joi.object({
        steps: Joi.array().items(Joi.object({
            name: Joi.string().required(),
            description: Joi.string().optional(),
            type: Joi.string().valid('approval', 'review', 'task', 'notification').required(),
            order: Joi.number().integer().min(1).required(),
            assigneeRole: Joi.string().optional(),
            durationDays: Joi.number().integer().min(1).optional(),
            isRequired: Joi.boolean().default(true),
            conditions: Joi.object().optional()
        })).min(1).required(),
        settings: Joi.object({
            autoStart: Joi.boolean().default(false),
            allowParallel: Joi.boolean().default(false),
            notifyOnCompletion: Joi.boolean().default(true)
        }).optional()
    }).required(),
    isDefault: Joi.boolean().default(false),
    documentTypes: Joi.array().items(Joi.string()).optional(),
    organizationId: Joi.string().uuid().required()
});
const updateTemplateSchema = Joi.object({
    name: Joi.string().max(100).optional(),
    description: Joi.string().max(500).optional(),
    definition: Joi.object().optional(),
    isDefault: Joi.boolean().optional(),
    documentTypes: Joi.array().items(Joi.string()).optional(),
    isActive: Joi.boolean().optional()
});
const getTemplatesSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    projectId: Joi.string().uuid().optional(),
    isActive: Joi.boolean().optional(),
    isDefault: Joi.boolean().optional()
});
/**
 * Create workflow template handler
 */
async function createWorkflowTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create workflow template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const templateData = value;
        // Check if this is set as default and update other templates if needed
        if (templateData.isDefault) {
            const query = 'SELECT * FROM c WHERE c.projectId = @projectId AND c.isDefault = true';
            const existingDefaultTemplates = await database_1.db.queryItems('workflow-templates', query, [templateData.projectId]);
            // Update existing default templates to not be default
            for (const template of existingDefaultTemplates) {
                const updatedTemplate = {
                    ...template,
                    id: template.id,
                    isDefault: false,
                    updatedBy: user.id,
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('workflow-templates', updatedTemplate);
            }
        }
        // Create template
        const templateId = (0, uuid_1.v4)();
        const template = {
            id: templateId,
            name: templateData.name,
            description: templateData.description || "",
            projectId: templateData.projectId,
            organizationId: templateData.organizationId,
            definition: templateData.definition,
            isDefault: templateData.isDefault || false,
            isActive: true,
            documentTypes: templateData.documentTypes || [],
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            tenantId: user.tenantId
        };
        await database_1.db.createItem('workflow-templates', template);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "workflow_template_created",
            userId: user.id,
            organizationId: templateData.organizationId,
            projectId: templateData.projectId,
            templateId: templateId,
            timestamp: new Date().toISOString(),
            details: {
                templateName: template.name,
                isDefault: template.isDefault,
                stepsCount: template.definition.steps.length
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Workflow template created successfully", {
            correlationId,
            templateId,
            userId: user.id,
            projectId: templateData.projectId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: template.id,
                name: template.name,
                description: template.description,
                projectId: template.projectId,
                organizationId: template.organizationId,
                definition: template.definition,
                isDefault: template.isDefault,
                isActive: template.isActive,
                documentTypes: template.documentTypes,
                createdBy: template.createdBy,
                createdAt: template.createdAt,
                updatedBy: template.updatedBy,
                updatedAt: template.updatedAt,
                _links: {
                    self: { href: `/workflow-templates/${templateId}` },
                    createWorkflow: { href: `/workflows?templateId=${templateId}` }
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create workflow template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get workflow template handler
 */
async function getWorkflowTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const templateId = request.params.id;
    if (!templateId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Template ID is required' }
        }, request);
    }
    logger_1.logger.info("Get workflow template started", { correlationId, templateId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get template
        const template = await database_1.db.readItem('workflow-templates', templateId, templateId);
        if (!template) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Template not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (template.createdBy === user.id ||
            template.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        logger_1.logger.info("Workflow template retrieved successfully", {
            correlationId,
            templateId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                ...template,
                _links: {
                    self: { href: `/workflow-templates/${templateId}` },
                    createWorkflow: { href: `/workflows?templateId=${templateId}` },
                    update: { href: `/workflow-templates/${templateId}` },
                    delete: { href: `/workflow-templates/${templateId}` }
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get workflow template failed", {
            correlationId,
            templateId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List workflow templates handler
 */
async function listWorkflowTemplates(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List workflow templates started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getTemplatesSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, projectId, isActive, isDefault } = value;
        // Build query
        let query = 'SELECT * FROM c WHERE 1=1';
        const parameters = [];
        // Add tenant isolation
        if (user.tenantId) {
            query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
            parameters.push(user.tenantId, user.id);
        }
        // Add filters
        if (projectId) {
            query += ' AND c.projectId = @projectId';
            parameters.push(projectId);
        }
        if (isActive !== undefined) {
            query += ' AND c.isActive = @isActive';
            parameters.push(isActive);
        }
        if (isDefault !== undefined) {
            query += ' AND c.isDefault = @isDefault';
            parameters.push(isDefault);
        }
        query += ' ORDER BY c.createdAt DESC';
        // Execute query with pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;
        const templates = await database_1.db.queryItems('workflow-templates', paginatedQuery, parameters);
        // Get total count
        const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
        const countResult = await database_1.db.queryItems('workflow-templates', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        logger_1.logger.info("Workflow templates retrieved successfully", {
            correlationId,
            userId: user.id,
            count: templates.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                templates,
                pagination: {
                    page,
                    limit,
                    total,
                    hasMore: (page * limit) < total
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List workflow templates failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined workflow templates handler
 */
async function handleWorkflowTemplates(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createWorkflowTemplate(request, context);
        case 'GET':
            return await listWorkflowTemplates(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('workflow-templates', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflow-templates',
    handler: handleWorkflowTemplates
});
functions_1.app.http('workflow-templates-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'workflow-templates/{id}',
    handler: getWorkflowTemplate
});
//# sourceMappingURL=workflow-templates.js.map