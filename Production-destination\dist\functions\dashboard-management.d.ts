/**
 * Dashboard Management Function
 * Handles dashboard creation, customization, and widget management
 * Migrated from old-arch/src/analytics-service/dashboard/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create dashboard handler
 */
export declare function createDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get dashboard handler
 */
export declare function getDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
