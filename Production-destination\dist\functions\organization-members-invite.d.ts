/**
 * Organization Members Invite Function
 * Handles inviting new members to organizations
 * Migrated from old-arch/src/organization-service/members/invite/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Invite organization member handler
 */
export declare function inviteOrganizationMember(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
