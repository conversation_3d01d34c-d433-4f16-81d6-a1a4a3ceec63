"use strict";
/**
 * CORS middleware for Azure Functions
 * Handles Cross-Origin Resource Sharing headers and preflight requests
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.addCorsHeaders = addCorsHeaders;
exports.handlePreflight = handlePreflight;
exports.withCors = withCors;
const defaultCorsOptions = {
    allowedOrigins: ['*'],
    allowedMethods: ['GET', 'POST', 'PUT', 'DELETE', 'PATCH', 'OPTIONS'],
    allowedHeaders: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin',
        'Access-Control-Request-Method',
        'Access-Control-Request-Headers',
        'X-Tenant-ID',
        'X-User-ID'
    ],
    allowCredentials: true,
    maxAge: 86400 // 24 hours
};
/**
 * Add CORS headers to response
 */
function addCorsHeaders(response, request, options = {}) {
    const corsOptions = { ...defaultCorsOptions, ...options };
    const origin = request.headers.get('origin') || '*';
    // Determine allowed origin
    let allowedOrigin = '*';
    if (corsOptions.allowedOrigins && corsOptions.allowedOrigins.length > 0) {
        if (corsOptions.allowedOrigins.includes('*')) {
            allowedOrigin = origin;
        }
        else if (corsOptions.allowedOrigins.includes(origin)) {
            allowedOrigin = origin;
        }
        else {
            allowedOrigin = corsOptions.allowedOrigins[0];
        }
    }
    const corsHeaders = {
        'Access-Control-Allow-Origin': allowedOrigin,
        'Access-Control-Allow-Methods': corsOptions.allowedMethods?.join(', ') || '',
        'Access-Control-Allow-Headers': corsOptions.allowedHeaders?.join(', ') || '',
        'Access-Control-Max-Age': corsOptions.maxAge?.toString() || '86400',
        ...(corsOptions.allowCredentials && { 'Access-Control-Allow-Credentials': 'true' })
    };
    return {
        ...response,
        headers: {
            ...response.headers,
            ...corsHeaders
        }
    };
}
/**
 * Handle preflight OPTIONS request
 */
function handlePreflight(request, options = {}) {
    if (request.method === 'OPTIONS') {
        return addCorsHeaders({
            status: 200,
            headers: {
                'Content-Length': '0'
            }
        }, request, options);
    }
    return null;
}
/**
 * CORS middleware wrapper for Azure Functions
 */
function withCors(handler, options = {}) {
    return async (request, context) => {
        // Handle preflight request
        const preflightResponse = handlePreflight(request, options);
        if (preflightResponse) {
            return preflightResponse;
        }
        // Execute the handler
        const response = await handler(request, context);
        // Add CORS headers to response
        return addCorsHeaders(response, request, options);
    };
}
//# sourceMappingURL=cors.js.map