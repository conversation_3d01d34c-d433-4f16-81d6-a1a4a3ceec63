"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.trackNotificationEngagement = trackNotificationEngagement;
exports.getNotificationAnalytics = getNotificationAnalytics;
/**
 * Notification Tracking Function
 * Handles notification engagement tracking, analytics, and delivery monitoring
 * Migrated from old-arch/src/notification-service/analytics/track/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Notification tracking types and enums
var NotificationEvent;
(function (NotificationEvent) {
    NotificationEvent["DELIVERED"] = "delivered";
    NotificationEvent["VIEWED"] = "viewed";
    NotificationEvent["CLICKED"] = "clicked";
    NotificationEvent["DISMISSED"] = "dismissed";
    NotificationEvent["OPENED"] = "opened";
    NotificationEvent["FAILED"] = "failed";
})(NotificationEvent || (NotificationEvent = {}));
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["IN_APP"] = "in_app";
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["PUSH"] = "push";
    NotificationChannel["SMS"] = "sms";
})(NotificationChannel || (NotificationChannel = {}));
// Validation schemas
const trackNotificationSchema = Joi.object({
    notificationId: Joi.string().uuid().required(),
    event: Joi.string().valid(...Object.values(NotificationEvent)).required(),
    channel: Joi.string().valid(...Object.values(NotificationChannel)).optional(),
    action: Joi.string().max(100).optional(),
    metadata: Joi.object().optional(),
    timestamp: Joi.string().isoDate().optional(),
    userAgent: Joi.string().optional(),
    ipAddress: Joi.string().ip().optional(),
    deviceInfo: Joi.object({
        platform: Joi.string().optional(),
        browser: Joi.string().optional(),
        version: Joi.string().optional()
    }).optional()
});
const getNotificationAnalyticsSchema = Joi.object({
    notificationId: Joi.string().uuid().optional(),
    userId: Joi.string().uuid().optional(),
    organizationId: Joi.string().uuid().optional(),
    startDate: Joi.string().isoDate().optional(),
    endDate: Joi.string().isoDate().optional(),
    event: Joi.string().valid(...Object.values(NotificationEvent)).optional(),
    channel: Joi.string().valid(...Object.values(NotificationChannel)).optional(),
    groupBy: Joi.string().valid('event', 'channel', 'type', 'day', 'hour').optional(),
    limit: Joi.number().min(1).max(1000).default(100)
});
/**
 * Track notification engagement handler
 */
async function trackNotificationEngagement(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Track notification engagement started", { correlationId });
    try {
        // For tracking, we allow both authenticated and anonymous requests
        // Some tracking events (like email opens) may not have user context
        let user = null;
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (authResult.success) {
            user = authResult.user;
        }
        // Validate request body
        const body = await request.json();
        const { error, value } = trackNotificationSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const trackingRequest = value;
        // Get notification details
        const notification = await database_1.db.readItem('notifications', trackingRequest.notificationId, trackingRequest.notificationId);
        if (!notification) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Notification not found" }
            }, request);
        }
        const notificationData = notification;
        const now = new Date().toISOString();
        // Extract additional context from request
        const clientIP = trackingRequest.ipAddress ||
            request.headers.get('x-forwarded-for') ||
            request.headers.get('x-real-ip') ||
            'unknown';
        const userAgent = trackingRequest.userAgent ||
            request.headers.get('user-agent') ||
            'unknown';
        // Create tracking record
        const trackingId = (0, uuid_1.v4)();
        const trackingRecord = {
            id: trackingId,
            notificationId: trackingRequest.notificationId,
            userId: user?.id || notificationData.userId,
            organizationId: notificationData.organizationId,
            event: trackingRequest.event,
            channel: trackingRequest.channel || NotificationChannel.IN_APP,
            action: trackingRequest.action,
            timestamp: trackingRequest.timestamp || now,
            metadata: {
                ...trackingRequest.metadata,
                notificationType: notificationData.type,
                notificationTitle: notificationData.title,
                clientIP,
                userAgent,
                deviceInfo: trackingRequest.deviceInfo
            },
            createdAt: now,
            tenantId: notificationData.userId // Use notification userId for partitioning
        };
        await database_1.db.createItem('notification-tracking', trackingRecord);
        // Update notification status based on event
        if (trackingRequest.event === NotificationEvent.VIEWED && notificationData.status === 'unread') {
            const updatedNotification = {
                ...notificationData,
                status: 'read',
                readAt: now,
                updatedAt: now
            };
            await database_1.db.updateItem('notifications', updatedNotification);
        }
        // Update notification engagement metrics
        await updateNotificationMetrics(notificationData, trackingRequest.event, trackingRequest.channel);
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'NotificationEngagementTracked',
            aggregateId: trackingId,
            aggregateType: 'NotificationTracking',
            version: 1,
            data: {
                tracking: trackingRecord,
                notification: notificationData,
                trackedBy: user?.id
            },
            userId: user?.id || notificationData.userId,
            organizationId: notificationData.organizationId,
            tenantId: notificationData.userId
        });
        logger_1.logger.info("Notification engagement tracked successfully", {
            correlationId,
            trackingId,
            notificationId: trackingRequest.notificationId,
            event: trackingRequest.event,
            channel: trackingRequest.channel,
            userId: user?.id || notificationData.userId
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: trackingId,
                notificationId: trackingRequest.notificationId,
                event: trackingRequest.event,
                channel: trackingRequest.channel,
                timestamp: trackingRecord.timestamp,
                message: "Notification engagement tracked successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Track notification engagement failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get notification analytics handler
 */
async function getNotificationAnalytics(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get notification analytics started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Parse query parameters
        const url = new URL(request.url);
        const queryParams = {
            notificationId: url.searchParams.get('notificationId') || undefined,
            userId: url.searchParams.get('userId') || undefined,
            organizationId: url.searchParams.get('organizationId') || undefined,
            startDate: url.searchParams.get('startDate') || undefined,
            endDate: url.searchParams.get('endDate') || undefined,
            event: url.searchParams.get('event') || undefined,
            channel: url.searchParams.get('channel') || undefined,
            groupBy: url.searchParams.get('groupBy') || undefined,
            limit: parseInt(url.searchParams.get('limit') || '100')
        };
        // Validate request
        const { error, value } = getNotificationAnalyticsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const analyticsRequest = value;
        // Check permissions - users can only view their own analytics unless they're admin
        if (analyticsRequest.userId && analyticsRequest.userId !== user.id && !user.roles?.includes('admin')) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to notification analytics" }
            }, request);
        }
        // Build query based on filters
        const analytics = await getNotificationAnalyticsData(analyticsRequest, user);
        logger_1.logger.info("Notification analytics retrieved successfully", {
            correlationId,
            userId: user.id,
            filters: analyticsRequest,
            resultCount: analytics.summary.totalEvents
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: analytics
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get notification analytics failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update notification metrics
 */
async function updateNotificationMetrics(notification, event, channel) {
    try {
        // Get or create metrics record for this notification
        const metricsId = `metrics_${notification.id}`;
        let metrics = await database_1.db.readItem('notification-metrics', metricsId, metricsId);
        if (!metrics) {
            metrics = {
                id: metricsId,
                notificationId: notification.id,
                userId: notification.userId,
                organizationId: notification.organizationId,
                type: notification.type,
                sentAt: notification.createdAt,
                metrics: {
                    delivered: 0,
                    viewed: 0,
                    clicked: 0,
                    dismissed: 0,
                    opened: 0,
                    failed: 0
                },
                channels: {
                    in_app: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
                    email: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
                    push: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 },
                    sms: { delivered: 0, viewed: 0, clicked: 0, dismissed: 0 }
                },
                createdAt: new Date().toISOString(),
                updatedAt: new Date().toISOString(),
                tenantId: notification.userId
            };
        }
        const metricsData = metrics;
        // Update overall metrics
        if (metricsData.metrics[event] !== undefined) {
            metricsData.metrics[event]++;
        }
        // Update channel-specific metrics
        if (channel && metricsData.channels[channel] && metricsData.channels[channel][event] !== undefined) {
            metricsData.channels[channel][event]++;
        }
        metricsData.updatedAt = new Date().toISOString();
        await database_1.db.upsertItem('notification-metrics', metricsData);
    }
    catch (error) {
        logger_1.logger.error('Failed to update notification metrics', {
            error,
            notificationId: notification.id,
            event,
            channel
        });
    }
}
/**
 * Get notification analytics data
 */
async function getNotificationAnalyticsData(request, user) {
    try {
        // Build base query
        let query = 'SELECT * FROM c WHERE 1=1';
        const parameters = [];
        // Add filters
        if (request.notificationId) {
            query += ' AND c.notificationId = @notificationId';
            parameters.push(request.notificationId);
        }
        if (request.userId) {
            query += ' AND c.userId = @userId';
            parameters.push(request.userId);
        }
        else if (!user.roles?.includes('admin')) {
            // Non-admin users can only see their own data
            query += ' AND c.userId = @currentUserId';
            parameters.push(user.id);
        }
        if (request.organizationId) {
            query += ' AND c.organizationId = @organizationId';
            parameters.push(request.organizationId);
        }
        if (request.startDate) {
            query += ' AND c.timestamp >= @startDate';
            parameters.push(request.startDate);
        }
        if (request.endDate) {
            query += ' AND c.timestamp <= @endDate';
            parameters.push(request.endDate);
        }
        if (request.event) {
            query += ' AND c.event = @event';
            parameters.push(request.event);
        }
        if (request.channel) {
            query += ' AND c.channel = @channel';
            parameters.push(request.channel);
        }
        query += ` ORDER BY c.timestamp DESC OFFSET 0 LIMIT ${request.limit}`;
        // Get tracking data
        const trackingData = await database_1.db.queryItems('notification-tracking', query, parameters);
        // Calculate summary statistics
        const summary = calculateAnalyticsSummary(trackingData);
        // Group data if requested
        const groupedData = request.groupBy ? groupAnalyticsData(trackingData, request.groupBy) : undefined;
        return {
            summary,
            groupedData,
            events: trackingData.slice(0, 50), // Return recent events
            filters: request
        };
    }
    catch (error) {
        logger_1.logger.error('Failed to get notification analytics data', { error, request });
        throw error;
    }
}
/**
 * Calculate analytics summary
 */
function calculateAnalyticsSummary(trackingData) {
    const summary = {
        totalEvents: trackingData.length,
        uniqueNotifications: new Set(trackingData.map(t => t.notificationId)).size,
        uniqueUsers: new Set(trackingData.map(t => t.userId)).size,
        eventCounts: {},
        channelCounts: {},
        engagementRate: 0,
        clickThroughRate: 0
    };
    // Count events by type
    trackingData.forEach(event => {
        summary.eventCounts[event.event] = (summary.eventCounts[event.event] || 0) + 1;
        summary.channelCounts[event.channel] = (summary.channelCounts[event.channel] || 0) + 1;
    });
    // Calculate engagement rates
    const deliveredCount = summary.eventCounts[NotificationEvent.DELIVERED] || 0;
    const viewedCount = summary.eventCounts[NotificationEvent.VIEWED] || 0;
    const clickedCount = summary.eventCounts[NotificationEvent.CLICKED] || 0;
    if (deliveredCount > 0) {
        summary.engagementRate = Math.round((viewedCount / deliveredCount) * 100);
        summary.clickThroughRate = Math.round((clickedCount / deliveredCount) * 100);
    }
    return summary;
}
/**
 * Group analytics data
 */
function groupAnalyticsData(trackingData, groupBy) {
    const grouped = {};
    trackingData.forEach(event => {
        let key;
        switch (groupBy) {
            case 'event':
                key = event.event;
                break;
            case 'channel':
                key = event.channel;
                break;
            case 'type':
                key = event.metadata?.notificationType || 'unknown';
                break;
            case 'day':
                key = new Date(event.timestamp).toISOString().split('T')[0];
                break;
            case 'hour':
                key = new Date(event.timestamp).toISOString().substring(0, 13);
                break;
            default:
                key = 'all';
        }
        if (!grouped[key]) {
            grouped[key] = {
                key,
                count: 0,
                events: {}
            };
        }
        grouped[key].count++;
        grouped[key].events[event.event] = (grouped[key].events[event.event] || 0) + 1;
    });
    return Object.values(grouped).sort((a, b) => b.count - a.count);
}
// Register functions
functions_1.app.http('notification-tracking-track', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'anonymous', // Allow anonymous tracking for email opens, etc.
    route: 'notifications/track',
    handler: trackNotificationEngagement
});
functions_1.app.http('notification-analytics', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/analytics',
    handler: getNotificationAnalytics
});
//# sourceMappingURL=notification-tracking.js.map