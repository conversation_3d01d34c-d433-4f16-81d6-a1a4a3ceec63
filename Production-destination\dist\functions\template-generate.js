"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateFromTemplate = generateFromTemplate;
/**
 * Template Generate Function
 * Handles document generation from templates with variable substitution
 * Migrated from old-arch/src/template-service/generate/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const notification_1 = require("../shared/services/notification");
const event_1 = require("../shared/services/event");
// Output formats
var OutputFormat;
(function (OutputFormat) {
    OutputFormat["PDF"] = "pdf";
    OutputFormat["DOCX"] = "docx";
    OutputFormat["HTML"] = "html";
    OutputFormat["TXT"] = "txt";
})(OutputFormat || (OutputFormat = {}));
// Validation schema
const generateFromTemplateSchema = Joi.object({
    templateId: Joi.string().uuid().required(),
    variables: Joi.object().required(),
    outputFormat: Joi.string().valid(...Object.values(OutputFormat)).default(OutputFormat.PDF),
    outputName: Joi.string().max(255).optional(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    options: Joi.object({
        includeMetadata: Joi.boolean().default(true),
        watermark: Joi.string().max(100).optional(),
        password: Joi.string().min(6).max(50).optional(),
        quality: Joi.string().valid('low', 'medium', 'high').default('medium'),
        compression: Joi.boolean().default(true)
    }).optional()
});
/**
 * Generate document from template handler
 */
async function generateFromTemplate(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Generate from template started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = generateFromTemplateSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const generateRequest = value;
        const startTime = Date.now();
        // Get template
        const template = await database_1.db.readItem('templates', generateRequest.templateId, generateRequest.templateId);
        if (!template) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Template not found" }
            }, request);
        }
        const templateData = template;
        // Check template access permissions
        const hasAccess = await checkTemplateAccess(templateData, user, generateRequest.organizationId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to template" }
            }, request);
        }
        // Verify organization access
        const organization = await database_1.db.readItem('organizations', generateRequest.organizationId, generateRequest.organizationId);
        if (!organization) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Organization not found" }
            }, request);
        }
        // Check generation limits for organization tier
        const orgData = organization;
        if (await isGenerationLimitReached(generateRequest.organizationId, orgData.tier)) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Document generation limit reached for this organization tier",
                    tier: orgData.tier
                }
            }, request);
        }
        // Validate required variables
        const missingVariables = validateTemplateVariables(templateData, generateRequest.variables);
        if (missingVariables.length > 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Missing required template variables",
                    missingVariables
                }
            }, request);
        }
        // Generate document from template
        const generationResult = await performTemplateGeneration(templateData, generateRequest.variables, generateRequest.outputFormat, generateRequest.options || {});
        // Save generated document to blob storage
        const documentId = (0, uuid_1.v4)();
        const fileName = generateRequest.outputName || `${templateData.name}_${new Date().toISOString().split('T')[0]}.${generateRequest.outputFormat}`;
        const blobName = `${generateRequest.organizationId}/${generateRequest.projectId || 'templates'}/${documentId}_${fileName}`;
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlockBlobClient(blobName);
        await blobClient.upload(generationResult.content, generationResult.content.length, {
            blobHTTPHeaders: {
                blobContentType: getContentType(generateRequest.outputFormat),
                blobContentDisposition: `attachment; filename="${fileName}"`
            }
        });
        // Create document record
        const generatedDocument = {
            id: documentId,
            name: fileName,
            description: `Generated from template: ${templateData.name}`,
            blobName,
            contentType: getContentType(generateRequest.outputFormat),
            size: generationResult.content.length,
            organizationId: generateRequest.organizationId,
            projectId: generateRequest.projectId,
            templateId: generateRequest.templateId,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedBy: user.id,
            updatedAt: new Date().toISOString(),
            status: "GENERATED",
            metadata: {
                generatedFromTemplate: true,
                templateName: templateData.name,
                templateVersion: templateData.version,
                variables: generateRequest.variables,
                outputFormat: generateRequest.outputFormat,
                processingTime: Date.now() - startTime,
                substitutionCount: generationResult.substitutionCount
            },
            tenantId: user.tenantId
        };
        await database_1.db.createItem('documents', generatedDocument);
        // Update template usage count
        const updatedTemplate = {
            ...templateData,
            usageCount: (templateData.usageCount || 0) + 1,
            lastUsedAt: new Date().toISOString(),
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('templates', updatedTemplate);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "template_document_generated",
            userId: user.id,
            organizationId: generateRequest.organizationId,
            projectId: generateRequest.projectId,
            documentId,
            templateId: generateRequest.templateId,
            timestamp: new Date().toISOString(),
            details: {
                templateName: templateData.name,
                outputFormat: generateRequest.outputFormat,
                fileName,
                fileSize: generationResult.content.length,
                variableCount: Object.keys(generateRequest.variables).length,
                substitutionCount: generationResult.substitutionCount,
                processingTime: Date.now() - startTime
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentGeneratedFromTemplate',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: generatedDocument,
                template: templateData,
                variables: generateRequest.variables,
                generatedBy: user.id
            },
            userId: user.id,
            organizationId: generateRequest.organizationId,
            tenantId: user.tenantId
        });
        // Send notification
        await notification_1.notificationService.sendNotification({
            userId: user.id,
            type: 'DOCUMENT_GENERATED',
            title: 'Document generated successfully!',
            message: `Your document "${fileName}" has been generated from template "${templateData.name}".`,
            priority: 'normal',
            metadata: {
                documentId,
                templateId: generateRequest.templateId,
                templateName: templateData.name,
                fileName,
                outputFormat: generateRequest.outputFormat,
                organizationId: generateRequest.organizationId
            },
            organizationId: generateRequest.organizationId,
            projectId: generateRequest.projectId
        });
        // Generate download URL
        const permissions = new storage_blob_1.BlobSASPermissions();
        permissions.read = true;
        const downloadUrl = await blobClient.generateSasUrl({
            permissions,
            expiresOn: new Date(Date.now() + 24 * 60 * 60 * 1000) // 24 hours
        });
        const response = {
            documentId,
            templateId: generateRequest.templateId,
            templateName: templateData.name,
            outputFormat: generateRequest.outputFormat,
            fileName,
            fileSize: generationResult.content.length,
            downloadUrl,
            variables: generateRequest.variables,
            metadata: {
                generatedAt: new Date().toISOString(),
                processingTime: Date.now() - startTime,
                variableCount: Object.keys(generateRequest.variables).length,
                substitutionCount: generationResult.substitutionCount
            },
            success: true
        };
        logger_1.logger.info("Document generated from template successfully", {
            correlationId,
            documentId,
            templateId: generateRequest.templateId,
            templateName: templateData.name,
            outputFormat: generateRequest.outputFormat,
            fileSize: generationResult.content.length,
            processingTime: Date.now() - startTime,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Generate from template failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Check template access permissions
 */
async function checkTemplateAccess(template, user, organizationId) {
    // Template is public
    if (template.isPublic) {
        return true;
    }
    // User is the creator
    if (template.createdBy === user.id) {
        return true;
    }
    // Template belongs to user's organization
    if (template.organizationId === organizationId) {
        // Check if user is member of the organization
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, organizationId, 'ACTIVE']);
        return memberships.length > 0;
    }
    // Check explicit permissions
    if (template.permissions) {
        return template.permissions.some((p) => p.entityType === 'user' && p.entityId === user.id && p.permission === 'use');
    }
    return false;
}
/**
 * Validate template variables
 */
function validateTemplateVariables(template, providedVariables) {
    const requiredVariables = template.variables?.filter((v) => v.required) || [];
    const missingVariables = [];
    for (const variable of requiredVariables) {
        if (!(variable.name in providedVariables) || providedVariables[variable.name] === null || providedVariables[variable.name] === undefined) {
            missingVariables.push(variable.name);
        }
    }
    return missingVariables;
}
/**
 * Check if generation limit is reached for organization
 */
async function isGenerationLimitReached(organizationId, tier) {
    try {
        // Get current month's generation count
        const startOfMonth = new Date();
        startOfMonth.setDate(1);
        startOfMonth.setHours(0, 0, 0, 0);
        const generationQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate AND c.metadata.generatedFromTemplate = true';
        const result = await database_1.db.queryItems('documents', generationQuery, [organizationId, startOfMonth.toISOString()]);
        const currentCount = Number(result[0]) || 0;
        // Define tier limits
        const limits = {
            'FREE': 50,
            'PROFESSIONAL': 500,
            'ENTERPRISE': -1 // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        return limit > 0 && currentCount >= limit;
    }
    catch (error) {
        logger_1.logger.error('Failed to check generation limit', { error, organizationId });
        return false;
    }
}
/**
 * Perform template generation (simplified implementation)
 */
async function performTemplateGeneration(template, variables, outputFormat, options) {
    logger_1.logger.info("Performing template generation", {
        templateId: template.id,
        templateName: template.name,
        outputFormat,
        variableCount: Object.keys(variables).length
    });
    // Get template content
    let templateContent = template.content || template.body || "";
    // Perform variable substitution
    let substitutionCount = 0;
    for (const [key, value] of Object.entries(variables)) {
        const regex = new RegExp(`{{\\s*${key}\\s*}}`, 'g');
        const matches = templateContent.match(regex);
        if (matches) {
            substitutionCount += matches.length;
            templateContent = templateContent.replace(regex, String(value));
        }
    }
    // Add metadata if requested
    if (options.includeMetadata) {
        const metadata = `\n\n<!-- Generated from template: ${template.name} on ${new Date().toISOString()} -->`;
        templateContent += metadata;
    }
    // Add watermark if specified
    if (options.watermark) {
        templateContent = `${options.watermark}\n\n${templateContent}`;
    }
    // Convert to specified format
    let content;
    switch (outputFormat) {
        case OutputFormat.HTML:
            content = Buffer.from(`<!DOCTYPE html>
<html>
<head>
    <title>${template.name}</title>
    <meta charset="UTF-8">
    <style>
        body { font-family: Arial, sans-serif; line-height: 1.6; margin: 40px; }
        .document-content { max-width: 800px; margin: 0 auto; }
    </style>
</head>
<body>
    <div class="document-content">
        ${templateContent.replace(/\n/g, '<br>')}
    </div>
</body>
</html>`, 'utf-8');
            break;
        case OutputFormat.TXT:
            content = Buffer.from(templateContent, 'utf-8');
            break;
        case OutputFormat.PDF:
        case OutputFormat.DOCX:
        default:
            // In production, this would use libraries like puppeteer for PDF or docx for DOCX
            content = Buffer.from(`[${outputFormat.toUpperCase()} Content]\n\n${templateContent}\n\n[Generated from template: ${template.name}]`, 'utf-8');
            break;
    }
    return { content, substitutionCount };
}
/**
 * Get content type for output format
 */
function getContentType(format) {
    const contentTypes = {
        [OutputFormat.PDF]: 'application/pdf',
        [OutputFormat.DOCX]: 'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        [OutputFormat.HTML]: 'text/html',
        [OutputFormat.TXT]: 'text/plain'
    };
    return contentTypes[format] || 'application/octet-stream';
}
// Register functions
functions_1.app.http('template-generate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'templates/generate',
    handler: generateFromTemplate
});
//# sourceMappingURL=template-generate.js.map