/**
 * Business Intelligence Function
 * Handles advanced business intelligence and reporting
 * Migrated from old-arch/src/analytics-service/business-intelligence/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Generate business intelligence report handler
 */
export declare function generateBIReport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get BI dashboard handler
 */
export declare function getBIDashboard(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
