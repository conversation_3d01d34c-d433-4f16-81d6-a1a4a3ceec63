"use strict";
/**
 * Notification Service
 * Handles sending notifications via various channels (in-app, email, push)
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.notificationService = exports.NotificationService = exports.NotificationChannelType = void 0;
const logger_1 = require("../utils/logger");
const database_1 = require("./database");
const uuid_1 = require("uuid");
var NotificationChannelType;
(function (NotificationChannelType) {
    NotificationChannelType["IN_APP"] = "in_app";
    NotificationChannelType["EMAIL"] = "email";
    NotificationChannelType["PUSH"] = "push";
    NotificationChannelType["WEBHOOK"] = "webhook";
    NotificationChannelType["SLACK"] = "slack";
})(NotificationChannelType || (exports.NotificationChannelType = NotificationChannelType = {}));
class NotificationService {
    /**
     * Send a notification through multiple channels
     */
    async sendNotification(request) {
        try {
            logger_1.logger.info('Sending notification', {
                userId: request.userId,
                type: request.type,
                title: request.title
            });
            const results = [];
            let notificationId;
            // Always send in-app notification
            try {
                const inAppResult = await this.sendInAppNotification({
                    userId: request.userId,
                    type: request.type,
                    title: request.title,
                    message: request.message,
                    resourceId: request.resourceId,
                    resourceType: request.resourceType,
                    priority: request.priority || 'normal',
                    metadata: request.metadata
                });
                notificationId = inAppResult.notificationId;
                results.push({ type: 'inApp', success: true });
            }
            catch (error) {
                logger_1.logger.error('Failed to send in-app notification', { error, userId: request.userId });
                results.push({ type: 'inApp', success: false, error: error instanceof Error ? error.message : String(error) });
            }
            // Send through additional channels if specified
            if (request.channels) {
                for (const channelType of request.channels) {
                    try {
                        switch (channelType) {
                            case NotificationChannelType.EMAIL:
                                await this.sendEmailNotification({
                                    to: request.userId, // This should be resolved to email
                                    subject: request.title,
                                    body: request.message,
                                    priority: request.priority
                                });
                                results.push({ type: 'email', success: true });
                                break;
                            case NotificationChannelType.PUSH:
                                await this.sendPushNotification({
                                    userId: request.userId,
                                    title: request.title,
                                    body: request.message,
                                    data: request.metadata
                                });
                                results.push({ type: 'push', success: true });
                                break;
                            case NotificationChannelType.WEBHOOK:
                                await this.sendWebhookNotification(request, {});
                                results.push({ type: 'webhook', success: true });
                                break;
                            case NotificationChannelType.SLACK:
                                // Slack notification implementation would go here
                                logger_1.logger.info('Slack notification would be sent', { userId: request.userId });
                                results.push({ type: 'slack', success: true });
                                break;
                            default:
                                logger_1.logger.warn('Unknown notification channel type', { type: channelType });
                        }
                    }
                    catch (error) {
                        logger_1.logger.error(`Failed to send ${channelType} notification`, { error, userId: request.userId });
                        results.push({
                            type: channelType,
                            success: false,
                            error: error instanceof Error ? error.message : String(error)
                        });
                    }
                }
            }
            const overallSuccess = results.some(r => r.success);
            logger_1.logger.info('Notification sending completed', {
                userId: request.userId,
                notificationId,
                overallSuccess,
                results
            });
            return {
                success: overallSuccess,
                notificationId,
                channels: results
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to send notification', { error, request });
            throw error;
        }
    }
    /**
     * Send in-app notification
     */
    async sendInAppNotification(request) {
        try {
            const notificationId = (0, uuid_1.v4)();
            const notification = {
                id: notificationId,
                userId: request.userId,
                type: request.type,
                title: request.title,
                message: request.message,
                resourceType: request.resourceType,
                resourceId: request.resourceId,
                priority: request.priority || 'normal',
                status: 'unread',
                createdAt: new Date().toISOString(),
                metadata: request.metadata,
                tenantId: request.userId // Simplified tenant resolution
            };
            await database_1.db.createItem('notifications', notification);
            logger_1.logger.info('In-app notification sent successfully', {
                notificationId,
                userId: request.userId,
                type: request.type
            });
            return {
                success: true,
                notificationId
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to send in-app notification', { error, request });
            throw error;
        }
    }
    /**
     * Send email notification (simplified implementation)
     */
    async sendEmailNotification(request) {
        try {
            // This is a simplified implementation
            // In production, this would integrate with email service like SendGrid, Postmark, etc.
            logger_1.logger.info('Email notification would be sent', {
                to: request.to,
                subject: request.subject,
                priority: request.priority
            });
            // Simulate email sending
            const messageId = (0, uuid_1.v4)();
            return {
                success: true,
                messageId
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to send email notification', { error, request });
            throw error;
        }
    }
    /**
     * Send push notification (simplified implementation)
     */
    async sendPushNotification(request) {
        try {
            // This is a simplified implementation
            // In production, this would integrate with Azure Notification Hubs or similar service
            logger_1.logger.info('Push notification would be sent', {
                userId: request.userId,
                title: request.title,
                body: request.body
            });
            // Simulate push notification sending
            const messageId = (0, uuid_1.v4)();
            return {
                success: true,
                messageId
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to send push notification', { error, request });
            throw error;
        }
    }
    /**
     * Send webhook notification
     */
    async sendWebhookNotification(request, webhookConfig) {
        try {
            if (!webhookConfig?.url) {
                throw new Error('Webhook URL not configured');
            }
            const payload = {
                type: 'notification',
                data: {
                    userId: request.userId,
                    notificationType: request.type,
                    title: request.title,
                    message: request.message,
                    resourceId: request.resourceId,
                    resourceType: request.resourceType,
                    priority: request.priority,
                    metadata: request.metadata,
                    timestamp: new Date().toISOString()
                }
            };
            const response = await fetch(webhookConfig.url, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'User-Agent': 'DocuContext-Notifications/1.0',
                    ...(webhookConfig.headers || {})
                },
                body: JSON.stringify(payload)
            });
            logger_1.logger.info('Webhook notification sent', {
                url: webhookConfig.url,
                status: response.status,
                userId: request.userId
            });
            return {
                success: response.ok,
                responseStatus: response.status
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to send webhook notification', { error, request, webhookConfig });
            throw error;
        }
    }
    /**
     * Get user notification preferences
     */
    async getUserNotificationPreferences(userId) {
        try {
            const user = await database_1.db.readItem('users', userId, userId);
            if (!user) {
                throw new Error('User not found');
            }
            const preferences = user.preferences?.notifications || {};
            return {
                inApp: preferences.inApp !== false, // Default to true
                email: preferences.email !== false, // Default to true
                push: preferences.push || false, // Default to false
                channels: preferences.channels || []
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user notification preferences', { error, userId });
            throw error;
        }
    }
    /**
     * Mark notification as read
     */
    async markNotificationAsRead(notificationId, userId) {
        try {
            const notification = await database_1.db.readItem('notifications', notificationId, notificationId);
            if (!notification) {
                throw new Error('Notification not found');
            }
            if (notification.userId !== userId) {
                throw new Error('Access denied');
            }
            const updatedNotification = {
                ...notification,
                status: 'read',
                readAt: new Date().toISOString()
            };
            await database_1.db.updateItem('notifications', updatedNotification);
            logger_1.logger.info('Notification marked as read', { notificationId, userId });
            return true;
        }
        catch (error) {
            logger_1.logger.error('Failed to mark notification as read', { error, notificationId, userId });
            throw error;
        }
    }
    /**
     * Get user notifications with pagination
     */
    async getUserNotifications(userId, options = {}) {
        try {
            const page = options.page || 1;
            const limit = Math.min(options.limit || 20, 100);
            const offset = (page - 1) * limit;
            let query = 'SELECT * FROM c WHERE c.userId = @userId';
            const parameters = [{ name: '@userId', value: userId }];
            if (options.status && options.status !== 'all') {
                query += ' AND c.status = @status';
                parameters.push({ name: '@status', value: options.status });
            }
            if (options.type) {
                query += ' AND c.type = @type';
                parameters.push({ name: '@type', value: options.type });
            }
            query += ' ORDER BY c.createdAt DESC';
            const notifications = await database_1.db.queryItems('notifications', query, parameters);
            // Get unread count
            const unreadQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.userId = @userId AND c.status = @status';
            const unreadResult = await database_1.db.queryItems('notifications', unreadQuery, [userId, 'unread']);
            const unreadCount = Number(unreadResult[0]) || 0;
            const total = notifications.length;
            const paginatedNotifications = notifications.slice(offset, offset + limit);
            return {
                notifications: paginatedNotifications,
                total,
                unreadCount,
                page,
                limit,
                hasMore: offset + limit < total
            };
        }
        catch (error) {
            logger_1.logger.error('Failed to get user notifications', { error, userId, options });
            throw error;
        }
    }
}
exports.NotificationService = NotificationService;
// Export singleton instance
exports.notificationService = new NotificationService();
//# sourceMappingURL=notification.js.map