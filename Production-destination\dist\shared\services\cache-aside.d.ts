/**
 * Generic Cache-Aside Service
 * Provides a reusable pattern for cache-aside operations with database fallback
 * Ensures data consistency and graceful degradation when Redis is unavailable
 */
import { EventEmitter } from 'events';
export interface CacheAsideOptions {
    ttlSeconds?: number;
    enableFallback?: boolean;
    cachePrefix?: string;
    invalidatePatterns?: string[];
    enableWarming?: boolean;
    warmingPriority?: 'high' | 'medium' | 'low';
    eventDriven?: boolean;
}
export interface DatabaseQuery {
    containerName: string;
    query?: string;
    parameters?: any[];
    itemId?: string;
    partitionKey?: string;
}
export interface CacheEvent {
    type: 'invalidate' | 'warm' | 'update' | 'delete';
    key: string;
    pattern?: string;
    data?: any;
    priority?: 'high' | 'medium' | 'low';
    timestamp: Date;
    source: string;
}
export interface WarmingRule {
    pattern: string;
    dbQuery: DatabaseQuery;
    options: CacheAsideOptions;
    frequency: number;
    lastWarmed?: Date;
    priority: 'high' | 'medium' | 'low';
}
export declare class CacheAsideService extends EventEmitter {
    private static instance;
    private warmingRules;
    private warmingQueue;
    private isWarmingActive;
    private warmingInterval;
    private eventQueue;
    private isProcessingEvents;
    private constructor();
    static getInstance(): CacheAsideService;
    /**
     * Setup event-driven cache processing
     */
    private setupEventProcessing;
    /**
     * Setup cache warming system
     */
    private setupCacheWarming;
    /**
     * Add cache warming rule
     */
    addWarmingRule(ruleId: string, rule: WarmingRule): void;
    /**
     * Remove cache warming rule
     */
    removeWarmingRule(ruleId: string): void;
    /**
     * Emit cache event for event-driven invalidation
     */
    emitCacheEvent(event: CacheEvent): void;
    /**
     * Add event to warming queue
     */
    private addToWarmingQueue;
    /**
     * Generic get operation with cache-aside pattern
     */
    get<T extends Record<string, any>>(cacheKey: string, dbQuery: DatabaseQuery, options?: CacheAsideOptions): Promise<T | null>;
    /**
     * Generic set operation with cache invalidation
     */
    set<T>(cacheKey: string, data: T, options?: CacheAsideOptions): Promise<boolean>;
    /**
     * Get list of items with cache-aside pattern
     */
    getList<T extends Record<string, any>>(cacheKey: string, dbQuery: DatabaseQuery, options?: CacheAsideOptions): Promise<T[]>;
    /**
     * Delete from cache and invalidate related patterns
     */
    delete(cacheKey: string, options?: CacheAsideOptions): Promise<boolean>;
    /**
     * Query database for single item
     */
    private queryDatabase;
    /**
     * Query database for list of items
     */
    private queryDatabaseList;
    /**
     * Invalidate cache pattern using production-safe approach
     */
    private invalidatePattern;
    /**
     * Extract cache tags from pattern for efficient invalidation
     */
    private extractTagsFromPattern;
    /**
     * Process event queue for event-driven cache operations
     */
    private processEventQueue;
    /**
     * Process individual cache event
     */
    private processEvent;
    /**
     * Process cache warming rules
     */
    private processWarmingRules;
    /**
     * Execute cache warming rule
     */
    private executeWarmingRule;
    /**
     * Warm specific cache entry
     */
    private warmCache;
    /**
     * Get cache warming statistics
     */
    getWarmingStats(): {
        rulesCount: number;
        queueSize: number;
        isActive: boolean;
    };
    /**
     * Get event processing statistics
     */
    getEventStats(): {
        queueSize: number;
        isProcessing: boolean;
    };
    /**
     * Cleanup resources
     */
    cleanup(): void;
}
export declare const cacheAside: CacheAsideService;
export default cacheAside;
