/**
 * AI Batch Processing Function
 * Handles batch document processing with AI models
 * Migrated from old-arch/src/ai-service/batch-processing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create batch processing job handler
 */
export declare function createBatchJob(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get batch job status handler
 */
export declare function getBatchJobStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
