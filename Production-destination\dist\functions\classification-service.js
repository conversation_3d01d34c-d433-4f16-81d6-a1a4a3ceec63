"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.classifyDocument = classifyDocument;
exports.createClassificationCategory = createClassificationCategory;
/**
 * Classification Service Function
 * Handles document classification and categorization
 * Migrated from old-arch/src/classification-service/classify/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Classification types and enums
var ClassificationType;
(function (ClassificationType) {
    ClassificationType["DOCUMENT_TYPE"] = "DOCUMENT_TYPE";
    ClassificationType["CONTENT_CATEGORY"] = "CONTENT_CATEGORY";
    ClassificationType["SENSITIVITY_LEVEL"] = "SENSITIVITY_LEVEL";
    ClassificationType["BUSINESS_UNIT"] = "BUSINESS_UNIT";
    ClassificationType["COMPLIANCE_TAG"] = "COMPLIANCE_TAG";
})(ClassificationType || (ClassificationType = {}));
var ConfidenceLevel;
(function (ConfidenceLevel) {
    ConfidenceLevel["LOW"] = "LOW";
    ConfidenceLevel["MEDIUM"] = "MEDIUM";
    ConfidenceLevel["HIGH"] = "HIGH";
    ConfidenceLevel["VERY_HIGH"] = "VERY_HIGH";
})(ConfidenceLevel || (ConfidenceLevel = {}));
// Validation schemas
const classifyDocumentSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    content: Joi.string().max(100000).optional(),
    metadata: Joi.object({
        fileName: Joi.string().optional(),
        contentType: Joi.string().optional(),
        size: Joi.number().optional(),
        author: Joi.string().optional()
    }).optional(),
    classificationTypes: Joi.array().items(Joi.string().valid(...Object.values(ClassificationType))).min(1).required(),
    options: Joi.object({
        useAI: Joi.boolean().default(true),
        useRules: Joi.boolean().default(true),
        minConfidence: Joi.number().min(0).max(1).default(0.7),
        autoApply: Joi.boolean().default(false)
    }).optional()
});
const createCategorySchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(ClassificationType)).required(),
    organizationId: Joi.string().uuid().required(),
    parentCategoryId: Joi.string().uuid().optional(),
    rules: Joi.array().items(Joi.object({
        field: Joi.string().required(),
        operator: Joi.string().valid('contains', 'equals', 'starts_with', 'ends_with', 'regex').required(),
        value: Joi.string().required(),
        weight: Joi.number().min(0).max(1).default(1)
    })).optional(),
    keywords: Joi.array().items(Joi.string().max(50)).max(100).optional(),
    color: Joi.string().pattern(/^#[0-9A-F]{6}$/i).optional(),
    icon: Joi.string().max(50).optional()
});
/**
 * Classify document handler
 */
async function classifyDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Classify document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = classifyDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const classifyRequest = value;
        // Get document
        const document = await database_1.db.readItem('documents', classifyRequest.documentId, classifyRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check document access
        const hasAccess = await checkDocumentAccess(documentData, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to document" }
            }, request);
        }
        // Perform classification
        const classificationResults = await performClassification(documentData, classifyRequest.content, classifyRequest.classificationTypes, classifyRequest.options || {}, user);
        // Apply classifications if auto-apply is enabled
        if (classifyRequest.options?.autoApply) {
            await applyClassifications(documentData, classificationResults, user);
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_classified",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId: classifyRequest.documentId,
            timestamp: new Date().toISOString(),
            details: {
                documentName: documentData.name,
                classificationTypes: classifyRequest.classificationTypes,
                resultCount: classificationResults.length,
                autoApplied: classifyRequest.options?.autoApply || false,
                averageConfidence: classificationResults.reduce((sum, r) => sum + r.confidence, 0) / classificationResults.length
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentClassified',
            aggregateId: classifyRequest.documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentData,
                classificationResults,
                classifiedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document classified successfully", {
            correlationId,
            documentId: classifyRequest.documentId,
            documentName: documentData.name,
            classificationTypes: classifyRequest.classificationTypes,
            resultCount: classificationResults.length,
            classifiedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId: classifyRequest.documentId,
                documentName: documentData.name,
                classificationResults,
                summary: {
                    totalClassifications: classificationResults.length,
                    averageConfidence: classificationResults.reduce((sum, r) => sum + r.confidence, 0) / classificationResults.length,
                    highConfidenceResults: classificationResults.filter(r => r.confidenceLevel === ConfidenceLevel.HIGH || r.confidenceLevel === ConfidenceLevel.VERY_HIGH).length
                },
                appliedAutomatically: classifyRequest.options?.autoApply || false,
                classifiedAt: new Date().toISOString(),
                message: "Document classified successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Classify document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Create classification category handler
 */
async function createClassificationCategory(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create classification category started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createCategorySchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const categoryRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(categoryRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check if category name already exists
        const existingCategory = await checkCategoryExists(categoryRequest.name, categoryRequest.type, categoryRequest.organizationId);
        if (existingCategory) {
            return (0, cors_1.addCorsHeaders)({
                status: 409,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Category with this name already exists" }
            }, request);
        }
        // Create classification category
        const categoryId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const category = {
            id: categoryId,
            name: categoryRequest.name,
            description: categoryRequest.description,
            type: categoryRequest.type,
            organizationId: categoryRequest.organizationId,
            parentCategoryId: categoryRequest.parentCategoryId,
            rules: categoryRequest.rules || [],
            keywords: categoryRequest.keywords || [],
            color: categoryRequest.color,
            icon: categoryRequest.icon,
            statistics: {
                documentsClassified: 0,
                averageConfidence: 0
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('classification-categories', category);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "classification_category_created",
            userId: user.id,
            organizationId: categoryRequest.organizationId,
            timestamp: now,
            details: {
                categoryId,
                categoryName: categoryRequest.name,
                categoryType: categoryRequest.type,
                ruleCount: categoryRequest.rules?.length || 0,
                keywordCount: categoryRequest.keywords?.length || 0
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ClassificationCategoryCreated',
            aggregateId: categoryId,
            aggregateType: 'ClassificationCategory',
            version: 1,
            data: {
                category,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: categoryRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Classification category created successfully", {
            correlationId,
            categoryId,
            categoryName: categoryRequest.name,
            categoryType: categoryRequest.type,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: categoryId,
                name: categoryRequest.name,
                type: categoryRequest.type,
                organizationId: categoryRequest.organizationId,
                ruleCount: categoryRequest.rules?.length || 0,
                keywordCount: categoryRequest.keywords?.length || 0,
                createdAt: now,
                message: "Classification category created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create classification category failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkDocumentAccess(document, userId) {
    try {
        // Check if user is the owner
        if (document.createdBy === userId) {
            return true;
        }
        // Check organization membership
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [document.organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check document access', { error, documentId: document.id, userId });
        return false;
    }
}
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkCategoryExists(name, type, organizationId) {
    try {
        const existingQuery = 'SELECT * FROM c WHERE c.name = @name AND c.type = @type AND c.organizationId = @orgId';
        const existing = await database_1.db.queryItems('classification-categories', existingQuery, [name, type, organizationId]);
        return existing.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check category exists', { error, name, type, organizationId });
        return false;
    }
}
async function performClassification(document, content, classificationTypes, options, user) {
    try {
        const results = [];
        // Get classification categories for the organization
        const categoriesQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.type IN (@types)';
        const categories = await database_1.db.queryItems('classification-categories', categoriesQuery, [document.organizationId, classificationTypes]);
        // Extract text content for analysis
        const textContent = content || document.extractedText || document.name || '';
        for (const type of classificationTypes) {
            const typeCategories = categories.filter((cat) => cat.type === type);
            const classification = await classifyByType(textContent, document, typeCategories, options);
            if (classification) {
                results.push(classification);
            }
        }
        return results;
    }
    catch (error) {
        logger_1.logger.error('Failed to perform classification', { error, documentId: document.id });
        return [];
    }
}
async function classifyByType(content, document, categories, options) {
    try {
        let bestMatch = null;
        let bestScore = 0;
        for (const category of categories) {
            let score = 0;
            const reasoning = [];
            // Rule-based classification
            if (options.useRules && category.rules.length > 0) {
                for (const rule of category.rules) {
                    const ruleScore = evaluateRule(rule, content, document);
                    score += ruleScore * rule.weight;
                    if (ruleScore > 0) {
                        reasoning.push(`Matched rule: ${rule.field} ${rule.operator} "${rule.value}"`);
                    }
                }
            }
            // Keyword-based classification
            if (category.keywords.length > 0) {
                const keywordScore = evaluateKeywords(category.keywords, content);
                score += keywordScore * 0.5; // Weight keywords lower than rules
                if (keywordScore > 0) {
                    reasoning.push(`Matched keywords: ${category.keywords.filter((k) => content.toLowerCase().includes(k.toLowerCase())).join(', ')}`);
                }
            }
            // AI-based classification (simplified)
            if (options.useAI) {
                const aiScore = await performAIClassification(content, category);
                score += aiScore * 0.7;
                if (aiScore > 0.5) {
                    reasoning.push('AI model prediction');
                }
            }
            // Normalize score
            score = Math.min(score, 1);
            if (score > bestScore && score >= options.minConfidence) {
                bestScore = score;
                bestMatch = { category, score, reasoning };
            }
        }
        if (bestMatch) {
            return {
                type: bestMatch.category.type,
                category: bestMatch.category.name,
                confidence: bestMatch.score,
                confidenceLevel: getConfidenceLevel(bestMatch.score),
                reasoning: bestMatch.reasoning,
                suggestedActions: generateSuggestedActions(bestMatch.category, bestMatch.score)
            };
        }
        return null;
    }
    catch (error) {
        logger_1.logger.error('Failed to classify by type', { error });
        return null;
    }
}
function evaluateRule(rule, content, document) {
    try {
        let fieldValue = '';
        switch (rule.field) {
            case 'content':
                fieldValue = content.toLowerCase();
                break;
            case 'fileName':
                fieldValue = document.name?.toLowerCase() || '';
                break;
            case 'contentType':
                fieldValue = document.contentType?.toLowerCase() || '';
                break;
            default:
                fieldValue = document[rule.field]?.toString().toLowerCase() || '';
        }
        const ruleValue = rule.value.toLowerCase();
        switch (rule.operator) {
            case 'contains':
                return fieldValue.includes(ruleValue) ? 1 : 0;
            case 'equals':
                return fieldValue === ruleValue ? 1 : 0;
            case 'starts_with':
                return fieldValue.startsWith(ruleValue) ? 1 : 0;
            case 'ends_with':
                return fieldValue.endsWith(ruleValue) ? 1 : 0;
            case 'regex':
                try {
                    const regex = new RegExp(ruleValue, 'i');
                    return regex.test(fieldValue) ? 1 : 0;
                }
                catch {
                    return 0;
                }
            default:
                return 0;
        }
    }
    catch (error) {
        return 0;
    }
}
function evaluateKeywords(keywords, content) {
    try {
        const contentLower = content.toLowerCase();
        const matchedKeywords = keywords.filter(keyword => contentLower.includes(keyword.toLowerCase()));
        return matchedKeywords.length / keywords.length;
    }
    catch (error) {
        return 0;
    }
}
async function performAIClassification(content, category) {
    try {
        // Simplified AI classification - in production, use actual ML models
        const contentWords = content.toLowerCase().split(/\s+/);
        const categoryWords = (category.name + ' ' + (category.description || '')).toLowerCase().split(/\s+/);
        const commonWords = contentWords.filter(word => categoryWords.includes(word));
        const similarity = commonWords.length / Math.max(contentWords.length, categoryWords.length);
        return Math.min(similarity * 2, 1); // Boost similarity score
    }
    catch (error) {
        return 0;
    }
}
function getConfidenceLevel(confidence) {
    if (confidence >= 0.9)
        return ConfidenceLevel.VERY_HIGH;
    if (confidence >= 0.75)
        return ConfidenceLevel.HIGH;
    if (confidence >= 0.5)
        return ConfidenceLevel.MEDIUM;
    return ConfidenceLevel.LOW;
}
function generateSuggestedActions(category, confidence) {
    const actions = [];
    if (confidence >= 0.8) {
        actions.push('Apply classification automatically');
    }
    else if (confidence >= 0.6) {
        actions.push('Review and confirm classification');
    }
    else {
        actions.push('Manual review recommended');
    }
    if (category.type === ClassificationType.SENSITIVITY_LEVEL) {
        actions.push('Review access permissions');
        actions.push('Apply data protection policies');
    }
    if (category.type === ClassificationType.COMPLIANCE_TAG) {
        actions.push('Ensure compliance requirements are met');
        actions.push('Set retention policies');
    }
    return actions;
}
async function applyClassifications(document, classifications, user) {
    try {
        const appliedClassifications = classifications.filter(c => c.confidenceLevel === ConfidenceLevel.HIGH || c.confidenceLevel === ConfidenceLevel.VERY_HIGH);
        if (appliedClassifications.length > 0) {
            const updatedDocument = {
                ...document,
                id: document.id,
                classifications: appliedClassifications.map(c => ({
                    type: c.type,
                    category: c.category,
                    confidence: c.confidence,
                    appliedAt: new Date().toISOString(),
                    appliedBy: user.id
                })),
                updatedAt: new Date().toISOString()
            };
            await database_1.db.updateItem('documents', updatedDocument);
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to apply classifications', { error, documentId: document.id });
    }
}
// Register functions
functions_1.app.http('document-classify', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/classify',
    handler: classifyDocument
});
functions_1.app.http('classification-category-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'classification/categories',
    handler: createClassificationCategory
});
//# sourceMappingURL=classification-service.js.map