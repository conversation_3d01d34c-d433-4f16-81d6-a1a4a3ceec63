/**
 * Audit Logging Function
 * Handles comprehensive audit logging and compliance tracking
 * Enhanced from existing audit-log.ts with additional features
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create audit log entry handler
 */
export declare function createAuditLog(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get audit logs handler
 */
export declare function getAuditLogs(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
