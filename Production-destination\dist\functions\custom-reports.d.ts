/**
 * Custom Reports Function
 * Handles custom report generation and management
 * Migrated from old-arch/src/analytics-service/reports/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create custom report handler
 */
export declare function createCustomReport(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get report status handler
 */
export declare function getReportStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
