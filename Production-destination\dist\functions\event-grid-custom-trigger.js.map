{"version": 3, "file": "event-grid-custom-trigger.js", "sourceRoot": "", "sources": ["../../src/functions/event-grid-custom-trigger.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAEH,gDAA0E;AAC1E,mDAAgD;AAChD,0DAAiD;AACjD,sFAAiF;AAEjF,8DAA8D;AAC9D,IAAK,SAWJ;AAXD,WAAK,SAAS;IACZ,oDAAuC,CAAA;IACvC,sDAAyC,CAAA;IACzC,gDAAmC,CAAA;IACnC,kDAAqC,CAAA;IACrC,sDAAyC,CAAA;IACzC,gDAAmC,CAAA;IACnC,oDAAuC,CAAA;IACvC,wDAA2C,CAAA;IAC3C,uDAA0C,CAAA;IAC1C,oDAAuC,CAAA;AACzC,CAAC,EAXI,SAAS,KAAT,SAAS,QAWb;AAED;;GAEG;AACH,KAAK,UAAU,YAAY,CAAC,SAAoB,EAAE,OAAe,EAAE,IAAS;IAC1E,IAAI,CAAC;QACH,MAAM,6CAAoB,CAAC,YAAY,CAAC;YACtC,SAAS;YACT,OAAO;YACP,IAAI;SACL,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,6CAA6C,EAAE;YAC1D,SAAS;YACT,OAAO;YACP,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CAAC,KAAqB,EAAE,OAA0B;IACrF,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;QACjD,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,OAAO,EAAE,KAAK,CAAC,EAAE;QACjB,YAAY,EAAE,OAAO,CAAC,YAAY;KACnC,CAAC,CAAC;IAEH,IAAI,CAAC;QACH,MAAM,kBAAkB,CAAC,KAAK,CAAC,CAAC;QAChC,eAAM,CAAC,IAAI,CAAC,qCAAqC,EAAE;YACjD,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS;SAC3B,CAAC,CAAC;IACL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,+BAA+B,EAAE;YAC5C,OAAO,EAAE,KAAK,CAAC,EAAE;YACjB,SAAS,EAAE,KAAK,CAAC,SAAS;YAC1B,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC,CAAC,sCAAsC;IACrD,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,kBAAkB,CAAC,KAAqB;IACrD,eAAM,CAAC,IAAI,CAAC,yBAAyB,EAAE;QACrC,SAAS,EAAE,KAAK,CAAC,SAAS;QAC1B,OAAO,EAAE,KAAK,CAAC,OAAO;QACtB,OAAO,EAAE,KAAK,CAAC,EAAE;KAClB,CAAC,CAAC;IAEH,uBAAuB;IACvB,MAAM,SAAS,GAAI,KAAK,CAAC,IAAY,IAAI,EAAE,CAAC;IAE5C,QAAQ,KAAK,CAAC,SAAS,EAAE,CAAC;QACxB,KAAK,SAAS,CAAC,iBAAiB;YAC9B,MAAM,mBAAmB,CAAC,SAAS,EAAE,UAAU,CAAC,CAAC;YACjD,MAAM;QACR,KAAK,SAAS,CAAC,kBAAkB;YAC/B,MAAM,mBAAmB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM;QACR,KAAK,SAAS,CAAC,eAAe;YAC5B,MAAM,mBAAmB,CAAC,SAAS,EAAE,QAAQ,CAAC,CAAC;YAC/C,MAAM;QACR,KAAK,SAAS,CAAC,gBAAgB;YAC7B,MAAM,mBAAmB,CAAC,SAAS,EAAE,SAAS,CAAC,CAAC;YAChD,MAAM;QACR,KAAK,SAAS,CAAC,kBAAkB;YAC/B,MAAM,mBAAmB,CAAC,SAAS,EAAE,WAAW,CAAC,CAAC;YAClD,MAAM;QACR,KAAK,SAAS,CAAC,eAAe;YAC5B,MAAM,eAAe,CAAC,SAAS,EAAE,YAAY,CAAC,CAAC;YAC/C,MAAM;QACR,KAAK,SAAS,CAAC,mBAAmB;YAChC,MAAM,iBAAiB,CAAC,SAAS,EAAE,cAAc,CAAC,CAAC;YACnD,MAAM;QACR,KAAK,SAAS,CAAC,iBAAiB;YAC9B,MAAM,iBAAiB,CAAC,SAAS,EAAE,mBAAmB,CAAC,CAAC;YACxD,MAAM;QACR,KAAK,SAAS,CAAC,mBAAmB;YAChC,MAAM,oBAAoB,CAAC,SAAS,CAAC,CAAC;YACtC,MAAM;QACR,KAAK,SAAS,CAAC,iBAAiB;YAC9B,MAAM,uBAAuB,CAAC,SAAS,CAAC,CAAC;YACzC,MAAM;QACR;YACE,eAAM,CAAC,IAAI,CAAC,6BAA6B,EAAE,EAAE,SAAS,EAAE,KAAK,CAAC,SAAS,EAAE,CAAC,CAAC;IAC/E,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAC,IAAS,EAAE,MAAc;IAC1D,MAAM,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,UAAU,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAExE,eAAM,CAAC,IAAI,CAAC,YAAY,MAAM,iBAAiB,EAAE,EAAE,UAAU,EAAE,QAAQ,EAAE,UAAU,EAAE,CAAC,CAAC;IAEvF,IAAI,CAAC;QACH,mDAAmD;QACnD,IAAI,UAAU,EAAE,CAAC;YACf,IAAI,CAAC;gBACH,MAAM,SAAS,GAAG,MAAM,aAAE,CAAC,UAAU,CAAM,WAAW,EACpD,0CAA0C,EAC1C,CAAC,EAAE,IAAI,EAAE,aAAa,EAAE,KAAK,EAAE,UAAU,EAAE,CAAC,CAC7C,CAAC;gBAEF,IAAI,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;oBACzB,MAAM,QAAQ,GAAG,SAAS,CAAC,CAAC,CAAC,CAAC;oBAC9B,MAAM,UAAU,GAAQ;wBACtB,GAAG,QAAQ;wBACX,CAAC,GAAG,MAAM,IAAI,CAAC,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;qBAC1C,CAAC;oBAEF,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;wBAC1B,UAAU,CAAC,MAAM,GAAG,YAAY,CAAC;wBACjC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBACjC,CAAC;yBAAM,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;wBAClC,UAAU,CAAC,MAAM,GAAG,WAAW,CAAC;oBAClC,CAAC;yBAAM,IAAI,MAAM,KAAK,QAAQ,EAAE,CAAC;wBAC/B,UAAU,CAAC,UAAU,GAAG,UAAU,CAAC;wBACnC,UAAU,CAAC,QAAQ,GAAG,QAAQ,CAAC;oBACjC,CAAC;oBAED,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,UAAU,CAAC,CAAC;gBAC/C,CAAC;YACH,CAAC;YAAC,OAAO,OAAO,EAAE,CAAC;gBACjB,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;oBACnD,UAAU;oBACV,MAAM;oBACN,KAAK,EAAE,OAAO,YAAY,KAAK,CAAC,CAAC,CAAC,OAAO,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,OAAO,CAAC;iBACpE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;QAED,2BAA2B;QAC3B,IAAI,MAAM,KAAK,UAAU,EAAE,CAAC;YAC1B,MAAM,YAAY,CAChB,SAAS,CAAC,mBAAmB,EAC7B,aAAa,UAAU,mBAAmB,EAC1C;gBACE,UAAU;gBACV,QAAQ;gBACR,UAAU;gBACV,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,gBAAgB;aAC9B,CACF,CAAC;QACJ,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE;YACtD,UAAU;YACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,mBAAmB,CAAC,IAAS,EAAE,MAAc;IAC1D,MAAM,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,YAAY,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAE1E,eAAM,CAAC,IAAI,CAAC,YAAY,MAAM,iBAAiB,EAAE,EAAE,UAAU,EAAE,SAAS,EAAE,WAAW,EAAE,CAAC,CAAC;IAEzF,IAAI,CAAC;QACH,2BAA2B;QAC3B,MAAM,YAAY,CAChB,SAAS,CAAC,mBAAmB,EAC7B,aAAa,UAAU,IAAI,MAAM,YAAY,EAC7C;YACE,UAAU;YACV,MAAM;YACN,SAAS;YACT,WAAW;YACX,YAAY;YACZ,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,WAAW,EAAE,gBAAgB;SAC9B,CACF,CAAC;QAEF,6CAA6C;QAC7C,IAAI,MAAM,KAAK,WAAW,EAAE,CAAC;YAC3B,MAAM,YAAY,CAChB,SAAS,CAAC,iBAAiB,EAC3B,aAAa,UAAU,aAAa,EACpC;gBACE,UAAU;gBACV,MAAM;gBACN,WAAW;gBACX,gBAAgB,EAAE,oBAAoB;gBACtC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,gBAAgB;aAC9B,CACF,CAAC;QACJ,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,2BAA2B,MAAM,QAAQ,EAAE;YACtD,UAAU;YACV,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,eAAe,CAAC,IAAS,EAAE,MAAc;IACtD,MAAM,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,GAAG,IAAI,CAAC;IAEnD,eAAM,CAAC,IAAI,CAAC,QAAQ,MAAM,iBAAiB,EAAE,EAAE,MAAM,EAAE,KAAK,EAAE,kBAAkB,EAAE,CAAC,CAAC;IAEpF,IAAI,CAAC;QACH,IAAI,MAAM,KAAK,YAAY,EAAE,CAAC;YAC5B,4BAA4B;YAC5B,MAAM,YAAY,CAChB,SAAS,CAAC,iBAAiB,EAC3B,SAAS,MAAM,UAAU,EACzB;gBACE,MAAM;gBACN,KAAK;gBACL,gBAAgB,EAAE,SAAS;gBAC3B,kBAAkB;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,gBAAgB;aAC9B,CACF,CAAC;YAEF,uBAAuB;YACvB,MAAM,YAAY,CAChB,SAAS,CAAC,mBAAmB,EAC7B,SAAS,MAAM,yBAAyB,EACxC;gBACE,MAAM;gBACN,kBAAkB;gBAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,WAAW,EAAE,gBAAgB;aAC9B,CACF,CAAC;QACJ,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,uBAAuB,MAAM,QAAQ,EAAE;YAClD,MAAM;YACN,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,iBAAiB,CAAC,IAAS,EAAE,SAAiB;IAC3D,MAAM,EAAE,YAAY,EAAE,OAAO,EAAE,SAAS,EAAE,QAAQ,EAAE,GAAG,IAAI,CAAC;IAE5D,eAAM,CAAC,IAAI,CAAC,UAAU,SAAS,iBAAiB,EAAE,EAAE,YAAY,EAAE,SAAS,EAAE,QAAQ,EAAE,CAAC,CAAC;IAEzF,IAAI,CAAC;QACH,IAAI,SAAS,KAAK,cAAc,EAAE,CAAC;YACjC,uBAAuB;YACvB,MAAM,aAAE,CAAC,UAAU,CAAC,eAAe,EAAE;gBACnC,EAAE,EAAE,UAAU,IAAI,CAAC,GAAG,EAAE,EAAE;gBAC1B,YAAY;gBACZ,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACpC,CAAC,CAAC;YAEH,8BAA8B;YAC9B,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;gBAC/B,MAAM,YAAY,CAChB,SAAS,CAAC,iBAAiB,EAC3B,qBAAqB,EACrB;oBACE,SAAS,EAAE,qBAAqB;oBAChC,QAAQ,EAAE,YAAY,KAAK,UAAU,CAAC,CAAC,CAAC,MAAM,CAAC,CAAC,CAAC,QAAQ;oBACzD,YAAY;oBACZ,OAAO;oBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;oBACnC,WAAW,EAAE,gBAAgB;iBAC9B,CACF,CAAC;YACJ,CAAC;QACH,CAAC;aAAM,IAAI,SAAS,KAAK,mBAAmB,EAAE,CAAC;YAC7C,0BAA0B;YAC1B,MAAM,aAAE,CAAC,UAAU,CAAC,oBAAoB,EAAE;gBACxC,EAAE,EAAE,SAAS,IAAI,CAAC,GAAG,EAAE,EAAE;gBACzB,SAAS;gBACT,QAAQ;gBACR,OAAO;gBACP,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACnC,MAAM,EAAE,QAAQ;aACjB,CAAC,CAAC;QACL,CAAC;IAEH,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,yBAAyB,SAAS,QAAQ,EAAE;YACvD,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,oBAAoB,CAAC,IAAS;IAC3C,MAAM,EAAE,aAAa,EAAE,YAAY,EAAE,GAAG,IAAI,CAAC;IAE7C,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE,EAAE,aAAa,EAAE,YAAY,EAAE,CAAC,CAAC;IAEnF,IAAI,CAAC;QACH,uBAAuB;QACvB,MAAM,aAAE,CAAC,UAAU,CAAC,gBAAgB,EAAE;YACpC,EAAE,EAAE,aAAa,IAAI,CAAC,GAAG,EAAE,EAAE;YAC7B,aAAa;YACb,IAAI,EAAE,IAAI,CAAC,IAAI,IAAI,EAAE;YACrB,YAAY;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,0CAA0C,EAAE;YACvD,aAAa;YACb,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CAAC,IAAS;IAC9C,MAAM,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,MAAM,EAAE,GAAG,IAAI,CAAC;IAErE,eAAM,CAAC,IAAI,CAAC,kCAAkC,EAAE,EAAE,cAAc,EAAE,gBAAgB,EAAE,SAAS,EAAE,CAAC,CAAC;IAEjG,IAAI,CAAC;QACH,+BAA+B;QAC/B,MAAM,aAAE,CAAC,UAAU,CAAC,wBAAwB,EAAE;YAC5C,EAAE,EAAE,gBAAgB,IAAI,CAAC,GAAG,EAAE,EAAE;YAChC,cAAc;YACd,gBAAgB;YAChB,SAAS;YACT,MAAM;YACN,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;SACpC,CAAC,CAAC;IAEL,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;YACrD,cAAc;YACd,KAAK,EAAE,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;SAC9D,CAAC,CAAC;QACH,MAAM,KAAK,CAAC;IACd,CAAC;AACH,CAAC;AAED,kCAAkC;AAClC,eAAG,CAAC,SAAS,CAAC,wBAAwB,EAAE;IACtC,OAAO,EAAE,sBAAsB;CAChC,CAAC,CAAC"}