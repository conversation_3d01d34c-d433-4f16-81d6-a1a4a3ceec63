/**
 * Queue Storage Handlers for Azure Functions
 * Handles queue-based message processing for async operations
 */
import { QueueServiceClient, QueueClient } from '@azure/storage-queue';
/**
 * Initialize queue service client
 */
declare function getQueueServiceClient(): QueueServiceClient;
/**
 * Get queue client for specific queue
 */
declare function getQueueClient(queueName: string): QueueClient;
export { getQueueClient, getQueueServiceClient };
