/**
 * Workflow Automation Function
 * Handles advanced workflow automation, triggers, and conditions
 * Migrated from old-arch/src/workflow-service/automation/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create automation handler
 */
export declare function createAutomation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Execute automation handler
 */
export declare function executeAutomation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
