/**
 * Subscription Management Function
 * Handles subscription plans, billing, and tier management
 * Migrated from old-arch/src/subscription-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create subscription handler
 */
export declare function createSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get subscription handler
 */
export declare function getSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
