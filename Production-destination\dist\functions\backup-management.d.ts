/**
 * Backup Management Function
 * Handles system backup operations and data recovery
 * Migrated from old-arch/src/admin-service/backup/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create backup handler
 */
export declare function createBackup(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get backup status handler
 */
export declare function getBackupStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
