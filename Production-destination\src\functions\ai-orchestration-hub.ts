/**
 * AI Orchestration Hub Function
 * Central hub for coordinating AI operations across different services
 * Migrated from old-arch/src/ai-service/orchestration-hub/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext, app } from '@azure/functions';
import { v4 as uuidv4 } from "uuid";
import * as Jo<PERSON> from 'joi';
import { logger } from '../shared/utils/logger';
import { addCorsHeaders, handlePreflight } from '../shared/middleware/cors';
import { authenticateRequest } from '../shared/utils/auth';
import { db } from '../shared/services/database';
import { notificationService } from '../shared/services/notification';
import { eventService } from '../shared/services/event';

// AI operation types
enum AIOperationType {
  DOCUMENT_ANALYSIS = 'DOCUMENT_ANALYSIS',
  CONTENT_GENERATION = 'CONTENT_GENERATION',
  CONTENT_COMPLETION = 'CONTENT_COMPLETION',
  DOCUMENT_SUMMARIZATION = 'DOCUMENT_SUMMARIZATION',
  INTELLIGENT_SEARCH = 'INTELLIGENT_SEARCH',
  WORKFLOW_OPTIMIZATION = 'WORKFLOW_OPTIMIZATION',
  BATCH_PROCESSING = 'BATCH_PROCESSING'
}

enum AIOperationStatus {
  PENDING = 'PENDING',
  RUNNING = 'RUNNING',
  COMPLETED = 'COMPLETED',
  FAILED = 'FAILED',
  CANCELLED = 'CANCELLED'
}

enum AIOperationPriority {
  LOW = 'LOW',
  NORMAL = 'NORMAL',
  HIGH = 'HIGH',
  URGENT = 'URGENT'
}

// Validation schema
const aiOperationSchema = Joi.object({
  operationType: Joi.string().valid(...Object.values(AIOperationType)).required(),
  priority: Joi.string().valid(...Object.values(AIOperationPriority)).default(AIOperationPriority.NORMAL),
  parameters: Joi.object({
    documentIds: Joi.array().items(Joi.string().uuid()).optional(),
    workflowId: Joi.string().uuid().optional(),
    prompt: Joi.string().max(2000).optional(),
    options: Joi.object().optional(),
    targetFormat: Joi.string().optional(),
    customInstructions: Joi.string().max(1000).optional()
  }).required(),
  organizationId: Joi.string().uuid().required(),
  projectId: Joi.string().uuid().optional(),
  callbackUrl: Joi.string().uri().optional(),
  metadata: Joi.object().optional()
});

interface AIOperationRequest {
  operationType: AIOperationType;
  priority: AIOperationPriority;
  parameters: {
    documentIds?: string[];
    workflowId?: string;
    prompt?: string;
    options?: any;
    targetFormat?: string;
    customInstructions?: string;
  };
  organizationId: string;
  projectId?: string;
  callbackUrl?: string;
  metadata?: any;
}

interface AIOperationResponse {
  operationId: string;
  operationType: AIOperationType;
  status: AIOperationStatus;
  priority: AIOperationPriority;
  estimatedCompletionTime?: string;
  result?: any;
  progress?: {
    percentage: number;
    currentStep: string;
    totalSteps: number;
  };
  error?: string;
  createdAt: string;
  updatedAt: string;
}

/**
 * Create AI operation handler
 */
export async function createAIOperation(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  logger.info("Create AI operation started", { correlationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    // Validate request body
    const body = await request.json();
    const { error, value } = aiOperationSchema.validate(body);
    
    if (error) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: 'Validation Error', 
          message: error.details.map(d => d.message).join(', ')
        }
      }, request);
    }

    const operationRequest: AIOperationRequest = value;

    // Validate organization access
    const organization = await db.readItem('organizations', operationRequest.organizationId, operationRequest.organizationId);
    if (!organization) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Organization not found" }
      }, request);
    }

    // Check if user has access to the organization
    const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
    const memberships = await db.queryItems('organization-members', membershipQuery, [user.id, operationRequest.organizationId, 'ACTIVE']);
    
    if (memberships.length === 0) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied to organization" }
      }, request);
    }

    // Check AI operation limits for organization tier
    const orgData = organization as any;
    if (await isAIOperationLimitReached(operationRequest.organizationId, orgData.tier)) {
      return addCorsHeaders({
        status: 429,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { 
          error: "AI operation limit reached for this organization tier",
          tier: orgData.tier
        }
      }, request);
    }

    // Create AI operation
    const operationId = uuidv4();
    const now = new Date().toISOString();

    const aiOperation = {
      id: operationId,
      operationType: operationRequest.operationType,
      status: AIOperationStatus.PENDING,
      priority: operationRequest.priority,
      parameters: operationRequest.parameters,
      organizationId: operationRequest.organizationId,
      projectId: operationRequest.projectId,
      callbackUrl: operationRequest.callbackUrl,
      metadata: operationRequest.metadata || {},
      createdBy: user.id,
      createdAt: now,
      updatedAt: now,
      estimatedCompletionTime: calculateEstimatedCompletionTime(operationRequest),
      progress: {
        percentage: 0,
        currentStep: 'Initializing',
        totalSteps: getOperationSteps(operationRequest.operationType)
      },
      tenantId: user.tenantId
    };

    await db.createItem('ai-operations', aiOperation);

    // Queue the operation for processing
    await queueAIOperation(aiOperation);

    // Create activity record
    await db.createItem('activities', {
      id: uuidv4(),
      type: "ai_operation_created",
      userId: user.id,
      organizationId: operationRequest.organizationId,
      projectId: operationRequest.projectId,
      timestamp: now,
      details: {
        operationId,
        operationType: operationRequest.operationType,
        priority: operationRequest.priority,
        organizationName: orgData.name
      },
      tenantId: user.tenantId
    });

    // Publish domain event
    await eventService.publishEvent({
      type: 'AIOperationCreated',
      aggregateId: operationId,
      aggregateType: 'AIOperation',
      version: 1,
      data: {
        operation: aiOperation,
        createdBy: user.id
      },
      userId: user.id,
      organizationId: operationRequest.organizationId,
      tenantId: user.tenantId
    });

    // Send notification for high priority operations
    if (operationRequest.priority === AIOperationPriority.HIGH || operationRequest.priority === AIOperationPriority.URGENT) {
      await notificationService.sendNotification({
        userId: user.id,
        type: 'AI_OPERATION_CREATED',
        title: 'High Priority AI Operation Started',
        message: `Your ${operationRequest.operationType} operation has been queued with ${operationRequest.priority} priority.`,
        priority: 'high',
        metadata: {
          operationId,
          operationType: operationRequest.operationType,
          organizationId: operationRequest.organizationId
        },
        organizationId: operationRequest.organizationId,
        projectId: operationRequest.projectId
      });
    }

    const response: AIOperationResponse = {
      operationId,
      operationType: aiOperation.operationType,
      status: aiOperation.status,
      priority: aiOperation.priority,
      estimatedCompletionTime: aiOperation.estimatedCompletionTime,
      progress: aiOperation.progress,
      createdAt: aiOperation.createdAt,
      updatedAt: aiOperation.updatedAt
    };

    logger.info("AI operation created successfully", {
      correlationId,
      operationId,
      operationType: operationRequest.operationType,
      priority: operationRequest.priority,
      userId: user.id
    });

    return addCorsHeaders({
      status: 201,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Create AI operation failed", {
      correlationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Get AI operation status handler
 */
export async function getAIOperationStatus(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit> {
  // Handle preflight OPTIONS request
  const preflightResponse = handlePreflight(request);
  if (preflightResponse) {
    return preflightResponse;
  }

  const correlationId = context.invocationId;
  const operationId = request.params.operationId;
  
  logger.info("Get AI operation status started", { correlationId, operationId });

  try {
    // Authenticate user
    const authResult = await authenticateRequest(request);
    if (!authResult.success || !authResult.user) {
      return addCorsHeaders({
        status: 401,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: 'Unauthorized', message: authResult.error }
      }, request);
    }

    const user = authResult.user;

    if (!operationId) {
      return addCorsHeaders({
        status: 400,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Operation ID is required" }
      }, request);
    }

    // Get AI operation
    const aiOperation = await db.readItem('ai-operations', operationId, operationId);
    if (!aiOperation) {
      return addCorsHeaders({
        status: 404,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "AI operation not found" }
      }, request);
    }

    const operationData = aiOperation as any;

    // Check access permissions
    const hasAccess = (
      operationData.createdBy === user.id ||
      operationData.organizationId === user.tenantId ||
      user.roles?.includes('admin')
    );

    if (!hasAccess) {
      return addCorsHeaders({
        status: 403,
        headers: { 'Content-Type': 'application/json' },
        jsonBody: { error: "Access denied" }
      }, request);
    }

    const response: AIOperationResponse = {
      operationId: operationData.id,
      operationType: operationData.operationType,
      status: operationData.status,
      priority: operationData.priority,
      estimatedCompletionTime: operationData.estimatedCompletionTime,
      result: operationData.result,
      progress: operationData.progress,
      error: operationData.error,
      createdAt: operationData.createdAt,
      updatedAt: operationData.updatedAt
    };

    logger.info("AI operation status retrieved successfully", {
      correlationId,
      operationId,
      status: operationData.status,
      userId: user.id
    });

    return addCorsHeaders({
      status: 200,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: response
    }, request);

  } catch (error) {
    const errorMessage = error instanceof Error ? error.message : String(error);
    logger.error("Get AI operation status failed", {
      correlationId,
      operationId,
      error: errorMessage
    });

    return addCorsHeaders({
      status: 500,
      headers: { 'Content-Type': 'application/json' },
      jsonBody: { error: "Internal server error" }
    }, request);
  }
}

/**
 * Check if AI operation limit is reached for organization
 */
async function isAIOperationLimitReached(organizationId: string, tier: string): Promise<boolean> {
  try {
    // Get current month's operations count
    const startOfMonth = new Date();
    startOfMonth.setDate(1);
    startOfMonth.setHours(0, 0, 0, 0);

    const operationsQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.createdAt >= @startDate';
    const result = await db.queryItems('ai-operations', operationsQuery, [organizationId, startOfMonth.toISOString()]);
    const currentCount = Number(result[0]) || 0;

    // Define tier limits
    const limits: { [key: string]: number } = {
      'FREE': 10,
      'PROFESSIONAL': 100,
      'ENTERPRISE': -1 // Unlimited
    };

    const limit = limits[tier] || limits['FREE'];
    return limit > 0 && currentCount >= limit;

  } catch (error) {
    logger.error('Failed to check AI operation limit', { error, organizationId });
    return false;
  }
}

/**
 * Calculate estimated completion time
 */
function calculateEstimatedCompletionTime(operationRequest: AIOperationRequest): string {
  // Simplified estimation based on operation type
  const estimationMinutes: { [key: string]: number } = {
    [AIOperationType.DOCUMENT_ANALYSIS]: 5,
    [AIOperationType.CONTENT_GENERATION]: 10,
    [AIOperationType.CONTENT_COMPLETION]: 3,
    [AIOperationType.DOCUMENT_SUMMARIZATION]: 2,
    [AIOperationType.INTELLIGENT_SEARCH]: 1,
    [AIOperationType.WORKFLOW_OPTIMIZATION]: 15,
    [AIOperationType.BATCH_PROCESSING]: 30
  };

  const baseMinutes = estimationMinutes[operationRequest.operationType] || 5;
  
  // Adjust based on priority
  const priorityMultiplier = operationRequest.priority === AIOperationPriority.URGENT ? 0.5 : 
                           operationRequest.priority === AIOperationPriority.HIGH ? 0.7 : 1.0;

  const estimatedMinutes = Math.ceil(baseMinutes * priorityMultiplier);
  const completionTime = new Date(Date.now() + estimatedMinutes * 60 * 1000);
  
  return completionTime.toISOString();
}

/**
 * Get number of steps for operation type
 */
function getOperationSteps(operationType: AIOperationType): number {
  const steps: { [key: string]: number } = {
    [AIOperationType.DOCUMENT_ANALYSIS]: 4,
    [AIOperationType.CONTENT_GENERATION]: 5,
    [AIOperationType.CONTENT_COMPLETION]: 3,
    [AIOperationType.DOCUMENT_SUMMARIZATION]: 3,
    [AIOperationType.INTELLIGENT_SEARCH]: 2,
    [AIOperationType.WORKFLOW_OPTIMIZATION]: 6,
    [AIOperationType.BATCH_PROCESSING]: 8
  };

  return steps[operationType] || 3;
}

/**
 * Queue AI operation for processing using shared Service Bus service (production implementation)
 */
async function queueAIOperation(aiOperation: any): Promise<void> {
  const { serviceBusEnhanced } = require('../shared/services/service-bus');

  try {
    // Ensure Service Bus is initialized
    await serviceBusEnhanced.initialize();

    const queueName = 'ai-operations';

    // Prepare message for Service Bus using the enhanced service
    const message = {
      body: {
        operationId: aiOperation.id,
        operationType: aiOperation.operationType,
        data: aiOperation,
        queuedAt: new Date().toISOString()
      },
      messageId: `ai-op-${aiOperation.id}-${Date.now()}`,
      subject: `AI Operation: ${aiOperation.operationType}`,
      correlationId: aiOperation.id,
      applicationProperties: {
        operationId: aiOperation.id,
        operationType: aiOperation.operationType,
        priority: aiOperation.priority || 'normal',
        organizationId: aiOperation.organizationId,
        userId: aiOperation.userId,
        source: 'ai-orchestration-hub'
      },
      // Set message TTL and scheduling based on priority
      timeToLive: aiOperation.priority === 'high' ? 300000 : 3600000, // 5 min for high, 1 hour for normal/low
      scheduledEnqueueTime: aiOperation.priority === 'low' ? new Date(Date.now() + 30000) : undefined // 30 sec delay for low priority
    };

    const success = await serviceBusEnhanced.sendToQueue(queueName, message);

    if (!success) {
      throw new Error('Failed to send message to Service Bus queue');
    }

    logger.info("AI operation queued for processing via shared Service Bus", {
      operationId: aiOperation.id,
      operationType: aiOperation.operationType,
      priority: aiOperation.priority,
      queueName: queueName
    });

    // Update operation status to queued
    await updateAIOperationStatus(aiOperation.id, 'queued', {
      queuedAt: new Date().toISOString(),
      queueName: queueName
    });

  } catch (error) {
    logger.error('Failed to queue AI operation', {
      error: error instanceof Error ? error.message : String(error),
      operationId: aiOperation.id
    });

    // Fallback: Update status to failed
    await updateAIOperationStatus(aiOperation.id, 'failed', {
      error: 'Failed to queue operation',
      failedAt: new Date().toISOString()
    });

    throw error;
  }
}

/**
 * Update AI operation status in database
 */
async function updateAIOperationStatus(operationId: string, status: string, additionalData: any = {}): Promise<void> {
  try {
    const { db } = require('../shared/services/database');

    const operation = await db.readItem('ai-operations', operationId, operationId);
    if (operation) {
      const updatedOperation = {
        ...operation,
        status,
        ...additionalData,
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('ai-operations', updatedOperation);
      logger.info('AI operation status updated', { operationId, status });
    }
  } catch (error) {
    logger.error('Failed to update AI operation status', {
      operationId,
      status,
      error: error instanceof Error ? error.message : String(error)
    });
  }
}

/**
 * Simulate AI operation processing (for demo purposes)
 */
async function simulateAIOperationProcessing(aiOperation: any): Promise<void> {
  try {
    // Update status to running
    const updatedOperation = {
      ...aiOperation,
      status: AIOperationStatus.RUNNING,
      progress: {
        percentage: 25,
        currentStep: 'Processing',
        totalSteps: aiOperation.progress.totalSteps
      },
      updatedAt: new Date().toISOString()
    };

    await db.updateItem('ai-operations', updatedOperation);

    // Simulate processing time
    setTimeout(async () => {
      // Complete the operation
      const completedOperation = {
        ...updatedOperation,
        status: AIOperationStatus.COMPLETED,
        progress: {
          percentage: 100,
          currentStep: 'Completed',
          totalSteps: aiOperation.progress.totalSteps
        },
        result: {
          success: true,
          message: `${aiOperation.operationType} completed successfully`,
          data: {
            operationType: aiOperation.operationType,
            processingTime: '2.5 seconds',
            confidence: 0.95
          }
        },
        updatedAt: new Date().toISOString()
      };

      await db.updateItem('ai-operations', completedOperation);

      logger.info("AI operation completed", {
        operationId: aiOperation.id,
        operationType: aiOperation.operationType
      });

    }, 3000); // 3 second processing simulation

  } catch (error) {
    logger.error('Failed to process AI operation', { error, operationId: aiOperation.id });
  }
}

// Register functions
app.http('ai-operation-create', {
  methods: ['POST', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/operations',
  handler: createAIOperation
});

app.http('ai-operation-status', {
  methods: ['GET', 'OPTIONS'],
  authLevel: 'function',
  route: 'ai/operations/{operationId}',
  handler: getAIOperationStatus
});
