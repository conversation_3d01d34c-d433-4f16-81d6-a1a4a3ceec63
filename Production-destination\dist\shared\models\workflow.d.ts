/**
 * Workflow Models and Interfaces
 * Defines the structure for workflow-related data models
 */
export interface Workflow {
    id: string;
    name: string;
    description?: string;
    definition: WorkflowDefinition;
    status: WorkflowStatus;
    organizationId: string;
    projectId?: string;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    startedAt?: string;
    completedAt?: string;
    currentStep?: string;
    assignedTo?: string[];
    priority: WorkflowPriority;
    dueDate?: string;
    metadata?: WorkflowMetadata;
    tenantId: string;
    templateId?: string;
    isTemplate?: boolean;
}
export declare enum WorkflowStatus {
    DRAFT = "DRAFT",
    ACTIVE = "ACTIVE",
    RUNNING = "RUNNING",
    PAUSED = "PAUSED",
    COMPLETED = "COMPLETED",
    CANCELLED = "CANCELLED",
    ERROR = "ERROR",
    ARCHIVED = "ARCHIVED"
}
export declare enum WorkflowPriority {
    LOW = "LOW",
    NORMAL = "NORMAL",
    HIGH = "HIGH",
    URGENT = "URGENT"
}
export interface WorkflowDefinition {
    version: string;
    steps: WorkflowStep[];
    triggers: WorkflowTrigger[];
    variables?: WorkflowVariable[];
    conditions?: WorkflowCondition[];
    notifications?: WorkflowNotification[];
    settings?: WorkflowSettings;
}
export interface WorkflowStep {
    id: string;
    name: string;
    description?: string;
    type: WorkflowStepType;
    action: WorkflowAction;
    assignedTo?: string[];
    dependencies?: string[];
    conditions?: WorkflowCondition[];
    timeout?: number;
    retryPolicy?: RetryPolicy;
    position: {
        x: number;
        y: number;
    };
    status?: WorkflowStepStatus;
    startedAt?: string;
    completedAt?: string;
    result?: any;
    error?: string;
}
export declare enum WorkflowStepType {
    MANUAL = "MANUAL",
    AUTOMATED = "AUTOMATED",
    APPROVAL = "APPROVAL",
    REVIEW = "REVIEW",
    NOTIFICATION = "NOTIFICATION",
    CONDITION = "CONDITION",
    PARALLEL = "PARALLEL",
    SEQUENTIAL = "SEQUENTIAL",
    LOOP = "LOOP",
    WEBHOOK = "WEBHOOK",
    API_CALL = "API_CALL",
    DOCUMENT_PROCESSING = "DOCUMENT_PROCESSING",
    AI_ANALYSIS = "AI_ANALYSIS"
}
export declare enum WorkflowStepStatus {
    PENDING = "PENDING",
    RUNNING = "RUNNING",
    COMPLETED = "COMPLETED",
    FAILED = "FAILED",
    SKIPPED = "SKIPPED",
    CANCELLED = "CANCELLED",
    WAITING_FOR_APPROVAL = "WAITING_FOR_APPROVAL"
}
export interface WorkflowAction {
    type: string;
    parameters: {
        [key: string]: any;
    };
    inputMapping?: {
        [key: string]: string;
    };
    outputMapping?: {
        [key: string]: string;
    };
}
export interface WorkflowTrigger {
    id: string;
    type: WorkflowTriggerType;
    conditions: WorkflowCondition[];
    enabled: boolean;
}
export declare enum WorkflowTriggerType {
    MANUAL = "MANUAL",
    SCHEDULED = "SCHEDULED",
    EVENT = "EVENT",
    WEBHOOK = "WEBHOOK",
    DOCUMENT_UPLOAD = "DOCUMENT_UPLOAD",
    DOCUMENT_PROCESSED = "DOCUMENT_PROCESSED",
    USER_ACTION = "USER_ACTION",
    API_CALL = "API_CALL"
}
export interface WorkflowCondition {
    field: string;
    operator: ConditionOperator;
    value: any;
    logicalOperator?: 'AND' | 'OR';
}
export declare enum ConditionOperator {
    EQUALS = "EQUALS",
    NOT_EQUALS = "NOT_EQUALS",
    GREATER_THAN = "GREATER_THAN",
    LESS_THAN = "LESS_THAN",
    GREATER_THAN_OR_EQUAL = "GREATER_THAN_OR_EQUAL",
    LESS_THAN_OR_EQUAL = "LESS_THAN_OR_EQUAL",
    CONTAINS = "CONTAINS",
    NOT_CONTAINS = "NOT_CONTAINS",
    STARTS_WITH = "STARTS_WITH",
    ENDS_WITH = "ENDS_WITH",
    IN = "IN",
    NOT_IN = "NOT_IN",
    IS_NULL = "IS_NULL",
    IS_NOT_NULL = "IS_NOT_NULL"
}
export interface WorkflowVariable {
    name: string;
    type: 'string' | 'number' | 'boolean' | 'object' | 'array';
    defaultValue?: any;
    description?: string;
    required?: boolean;
}
export interface WorkflowNotification {
    id: string;
    type: 'email' | 'inApp' | 'webhook';
    recipients: string[];
    template: string;
    conditions?: WorkflowCondition[];
    enabled: boolean;
}
export interface WorkflowSettings {
    allowParallelExecution: boolean;
    maxRetries: number;
    defaultTimeout: number;
    autoArchiveAfterDays?: number;
    requireApprovalForChanges: boolean;
    enableAuditLog: boolean;
}
export interface RetryPolicy {
    maxRetries: number;
    retryDelay: number;
    backoffMultiplier?: number;
    maxRetryDelay?: number;
}
export interface WorkflowMetadata {
    tags?: string[];
    category?: string;
    estimatedDuration?: number;
    complexity?: 'LOW' | 'MEDIUM' | 'HIGH';
    businessValue?: 'LOW' | 'MEDIUM' | 'HIGH';
    lastExecutionTime?: number;
    averageExecutionTime?: number;
    successRate?: number;
    executionCount?: number;
    [key: string]: any;
}
export interface WorkflowExecution {
    id: string;
    workflowId: string;
    status: WorkflowStatus;
    startedBy: string;
    startedAt: string;
    completedAt?: string;
    currentStepId?: string;
    stepExecutions: WorkflowStepExecution[];
    variables: {
        [key: string]: any;
    };
    error?: string;
    organizationId: string;
    projectId?: string;
    tenantId: string;
}
export interface WorkflowStepExecution {
    id: string;
    stepId: string;
    executionId: string;
    status: WorkflowStepStatus;
    assignedTo?: string[];
    startedAt?: string;
    completedAt?: string;
    result?: any;
    error?: string;
    retryCount: number;
    logs: WorkflowLog[];
}
export interface WorkflowLog {
    id: string;
    timestamp: string;
    level: 'INFO' | 'WARN' | 'ERROR' | 'DEBUG';
    message: string;
    details?: any;
    stepId?: string;
    userId?: string;
}
export interface WorkflowTemplate {
    id: string;
    name: string;
    description?: string;
    category: string;
    definition: WorkflowDefinition;
    isPublic: boolean;
    createdBy: string;
    createdAt: string;
    updatedBy: string;
    updatedAt: string;
    version: string;
    tags?: string[];
    usageCount: number;
    rating?: number;
    organizationId?: string;
    tenantId: string;
}
export interface WorkflowApproval {
    id: string;
    workflowId: string;
    executionId: string;
    stepId: string;
    requestedBy: string;
    requestedAt: string;
    assignedTo: string[];
    status: 'PENDING' | 'APPROVED' | 'REJECTED' | 'CANCELLED';
    approvedBy?: string;
    approvedAt?: string;
    rejectedBy?: string;
    rejectedAt?: string;
    comments?: string;
    dueDate?: string;
    organizationId: string;
    tenantId: string;
}
export interface WorkflowComment {
    id: string;
    workflowId: string;
    executionId?: string;
    stepId?: string;
    userId: string;
    userName: string;
    content: string;
    createdAt: string;
    updatedAt?: string;
    parentCommentId?: string;
    status: 'active' | 'deleted' | 'hidden';
    mentions?: string[];
}
export interface WorkflowAnalytics {
    workflowId: string;
    totalExecutions: number;
    successfulExecutions: number;
    failedExecutions: number;
    averageExecutionTime: number;
    medianExecutionTime: number;
    successRate: number;
    mostCommonFailureReasons: {
        reason: string;
        count: number;
    }[];
    stepAnalytics: {
        stepId: string;
        stepName: string;
        averageExecutionTime: number;
        successRate: number;
        failureCount: number;
    }[];
    timeSeriesData: {
        date: string;
        executions: number;
        successRate: number;
        averageTime: number;
    }[];
}
export interface WorkflowCreateRequest {
    name: string;
    description?: string;
    definition: WorkflowDefinition;
    organizationId: string;
    projectId?: string;
    priority?: WorkflowPriority;
    dueDate?: string;
    templateId?: string;
}
export interface WorkflowUpdateRequest {
    name?: string;
    description?: string;
    definition?: WorkflowDefinition;
    status?: WorkflowStatus;
    priority?: WorkflowPriority;
    dueDate?: string;
    assignedTo?: string[];
}
export interface WorkflowExecutionRequest {
    workflowId: string;
    variables?: {
        [key: string]: any;
    };
    assignedTo?: string[];
    priority?: WorkflowPriority;
    dueDate?: string;
}
