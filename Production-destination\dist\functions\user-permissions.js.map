{"version": 3, "file": "user-permissions.js", "sourceRoot": "", "sources": ["../../src/functions/user-permissions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAsEA,gDAkLC;AAxPD;;;GAGG;AACH,gDAAyF;AACzF,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,mBAAmB;AACnB,IAAY,UASX;AATD,WAAY,UAAU;IACpB,2CAA6B,CAAA;IAC7B,uDAAyC,CAAA;IACzC,yDAA2C,CAAA;IAC3C,yDAA2C,CAAA;IAC3C,6CAA+B,CAAA;IAC/B,+CAAiC,CAAA;IACjC,+CAAiC,CAAA;IACjC,6BAAe,CAAA;AACjB,CAAC,EATW,UAAU,0BAAV,UAAU,QASrB;AAED,IAAY,gBAoCX;AApCD,WAAY,gBAAgB;IAC1B,2BAA2B;IAC3B,2DAAuC,CAAA;IACvC,+DAA2C,CAAA;IAC3C,+DAA2C,CAAA;IAC3C,2EAAuD,CAAA;IACvD,6EAAyD,CAAA;IACzD,6EAAyD,CAAA;IACzD,+EAA2D,CAAA;IAE3D,sBAAsB;IACtB,qDAAiC,CAAA;IACjC,iDAA6B,CAAA;IAC7B,qDAAiC,CAAA;IACjC,qDAAiC,CAAA;IACjC,qDAAiC,CAAA;IACjC,6DAAyC,CAAA;IACzC,mEAA+C,CAAA;IAC/C,qEAAiD,CAAA;IAEjD,uBAAuB;IACvB,uDAAmC,CAAA;IACnC,mDAA+B,CAAA;IAC/B,uDAAmC,CAAA;IACnC,uDAAmC,CAAA;IACnC,qDAAiC,CAAA;IACjC,yDAAqC,CAAA;IACrC,yDAAqC,CAAA;IACrC,6DAAyC,CAAA;IAEzC,wBAAwB;IACxB,qDAAiC,CAAA;IAEjC,mBAAmB;IACnB,mEAA+C,CAAA;IAC/C,yDAAqC,CAAA;AACvC,CAAC,EApCW,gBAAgB,gCAAhB,gBAAgB,QAoC3B;AAED,qBAAqB;AACrB,MAAM,oBAAoB,GAAG,GAAG,CAAC,MAAM,CAAC;IACtC,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC1C,CAAC,CAAC;AAEH;;GAEG;AACI,KAAK,UAAU,kBAAkB,CAAC,OAAoB,EAAE,OAA0B;IACvF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC;IAErC,eAAM,CAAC,IAAI,CAAC,8BAA8B,EAAE,EAAE,aAAa,EAAE,MAAM,EAAE,CAAC,CAAC;IAEvE,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC7B,MAAM,YAAY,GAAG,MAAM,IAAI,IAAI,CAAC,EAAE,CAAC;QAEvC,4BAA4B;QAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,oBAAoB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEpE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,EAAE,cAAc,EAAE,SAAS,EAAE,GAAG,KAAK,CAAC;QAE5C,kEAAkE;QAClE,MAAM,aAAa,GAAG,YAAY,KAAK,IAAI,CAAC,EAAE,CAAC;QAC/C,MAAM,aAAa,GAAG,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,IAAK,IAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,CAAC;QACpH,MAAM,UAAU,GAAI,IAAY,CAAC,WAAW,EAAE,QAAQ,CAAC,UAAU,CAAC,kBAAkB,CAAC;YACnE,cAAc;YACb,IAAY,CAAC,eAAe,EAAE,QAAQ,CAAC,cAAc,CAAC,CAAC;QAE1E,IAAI,CAAC,aAAa,IAAI,CAAC,aAAa,IAAI,CAAC,UAAU,EAAE,CAAC;YACpD,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,2DAA2D,EAAE;aACjF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,kBAAkB;QAClB,MAAM,UAAU,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,OAAO,EAAE,YAAY,EAAE,YAAY,CAAC,CAAC;QAC1E,IAAI,CAAC,UAAU,EAAE,CAAC;YAChB,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,gBAAgB,EAAE;aACtC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,0BAA0B;QAC1B,MAAM,WAAW,GAAI,UAAkB,CAAC,WAAW,IAAI,EAAE,CAAC;QAE1D,wCAAwC;QACxC,IAAI,cAAc,GAAa,EAAE,CAAC;QAElC,mCAAmC;QACnC,IAAI,WAAW,CAAC,QAAQ,CAAC,UAAU,CAAC,YAAY,CAAC,EAAE,CAAC;YAClD,cAAc,GAAG,MAAM,CAAC,MAAM,CAAC,gBAAgB,CAAC,CAAC;QACnD,CAAC;aAAM,CAAC;YACN,oCAAoC;YACpC,IAAI,KAAK,GAAG,gEAAgE,CAAC;YAC7E,MAAM,UAAU,GAAG,CAAC,YAAY,CAAC,CAAC;YAElC,sCAAsC;YACtC,IAAI,cAAc,EAAE,CAAC;gBACnB,KAAK,IAAI,+FAA+F,CAAC;gBAEzG,iCAAiC;gBACjC,IAAI,SAAS,EAAE,CAAC;oBACd,KAAK,IAAI,+FAA+F,CAAC;oBACzG,UAAU,CAAC,IAAI,CAAC,cAAc,EAAE,SAAS,CAAC,CAAC;gBAC7C,CAAC;qBAAM,CAAC;oBACN,UAAU,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;gBAClC,CAAC;gBAED,KAAK,IAAI,GAAG,CAAC;YACf,CAAC;YAED,MAAM,eAAe,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,KAAK,EAAE,UAAU,CAAC,CAAC;YAEnF,sCAAsC;YACtC,IAAI,eAAe,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;gBAC/B,mBAAmB;gBACnB,MAAM,OAAO,GAAG,eAAe,CAAC,GAAG,CAAC,CAAC,UAAe,EAAE,EAAE,CAAC,UAAU,CAAC,MAAM,CAAC,CAAC;gBAE5E,YAAY;gBACZ,MAAM,SAAS,GAAG,sDAAsD,CAAC;gBACzE,MAAM,KAAK,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,OAAO,EAAE,SAAS,EAAE,CAAC,OAAO,CAAC,CAAC,CAAC;gBAEjE,0BAA0B;gBAC1B,KAAK,CAAC,OAAO,CAAC,CAAC,IAAS,EAAE,EAAE;oBAC1B,IAAI,IAAI,CAAC,WAAW,EAAE,CAAC;wBACrB,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,IAAI,CAAC,WAAW,CAAC,CAAC;oBAC5D,CAAC;gBACH,CAAC,CAAC,CAAC;YACL,CAAC;YAED,oCAAoC;YACpC,WAAW,CAAC,OAAO,CAAC,CAAC,QAAoB,EAAE,EAAE;gBAC3C,QAAQ,QAAQ,EAAE,CAAC;oBACjB,KAAK,UAAU,CAAC,kBAAkB;wBAChC,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,+BAA+B,EAAE,CAAC,CAAC;wBAC3E,MAAM;oBACR,KAAK,UAAU,CAAC,mBAAmB;wBACjC,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,gCAAgC,EAAE,CAAC,CAAC;wBAC5E,MAAM;oBACR,KAAK,UAAU,CAAC,mBAAmB;wBACjC,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,gCAAgC,EAAE,CAAC,CAAC;wBAC5E,MAAM;oBACR,KAAK,UAAU,CAAC,aAAa;wBAC3B,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,0BAA0B,EAAE,CAAC,CAAC;wBACtE,MAAM;oBACR,KAAK,UAAU,CAAC,cAAc;wBAC5B,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,2BAA2B,EAAE,CAAC,CAAC;wBACvE,MAAM;oBACR,KAAK,UAAU,CAAC,cAAc;wBAC5B,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,2BAA2B,EAAE,CAAC,CAAC;wBACvE,MAAM;oBACR,KAAK,UAAU,CAAC,KAAK;wBACnB,cAAc,GAAG,CAAC,GAAG,cAAc,EAAE,GAAG,mBAAmB,EAAE,CAAC,CAAC;wBAC/D,MAAM;gBACV,CAAC;YACH,CAAC,CAAC,CAAC;QACL,CAAC;QAED,oBAAoB;QACpB,MAAM,iBAAiB,GAAG,CAAC,GAAG,IAAI,GAAG,CAAC,cAAc,CAAC,CAAC,CAAC;QAEvD,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE;YACrD,aAAa;YACb,YAAY;YACZ,gBAAgB,EAAE,iBAAiB,CAAC,MAAM;SAC3C,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,MAAM,EAAE,YAAY;gBACpB,WAAW;gBACX,WAAW,EAAE,iBAAiB;gBAC9B,cAAc;gBACd,SAAS;aACV;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,6BAA6B,EAAE;YAC1C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED,oDAAoD;AACpD,SAAS,+BAA+B;IACtC,OAAO;QACL,gBAAgB,CAAC,mBAAmB;QACpC,gBAAgB,CAAC,iBAAiB;QAClC,gBAAgB,CAAC,mBAAmB;QACpC,gBAAgB,CAAC,yBAAyB;QAC1C,gBAAgB,CAAC,0BAA0B;QAC3C,gBAAgB,CAAC,0BAA0B;QAC3C,gBAAgB,CAAC,2BAA2B;QAC5C,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,gBAAgB;QACjC,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,qBAAqB;QACtC,gBAAgB,CAAC,gBAAgB;KAClC,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC;IACvC,OAAO;QACL,gBAAgB,CAAC,iBAAiB;QAClC,gBAAgB,CAAC,yBAAyB;QAC1C,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,gBAAgB;QACjC,gBAAgB,CAAC,cAAc;KAChC,CAAC;AACJ,CAAC;AAED,SAAS,gCAAgC;IACvC,OAAO;QACL,gBAAgB,CAAC,iBAAiB;QAClC,gBAAgB,CAAC,yBAAyB;QAC1C,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,gBAAgB;QACjC,gBAAgB,CAAC,cAAc;KAChC,CAAC;AACJ,CAAC;AAED,SAAS,0BAA0B;IACjC,OAAO;QACL,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,kBAAkB;QACnC,gBAAgB,CAAC,qBAAqB;QACtC,gBAAgB,CAAC,sBAAsB;QACvC,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,gBAAgB;QACjC,gBAAgB,CAAC,gBAAgB;QACjC,gBAAgB,CAAC,kBAAkB;KACpC,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B;IAClC,OAAO;QACL,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,eAAe;QAChC,gBAAgB,CAAC,cAAc;QAC/B,gBAAgB,CAAC,gBAAgB;KAClC,CAAC;AACJ,CAAC;AAED,SAAS,2BAA2B;IAClC,OAAO;QACL,gBAAgB,CAAC,YAAY;QAC7B,gBAAgB,CAAC,aAAa;QAC9B,gBAAgB,CAAC,gBAAgB;KAClC,CAAC;AACJ,CAAC;AAED,SAAS,mBAAmB;IAC1B,OAAO;QACL,gBAAgB,CAAC,aAAa;KAC/B,CAAC;AACJ,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,kBAAkB,EAAE;IAC3B,OAAO,EAAE,CAAC,KAAK,EAAE,SAAS,CAAC;IAC3B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,6BAA6B;IACpC,OAAO,EAAE,kBAAkB;CAC5B,CAAC,CAAC"}