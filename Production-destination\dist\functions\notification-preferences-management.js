"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getNotificationPreferences = getNotificationPreferences;
exports.updateNotificationPreferences = updateNotificationPreferences;
exports.resetNotificationPreferences = resetNotificationPreferences;
/**
 * Notification Preferences Management Function
 * Handles advanced notification preference management, templates, and delivery settings
 * Migrated from old-arch/src/notification-service/preferences/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Notification preference types and enums
var NotificationChannel;
(function (NotificationChannel) {
    NotificationChannel["EMAIL"] = "email";
    NotificationChannel["IN_APP"] = "in_app";
    NotificationChannel["PUSH"] = "push";
    NotificationChannel["SMS"] = "sms";
})(NotificationChannel || (NotificationChannel = {}));
var NotificationFrequency;
(function (NotificationFrequency) {
    NotificationFrequency["IMMEDIATE"] = "immediate";
    NotificationFrequency["HOURLY"] = "hourly";
    NotificationFrequency["DAILY"] = "daily";
    NotificationFrequency["WEEKLY"] = "weekly";
    NotificationFrequency["NEVER"] = "never";
})(NotificationFrequency || (NotificationFrequency = {}));
var NotificationType;
(function (NotificationType) {
    NotificationType["DOCUMENT_UPLOADED"] = "DOCUMENT_UPLOADED";
    NotificationType["DOCUMENT_PROCESSED"] = "DOCUMENT_PROCESSED";
    NotificationType["DOCUMENT_PROCESSING_FAILED"] = "DOCUMENT_PROCESSING_FAILED";
    NotificationType["DOCUMENT_SHARED"] = "DOCUMENT_SHARED";
    NotificationType["DOCUMENT_COMMENTED"] = "DOCUMENT_COMMENTED";
    NotificationType["DOCUMENT_APPROVED"] = "DOCUMENT_APPROVED";
    NotificationType["DOCUMENT_REJECTED"] = "DOCUMENT_REJECTED";
    NotificationType["WORKFLOW_ASSIGNED"] = "WORKFLOW_ASSIGNED";
    NotificationType["WORKFLOW_COMPLETED"] = "WORKFLOW_COMPLETED";
    NotificationType["WORKFLOW_OVERDUE"] = "WORKFLOW_OVERDUE";
    NotificationType["PROJECT_INVITATION"] = "PROJECT_INVITATION";
    NotificationType["PROJECT_MEMBER_ADDED"] = "PROJECT_MEMBER_ADDED";
    NotificationType["ORGANIZATION_INVITATION"] = "ORGANIZATION_INVITATION";
    NotificationType["MENTION"] = "MENTION";
    NotificationType["COLLABORATION_STARTED"] = "COLLABORATION_STARTED";
    NotificationType["SYSTEM_UPDATE"] = "SYSTEM_UPDATE";
    NotificationType["SECURITY_ALERT"] = "SECURITY_ALERT";
})(NotificationType || (NotificationType = {}));
// Validation schemas
const updateNotificationPreferencesSchema = Joi.object({
    channels: Joi.object({
        email: Joi.boolean().optional(),
        in_app: Joi.boolean().optional(),
        push: Joi.boolean().optional(),
        sms: Joi.boolean().optional()
    }).optional(),
    types: Joi.object().pattern(Joi.string().valid(...Object.values(NotificationType)), Joi.object({
        email: Joi.boolean().optional(),
        in_app: Joi.boolean().optional(),
        push: Joi.boolean().optional(),
        sms: Joi.boolean().optional(),
        enabled: Joi.boolean().optional()
    })).optional(),
    frequency: Joi.object({
        digest: Joi.string().valid(...Object.values(NotificationFrequency)).optional(),
        summary: Joi.string().valid(...Object.values(NotificationFrequency)).optional(),
        reminders: Joi.string().valid(...Object.values(NotificationFrequency)).optional()
    }).optional(),
    quietHours: Joi.object({
        enabled: Joi.boolean().optional(),
        start: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
        end: Joi.string().pattern(/^([01]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
        timezone: Joi.string().optional(),
        weekendsOnly: Joi.boolean().optional()
    }).optional(),
    delivery: Joi.object({
        maxEmailsPerHour: Joi.number().min(1).max(100).optional(),
        maxPushPerHour: Joi.number().min(1).max(100).optional(),
        batchSimilarNotifications: Joi.boolean().optional(),
        priorityOverride: Joi.boolean().optional()
    }).optional(),
    categories: Joi.object({
        documents: Joi.boolean().optional(),
        workflows: Joi.boolean().optional(),
        projects: Joi.boolean().optional(),
        collaboration: Joi.boolean().optional(),
        system: Joi.boolean().optional(),
        security: Joi.boolean().optional()
    }).optional()
});
/**
 * Get notification preferences handler
 */
async function getNotificationPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get notification preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get user's notification preferences
        let preferences = await database_1.db.readItem('notification-preferences', user.id, user.id);
        if (!preferences) {
            // Return default preferences if none exist
            preferences = getDefaultNotificationPreferences(user.id);
        }
        const preferencesData = preferences;
        logger_1.logger.info("Notification preferences retrieved successfully", {
            correlationId,
            userId: user.id,
            hasCustomPreferences: !!preferences
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: user.id,
                preferences: preferencesData,
                lastUpdated: preferencesData.updatedAt,
                isDefault: !preferences
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get notification preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update notification preferences handler
 */
async function updateNotificationPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update notification preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateNotificationPreferencesSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const preferenceUpdates = value;
        // Get current preferences or create default
        let currentPreferences = await database_1.db.readItem('notification-preferences', user.id, user.id);
        if (!currentPreferences) {
            currentPreferences = getDefaultNotificationPreferences(user.id);
        }
        const currentData = currentPreferences;
        const now = new Date().toISOString();
        // Deep merge preferences
        const updatedPreferences = {
            ...currentData,
            ...deepMergePreferences(currentData, preferenceUpdates),
            userId: user.id,
            updatedAt: now,
            updatedBy: user.id,
            tenantId: user.tenantId
        };
        await database_1.db.upsertItem('notification-preferences', updatedPreferences);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "notification_preferences_updated",
            userId: user.id,
            timestamp: now,
            details: {
                updatedFields: Object.keys(preferenceUpdates),
                channelsEnabled: Object.entries(updatedPreferences.channels || {})
                    .filter(([_, enabled]) => enabled)
                    .map(([channel, _]) => channel),
                quietHoursEnabled: updatedPreferences.quietHours?.enabled || false
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'NotificationPreferencesUpdated',
            aggregateId: user.id,
            aggregateType: 'NotificationPreferences',
            version: 1,
            data: {
                userId: user.id,
                updatedFields: Object.keys(preferenceUpdates),
                previousPreferences: currentData,
                newPreferences: updatedPreferences
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Notification preferences updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(preferenceUpdates)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: user.id,
                preferences: updatedPreferences,
                updatedFields: Object.keys(preferenceUpdates),
                updatedAt: now,
                message: "Notification preferences updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update notification preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Reset notification preferences to defaults handler
 */
async function resetNotificationPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Reset notification preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        const now = new Date().toISOString();
        // Create default preferences
        const defaultPreferences = getDefaultNotificationPreferences(user.id);
        defaultPreferences.updatedAt = now;
        defaultPreferences.updatedBy = user.id;
        await database_1.db.upsertItem('notification-preferences', defaultPreferences);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "notification_preferences_reset",
            userId: user.id,
            timestamp: now,
            details: {
                resetToDefaults: true
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'NotificationPreferencesReset',
            aggregateId: user.id,
            aggregateType: 'NotificationPreferences',
            version: 1,
            data: {
                userId: user.id,
                resetToDefaults: true,
                defaultPreferences
            },
            userId: user.id,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Notification preferences reset successfully", {
            correlationId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                userId: user.id,
                preferences: defaultPreferences,
                resetAt: now,
                message: "Notification preferences reset to defaults successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Reset notification preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get default notification preferences
 */
function getDefaultNotificationPreferences(userId) {
    const now = new Date().toISOString();
    return {
        id: userId,
        userId,
        channels: {
            email: true,
            in_app: true,
            push: true,
            sms: false
        },
        types: Object.values(NotificationType).reduce((acc, type) => {
            acc[type] = {
                email: getDefaultChannelSetting(type, 'email'),
                in_app: getDefaultChannelSetting(type, 'in_app'),
                push: getDefaultChannelSetting(type, 'push'),
                sms: getDefaultChannelSetting(type, 'sms'),
                enabled: true
            };
            return acc;
        }, {}),
        frequency: {
            digest: NotificationFrequency.DAILY,
            summary: NotificationFrequency.WEEKLY,
            reminders: NotificationFrequency.IMMEDIATE
        },
        quietHours: {
            enabled: false,
            start: "22:00",
            end: "08:00",
            timezone: "UTC",
            weekendsOnly: false
        },
        delivery: {
            maxEmailsPerHour: 10,
            maxPushPerHour: 20,
            batchSimilarNotifications: true,
            priorityOverride: true
        },
        categories: {
            documents: true,
            workflows: true,
            projects: true,
            collaboration: true,
            system: true,
            security: true
        },
        createdAt: now,
        updatedAt: now,
        tenantId: userId
    };
}
/**
 * Get default channel setting for notification type
 */
function getDefaultChannelSetting(type, channel) {
    const highPriorityTypes = [
        NotificationType.WORKFLOW_ASSIGNED,
        NotificationType.WORKFLOW_OVERDUE,
        NotificationType.DOCUMENT_PROCESSING_FAILED,
        NotificationType.SECURITY_ALERT,
        NotificationType.MENTION
    ];
    const emailOnlyTypes = [
        NotificationType.DOCUMENT_PROCESSED,
        NotificationType.SYSTEM_UPDATE
    ];
    const inAppOnlyTypes = [
        NotificationType.DOCUMENT_UPLOADED,
        NotificationType.COLLABORATION_STARTED
    ];
    switch (channel) {
        case 'email':
            return !inAppOnlyTypes.includes(type);
        case 'in_app':
            return true; // All types enabled for in-app
        case 'push':
            return highPriorityTypes.includes(type);
        case 'sms':
            return false; // SMS disabled by default
        default:
            return false;
    }
}
/**
 * Deep merge notification preferences
 */
function deepMergePreferences(current, updates) {
    const result = { ...current };
    for (const key in updates) {
        if (updates[key] !== null && typeof updates[key] === 'object' && !Array.isArray(updates[key])) {
            result[key] = deepMergePreferences(current[key] || {}, updates[key]);
        }
        else {
            result[key] = updates[key];
        }
    }
    return result;
}
// Register functions
functions_1.app.http('notification-preferences-get', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/preferences',
    handler: getNotificationPreferences
});
functions_1.app.http('notification-preferences-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/preferences/update',
    handler: updateNotificationPreferences
});
functions_1.app.http('notification-preferences-reset', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'notifications/preferences/reset',
    handler: resetNotificationPreferences
});
//# sourceMappingURL=notification-preferences-management.js.map