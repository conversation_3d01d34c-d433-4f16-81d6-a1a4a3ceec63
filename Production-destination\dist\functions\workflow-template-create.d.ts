/**
 * Workflow Template Create Function
 * Handles creation of workflow templates for reuse across projects
 * Migrated from old-arch/src/workflow-service/templates/create/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create workflow template handler
 */
export declare function createWorkflowTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
