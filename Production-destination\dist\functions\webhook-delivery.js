"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.deliverWebhook = deliverWebhook;
/**
 * Webhook Delivery Function
 * Handles webhook delivery with retry logic and failure handling
 * Migrated from old-arch/src/integration-service/webhook-delivery/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
const crypto = __importStar(require("crypto"));
// Webhook delivery status
var DeliveryStatus;
(function (DeliveryStatus) {
    DeliveryStatus["PENDING"] = "pending";
    DeliveryStatus["DELIVERED"] = "delivered";
    DeliveryStatus["FAILED"] = "failed";
    DeliveryStatus["RETRYING"] = "retrying";
    DeliveryStatus["ABANDONED"] = "abandoned";
})(DeliveryStatus || (DeliveryStatus = {}));
// Validation schema
const deliverWebhookSchema = Joi.object({
    webhookId: Joi.string().uuid().required(),
    eventType: Joi.string().required(),
    payload: Joi.object().required(),
    deliveryAttempt: Joi.number().min(1).default(1),
    isRetry: Joi.boolean().default(false),
    metadata: Joi.object().optional()
});
/**
 * Deliver webhook handler
 */
async function deliverWebhook(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Webhook delivery started", { correlationId });
    try {
        // Authenticate user (for manual webhook delivery)
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = deliverWebhookSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const deliveryRequest = value;
        const startTime = Date.now();
        // Get webhook configuration
        const webhook = await database_1.db.readItem('webhooks', deliveryRequest.webhookId, deliveryRequest.webhookId);
        if (!webhook) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Webhook not found" }
            }, request);
        }
        const webhookData = webhook;
        // Check if webhook is active
        if (!webhookData.isActive) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Webhook is not active" }
            }, request);
        }
        // Check if event type is supported by webhook
        if (!webhookData.events.includes(deliveryRequest.eventType)) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: "Event type not supported by webhook",
                    supportedEvents: webhookData.events
                }
            }, request);
        }
        // Perform webhook delivery
        const deliveryResult = await performWebhookDelivery(webhookData, deliveryRequest.eventType, deliveryRequest.payload, deliveryRequest.deliveryAttempt);
        const responseTime = Date.now() - startTime;
        // Create delivery record
        const deliveryId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const deliveryRecord = {
            id: deliveryId,
            webhookId: deliveryRequest.webhookId,
            eventType: deliveryRequest.eventType,
            payload: deliveryRequest.payload,
            deliveryAttempt: deliveryRequest.deliveryAttempt,
            isRetry: deliveryRequest.isRetry,
            status: deliveryResult.success ? DeliveryStatus.DELIVERED : DeliveryStatus.FAILED,
            statusCode: deliveryResult.statusCode,
            responseBody: deliveryResult.responseBody,
            responseTime,
            errorMessage: deliveryResult.errorMessage,
            deliveredAt: now,
            nextRetryAt: deliveryResult.success ? null : calculateNextRetryTime(deliveryRequest.deliveryAttempt, webhookData.retryConfig),
            organizationId: webhookData.organizationId,
            projectId: webhookData.projectId,
            metadata: deliveryRequest.metadata,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('webhook-deliveries', deliveryRecord);
        // Update webhook statistics
        const updatedWebhook = {
            ...webhookData,
            statistics: {
                ...webhookData.statistics,
                totalDeliveries: (webhookData.statistics.totalDeliveries || 0) + 1,
                successfulDeliveries: deliveryResult.success
                    ? (webhookData.statistics.successfulDeliveries || 0) + 1
                    : webhookData.statistics.successfulDeliveries || 0,
                failedDeliveries: !deliveryResult.success
                    ? (webhookData.statistics.failedDeliveries || 0) + 1
                    : webhookData.statistics.failedDeliveries || 0,
                lastDeliveryAt: now,
                lastSuccessAt: deliveryResult.success ? now : webhookData.statistics.lastSuccessAt,
                lastFailureAt: !deliveryResult.success ? now : webhookData.statistics.lastFailureAt
            },
            updatedAt: now
        };
        await database_1.db.updateItem('webhooks', updatedWebhook);
        // Schedule retry if delivery failed and retries are available
        if (!deliveryResult.success && shouldRetry(deliveryRequest.deliveryAttempt, webhookData.retryConfig)) {
            await scheduleWebhookRetry(deliveryRecord, webhookData);
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "webhook_delivered",
            userId: user.id,
            organizationId: webhookData.organizationId,
            projectId: webhookData.projectId,
            timestamp: now,
            details: {
                webhookId: deliveryRequest.webhookId,
                webhookName: webhookData.name,
                eventType: deliveryRequest.eventType,
                deliveryAttempt: deliveryRequest.deliveryAttempt,
                success: deliveryResult.success,
                statusCode: deliveryResult.statusCode,
                responseTime,
                isRetry: deliveryRequest.isRetry
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: deliveryResult.success ? 'WebhookDelivered' : 'WebhookDeliveryFailed',
            aggregateId: deliveryId,
            aggregateType: 'WebhookDelivery',
            version: 1,
            data: {
                delivery: deliveryRecord,
                webhook: webhookData,
                deliveryResult
            },
            userId: user.id,
            organizationId: webhookData.organizationId,
            tenantId: user.tenantId
        });
        const response = {
            deliveryId,
            webhookId: deliveryRequest.webhookId,
            eventType: deliveryRequest.eventType,
            status: deliveryRecord.status,
            deliveryAttempt: deliveryRequest.deliveryAttempt,
            statusCode: deliveryResult.statusCode,
            responseTime,
            success: deliveryResult.success,
            nextRetryAt: deliveryRecord.nextRetryAt || undefined,
            errorMessage: deliveryResult.errorMessage
        };
        logger_1.logger.info("Webhook delivery completed", {
            correlationId,
            deliveryId,
            webhookId: deliveryRequest.webhookId,
            eventType: deliveryRequest.eventType,
            success: deliveryResult.success,
            statusCode: deliveryResult.statusCode,
            responseTime,
            deliveryAttempt: deliveryRequest.deliveryAttempt,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: deliveryResult.success ? 200 : 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: response
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Webhook delivery failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Perform actual webhook delivery
 */
async function performWebhookDelivery(webhook, eventType, payload, deliveryAttempt) {
    try {
        logger_1.logger.info("Performing webhook delivery", {
            webhookId: webhook.id,
            url: webhook.url,
            eventType,
            deliveryAttempt
        });
        // Prepare webhook payload
        const webhookPayload = {
            id: (0, uuid_1.v4)(),
            event: eventType,
            data: payload,
            timestamp: new Date().toISOString(),
            webhook: {
                id: webhook.id,
                name: webhook.name
            },
            delivery_attempt: deliveryAttempt
        };
        // Generate signature
        const signature = generateWebhookSignature(JSON.stringify(webhookPayload), webhook.secret);
        // Prepare headers
        const headers = {
            'Content-Type': 'application/json',
            'User-Agent': 'DocuContext-Webhook/1.0',
            'X-Webhook-Signature': signature,
            'X-Webhook-Event': eventType,
            'X-Webhook-Delivery': (0, uuid_1.v4)(),
            'X-Webhook-Timestamp': new Date().toISOString(),
            ...webhook.headers
        };
        // Make HTTP request
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), webhook.retryConfig?.timeout || 30000);
        const response = await fetch(webhook.url, {
            method: 'POST',
            headers,
            body: JSON.stringify(webhookPayload),
            signal: controller.signal
        });
        clearTimeout(timeoutId);
        const responseBody = await response.text();
        if (response.ok) {
            return {
                success: true,
                statusCode: response.status,
                responseBody: responseBody.substring(0, 1000) // Limit response body size
            };
        }
        else {
            return {
                success: false,
                statusCode: response.status,
                responseBody: responseBody.substring(0, 1000),
                errorMessage: `HTTP ${response.status}: ${response.statusText}`
            };
        }
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error('Webhook delivery request failed', {
            error: errorMessage,
            webhookId: webhook.id,
            url: webhook.url
        });
        return {
            success: false,
            errorMessage
        };
    }
}
/**
 * Generate webhook signature
 */
function generateWebhookSignature(payload, secret) {
    return crypto
        .createHmac('sha256', secret)
        .update(payload, 'utf8')
        .digest('hex');
}
/**
 * Calculate next retry time
 */
function calculateNextRetryTime(deliveryAttempt, retryConfig) {
    if (!retryConfig || deliveryAttempt >= (retryConfig.maxRetries || 3)) {
        return null;
    }
    const baseDelay = retryConfig.retryDelay || 5000; // 5 seconds default
    const backoffMultiplier = retryConfig.backoffMultiplier || 2;
    // Exponential backoff: delay * (multiplier ^ (attempt - 1))
    const delay = baseDelay * Math.pow(backoffMultiplier, deliveryAttempt - 1);
    // Cap maximum delay at 1 hour
    const maxDelay = 60 * 60 * 1000; // 1 hour
    const actualDelay = Math.min(delay, maxDelay);
    const nextRetryTime = new Date(Date.now() + actualDelay);
    return nextRetryTime.toISOString();
}
/**
 * Check if webhook should be retried
 */
function shouldRetry(deliveryAttempt, retryConfig) {
    const maxRetries = retryConfig?.maxRetries || 3;
    return deliveryAttempt < maxRetries;
}
/**
 * Schedule webhook retry
 */
async function scheduleWebhookRetry(deliveryRecord, webhook) {
    try {
        // In production, this would schedule a retry using Azure Service Bus or similar
        // For now, we'll just log the retry scheduling
        logger_1.logger.info("Webhook retry scheduled", {
            deliveryId: deliveryRecord.id,
            webhookId: webhook.id,
            nextRetryAt: deliveryRecord.nextRetryAt,
            deliveryAttempt: deliveryRecord.deliveryAttempt + 1
        });
        // Update delivery record status
        const updatedDelivery = {
            ...deliveryRecord,
            status: DeliveryStatus.RETRYING,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('webhook-deliveries', updatedDelivery);
    }
    catch (error) {
        logger_1.logger.error('Failed to schedule webhook retry', {
            error,
            deliveryId: deliveryRecord.id
        });
    }
}
// Register functions
functions_1.app.http('webhook-delivery', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'webhooks/deliver',
    handler: deliverWebhook
});
//# sourceMappingURL=webhook-delivery.js.map