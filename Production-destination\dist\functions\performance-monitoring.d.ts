/**
 * Performance Monitoring Function
 * Handles system performance monitoring, alerting, and optimization
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Record metric handler
 */
export declare function recordMetric(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Create alert rule handler
 */
export declare function createAlertRule(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get metrics handler
 */
export declare function getMetrics(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
