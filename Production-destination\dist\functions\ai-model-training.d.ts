/**
 * AI Model Training Function
 * Handles custom AI model training, deployment, and management
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create AI model handler
 */
export declare function createModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Train model handler
 */
export declare function trainModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Deploy model handler
 */
export declare function deployModel(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
