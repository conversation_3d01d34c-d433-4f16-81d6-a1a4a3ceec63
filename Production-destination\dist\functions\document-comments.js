"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createComment = createComment;
exports.getComments = getComments;
/**
 * Document Comments Function
 * Handles document comment operations (create, read, update, delete)
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const createCommentSchema = Joi.object({
    content: Joi.string().required().max(2000),
    pageNumber: Joi.number().integer().min(1).optional(),
    coordinates: Joi.object({
        x: Joi.number().required(),
        y: Joi.number().required(),
        width: Joi.number().optional(),
        height: Joi.number().optional()
    }).optional(),
    projectId: Joi.string().uuid().required(),
    organizationId: Joi.string().uuid().required(),
    metadata: Joi.object().optional()
});
const updateCommentSchema = Joi.object({
    content: Joi.string().max(2000).optional(),
    isResolved: Joi.boolean().optional()
});
const getCommentsSchema = Joi.object({
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(20),
    pageNumber: Joi.number().integer().min(1).optional(),
    resolved: Joi.boolean().optional()
});
/**
 * Create comment handler
 */
async function createComment(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Create comment started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createCommentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const commentData = value;
        // Verify document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Create comment
        const commentId = (0, uuid_1.v4)();
        const comment = {
            id: commentId,
            documentId,
            content: commentData.content,
            pageNumber: commentData.pageNumber,
            coordinates: commentData.coordinates,
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            organizationId: commentData.organizationId,
            projectId: commentData.projectId,
            replies: [],
            isResolved: false,
            metadata: commentData.metadata,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('comments', comment);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "comment_created",
            userId: user.id,
            organizationId: commentData.organizationId,
            projectId: commentData.projectId,
            documentId,
            commentId,
            timestamp: new Date().toISOString(),
            details: {
                content: commentData.content.substring(0, 100) + (commentData.content.length > 100 ? "..." : ""),
                pageNumber: commentData.pageNumber
            }
        });
        logger_1.logger.info("Comment created successfully", {
            correlationId,
            commentId,
            documentId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: comment
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create comment failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get comments handler
 */
async function getComments(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    if (!documentId) {
        return (0, cors_1.addCorsHeaders)({
            status: 400,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Document ID is required' }
        }, request);
    }
    logger_1.logger.info("Get comments started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = getCommentsSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { page, limit, pageNumber, resolved } = value;
        // Verify document exists and user has access
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Build query
        let query = 'SELECT * FROM c WHERE c.documentId = @documentId';
        const parameters = [documentId];
        // Add filters
        if (pageNumber !== undefined) {
            query += ' AND c.pageNumber = @pageNumber';
            parameters.push(pageNumber);
        }
        if (resolved !== undefined) {
            query += ' AND c.isResolved = @resolved';
            parameters.push(resolved);
        }
        // Add tenant isolation
        if (user.tenantId) {
            query += ' AND (c.organizationId = @tenantId OR c.createdBy = @userId)';
            parameters.push(user.tenantId, user.id);
        }
        query += ' ORDER BY c.createdAt DESC';
        // Execute query with pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${query} OFFSET ${offset} LIMIT ${limit}`;
        const comments = await database_1.db.queryItems('comments', paginatedQuery, parameters);
        // Get total count
        const countQuery = query.replace('SELECT * FROM c', 'SELECT VALUE COUNT(1) FROM c');
        const countResult = await database_1.db.queryItems('comments', countQuery, parameters);
        const total = Number(countResult[0]) || 0;
        logger_1.logger.info("Comments retrieved successfully", {
            correlationId,
            documentId,
            userId: user.id,
            count: comments.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                comments,
                pagination: {
                    page,
                    limit,
                    total,
                    hasMore: (page * limit) < total
                }
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get comments failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined document comments handler
 */
async function handleDocumentComments(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'POST':
            return await createComment(request, context);
        case 'GET':
            return await getComments(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('document-comments', {
    methods: ['GET', 'POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/comments',
    handler: handleDocumentComments
});
//# sourceMappingURL=document-comments.js.map