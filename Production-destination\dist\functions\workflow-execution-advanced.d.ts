/**
 * Advanced Workflow Execution Function
 * Handles advanced workflow operations like approval, assignment, cancellation, rejection
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Workflow action handler
 */
export declare function executeWorkflowAction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Bulk workflow action handler
 */
export declare function executeBulkWorkflowAction(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
