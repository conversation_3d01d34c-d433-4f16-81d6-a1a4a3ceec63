"use strict";
/**
 * User Models and Interfaces
 * Defines the structure for user-related data models
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.ActivityType = exports.SystemRole = exports.UserStatus = void 0;
var UserStatus;
(function (UserStatus) {
    UserStatus["ACTIVE"] = "ACTIVE";
    UserStatus["INACTIVE"] = "INACTIVE";
    UserStatus["SUSPENDED"] = "SUSPENDED";
    UserStatus["PENDING_VERIFICATION"] = "PENDING_VERIFICATION";
    UserStatus["DELETED"] = "DELETED";
})(UserStatus || (exports.UserStatus = UserStatus = {}));
var SystemRole;
(function (SystemRole) {
    SystemRole["SUPER_ADMIN"] = "SUPER_ADMIN";
    SystemRole["ADMIN"] = "ADMIN";
    SystemRole["USER"] = "USER";
    SystemRole["GUEST"] = "GUEST";
})(SystemRole || (exports.SystemRole = SystemRole = {}));
var ActivityType;
(function (ActivityType) {
    ActivityType["LOGIN"] = "LOGIN";
    ActivityType["LOGOUT"] = "LOGOUT";
    ActivityType["DOCUMENT_CREATED"] = "DOCUMENT_CREATED";
    ActivityType["DOCUMENT_UPDATED"] = "DOCUMENT_UPDATED";
    ActivityType["DOCUMENT_DELETED"] = "DOCUMENT_DELETED";
    ActivityType["DOCUMENT_SHARED"] = "DOCUMENT_SHARED";
    ActivityType["DOCUMENT_SIGNED"] = "DOCUMENT_SIGNED";
    ActivityType["WORKFLOW_CREATED"] = "WORKFLOW_CREATED";
    ActivityType["WORKFLOW_STARTED"] = "WORKFLOW_STARTED";
    ActivityType["WORKFLOW_COMPLETED"] = "WORKFLOW_COMPLETED";
    ActivityType["PROJECT_CREATED"] = "PROJECT_CREATED";
    ActivityType["PROJECT_JOINED"] = "PROJECT_JOINED";
    ActivityType["ORGANIZATION_JOINED"] = "ORGANIZATION_JOINED";
    ActivityType["COMMENT_ADDED"] = "COMMENT_ADDED";
    ActivityType["SEARCH_PERFORMED"] = "SEARCH_PERFORMED";
    ActivityType["SETTINGS_UPDATED"] = "SETTINGS_UPDATED";
    ActivityType["PASSWORD_CHANGED"] = "PASSWORD_CHANGED";
    ActivityType["TWO_FACTOR_ENABLED"] = "TWO_FACTOR_ENABLED";
    ActivityType["API_KEY_CREATED"] = "API_KEY_CREATED";
})(ActivityType || (exports.ActivityType = ActivityType = {}));
//# sourceMappingURL=user.js.map