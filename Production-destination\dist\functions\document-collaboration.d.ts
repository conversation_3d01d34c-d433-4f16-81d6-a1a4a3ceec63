/**
 * Document Collaboration Function
 * Handles document sharing, commenting, and collaborative features
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Share document handler
 */
export declare function shareDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Add comment handler
 */
export declare function addComment(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * List comments handler
 */
export declare function listComments(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
