"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.getUserProfile = getUserProfile;
exports.updateUserProfile = updateUserProfile;
exports.updateUserPreferences = updateUserPreferences;
/**
 * User Management Functions
 * Handles user profile, preferences, and basic user operations
 */
const functions_1 = require("@azure/functions");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Validation schemas
const updateProfileSchema = Joi.object({
    firstName: Joi.string().max(50).optional(),
    lastName: Joi.string().max(50).optional(),
    displayName: Joi.string().max(100).optional(),
    avatarUrl: Joi.string().uri().optional()
});
const updatePreferencesSchema = Joi.object({
    theme: Joi.string().valid('light', 'dark', 'system').optional(),
    language: Joi.string().max(10).optional(),
    timezone: Joi.string().max(50).optional(),
    dateFormat: Joi.string().max(20).optional(),
    notifications: Joi.object({
        email: Joi.boolean().optional(),
        inApp: Joi.boolean().optional(),
        documentUploaded: Joi.boolean().optional(),
        documentProcessed: Joi.boolean().optional(),
        commentAdded: Joi.boolean().optional(),
        mentionedInComment: Joi.boolean().optional(),
        projectInvitation: Joi.boolean().optional(),
        organizationInvitation: Joi.boolean().optional()
    }).optional(),
    dashboard: Joi.object({
        defaultView: Joi.string().valid('recent', 'projects', 'documents').optional(),
        showWelcomeMessage: Joi.boolean().optional(),
        pinnedProjects: Joi.array().items(Joi.string().uuid()).optional(),
        pinnedDocuments: Joi.array().items(Joi.string().uuid()).optional()
    }).optional(),
    documentViewer: Joi.object({
        defaultZoom: Joi.number().min(25).max(500).optional(),
        showAnnotations: Joi.boolean().optional(),
        showComments: Joi.boolean().optional(),
        highlightEntities: Joi.boolean().optional()
    }).optional()
});
/**
 * Get default user preferences
 */
function getDefaultPreferences() {
    return {
        theme: 'system',
        language: 'en',
        timezone: 'UTC',
        dateFormat: 'MM/DD/YYYY',
        notifications: {
            email: true,
            inApp: true,
            documentUploaded: true,
            documentProcessed: true,
            commentAdded: true,
            mentionedInComment: true,
            projectInvitation: true,
            organizationInvitation: true
        },
        dashboard: {
            defaultView: 'recent',
            showWelcomeMessage: true,
            pinnedProjects: [],
            pinnedDocuments: []
        },
        documentViewer: {
            defaultZoom: 100,
            showAnnotations: true,
            showComments: true,
            highlightEntities: true
        }
    };
}
/**
 * Get user profile handler
 */
async function getUserProfile(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Get user profile started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Get user profile from database
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User profile not found" }
            }, request);
        }
        // Ensure preferences exist
        if (!userProfile.preferences) {
            userProfile.preferences = getDefaultPreferences();
        }
        // Remove sensitive fields
        const sanitizedProfile = {
            ...userProfile,
            refreshTokens: undefined,
            _rid: undefined,
            _self: undefined,
            _etag: undefined,
            _attachments: undefined,
            _ts: undefined
        };
        logger_1.logger.info("User profile retrieved successfully", {
            correlationId,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: sanitizedProfile
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get user profile failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update user profile handler
 */
async function updateUserProfile(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update user profile started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateProfileSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const updates = value;
        // Get current user profile
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User profile not found" }
            }, request);
        }
        // Update profile fields
        const updatedProfile = {
            ...userProfile,
            ...updates,
            updatedAt: new Date().toISOString()
        };
        // Update display name if first/last name changed
        if (updates.firstName || updates.lastName) {
            updatedProfile.displayName = `${updatedProfile.firstName} ${updatedProfile.lastName}`.trim();
        }
        // Save updated profile
        await database_1.db.updateItem('users', updatedProfile);
        // Remove sensitive fields from response
        const sanitizedProfile = {
            ...updatedProfile,
            refreshTokens: undefined,
            _rid: undefined,
            _self: undefined,
            _etag: undefined,
            _attachments: undefined,
            _ts: undefined
        };
        logger_1.logger.info("User profile updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(updates)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: sanitizedProfile
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update user profile failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update user preferences handler
 */
async function updateUserPreferences(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update user preferences started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updatePreferencesSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const preferenceUpdates = value;
        // Get current user profile
        const userProfile = await database_1.db.readItem('users', user.id, user.id);
        if (!userProfile) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "User profile not found" }
            }, request);
        }
        // Merge existing preferences with updates
        const currentPreferences = userProfile.preferences || getDefaultPreferences();
        const updatedPreferences = {
            ...currentPreferences,
            ...preferenceUpdates,
            // Deep merge nested objects
            notifications: {
                ...currentPreferences.notifications,
                ...preferenceUpdates.notifications
            },
            dashboard: {
                ...currentPreferences.dashboard,
                ...preferenceUpdates.dashboard
            },
            documentViewer: {
                ...currentPreferences.documentViewer,
                ...preferenceUpdates.documentViewer
            }
        };
        // Update user profile with new preferences
        const updatedProfile = {
            ...userProfile,
            id: userProfile.id,
            preferences: updatedPreferences,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('users', updatedProfile);
        logger_1.logger.info("User preferences updated successfully", {
            correlationId,
            userId: user.id,
            updatedFields: Object.keys(preferenceUpdates)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                preferences: updatedPreferences,
                message: "Preferences updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update user preferences failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Combined user profile handler
 */
async function handleUserProfile(request, context) {
    const method = request.method.toUpperCase();
    switch (method) {
        case 'GET':
            return await getUserProfile(request, context);
        case 'PATCH':
            return await updateUserProfile(request, context);
        case 'OPTIONS':
            return (0, cors_1.handlePreflight)(request) || { status: 200 };
        default:
            return (0, cors_1.addCorsHeaders)({
                status: 405,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Method not allowed' }
            }, request);
    }
}
// Register functions
functions_1.app.http('user-profile', {
    methods: ['GET', 'PATCH', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/profile',
    handler: handleUserProfile
});
functions_1.app.http('user-profile-preferences-update', {
    methods: ['PATCH', 'OPTIONS'],
    authLevel: 'function',
    route: 'users/profile/preferences',
    handler: updateUserPreferences
});
//# sourceMappingURL=user-management.js.map