/**
 * External API Management Function
 * Handles external API connections, authentication, and management
 * Migrated from old-arch/src/integration-service/external-apis/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create API connection handler
 */
export declare function createApiConnection(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Test API connection handler
 */
export declare function testApiConnection(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
