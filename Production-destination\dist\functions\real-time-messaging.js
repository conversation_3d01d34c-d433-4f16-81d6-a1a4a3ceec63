"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.sendMessage = sendMessage;
exports.createChannel = createChannel;
exports.listMessages = listMessages;
exports.getSignalRMetrics = getSignalRMetrics;
/**
 * Real-time Messaging Function
 * Handles real-time chat, messaging, and communication features
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_grid_handlers_1 = require("./event-grid-handlers");
const signalr_1 = require("../shared/services/signalr");
// Message types enum
var MessageType;
(function (MessageType) {
    MessageType["TEXT"] = "TEXT";
    MessageType["FILE"] = "FILE";
    MessageType["IMAGE"] = "IMAGE";
    MessageType["SYSTEM"] = "SYSTEM";
    MessageType["NOTIFICATION"] = "NOTIFICATION";
    MessageType["MENTION"] = "MENTION";
    MessageType["REACTION"] = "REACTION";
})(MessageType || (MessageType = {}));
// Channel types enum
var ChannelType;
(function (ChannelType) {
    ChannelType["DIRECT"] = "DIRECT";
    ChannelType["GROUP"] = "GROUP";
    ChannelType["PROJECT"] = "PROJECT";
    ChannelType["ORGANIZATION"] = "ORGANIZATION";
    ChannelType["DOCUMENT"] = "DOCUMENT";
})(ChannelType || (ChannelType = {}));
// Note: SignalR interfaces and state management are now handled by the enhanced shared service
// Validation schemas
const sendMessageSchema = Joi.object({
    channelId: Joi.string().uuid().required(),
    content: Joi.string().required().max(4000),
    type: Joi.string().valid(...Object.values(MessageType)).default(MessageType.TEXT),
    parentMessageId: Joi.string().uuid().optional(),
    mentions: Joi.array().items(Joi.string().uuid()).optional(),
    attachments: Joi.array().items(Joi.object({
        id: Joi.string().uuid().required(),
        name: Joi.string().required(),
        type: Joi.string().required(),
        size: Joi.number().integer().min(0).required(),
        url: Joi.string().uri().required()
    })).optional(),
    metadata: Joi.object().optional()
});
const createChannelSchema = Joi.object({
    name: Joi.string().required().min(2).max(100),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(ChannelType)).required(),
    participants: Joi.array().items(Joi.string().uuid()).min(1).required(),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    documentId: Joi.string().uuid().optional(),
    isPrivate: Joi.boolean().default(false),
    settings: Joi.object({
        allowFileSharing: Joi.boolean().default(true),
        allowMentions: Joi.boolean().default(true),
        retentionDays: Joi.number().integer().min(1).max(365).optional()
    }).optional()
});
const listMessagesSchema = Joi.object({
    channelId: Joi.string().uuid().required(),
    page: Joi.number().integer().min(1).default(1),
    limit: Joi.number().integer().min(1).max(100).default(50),
    before: Joi.string().uuid().optional(),
    after: Joi.string().uuid().optional(),
    search: Joi.string().max(100).optional()
});
/**
 * Send message handler
 */
async function sendMessage(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Send message started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = sendMessageSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { channelId, content, type, parentMessageId, mentions, attachments, metadata } = value;
        // Get channel and verify access
        const channel = await database_1.db.readItem('channels', channelId, channelId);
        if (!channel) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Channel not found" }
            }, request);
        }
        // Check if user is a participant
        const isParticipant = channel.participants.includes(user.id);
        if (!isParticipant) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to channel" }
            }, request);
        }
        // Create message
        const messageId = (0, uuid_1.v4)();
        const message = {
            id: messageId,
            channelId,
            content,
            type,
            parentMessageId,
            mentions: mentions || [],
            attachments: attachments || [],
            metadata: metadata || {},
            senderId: user.id,
            sentAt: new Date().toISOString(),
            editedAt: null,
            deletedAt: null,
            reactions: [],
            threadCount: 0,
            organizationId: channel.organizationId,
            projectId: channel.projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('messages', message);
        // Update channel last activity
        const updatedChannel = {
            ...channel,
            id: channelId,
            lastMessageId: messageId,
            lastMessageAt: message.sentAt,
            messageCount: (channel.messageCount || 0) + 1,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('channels', updatedChannel);
        // Update parent message thread count if this is a reply
        if (parentMessageId) {
            const parentMessage = await database_1.db.readItem('messages', parentMessageId, parentMessageId);
            if (parentMessage) {
                const updatedParent = {
                    ...parentMessage,
                    id: parentMessageId,
                    threadCount: (parentMessage.threadCount || 0) + 1
                };
                await database_1.db.updateItem('messages', updatedParent);
            }
        }
        // Send notifications to mentioned users
        if (mentions && mentions.length > 0) {
            for (const mentionedUserId of mentions) {
                await database_1.db.createItem('notifications', {
                    id: (0, uuid_1.v4)(),
                    recipientId: mentionedUserId,
                    senderId: user.id,
                    type: 'MESSAGE_MENTION',
                    title: `You were mentioned in ${channel.name}`,
                    message: `${user.name || user.email} mentioned you: ${content.substring(0, 100)}${content.length > 100 ? '...' : ''}`,
                    priority: 'MEDIUM',
                    actionUrl: `/channels/${channelId}/messages/${messageId}`,
                    actionText: 'View Message',
                    channelId,
                    messageId,
                    organizationId: channel.organizationId,
                    projectId: channel.projectId,
                    isRead: false,
                    createdAt: new Date().toISOString(),
                    tenantId: user.tenantId
                });
            }
        }
        // Broadcast message to channel participants (simplified)
        await broadcastMessage(channel, message, user);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "message_sent",
            userId: user.id,
            organizationId: channel.organizationId,
            projectId: channel.projectId,
            channelId,
            messageId,
            timestamp: new Date().toISOString(),
            details: {
                channelName: channel.name,
                messageType: type,
                hasAttachments: (attachments || []).length > 0,
                mentionCount: (mentions || []).length,
                isReply: !!parentMessageId
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Message sent successfully", {
            correlationId,
            messageId,
            channelId,
            userId: user.id,
            type,
            mentionCount: (mentions || []).length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: messageId,
                channelId,
                content,
                type,
                senderId: user.id,
                sentAt: message.sentAt,
                mentions: mentions || [],
                attachments: attachments || [],
                message: "Message sent successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Send message failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Create channel handler
 */
async function createChannel(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create channel started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createChannelSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const channelData = value;
        // Check organization access
        const membershipQuery = 'SELECT * FROM c WHERE c.userId = @userId AND c.organizationId = @orgId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [user.id, channelData.organizationId, 'active']);
        if (memberships.length === 0) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Ensure creator is in participants list
        const participants = Array.from(new Set([...channelData.participants, user.id]));
        // Create channel
        const channelId = (0, uuid_1.v4)();
        const channel = {
            id: channelId,
            name: channelData.name,
            description: channelData.description || "",
            type: channelData.type,
            participants,
            organizationId: channelData.organizationId,
            projectId: channelData.projectId,
            documentId: channelData.documentId,
            isPrivate: channelData.isPrivate,
            settings: channelData.settings || {
                allowFileSharing: true,
                allowMentions: true
            },
            createdBy: user.id,
            createdAt: new Date().toISOString(),
            updatedAt: new Date().toISOString(),
            lastMessageId: null,
            lastMessageAt: null,
            messageCount: 0,
            isActive: true,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('channels', channel);
        // Create channel memberships
        for (const participantId of participants) {
            await database_1.db.createItem('channel-members', {
                id: (0, uuid_1.v4)(),
                channelId,
                userId: participantId,
                role: participantId === user.id ? 'ADMIN' : 'MEMBER',
                joinedAt: new Date().toISOString(),
                lastReadAt: new Date().toISOString(),
                notificationSettings: {
                    muted: false,
                    mentions: true,
                    allMessages: true
                },
                organizationId: channelData.organizationId,
                tenantId: user.tenantId
            });
        }
        // Send welcome message
        const welcomeMessage = {
            id: (0, uuid_1.v4)(),
            channelId,
            content: `Welcome to ${channel.name}! This channel was created by ${user.name || user.email}.`,
            type: MessageType.SYSTEM,
            senderId: 'system',
            sentAt: new Date().toISOString(),
            organizationId: channelData.organizationId,
            projectId: channelData.projectId,
            tenantId: user.tenantId
        };
        await database_1.db.createItem('messages', welcomeMessage);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "channel_created",
            userId: user.id,
            organizationId: channelData.organizationId,
            projectId: channelData.projectId,
            channelId,
            timestamp: new Date().toISOString(),
            details: {
                channelName: channel.name,
                channelType: channel.type,
                participantCount: participants.length,
                isPrivate: channel.isPrivate
            },
            tenantId: user.tenantId
        });
        logger_1.logger.info("Channel created successfully", {
            correlationId,
            channelId,
            userId: user.id,
            organizationId: channelData.organizationId,
            type: channelData.type,
            participantCount: participants.length
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: channelId,
                name: channel.name,
                type: channel.type,
                participants,
                organizationId: channelData.organizationId,
                projectId: channelData.projectId,
                isPrivate: channel.isPrivate,
                message: "Channel created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create channel failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * List messages handler
 */
async function listMessages(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("List messages started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate query parameters
        const queryParams = Object.fromEntries(request.query.entries());
        const { error, value } = listMessagesSchema.validate(queryParams);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const { channelId, page, limit, before, after, search } = value;
        // Get channel and verify access
        const channel = await database_1.db.readItem('channels', channelId, channelId);
        if (!channel) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Channel not found" }
            }, request);
        }
        // Check if user is a participant
        const isParticipant = channel.participants.includes(user.id);
        if (!isParticipant) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to channel" }
            }, request);
        }
        // Build query
        let queryText = 'SELECT * FROM c WHERE c.channelId = @channelId AND c.deletedAt IS NULL';
        const parameters = [channelId];
        if (before) {
            queryText += ' AND c.sentAt < (SELECT VALUE m.sentAt FROM messages m WHERE m.id = @before)[0]';
            parameters.push(before);
        }
        if (after) {
            queryText += ' AND c.sentAt > (SELECT VALUE m.sentAt FROM messages m WHERE m.id = @after)[0]';
            parameters.push(after);
        }
        if (search) {
            queryText += ' AND CONTAINS(LOWER(c.content), LOWER(@search))';
            parameters.push(search);
        }
        // Add ordering
        queryText += ' ORDER BY c.sentAt DESC';
        // Add pagination
        const offset = (page - 1) * limit;
        const paginatedQuery = `${queryText} OFFSET ${offset} LIMIT ${limit}`;
        // Execute query
        const messages = await database_1.db.queryItems('messages', paginatedQuery, parameters);
        // Enrich messages with sender information
        const enrichedMessages = await Promise.all(messages.map(async (message) => {
            let senderName = 'System';
            let senderAvatar = null;
            if (message.senderId && message.senderId !== 'system') {
                try {
                    const sender = await database_1.db.readItem('users', message.senderId, message.senderId);
                    if (sender) {
                        senderName = sender.name || sender.email;
                        senderAvatar = sender.avatar;
                    }
                }
                catch (error) {
                    // Sender might not exist
                }
            }
            return {
                ...message,
                senderName,
                senderAvatar
            };
        }));
        // Update user's last read timestamp
        try {
            const membershipQuery = 'SELECT * FROM c WHERE c.channelId = @channelId AND c.userId = @userId';
            const memberships = await database_1.db.queryItems('channel-members', membershipQuery, [channelId, user.id]);
            if (memberships.length > 0) {
                const membership = memberships[0];
                const updatedMembership = {
                    ...membership,
                    id: membership.id,
                    lastReadAt: new Date().toISOString()
                };
                await database_1.db.updateItem('channel-members', updatedMembership);
            }
        }
        catch (error) {
            // Non-critical error
        }
        logger_1.logger.info("Messages listed successfully", {
            correlationId,
            channelId,
            userId: user.id,
            count: messages.length,
            page,
            limit
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                channelId,
                messages: enrichedMessages.reverse(), // Return in chronological order
                hasMore: messages.length === limit,
                page,
                limit
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("List messages failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Enhanced broadcast message to channel participants
 */
async function broadcastMessage(channel, message, sender) {
    const startTime = Date.now();
    try {
        logger_1.logger.info("Broadcasting message to channel participants", {
            channelId: channel.id,
            messageId: message.id,
            participantCount: channel.participants.length
        });
        // Broadcast to SignalR group using enhanced shared service
        const groupName = `channel-${channel.id}`;
        await signalr_1.signalREnhanced.sendToGroup(groupName, {
            target: 'ReceiveMessage',
            arguments: [{
                    id: message.id,
                    channelId: channel.id,
                    content: message.content,
                    type: message.type,
                    senderId: message.senderId,
                    senderName: sender.name || sender.email,
                    sentAt: message.sentAt,
                    mentions: message.mentions || [],
                    attachments: message.attachments || [],
                    parentMessageId: message.parentMessageId,
                    metadata: message.metadata || {}
                }]
        });
        // Send push notifications to offline participants
        for (const participantId of channel.participants) {
            if (participantId !== sender.id) {
                await sendPushNotification(participantId, {
                    title: `New message in ${channel.name}`,
                    body: `${sender.name || sender.email}: ${message.content.substring(0, 100)}`,
                    data: {
                        channelId: channel.id,
                        messageId: message.id,
                        type: 'new_message'
                    }
                });
            }
        }
        // Publish metrics event using Event Grid
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PERFORMANCE_ALERT, 'messaging/broadcast', {
            service: 'real-time-messaging',
            eventType: 'message_broadcast',
            channelId: channel.id,
            messageId: message.id,
            participantCount: channel.participants.length,
            processingTime: Date.now() - startTime,
            timestamp: new Date().toISOString()
        });
    }
    catch (error) {
        logger_1.logger.error("Error broadcasting message", {
            channelId: channel.id,
            messageId: message.id,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
// Register functions
functions_1.app.http('message-send', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'messages',
    handler: sendMessage
});
functions_1.app.http('channel-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'channels',
    handler: createChannel
});
functions_1.app.http('message-list', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'channels/{channelId}/messages',
    handler: listMessages
});
/**
 * Enhanced SignalR negotiate function using shared service
 * Provides SignalR connection info for clients with enhanced features
 */
async function signalRNegotiate(request, context) {
    try {
        // Authenticate request
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized' }
            }, request);
        }
        const userId = authResult.user?.id;
        if (!userId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'User ID required' }
            }, request);
        }
        // Generate SignalR connection info
        const hubName = process.env.SIGNALR_HUB_NAME || 'hepztech';
        const connectionString = process.env.SIGNALR_CONNECTION_STRING;
        if (!connectionString) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'SignalR not configured' }
            }, request);
        }
        // Extract endpoint and access key from connection string
        const endpointMatch = connectionString.match(/Endpoint=([^;]+)/);
        const keyMatch = connectionString.match(/AccessKey=([^;]+)/);
        if (!endpointMatch || !keyMatch) {
            return (0, cors_1.addCorsHeaders)({
                status: 500,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Invalid SignalR connection string' }
            }, request);
        }
        const endpoint = endpointMatch[1];
        const accessKey = keyMatch[1];
        // Generate access token for user
        const token = generateSignalRAccessToken(userId, hubName, accessKey);
        const connectionId = `conn-${Date.now()}-${Math.random().toString(36).substring(2, 11)}`;
        const connectionInfo = {
            url: `${endpoint}/client/?hub=${hubName}`,
            accessToken: token,
            connectionId
        };
        // Register connection with enhanced shared service
        await signalr_1.signalREnhanced.registerConnection(connectionId, userId, {
            endpoint,
            hubName,
            userAgent: request.headers.get('user-agent') || 'unknown',
            ipAddress: request.headers.get('x-forwarded-for') || 'unknown'
        });
        // Publish connection event
        await (0, event_grid_handlers_1.publishEvent)(event_grid_handlers_1.EventType.PERFORMANCE_ALERT, 'signalr/connection', {
            service: 'signalr',
            eventType: 'connection_negotiated',
            connectionId,
            userId,
            endpoint,
            timestamp: new Date().toISOString()
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: connectionInfo
        }, request);
    }
    catch (error) {
        logger_1.logger.error('SignalR negotiate failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Failed to negotiate SignalR connection' }
        }, request);
    }
}
/**
 * SignalR broadcast function
 * Broadcasts messages to SignalR clients
 */
async function signalRBroadcast(request, context) {
    try {
        // Authenticate request
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized' }
            }, request);
        }
        const body = await request.json();
        const { target, message, userId, groupName, connectionId } = body;
        if (!target || !message) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Target and message are required' }
            }, request);
        }
        // Prepare SignalR message
        const signalRMessage = {
            target,
            arguments: [message]
        };
        // Determine broadcast scope using enhanced shared service
        let broadcastResult;
        if (connectionId) {
            // Send to specific connection (not directly supported, send to user instead)
            const connection = signalr_1.signalREnhanced.getConnection(connectionId);
            if (connection) {
                broadcastResult = await signalr_1.signalREnhanced.sendToUser(connection.userId, signalRMessage);
            }
            else {
                broadcastResult = { success: false, error: 'Connection not found' };
            }
        }
        else if (userId) {
            // Send to specific user
            broadcastResult = await signalr_1.signalREnhanced.sendToUser(userId, signalRMessage);
        }
        else if (groupName) {
            // Send to group
            broadcastResult = await signalr_1.signalREnhanced.sendToGroup(groupName, signalRMessage);
        }
        else {
            // Broadcast to all
            broadcastResult = await signalr_1.signalREnhanced.broadcast(signalRMessage);
        }
        // Log broadcast
        await database_1.db.createItem('signalr-broadcasts', {
            id: `broadcast-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            target,
            message,
            userId,
            groupName,
            connectionId,
            sentBy: authResult.user?.id,
            sentAt: new Date().toISOString(),
            result: broadcastResult
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: 'Broadcast sent successfully',
                result: broadcastResult
            }
        }, request);
    }
    catch (error) {
        logger_1.logger.error('SignalR broadcast failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Failed to broadcast message' }
        }, request);
    }
}
/**
 * SignalR group management function
 * Manages SignalR groups (join/leave)
 */
async function signalRGroupManagement(request, context) {
    try {
        // Authenticate request
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized' }
            }, request);
        }
        const body = await request.json();
        const { action, groupName, connectionId, userId } = body;
        if (!action || !groupName || (!connectionId && !userId)) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Action, groupName, and connectionId or userId are required' }
            }, request);
        }
        // Perform group action using enhanced shared service
        let result;
        switch (action.toLowerCase()) {
            case 'join':
                if (connectionId) {
                    result = await signalr_1.signalREnhanced.addToGroup(connectionId, groupName);
                }
                else {
                    // For user-based group management, we need to find their connections
                    const userConnections = signalr_1.signalREnhanced.getUserConnections(userId);
                    if (userConnections.length > 0) {
                        // Add all user connections to the group
                        const results = await Promise.all(userConnections.map(conn => signalr_1.signalREnhanced.addToGroup(conn.connectionId, groupName)));
                        result = { success: results.every(r => r), action: 'added', groupName, userId };
                    }
                    else {
                        result = { success: false, error: 'No active connections for user' };
                    }
                }
                break;
            case 'leave':
                if (connectionId) {
                    result = await signalr_1.signalREnhanced.removeFromGroup(connectionId, groupName);
                }
                else {
                    // For user-based group management, remove all user connections from group
                    const userConnections = signalr_1.signalREnhanced.getUserConnections(userId);
                    if (userConnections.length > 0) {
                        const results = await Promise.all(userConnections.map(conn => signalr_1.signalREnhanced.removeFromGroup(conn.connectionId, groupName)));
                        result = { success: results.every(r => r), action: 'removed', groupName, userId };
                    }
                    else {
                        result = { success: false, error: 'No active connections for user' };
                    }
                }
                break;
            default:
                return (0, cors_1.addCorsHeaders)({
                    status: 400,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: 'Invalid action. Use "join" or "leave"' }
                }, request);
        }
        // Log group management
        await database_1.db.createItem('signalr-group-management', {
            id: `group-${Date.now()}-${Math.random().toString(36).substr(2, 9)}`,
            action,
            groupName,
            connectionId,
            userId,
            managedBy: authResult.user?.id,
            managedAt: new Date().toISOString(),
            result
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                message: `Successfully ${action}ed ${connectionId ? 'connection' : 'user'} ${action === 'join' ? 'to' : 'from'} group`,
                result
            }
        }, request);
    }
    catch (error) {
        logger_1.logger.error('SignalR group management failed', {
            error: error instanceof Error ? error.message : String(error)
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: 'Failed to manage group membership' }
        }, request);
    }
}
// Helper functions for SignalR operations
function generateSignalRAccessToken(userId, hubName, accessKey) {
    try {
        // Production JWT token generation for Azure SignalR Service
        const jwt = require('jsonwebtoken');
        const now = Math.floor(Date.now() / 1000);
        const payload = {
            aud: `https://${process.env.SIGNALR_HUB_NAME}.service.signalr.net/client/?hub=${hubName}`,
            iss: null,
            exp: now + 3600, // 1 hour expiration
            iat: now,
            nbf: now,
            sub: userId,
            role: ['webpubsub.sendToGroup', 'webpubsub.joinLeaveGroup']
        };
        // Sign JWT with SignalR access key
        const token = jwt.sign(payload, accessKey, {
            algorithm: 'HS256',
            header: {
                typ: 'JWT',
                alg: 'HS256'
            }
        });
        logger_1.logger.info('SignalR access token generated', {
            userId,
            hubName,
            expiresAt: new Date((now + 3600) * 1000).toISOString()
        });
        return token;
    }
    catch (error) {
        logger_1.logger.error('Failed to generate SignalR access token', {
            error: error instanceof Error ? error.message : String(error),
            userId,
            hubName
        });
        throw new Error('Failed to generate SignalR access token');
    }
}
/**
 * Enhanced SignalR helper functions
 */
// Note: Connection registration is now handled by the enhanced shared SignalR service
async function sendPushNotification(userId, notification) {
    try {
        // Store notification for later delivery
        await database_1.db.createItem('push-notifications', {
            id: `push-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
            userId,
            title: notification.title,
            body: notification.body,
            data: notification.data,
            createdAt: new Date().toISOString(),
            delivered: false
        });
        logger_1.logger.debug('Push notification queued', { userId, title: notification.title });
    }
    catch (error) {
        logger_1.logger.error('Failed to queue push notification', {
            userId,
            error: error instanceof Error ? error.message : String(error)
        });
    }
}
async function checkUserOnlineStatus(userId) {
    try {
        // Check if user has any active SignalR connections using enhanced service
        const userConnections = signalr_1.signalREnhanced.getUserConnections(userId);
        return userConnections.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check user online status', {
            userId,
            error: error instanceof Error ? error.message : String(error)
        });
        return false;
    }
}
/**
 * Get SignalR metrics from enhanced shared service
 */
function getSignalRMetrics() {
    return signalr_1.signalREnhanced.getMetrics();
}
// Register SignalR functions
functions_1.app.http('signalr-negotiate', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'signalr/negotiate',
    handler: signalRNegotiate
});
functions_1.app.http('signalr-broadcast', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'signalr/broadcast',
    handler: signalRBroadcast
});
functions_1.app.http('signalr-groups', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'signalr/groups',
    handler: signalRGroupManagement
});
//# sourceMappingURL=real-time-messaging.js.map