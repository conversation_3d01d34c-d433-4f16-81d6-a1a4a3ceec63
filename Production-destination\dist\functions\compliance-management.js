"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createComplianceAssessment = createComplianceAssessment;
exports.updateComplianceStatus = updateComplianceStatus;
/**
 * Compliance Management Function
 * Handles compliance requirements, auditing, and regulatory compliance
 * Migrated from old-arch/src/compliance-service/management/index.ts
 */
const functions_1 = require("@azure/functions");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Compliance types and enums
var ComplianceFramework;
(function (ComplianceFramework) {
    ComplianceFramework["GDPR"] = "GDPR";
    ComplianceFramework["HIPAA"] = "HIPAA";
    ComplianceFramework["SOX"] = "SOX";
    ComplianceFramework["PCI_DSS"] = "PCI_DSS";
    ComplianceFramework["ISO_27001"] = "ISO_27001";
    ComplianceFramework["SOC2"] = "SOC2";
    ComplianceFramework["CCPA"] = "CCPA";
    ComplianceFramework["FERPA"] = "FERPA";
})(ComplianceFramework || (ComplianceFramework = {}));
var ComplianceStatus;
(function (ComplianceStatus) {
    ComplianceStatus["COMPLIANT"] = "COMPLIANT";
    ComplianceStatus["NON_COMPLIANT"] = "NON_COMPLIANT";
    ComplianceStatus["PENDING_REVIEW"] = "PENDING_REVIEW";
    ComplianceStatus["IN_PROGRESS"] = "IN_PROGRESS";
    ComplianceStatus["EXEMPT"] = "EXEMPT";
})(ComplianceStatus || (ComplianceStatus = {}));
var RiskLevel;
(function (RiskLevel) {
    RiskLevel["LOW"] = "LOW";
    RiskLevel["MEDIUM"] = "MEDIUM";
    RiskLevel["HIGH"] = "HIGH";
    RiskLevel["CRITICAL"] = "CRITICAL";
})(RiskLevel || (RiskLevel = {}));
// Validation schemas
const createComplianceAssessmentSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    framework: Joi.string().valid(...Object.values(ComplianceFramework)).required(),
    organizationId: Joi.string().uuid().required(),
    scope: Joi.object({
        departments: Joi.array().items(Joi.string()).optional(),
        systems: Joi.array().items(Joi.string()).optional(),
        dataTypes: Joi.array().items(Joi.string()).optional(),
        geographies: Joi.array().items(Joi.string()).optional()
    }).optional(),
    requirements: Joi.array().items(Joi.object({
        id: Joi.string().required(),
        title: Joi.string().required(),
        description: Joi.string().required(),
        category: Joi.string().required(),
        mandatory: Joi.boolean().default(true),
        evidence: Joi.array().items(Joi.string()).optional()
    })).required(),
    schedule: Joi.object({
        startDate: Joi.string().isoDate().required(),
        endDate: Joi.string().isoDate().required(),
        reviewFrequency: Joi.string().valid('monthly', 'quarterly', 'annually').default('annually')
    }).required(),
    assignees: Joi.array().items(Joi.object({
        userId: Joi.string().uuid().required(),
        role: Joi.string().valid('owner', 'reviewer', 'contributor').required()
    })).min(1).required()
});
const updateComplianceStatusSchema = Joi.object({
    assessmentId: Joi.string().uuid().required(),
    requirementId: Joi.string().required(),
    status: Joi.string().valid(...Object.values(ComplianceStatus)).required(),
    evidence: Joi.array().items(Joi.object({
        type: Joi.string().valid('document', 'screenshot', 'policy', 'procedure', 'certificate').required(),
        title: Joi.string().required(),
        description: Joi.string().optional(),
        url: Joi.string().uri().optional(),
        uploadedBy: Joi.string().uuid().required(),
        uploadedAt: Joi.string().isoDate().required()
    })).optional(),
    notes: Joi.string().max(1000).optional(),
    riskLevel: Joi.string().valid(...Object.values(RiskLevel)).optional(),
    remediation: Joi.object({
        required: Joi.boolean().default(false),
        dueDate: Joi.string().isoDate().optional(),
        assignedTo: Joi.string().uuid().optional(),
        description: Joi.string().optional(),
        priority: Joi.string().valid('low', 'medium', 'high', 'critical').optional()
    }).optional()
});
/**
 * Create compliance assessment handler
 */
async function createComplianceAssessment(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create compliance assessment started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createComplianceAssessmentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const assessmentRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(assessmentRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check compliance access
        const hasComplianceAccess = await checkComplianceAccess(user, assessmentRequest.organizationId);
        if (!hasComplianceAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to compliance management" }
            }, request);
        }
        // Create compliance assessment
        const assessmentId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const assessment = {
            id: assessmentId,
            name: assessmentRequest.name,
            description: assessmentRequest.description,
            framework: assessmentRequest.framework,
            status: ComplianceStatus.IN_PROGRESS,
            organizationId: assessmentRequest.organizationId,
            scope: {
                departments: [],
                systems: [],
                dataTypes: [],
                geographies: [],
                ...assessmentRequest.scope
            },
            requirements: assessmentRequest.requirements.map(req => ({
                ...req,
                mandatory: req.mandatory !== false,
                status: ComplianceStatus.PENDING_REVIEW,
                evidence: []
            })),
            schedule: {
                ...assessmentRequest.schedule,
                reviewFrequency: assessmentRequest.schedule.reviewFrequency || 'annually',
                nextReview: calculateNextReview(assessmentRequest.schedule.endDate, assessmentRequest.schedule.reviewFrequency || 'annually')
            },
            assignees: assessmentRequest.assignees.map(assignee => ({
                ...assignee,
                assignedAt: now
            })),
            statistics: {
                totalRequirements: assessmentRequest.requirements.length,
                compliantRequirements: 0,
                nonCompliantRequirements: 0,
                pendingRequirements: assessmentRequest.requirements.length,
                compliancePercentage: 0,
                lastUpdated: now
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('compliance-assessments', assessment);
        // Create notifications for assignees
        await notifyAssignees(assessment, 'assessment_created');
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "compliance_assessment_created",
            userId: user.id,
            organizationId: assessmentRequest.organizationId,
            timestamp: now,
            details: {
                assessmentId,
                assessmentName: assessmentRequest.name,
                framework: assessmentRequest.framework,
                requirementCount: assessmentRequest.requirements.length,
                assigneeCount: assessmentRequest.assignees.length
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ComplianceAssessmentCreated',
            aggregateId: assessmentId,
            aggregateType: 'ComplianceAssessment',
            version: 1,
            data: {
                assessment,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: assessmentRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Compliance assessment created successfully", {
            correlationId,
            assessmentId,
            assessmentName: assessmentRequest.name,
            framework: assessmentRequest.framework,
            requirementCount: assessmentRequest.requirements.length,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: assessmentId,
                name: assessmentRequest.name,
                framework: assessmentRequest.framework,
                status: ComplianceStatus.IN_PROGRESS,
                organizationId: assessmentRequest.organizationId,
                requirementCount: assessmentRequest.requirements.length,
                assigneeCount: assessmentRequest.assignees.length,
                schedule: assessment.schedule,
                statistics: assessment.statistics,
                createdAt: now,
                message: "Compliance assessment created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create compliance assessment failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Update compliance status handler
 */
async function updateComplianceStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Update compliance status started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = updateComplianceStatusSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const updateRequest = value;
        // Get compliance assessment
        const assessment = await database_1.db.readItem('compliance-assessments', updateRequest.assessmentId, updateRequest.assessmentId);
        if (!assessment) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Compliance assessment not found" }
            }, request);
        }
        const assessmentData = assessment;
        // Check access
        const hasAccess = await checkComplianceAccess(user, assessmentData.organizationId);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to compliance assessment" }
            }, request);
        }
        // Find and update requirement
        const requirementIndex = assessmentData.requirements.findIndex((req) => req.id === updateRequest.requirementId);
        if (requirementIndex === -1) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Requirement not found" }
            }, request);
        }
        const now = new Date().toISOString();
        // Update requirement
        assessmentData.requirements[requirementIndex] = {
            ...assessmentData.requirements[requirementIndex],
            status: updateRequest.status,
            evidence: updateRequest.evidence || assessmentData.requirements[requirementIndex].evidence,
            lastReviewed: now,
            reviewedBy: user.id,
            notes: updateRequest.notes,
            riskLevel: updateRequest.riskLevel,
            remediation: updateRequest.remediation
        };
        // Recalculate statistics
        const statistics = calculateComplianceStatistics(assessmentData.requirements);
        assessmentData.statistics = {
            ...statistics,
            lastUpdated: now
        };
        // Update overall assessment status
        assessmentData.status = determineOverallStatus(statistics);
        assessmentData.updatedAt = now;
        await database_1.db.updateItem('compliance-assessments', assessmentData);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "compliance_status_updated",
            userId: user.id,
            organizationId: assessmentData.organizationId,
            timestamp: now,
            details: {
                assessmentId: updateRequest.assessmentId,
                requirementId: updateRequest.requirementId,
                oldStatus: assessmentData.requirements[requirementIndex].status,
                newStatus: updateRequest.status,
                riskLevel: updateRequest.riskLevel,
                hasEvidence: !!updateRequest.evidence?.length
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'ComplianceStatusUpdated',
            aggregateId: updateRequest.assessmentId,
            aggregateType: 'ComplianceAssessment',
            version: 1,
            data: {
                assessmentId: updateRequest.assessmentId,
                requirementId: updateRequest.requirementId,
                status: updateRequest.status,
                riskLevel: updateRequest.riskLevel,
                updatedBy: user.id
            },
            userId: user.id,
            organizationId: assessmentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Compliance status updated successfully", {
            correlationId,
            assessmentId: updateRequest.assessmentId,
            requirementId: updateRequest.requirementId,
            status: updateRequest.status,
            updatedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                assessmentId: updateRequest.assessmentId,
                requirementId: updateRequest.requirementId,
                status: updateRequest.status,
                statistics: assessmentData.statistics,
                overallStatus: assessmentData.status,
                updatedAt: now,
                message: "Compliance status updated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Update compliance status failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkComplianceAccess(user, organizationId) {
    try {
        // Check if user has admin or compliance role
        if (user.roles?.includes('admin') || user.roles?.includes('compliance_officer')) {
            return true;
        }
        // Check organization-level permissions
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, user.id, 'ACTIVE']);
        if (memberships.length > 0) {
            const membership = memberships[0];
            return membership.role === 'OWNER' || membership.role === 'ADMIN';
        }
        return false;
    }
    catch (error) {
        logger_1.logger.error('Failed to check compliance access', { error, userId: user.id, organizationId });
        return false;
    }
}
function calculateNextReview(endDate, frequency) {
    const end = new Date(endDate);
    const nextReview = new Date(end);
    switch (frequency) {
        case 'monthly':
            nextReview.setMonth(nextReview.getMonth() + 1);
            break;
        case 'quarterly':
            nextReview.setMonth(nextReview.getMonth() + 3);
            break;
        case 'annually':
        default:
            nextReview.setFullYear(nextReview.getFullYear() + 1);
            break;
    }
    return nextReview.toISOString();
}
function calculateComplianceStatistics(requirements) {
    const total = requirements.length;
    const compliant = requirements.filter(req => req.status === ComplianceStatus.COMPLIANT).length;
    const nonCompliant = requirements.filter(req => req.status === ComplianceStatus.NON_COMPLIANT).length;
    const pending = requirements.filter(req => req.status === ComplianceStatus.PENDING_REVIEW || req.status === ComplianceStatus.IN_PROGRESS).length;
    return {
        totalRequirements: total,
        compliantRequirements: compliant,
        nonCompliantRequirements: nonCompliant,
        pendingRequirements: pending,
        compliancePercentage: total > 0 ? Math.round((compliant / total) * 100) : 0
    };
}
function determineOverallStatus(statistics) {
    if (statistics.pendingRequirements > 0) {
        return ComplianceStatus.IN_PROGRESS;
    }
    if (statistics.nonCompliantRequirements > 0) {
        return ComplianceStatus.NON_COMPLIANT;
    }
    if (statistics.compliantRequirements === statistics.totalRequirements) {
        return ComplianceStatus.COMPLIANT;
    }
    return ComplianceStatus.PENDING_REVIEW;
}
async function notifyAssignees(assessment, eventType) {
    try {
        for (const assignee of assessment.assignees) {
            await database_1.db.createItem('notifications', {
                id: (0, uuid_1.v4)(),
                userId: assignee.userId,
                type: 'compliance_assignment',
                title: `Compliance Assessment: ${assessment.name}`,
                message: `You have been assigned to the ${assessment.framework} compliance assessment.`,
                data: {
                    assessmentId: assessment.id,
                    assessmentName: assessment.name,
                    framework: assessment.framework,
                    role: assignee.role,
                    eventType
                },
                read: false,
                createdAt: new Date().toISOString(),
                tenantId: assessment.tenantId
            });
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to notify assignees', { error, assessmentId: assessment.id });
    }
}
// Register functions
functions_1.app.http('compliance-assessment-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'compliance/assessments',
    handler: createComplianceAssessment
});
functions_1.app.http('compliance-status-update', {
    methods: ['PUT', 'OPTIONS'],
    authLevel: 'function',
    route: 'compliance/status',
    handler: updateComplianceStatus
});
//# sourceMappingURL=compliance-management.js.map