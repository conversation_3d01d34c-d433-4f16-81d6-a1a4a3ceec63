{"version": 3, "file": "document-specialized-processing.js", "sourceRoot": "", "sources": ["../../src/functions/document-specialized-processing.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAwFA,gEAqNC;AA7SD;;;GAGG;AACH,gDAAyF;AACzF,sDAAwD;AACxD,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,qBAAqB;AACrB,MAAM,2BAA2B,GAAG,GAAG,CAAC,MAAM,CAAC;IAC7C,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC1C,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,SAAS,EAAE,SAAS,EAAE,UAAU,EAAE,cAAc,EAAE,YAAY,EAAE,KAAK,EAAE,iBAAiB,CAAC,CAAC,QAAQ,EAAE;IACrI,eAAe,EAAE,GAAG,CAAC,MAAM,CAAC;QAC1B,cAAc,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC3C,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC7C,eAAe,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,IAAI,CAAC;QAC5C,eAAe,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC7C,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,mBAAmB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;KAClD,CAAC,CAAC,QAAQ,EAAE;IACb,aAAa,EAAE,GAAG,CAAC,MAAM,CAAC;QACxB,cAAc,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,CAAC,QAAQ,EAAE;QAC1D,eAAe,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,QAAQ,EAAE;QACvC,gBAAgB,EAAE,GAAG,CAAC,OAAO,EAAE,CAAC,OAAO,CAAC,KAAK,CAAC;QAC9C,oBAAoB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;KAC9C,CAAC,CAAC,QAAQ,EAAE;IACb,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;CAC1C,CAAC,CAAC;AAoDH;;GAEG;AACI,KAAK,UAAU,0BAA0B,CAAC,OAAoB,EAAE,OAA0B;IAC/F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,yCAAyC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAE1E,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,2BAA2B,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEpE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,iBAAiB,GAAiC,KAAK,CAAC;QAC9D,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAC7B,MAAM,YAAY,GAAG,IAAA,SAAM,GAAE,CAAC;QAE9B,eAAe;QACf,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,iBAAiB,CAAC,UAAU,EAAE,iBAAiB,CAAC,UAAU,CAAC,CAAC;QAC5G,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QACF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QACF,MAAM,UAAU,GAAG,eAAe,CAAC,aAAa,CAAE,QAAgB,CAAC,QAAQ,CAAC,CAAC;QAE7E,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,QAAQ,EAAE,CAAC;QACrD,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;YACzC,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,qCAAqC,EAAE;aAC3D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;QAEjF,2DAA2D;QAC3D,MAAM,mBAAmB,GAAG,MAAM,4BAA4B,CAC5D,cAAc,EACd,iBAAiB,CAAC,YAAY,CAC/B,CAAC;QAEF,uCAAuC;QACvC,MAAM,eAAe,GAAG,MAAM,gCAAgC,CAC5D,mBAAmB,EACnB,iBAAiB,CAAC,YAAY,EAC9B,iBAAiB,CAAC,eAAe,IAAI,EAAE,EACvC,iBAAiB,CAAC,aAAa,IAAI,EAAE,CACtC,CAAC;QAEF,4BAA4B;QAC5B,MAAM,eAAe,GAAG,MAAM,uBAAuB,CACnD,eAAe,EACf,iBAAiB,CAAC,YAAY,EAC9B,iBAAiB,CAAC,eAAe,IAAI,EAAE,EACvC,IAAI,CACL,CAAC;QAEF,yCAAyC;QACzC,MAAM,QAAQ,GAAkC;YAC9C,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,YAAY;YACZ,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,aAAa,EAAE;gBACb,gBAAgB,EAAE,eAAe,CAAC,gBAAgB;gBAClD,SAAS,EAAE,eAAe,CAAC,SAAS;gBACpC,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,IAAI,EAAE;gBAC5C,UAAU,EAAE,mBAAmB,CAAC,UAAU,IAAI,EAAE;gBAChD,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,IAAI,EAAE;gBAC5C,QAAQ,EAAE,mBAAmB,CAAC,QAAQ,IAAI,EAAE;aAC7C;YACD,oBAAoB,EAAE;gBACpB,cAAc,EAAE,eAAe,CAAC,cAAc;gBAC9C,UAAU,EAAE,eAAe,CAAC,UAAU;gBACtC,SAAS,EAAE,eAAe,CAAC,SAAS;gBACpC,UAAU,EAAE,eAAe,CAAC,UAAU;gBACtC,QAAQ,EAAE,eAAe,CAAC,QAAQ;aACnC;YACD,eAAe;YACf,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YACtC,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,sCAAsC;QACtC,MAAM,aAAE,CAAC,UAAU,CAAC,gCAAgC,EAAE;YACpD,EAAE,EAAE,YAAY;YAChB,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,OAAO,EAAE,QAAQ;YACjB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,mDAAmD;QACnD,MAAM,eAAe,GAAG;YACtB,GAAG,QAAQ;YACX,EAAE,EAAG,QAAgB,CAAC,EAAE;YACxB,qBAAqB,EAAE;gBACrB,IAAI,EAAE,iBAAiB,CAAC,YAAY;gBACpC,YAAY;gBACZ,aAAa,EAAE,QAAQ,CAAC,aAAa;gBACrC,oBAAoB,EAAE,QAAQ,CAAC,oBAAoB;gBACnD,WAAW,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;aACtC;YACD,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;SACnB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,iCAAiC;YACvC,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,iBAAiB,CAAC,cAAc;YAChD,SAAS,EAAE,iBAAiB,CAAC,SAAS;YACtC,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,YAAY;YACZ,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE;gBACP,YAAY,EAAE,iBAAiB,CAAC,YAAY;gBAC5C,yBAAyB,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,MAAM;gBAC/E,kBAAkB,EAAE,eAAe,CAAC,SAAS,CAAC,MAAM;gBACpD,iBAAiB,EAAE,eAAe,CAAC,SAAS,CAAC,MAAM;gBACnD,oBAAoB,EAAE,eAAe,CAAC,YAAY,CAAC,MAAM;gBACzD,cAAc,EAAE,QAAQ,CAAC,cAAc;aACxC;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,eAAM,CAAC,IAAI,CAAC,2CAA2C,EAAE;YACvD,aAAa;YACb,UAAU,EAAE,iBAAiB,CAAC,UAAU;YACxC,YAAY;YACZ,YAAY,EAAE,iBAAiB,CAAC,YAAY;YAC5C,cAAc,EAAE,QAAQ,CAAC,cAAc;YACvC,eAAe,EAAE,MAAM,CAAC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,CAAC,MAAM;SACtE,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,QAAQ;SACnB,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,wCAAwC,EAAE;YACrD,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,4BAA4B,CAAC,cAAsB,EAAE,YAAoB;IACtF,sCAAsC;IACtC,uEAAuE;IAEvE,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;QAC/C,YAAY,EAAE,cAAc,CAAC,MAAM;QACnC,YAAY;KACb,CAAC,CAAC;IAEH,4BAA4B;IAC5B,OAAO;QACL,UAAU,EAAE,IAAI;QAChB,aAAa,EAAE;YACb,gBAAgB,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,UAAU,EAAE,IAAI,EAAE;YAC7D,cAAc,EAAE,EAAE,KAAK,EAAE,YAAY,EAAE,UAAU,EAAE,IAAI,EAAE;YACzD,cAAc,EAAE,EAAE,KAAK,EAAE,UAAU,EAAE,UAAU,EAAE,IAAI,EAAE;YACvD,aAAa,EAAE,EAAE,KAAK,EAAE,kBAAkB,EAAE,UAAU,EAAE,IAAI,EAAE;SAC/D;QACD,MAAM,EAAE,EAAE;QACV,QAAQ,EAAE,EAAE;QACZ,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAE;QACZ,QAAQ,EAAE,EAAE;KACb,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,gCAAgC,CAC7C,mBAAwB,EACxB,YAAoB,EACpB,eAAoB,EACpB,aAAkB;IAElB,MAAM,eAAe,GAAG;QACtB,gBAAgB,EAAE,EAAyB;QAC3C,SAAS,EAAE,EAAW;QACtB,cAAc,EAAE,EAAE,IAAI,EAAE,YAAY,EAAE,UAAU,EAAE,mBAAmB,CAAC,UAAU,EAAE;QAClF,UAAU,EAAE,EAAE,OAAO,EAAE,IAAI,EAAE,MAAM,EAAE,EAAc,EAAE,QAAQ,EAAE,EAAc,EAAE;QAC/E,SAAS,EAAE,EAAW;QACtB,UAAU,EAAE,EAAE;QACd,QAAQ,EAAE,EAAW;KACtB,CAAC;IAEF,mDAAmD;IACnD,IAAI,YAAY,KAAK,SAAS,EAAE,CAAC;QAC/B,eAAe,CAAC,gBAAgB,GAAG,mBAAmB,CAAC,aAAa,CAAC;QAErE,2BAA2B;QAC3B,MAAM,cAAc,GAAG,aAAa,CAAC,cAAc,IAAI,CAAC,gBAAgB,EAAE,cAAc,EAAE,cAAc,CAAC,CAAC;QAC1G,KAAK,MAAM,KAAK,IAAI,cAAc,EAAE,CAAC;YACnC,IAAI,CAAC,eAAe,CAAC,gBAAgB,CAAC,KAAK,CAAC,EAAE,CAAC;gBAC7C,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,IAAI,CAAC,2BAA2B,KAAK,EAAE,CAAC,CAAC;gBAC3E,eAAe,CAAC,UAAU,CAAC,OAAO,GAAG,KAAK,CAAC;YAC7C,CAAC;QACH,CAAC;QAED,mBAAmB;QACnB,IAAI,eAAe,CAAC,eAAe,EAAE,CAAC;YACpC,MAAM,WAAW,GAAG,UAAU,CAAC,eAAe,CAAC,gBAAgB,CAAC,cAAc,CAAC,EAAE,KAAK,IAAI,GAAG,CAAC,CAAC;YAC/F,IAAI,WAAW,GAAG,KAAK,EAAE,CAAC;gBACxB,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC;oBAC7B,IAAI,EAAE,aAAa;oBACnB,QAAQ,EAAE,SAAS;oBACnB,OAAO,EAAE,mCAAmC,WAAW,EAAE;oBACzD,cAAc,EAAE,gDAAgD;iBACjE,CAAC,CAAC;YACL,CAAC;QACH,CAAC;IACH,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,uBAAuB,CACpC,eAAoB,EACpB,YAAoB,EACpB,OAAY,EACZ,IAAS;IAET,MAAM,eAAe,GAAG;QACtB,YAAY,EAAE,EAAW;QACzB,gBAAgB,EAAE,KAAK;QACvB,aAAa,EAAE,EAAW;QAC1B,SAAS,EAAE,EAAc;KAC1B,CAAC;IAEF,IAAI,OAAO,CAAC,mBAAmB,EAAE,CAAC;QAChC,IAAI,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACjD,eAAe,CAAC,YAAY,CAAC,IAAI,CAAC;gBAChC,IAAI,EAAE,mBAAmB;gBACzB,KAAK,EAAE,UAAU,YAAY,oBAAoB;gBACjD,WAAW,EAAE,gBAAgB,eAAe,CAAC,UAAU,CAAC,MAAM,CAAC,MAAM,oBAAoB;gBACzF,UAAU,EAAE,IAAI,CAAC,EAAE;gBACnB,QAAQ,EAAE,MAAM;aACjB,CAAC,CAAC;QACL,CAAC;QAED,IAAI,eAAe,CAAC,SAAS,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;YACzC,eAAe,CAAC,gBAAgB,GAAG,IAAI,CAAC;YACxC,eAAe,CAAC,SAAS,CAAC,IAAI,CAAC,qDAAqD,CAAC,CAAC;QACxF,CAAC;IACH,CAAC;IAED,OAAO,eAAe,CAAC;AACzB,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc,CAAC,cAAqC;IACjE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,iCAAiC,EAAE;IAC1C,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,uCAAuC;IAC9C,OAAO,EAAE,0BAA0B;CACpC,CAAC,CAAC"}