/**
 * Document Templates Function
 * Handles document template management and generation
 * Migrated from old-arch/src/template-service/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create template handler
 */
export declare function createTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Generate document from template handler
 */
export declare function generateFromTemplate(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
