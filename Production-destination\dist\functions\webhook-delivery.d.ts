/**
 * Webhook Delivery Function
 * Handles webhook delivery with retry logic and failure handling
 * Migrated from old-arch/src/integration-service/webhook-delivery/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Deliver webhook handler
 */
export declare function deliverWebhook(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
