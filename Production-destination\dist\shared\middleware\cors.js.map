{"version": 3, "file": "cors.js", "sourceRoot": "", "sources": ["../../../src/shared/middleware/cors.ts"], "names": [], "mappings": ";AAAA;;;GAGG;;AAiCH,wCAmCC;AAKD,0CAaC;AAKD,4BAiBC;AAhGD,MAAM,kBAAkB,GAAgB;IACtC,cAAc,EAAE,CAAC,GAAG,CAAC;IACrB,cAAc,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,KAAK,EAAE,QAAQ,EAAE,OAAO,EAAE,SAAS,CAAC;IACpE,cAAc,EAAE;QACd,cAAc;QACd,eAAe;QACf,kBAAkB;QAClB,QAAQ;QACR,QAAQ;QACR,+BAA+B;QAC/B,gCAAgC;QAChC,aAAa;QACb,WAAW;KACZ;IACD,gBAAgB,EAAE,IAAI;IACtB,MAAM,EAAE,KAAK,CAAC,WAAW;CAC1B,CAAC;AAEF;;GAEG;AACH,SAAgB,cAAc,CAC5B,QAA0B,EAC1B,OAAoB,EACpB,UAAuB,EAAE;IAEzB,MAAM,WAAW,GAAG,EAAE,GAAG,kBAAkB,EAAE,GAAG,OAAO,EAAE,CAAC;IAC1D,MAAM,MAAM,GAAG,OAAO,CAAC,OAAO,CAAC,GAAG,CAAC,QAAQ,CAAC,IAAI,GAAG,CAAC;IAEpD,2BAA2B;IAC3B,IAAI,aAAa,GAAG,GAAG,CAAC;IACxB,IAAI,WAAW,CAAC,cAAc,IAAI,WAAW,CAAC,cAAc,CAAC,MAAM,GAAG,CAAC,EAAE,CAAC;QACxE,IAAI,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,GAAG,CAAC,EAAE,CAAC;YAC7C,aAAa,GAAG,MAAM,CAAC;QACzB,CAAC;aAAM,IAAI,WAAW,CAAC,cAAc,CAAC,QAAQ,CAAC,MAAM,CAAC,EAAE,CAAC;YACvD,aAAa,GAAG,MAAM,CAAC;QACzB,CAAC;aAAM,CAAC;YACN,aAAa,GAAG,WAAW,CAAC,cAAc,CAAC,CAAC,CAAC,CAAC;QAChD,CAAC;IACH,CAAC;IAED,MAAM,WAAW,GAAG;QAClB,6BAA6B,EAAE,aAAa;QAC5C,8BAA8B,EAAE,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QAC5E,8BAA8B,EAAE,WAAW,CAAC,cAAc,EAAE,IAAI,CAAC,IAAI,CAAC,IAAI,EAAE;QAC5E,wBAAwB,EAAE,WAAW,CAAC,MAAM,EAAE,QAAQ,EAAE,IAAI,OAAO;QACnE,GAAG,CAAC,WAAW,CAAC,gBAAgB,IAAI,EAAE,kCAAkC,EAAE,MAAM,EAAE,CAAC;KACpF,CAAC;IAEF,OAAO;QACL,GAAG,QAAQ;QACX,OAAO,EAAE;YACP,GAAG,QAAQ,CAAC,OAAO;YACnB,GAAG,WAAW;SACf;KACF,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAgB,eAAe,CAC7B,OAAoB,EACpB,UAAuB,EAAE;IAEzB,IAAI,OAAO,CAAC,MAAM,KAAK,SAAS,EAAE,CAAC;QACjC,OAAO,cAAc,CAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE;gBACP,gBAAgB,EAAE,GAAG;aACtB;SACF,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACvB,CAAC;IACD,OAAO,IAAI,CAAC;AACd,CAAC;AAED;;GAEG;AACH,SAAgB,QAAQ,CACtB,OAA0E,EAC1E,UAAuB,EAAE;IAEzB,OAAO,KAAK,EAAE,OAAoB,EAAE,OAAY,EAA6B,EAAE;QAC7E,2BAA2B;QAC3B,MAAM,iBAAiB,GAAG,eAAe,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAC5D,IAAI,iBAAiB,EAAE,CAAC;YACtB,OAAO,iBAAiB,CAAC;QAC3B,CAAC;QAED,sBAAsB;QACtB,MAAM,QAAQ,GAAG,MAAM,OAAO,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QAEjD,+BAA+B;QAC/B,OAAO,cAAc,CAAC,QAAQ,EAAE,OAAO,EAAE,OAAO,CAAC,CAAC;IACpD,CAAC,CAAC;AACJ,CAAC"}