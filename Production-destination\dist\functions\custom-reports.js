"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.createCustomReport = createCustomReport;
exports.getReportStatus = getReportStatus;
/**
 * Custom Reports Function
 * Handles custom report generation and management
 * Migrated from old-arch/src/analytics-service/reports/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Report types and enums
var ReportType;
(function (ReportType) {
    ReportType["DOCUMENT_ACTIVITY"] = "DOCUMENT_ACTIVITY";
    ReportType["USER_ACTIVITY"] = "USER_ACTIVITY";
    ReportType["ORGANIZATION_OVERVIEW"] = "ORGANIZATION_OVERVIEW";
    ReportType["PROJECT_PERFORMANCE"] = "PROJECT_PERFORMANCE";
    ReportType["STORAGE_USAGE"] = "STORAGE_USAGE";
    ReportType["WORKFLOW_ANALYTICS"] = "WORKFLOW_ANALYTICS";
    ReportType["SECURITY_AUDIT"] = "SECURITY_AUDIT";
    ReportType["COMPLIANCE_REPORT"] = "COMPLIANCE_REPORT";
    ReportType["CUSTOM"] = "CUSTOM";
})(ReportType || (ReportType = {}));
var ReportFormat;
(function (ReportFormat) {
    ReportFormat["PDF"] = "PDF";
    ReportFormat["EXCEL"] = "EXCEL";
    ReportFormat["CSV"] = "CSV";
    ReportFormat["JSON"] = "JSON";
})(ReportFormat || (ReportFormat = {}));
var ReportStatus;
(function (ReportStatus) {
    ReportStatus["PENDING"] = "PENDING";
    ReportStatus["GENERATING"] = "GENERATING";
    ReportStatus["COMPLETED"] = "COMPLETED";
    ReportStatus["FAILED"] = "FAILED";
    ReportStatus["EXPIRED"] = "EXPIRED";
})(ReportStatus || (ReportStatus = {}));
// Validation schemas
const createReportSchema = Joi.object({
    name: Joi.string().min(2).max(100).required(),
    description: Joi.string().max(500).optional(),
    type: Joi.string().valid(...Object.values(ReportType)).required(),
    format: Joi.string().valid(...Object.values(ReportFormat)).default(ReportFormat.PDF),
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    parameters: Joi.object({
        dateRange: Joi.object({
            startDate: Joi.string().isoDate().required(),
            endDate: Joi.string().isoDate().required()
        }).optional(),
        filters: Joi.object({
            userIds: Joi.array().items(Joi.string().uuid()).optional(),
            documentTypes: Joi.array().items(Joi.string()).optional(),
            categories: Joi.array().items(Joi.string()).optional(),
            tags: Joi.array().items(Joi.string()).optional(),
            status: Joi.array().items(Joi.string()).optional()
        }).optional(),
        groupBy: Joi.array().items(Joi.string().valid('user', 'date', 'category', 'type', 'project')).optional(),
        metrics: Joi.array().items(Joi.string()).optional(),
        includeCharts: Joi.boolean().default(true),
        includeDetails: Joi.boolean().default(true)
    }).optional(),
    schedule: Joi.object({
        enabled: Joi.boolean().default(false),
        frequency: Joi.string().valid('daily', 'weekly', 'monthly', 'quarterly').optional(),
        dayOfWeek: Joi.number().min(0).max(6).optional(),
        dayOfMonth: Joi.number().min(1).max(31).optional(),
        time: Joi.string().pattern(/^([0-1]?[0-9]|2[0-3]):[0-5][0-9]$/).optional(),
        recipients: Joi.array().items(Joi.string().email()).optional()
    }).optional()
});
/**
 * Create custom report handler
 */
async function createCustomReport(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Create custom report started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = createReportSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const reportRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(reportRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check report creation limits
        const canCreate = await checkReportCreationLimits(reportRequest.organizationId);
        if (!canCreate.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canCreate.reason }
            }, request);
        }
        // Create report
        const reportId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const report = {
            id: reportId,
            name: reportRequest.name,
            description: reportRequest.description,
            type: reportRequest.type,
            format: reportRequest.format || ReportFormat.PDF,
            status: ReportStatus.PENDING,
            organizationId: reportRequest.organizationId,
            projectId: reportRequest.projectId,
            parameters: reportRequest.parameters || {},
            schedule: reportRequest.schedule,
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        await database_1.db.createItem('custom-reports', report);
        // Start report generation asynchronously
        await generateReportAsync(report);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "custom_report_created",
            userId: user.id,
            organizationId: reportRequest.organizationId,
            projectId: reportRequest.projectId,
            timestamp: now,
            details: {
                reportId,
                reportName: reportRequest.name,
                reportType: reportRequest.type,
                reportFormat: reportRequest.format,
                isScheduled: !!reportRequest.schedule?.enabled
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'CustomReportCreated',
            aggregateId: reportId,
            aggregateType: 'CustomReport',
            version: 1,
            data: {
                report,
                createdBy: user.id
            },
            userId: user.id,
            organizationId: reportRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Custom report created successfully", {
            correlationId,
            reportId,
            reportName: reportRequest.name,
            reportType: reportRequest.type,
            createdBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: reportId,
                name: reportRequest.name,
                type: reportRequest.type,
                format: reportRequest.format,
                status: ReportStatus.PENDING,
                estimatedDuration: estimateReportDuration(reportRequest.type, reportRequest.parameters),
                createdAt: now,
                message: "Custom report created successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Create custom report failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Get report status handler
 */
async function getReportStatus(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const reportId = request.params.reportId;
    logger_1.logger.info("Get report status started", { correlationId, reportId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!reportId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Report ID is required" }
            }, request);
        }
        // Get report
        const report = await database_1.db.readItem('custom-reports', reportId, reportId);
        if (!report) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Report not found" }
            }, request);
        }
        const reportData = report;
        // Check access
        const hasAccess = await checkOrganizationAccess(reportData.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to report" }
            }, request);
        }
        logger_1.logger.info("Report status retrieved successfully", {
            correlationId,
            reportId,
            status: reportData.status,
            userId: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                id: reportData.id,
                name: reportData.name,
                type: reportData.type,
                format: reportData.format,
                status: reportData.status,
                results: reportData.results,
                createdAt: reportData.createdAt,
                updatedAt: reportData.updatedAt
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Get report status failed", {
            correlationId,
            reportId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkReportCreationLimits(organizationId) {
    try {
        // Get organization to check tier
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxReports: 5, maxScheduledReports: 1 },
            'PROFESSIONAL': { maxReports: 50, maxScheduledReports: 10 },
            'ENTERPRISE': { maxReports: -1, maxScheduledReports: -1 } // Unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        if (limit.maxReports === -1) {
            return { allowed: true };
        }
        // Check current report count
        const reportCountQuery = 'SELECT VALUE COUNT(1) FROM c WHERE c.organizationId = @orgId AND c.status != @expired';
        const countResult = await database_1.db.queryItems('custom-reports', reportCountQuery, [organizationId, ReportStatus.EXPIRED]);
        const currentCount = Number(countResult[0]) || 0;
        if (currentCount >= limit.maxReports) {
            return {
                allowed: false,
                reason: `Report limit reached (${limit.maxReports})`
            };
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check report creation limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function estimateReportDuration(reportType, parameters) {
    // Simplified estimation - in production, use historical data
    const baseTimeMinutes = {
        [ReportType.DOCUMENT_ACTIVITY]: 5,
        [ReportType.USER_ACTIVITY]: 3,
        [ReportType.ORGANIZATION_OVERVIEW]: 10,
        [ReportType.PROJECT_PERFORMANCE]: 7,
        [ReportType.STORAGE_USAGE]: 2,
        [ReportType.WORKFLOW_ANALYTICS]: 8,
        [ReportType.SECURITY_AUDIT]: 15,
        [ReportType.COMPLIANCE_REPORT]: 20,
        [ReportType.CUSTOM]: 10
    };
    const baseTime = baseTimeMinutes[reportType] || 10;
    // Adjust based on date range
    if (parameters?.dateRange) {
        const start = new Date(parameters.dateRange.startDate);
        const end = new Date(parameters.dateRange.endDate);
        const daysDiff = Math.ceil((end.getTime() - start.getTime()) / (1000 * 60 * 60 * 24));
        if (daysDiff > 365) {
            return `${baseTime * 3} minutes`;
        }
        else if (daysDiff > 90) {
            return `${baseTime * 2} minutes`;
        }
    }
    return `${baseTime} minutes`;
}
async function generateReportAsync(report) {
    try {
        // Update status to generating
        const updatedReport = {
            ...report,
            id: report.id,
            status: ReportStatus.GENERATING,
            updatedAt: new Date().toISOString()
        };
        await database_1.db.updateItem('custom-reports', updatedReport);
        // Simulate report generation (in production, this would be actual report generation)
        setTimeout(async () => {
            try {
                const reportData = await generateReportData(report);
                const downloadUrl = await uploadReportToStorage(report, reportData);
                const completedReport = {
                    ...updatedReport,
                    id: report.id,
                    status: ReportStatus.COMPLETED,
                    results: {
                        downloadUrl,
                        fileSize: reportData.length,
                        recordCount: reportData.recordCount || 0,
                        generatedAt: new Date().toISOString(),
                        expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString() // 7 days
                    },
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('custom-reports', completedReport);
                logger_1.logger.info('Report generated successfully', { reportId: report.id });
            }
            catch (error) {
                const failedReport = {
                    ...updatedReport,
                    id: report.id,
                    status: ReportStatus.FAILED,
                    updatedAt: new Date().toISOString()
                };
                await database_1.db.updateItem('custom-reports', failedReport);
                logger_1.logger.error('Report generation failed', { reportId: report.id, error });
            }
        }, 5000); // 5 second delay for demo
    }
    catch (error) {
        logger_1.logger.error('Failed to start report generation', { reportId: report.id, error });
    }
}
async function generateReportData(report) {
    // Simplified report data generation
    const mockData = {
        reportId: report.id,
        reportName: report.name,
        generatedAt: new Date().toISOString(),
        data: `Mock report data for ${report.type}`,
        recordCount: Math.floor(Math.random() * 1000) + 100
    };
    return Buffer.from(JSON.stringify(mockData, null, 2));
}
async function uploadReportToStorage(report, data) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient("reports");
        const fileName = `${report.id}-${Date.now()}.${report.format.toLowerCase()}`;
        const blobClient = containerClient.getBlobClient(fileName);
        await blobClient.getBlockBlobClient().upload(data, data.length);
        return blobClient.url;
    }
    catch (error) {
        logger_1.logger.error('Failed to upload report to storage', { reportId: report.id, error });
        throw error;
    }
}
// Register functions
functions_1.app.http('custom-report-create', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'reports/custom',
    handler: createCustomReport
});
functions_1.app.http('custom-report-status', {
    methods: ['GET', 'OPTIONS'],
    authLevel: 'function',
    route: 'reports/custom/{reportId}/status',
    handler: getReportStatus
});
//# sourceMappingURL=custom-reports.js.map