"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.uploadDocument = uploadDocument;
exports.completeUpload = completeUpload;
/**
 * Document Upload Function
 * Handles document upload operations with validation and processing
 * Migrated from old-arch/src/document-service/upload/index.ts
 */
const functions_1 = require("@azure/functions");
const storage_blob_1 = require("@azure/storage-blob");
const uuid_1 = require("uuid");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
const event_1 = require("../shared/services/event");
// Upload types and enums
var UploadStatus;
(function (UploadStatus) {
    UploadStatus["PENDING"] = "PENDING";
    UploadStatus["UPLOADING"] = "UPLOADING";
    UploadStatus["PROCESSING"] = "PROCESSING";
    UploadStatus["COMPLETED"] = "COMPLETED";
    UploadStatus["FAILED"] = "FAILED";
})(UploadStatus || (UploadStatus = {}));
var DocumentType;
(function (DocumentType) {
    DocumentType["PDF"] = "PDF";
    DocumentType["WORD"] = "WORD";
    DocumentType["EXCEL"] = "EXCEL";
    DocumentType["POWERPOINT"] = "POWERPOINT";
    DocumentType["IMAGE"] = "IMAGE";
    DocumentType["TEXT"] = "TEXT";
    DocumentType["OTHER"] = "OTHER";
})(DocumentType || (DocumentType = {}));
// Validation schemas
const uploadDocumentSchema = Joi.object({
    fileName: Joi.string().min(1).max(255).required(),
    contentType: Joi.string().required(),
    size: Joi.number().min(1).max(100 * 1024 * 1024).required(), // 100MB max
    organizationId: Joi.string().uuid().required(),
    projectId: Joi.string().uuid().optional(),
    category: Joi.string().max(50).optional(),
    tags: Joi.array().items(Joi.string().max(30)).max(20).optional(),
    description: Joi.string().max(500).optional(),
    metadata: Joi.object().optional(),
    autoProcess: Joi.boolean().default(true),
    extractText: Joi.boolean().default(true),
    generateThumbnail: Joi.boolean().default(true)
});
/**
 * Upload document handler
 */
async function uploadDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    logger_1.logger.info("Upload document started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = uploadDocumentSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const uploadRequest = value;
        // Check organization access
        const hasAccess = await checkOrganizationAccess(uploadRequest.organizationId, user.id);
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied to organization" }
            }, request);
        }
        // Check upload limits
        const canUpload = await checkUploadLimits(uploadRequest.organizationId, uploadRequest.size);
        if (!canUpload.allowed) {
            return (0, cors_1.addCorsHeaders)({
                status: 429,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: canUpload.reason }
            }, request);
        }
        // Validate file type
        const fileValidation = validateFileType(uploadRequest.fileName, uploadRequest.contentType);
        if (!fileValidation.valid) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: fileValidation.reason }
            }, request);
        }
        // Create document upload record
        const documentId = (0, uuid_1.v4)();
        const now = new Date().toISOString();
        const blobPath = `documents/${uploadRequest.organizationId}/${documentId}/${uploadRequest.fileName}`;
        const documentUpload = {
            id: documentId,
            fileName: generateUniqueFileName(uploadRequest.fileName),
            originalName: uploadRequest.fileName,
            contentType: uploadRequest.contentType,
            size: uploadRequest.size,
            status: UploadStatus.PENDING,
            organizationId: uploadRequest.organizationId,
            projectId: uploadRequest.projectId,
            category: uploadRequest.category,
            tags: uploadRequest.tags || [],
            description: uploadRequest.description,
            metadata: uploadRequest.metadata || {},
            blobPath,
            processing: {
                autoProcess: uploadRequest.autoProcess ?? true,
                extractText: uploadRequest.extractText ?? true,
                generateThumbnail: uploadRequest.generateThumbnail ?? true
            },
            createdBy: user.id,
            createdAt: now,
            updatedAt: now,
            tenantId: user.tenantId || user.id
        };
        // Generate upload URL
        const uploadUrl = await generateUploadUrl(blobPath, uploadRequest.contentType);
        documentUpload.uploadUrl = uploadUrl;
        // Save document record
        await database_1.db.createItem('documents', documentUpload);
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_upload_initiated",
            userId: user.id,
            organizationId: uploadRequest.organizationId,
            projectId: uploadRequest.projectId,
            documentId,
            timestamp: now,
            details: {
                fileName: uploadRequest.fileName,
                fileSize: uploadRequest.size,
                contentType: uploadRequest.contentType,
                category: uploadRequest.category,
                autoProcess: uploadRequest.autoProcess
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentUploadInitiated',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: documentUpload,
                uploadedBy: user.id
            },
            userId: user.id,
            organizationId: uploadRequest.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document upload initiated successfully", {
            correlationId,
            documentId,
            fileName: uploadRequest.fileName,
            fileSize: uploadRequest.size,
            uploadedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 201,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                fileName: documentUpload.fileName,
                originalName: uploadRequest.fileName,
                uploadUrl,
                status: UploadStatus.PENDING,
                expiresIn: 3600, // 1 hour
                instructions: {
                    method: 'PUT',
                    headers: {
                        'Content-Type': uploadRequest.contentType,
                        'x-ms-blob-type': 'BlockBlob'
                    }
                },
                createdAt: now,
                message: "Upload URL generated successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Upload document failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Complete upload handler
 */
async function completeUpload(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const documentId = request.params.documentId;
    logger_1.logger.info("Complete upload started", { correlationId, documentId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        if (!documentId) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document ID is required" }
            }, request);
        }
        // Get document
        const document = await database_1.db.readItem('documents', documentId, documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        const documentData = document;
        // Check access
        if (documentData.createdBy !== user.id) {
            const hasAccess = await checkOrganizationAccess(documentData.organizationId, user.id);
            if (!hasAccess) {
                return (0, cors_1.addCorsHeaders)({
                    status: 403,
                    headers: { 'Content-Type': 'application/json' },
                    jsonBody: { error: "Access denied to document" }
                }, request);
            }
        }
        // Verify blob exists
        const blobExists = await verifyBlobExists(documentData.blobPath);
        if (!blobExists) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "File upload not completed" }
            }, request);
        }
        // Update document status
        const updatedDocument = {
            ...documentData,
            id: documentId,
            status: UploadStatus.COMPLETED,
            updatedAt: new Date().toISOString(),
            uploadUrl: undefined // Remove upload URL for security
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Start processing if enabled
        if (documentData.processing.autoProcess) {
            await initiateDocumentProcessing(documentId, documentData.processing);
        }
        // Create activity record
        await database_1.db.createItem('activities', {
            id: (0, uuid_1.v4)(),
            type: "document_upload_completed",
            userId: user.id,
            organizationId: documentData.organizationId,
            projectId: documentData.projectId,
            documentId,
            timestamp: new Date().toISOString(),
            details: {
                fileName: documentData.originalName,
                fileSize: documentData.size,
                autoProcessing: documentData.processing.autoProcess
            },
            tenantId: user.tenantId
        });
        // Publish domain event
        await event_1.eventService.publishEvent({
            type: 'DocumentUploadCompleted',
            aggregateId: documentId,
            aggregateType: 'Document',
            version: 1,
            data: {
                document: updatedDocument,
                completedBy: user.id
            },
            userId: user.id,
            organizationId: documentData.organizationId,
            tenantId: user.tenantId
        });
        logger_1.logger.info("Document upload completed successfully", {
            correlationId,
            documentId,
            fileName: documentData.originalName,
            completedBy: user.id
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: {
                documentId,
                fileName: documentData.fileName,
                originalName: documentData.originalName,
                status: UploadStatus.COMPLETED,
                size: documentData.size,
                processingStarted: documentData.processing.autoProcess,
                completedAt: updatedDocument.updatedAt,
                message: "Document upload completed successfully"
            }
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Complete upload failed", {
            correlationId,
            documentId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Internal server error" }
        }, request);
    }
}
/**
 * Helper functions
 */
async function checkOrganizationAccess(organizationId, userId) {
    try {
        const membershipQuery = 'SELECT * FROM c WHERE c.organizationId = @orgId AND c.userId = @userId AND c.status = @status';
        const memberships = await database_1.db.queryItems('organization-members', membershipQuery, [organizationId, userId, 'ACTIVE']);
        return memberships.length > 0;
    }
    catch (error) {
        logger_1.logger.error('Failed to check organization access', { error, organizationId, userId });
        return false;
    }
}
async function checkUploadLimits(organizationId, fileSize) {
    try {
        // Get organization to check tier and usage
        const organization = await database_1.db.readItem('organizations', organizationId, organizationId);
        if (!organization) {
            return { allowed: false, reason: 'Organization not found' };
        }
        const orgData = organization;
        const tier = orgData.tier || 'FREE';
        // Define tier limits
        const limits = {
            'FREE': { maxFileSize: 10 * 1024 * 1024, maxStorageGB: 1 }, // 10MB, 1GB
            'PROFESSIONAL': { maxFileSize: 50 * 1024 * 1024, maxStorageGB: 100 }, // 50MB, 100GB
            'ENTERPRISE': { maxFileSize: 100 * 1024 * 1024, maxStorageGB: -1 } // 100MB, unlimited
        };
        const limit = limits[tier] || limits['FREE'];
        // Check file size limit
        if (fileSize > limit.maxFileSize) {
            return {
                allowed: false,
                reason: `File size exceeds limit (${Math.round(limit.maxFileSize / 1024 / 1024)}MB)`
            };
        }
        // Check storage limit (simplified)
        if (limit.maxStorageGB !== -1) {
            // In production, calculate actual storage usage
            const currentUsageGB = 0.5; // Mock current usage
            const newUsageGB = currentUsageGB + (fileSize / 1024 / 1024 / 1024);
            if (newUsageGB > limit.maxStorageGB) {
                return {
                    allowed: false,
                    reason: `Storage limit exceeded (${limit.maxStorageGB}GB)`
                };
            }
        }
        return { allowed: true };
    }
    catch (error) {
        logger_1.logger.error('Failed to check upload limits', { error, organizationId });
        return { allowed: false, reason: 'Failed to check limits' };
    }
}
function validateFileType(fileName, contentType) {
    // Define allowed file types
    const allowedTypes = [
        'application/pdf',
        'application/msword',
        'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
        'application/vnd.ms-excel',
        'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet',
        'application/vnd.ms-powerpoint',
        'application/vnd.openxmlformats-officedocument.presentationml.presentation',
        'image/jpeg',
        'image/png',
        'image/gif',
        'image/bmp',
        'image/tiff',
        'text/plain',
        'text/csv'
    ];
    if (!allowedTypes.includes(contentType)) {
        return { valid: false, reason: 'File type not supported' };
    }
    // Check file extension
    const extension = fileName.toLowerCase().split('.').pop();
    const dangerousExtensions = ['exe', 'bat', 'cmd', 'scr', 'vbs', 'js'];
    if (dangerousExtensions.includes(extension || '')) {
        return { valid: false, reason: 'File extension not allowed' };
    }
    return { valid: true };
}
function generateUniqueFileName(originalName) {
    const timestamp = Date.now();
    const random = Math.random().toString(36).substring(2, 8);
    const extension = originalName.split('.').pop();
    const nameWithoutExt = originalName.substring(0, originalName.lastIndexOf('.'));
    return `${nameWithoutExt}_${timestamp}_${random}.${extension}`;
}
async function generateUploadUrl(blobPath, contentType) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient("documents");
        const blobClient = containerClient.getBlobClient(blobPath);
        // Production SAS token generation with proper permissions
        const expiresOn = new Date(Date.now() + 60 * 60 * 1000); // 1 hour expiry
        try {
            // Extract storage account credentials from connection string
            const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
            if (!connectionString) {
                throw new Error('Azure Storage connection string not configured');
            }
            // Parse connection string to get account name and key
            const accountNameMatch = connectionString.match(/AccountName=([^;]+)/);
            const accountKeyMatch = connectionString.match(/AccountKey=([^;]+)/);
            if (!accountNameMatch || !accountKeyMatch) {
                throw new Error('Invalid Azure Storage connection string format');
            }
            const accountName = accountNameMatch[1];
            const accountKey = accountKeyMatch[1];
            // Create shared key credential
            const sharedKeyCredential = new storage_blob_1.StorageSharedKeyCredential(accountName, accountKey);
            // Define SAS permissions for upload
            const permissions = new storage_blob_1.BlobSASPermissions();
            permissions.write = true;
            permissions.create = true;
            permissions.add = true;
            // Generate SAS query parameters
            const sasOptions = {
                containerName: 'documents',
                blobName: blobPath,
                permissions,
                expiresOn,
                protocol: 'https'
            };
            const sasToken = (0, storage_blob_1.generateBlobSASQueryParameters)(sasOptions, sharedKeyCredential).toString();
            const sasUrl = `${blobClient.url}?${sasToken}`;
            logger_1.logger.info('SAS token generated successfully', {
                blobPath,
                expiresOn: expiresOn.toISOString(),
                permissions: 'write,create,add'
            });
            return sasUrl;
        }
        catch (error) {
            logger_1.logger.error('Failed to generate SAS token', {
                error: error instanceof Error ? error.message : String(error),
                blobPath
            });
            // Fallback to basic blob URL (not recommended for production)
            logger_1.logger.warn('Falling back to basic blob URL without SAS token');
            return blobClient.url;
        }
    }
    catch (error) {
        logger_1.logger.error('Failed to generate upload URL', { error, blobPath });
        throw error;
    }
}
async function verifyBlobExists(blobPath) {
    try {
        const blobServiceClient = new storage_blob_1.BlobServiceClient(process.env.AZURE_STORAGE_CONNECTION_STRING || "");
        const containerClient = blobServiceClient.getContainerClient("documents");
        const blobClient = containerClient.getBlobClient(blobPath);
        const exists = await blobClient.exists();
        return exists;
    }
    catch (error) {
        logger_1.logger.error('Failed to verify blob exists', { error, blobPath });
        return false;
    }
}
async function initiateDocumentProcessing(documentId, processing) {
    try {
        // In production, this would trigger document processing pipeline
        logger_1.logger.info('Document processing initiated', {
            documentId,
            extractText: processing.extractText,
            generateThumbnail: processing.generateThumbnail
        });
    }
    catch (error) {
        logger_1.logger.error('Failed to initiate document processing', { error, documentId });
    }
}
// Register functions
functions_1.app.http('document-upload', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/upload',
    handler: uploadDocument
});
functions_1.app.http('document-upload-complete', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/{documentId}/upload/complete',
    handler: completeUpload
});
//# sourceMappingURL=document-upload.js.map