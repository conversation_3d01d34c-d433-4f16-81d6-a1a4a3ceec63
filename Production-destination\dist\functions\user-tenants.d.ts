/**
 * User Tenants Function
 * Handles user tenant management and switching between organizations
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get user tenants handler
 */
export declare function getUserTenants(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Switch tenant handler
 */
export declare function switchTenant(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Invite user to organization handler
 */
export declare function inviteUserToOrganization(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
