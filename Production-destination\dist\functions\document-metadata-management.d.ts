/**
 * Document Metadata Management Function
 * Handles advanced document metadata management, tagging, and classification
 * Migrated from old-arch/src/document-service/metadata/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Update document metadata handler
 */
export declare function updateDocumentMetadata(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Extract document metadata handler
 */
export declare function extractDocumentMetadata(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
