"use strict";
var __createBinding = (this && this.__createBinding) || (Object.create ? (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    var desc = Object.getOwnPropertyDescriptor(m, k);
    if (!desc || ("get" in desc ? !m.__esModule : desc.writable || desc.configurable)) {
      desc = { enumerable: true, get: function() { return m[k]; } };
    }
    Object.defineProperty(o, k2, desc);
}) : (function(o, m, k, k2) {
    if (k2 === undefined) k2 = k;
    o[k2] = m[k];
}));
var __setModuleDefault = (this && this.__setModuleDefault) || (Object.create ? (function(o, v) {
    Object.defineProperty(o, "default", { enumerable: true, value: v });
}) : function(o, v) {
    o["default"] = v;
});
var __importStar = (this && this.__importStar) || (function () {
    var ownKeys = function(o) {
        ownKeys = Object.getOwnPropertyNames || function (o) {
            var ar = [];
            for (var k in o) if (Object.prototype.hasOwnProperty.call(o, k)) ar[ar.length] = k;
            return ar;
        };
        return ownKeys(o);
    };
    return function (mod) {
        if (mod && mod.__esModule) return mod;
        var result = {};
        if (mod != null) for (var k = ownKeys(mod), i = 0; i < k.length; i++) if (k[i] !== "default") __createBinding(result, mod, k[i]);
        __setModuleDefault(result, mod);
        return result;
    };
})();
Object.defineProperty(exports, "__esModule", { value: true });
exports.DocumentStatus = exports.DocumentType = void 0;
exports.processDocument = processDocument;
/**
 * Document Processing Function
 * Handles AI-powered document analysis and content extraction
 */
const functions_1 = require("@azure/functions");
const ai_form_recognizer_1 = require("@azure/ai-form-recognizer");
const storage_blob_1 = require("@azure/storage-blob");
const Joi = __importStar(require("joi"));
const logger_1 = require("../shared/utils/logger");
const cors_1 = require("../shared/middleware/cors");
const auth_1 = require("../shared/utils/auth");
const database_1 = require("../shared/services/database");
// Document types enum
var DocumentType;
(function (DocumentType) {
    DocumentType["GENERAL"] = "general";
    DocumentType["INVOICE"] = "invoice";
    DocumentType["CONTRACT"] = "contract";
    DocumentType["RECEIPT"] = "receipt";
    DocumentType["IDENTITY"] = "identity";
    DocumentType["FINANCIAL"] = "financial";
    DocumentType["LEGAL"] = "legal";
    DocumentType["MEDICAL"] = "medical";
    DocumentType["TAX"] = "tax";
    DocumentType["INSURANCE"] = "insurance";
})(DocumentType || (exports.DocumentType = DocumentType = {}));
var DocumentStatus;
(function (DocumentStatus) {
    DocumentStatus["PENDING"] = "pending";
    DocumentStatus["UPLOADED"] = "uploaded";
    DocumentStatus["PROCESSING"] = "processing";
    DocumentStatus["PROCESSED"] = "processed";
    DocumentStatus["FAILED"] = "failed";
    DocumentStatus["ARCHIVED"] = "archived";
})(DocumentStatus || (exports.DocumentStatus = DocumentStatus = {}));
// Processing request schema
const processingSchema = Joi.object({
    documentId: Joi.string().uuid().required(),
    analysisType: Joi.string().valid('layout', 'general', 'invoice', 'receipt', 'identity', 'custom').default('layout'),
    extractTables: Joi.boolean().default(true),
    extractKeyValuePairs: Joi.boolean().default(true),
    extractEntities: Joi.boolean().default(false),
    customModelId: Joi.string().optional()
});
/**
 * Extract text from document using Azure Document Intelligence
 */
async function extractTextFromDocument(documentBuffer, modelId, client) {
    try {
        const poller = await client.beginAnalyzeDocument(modelId, documentBuffer);
        const result = await poller.pollUntilDone();
        if (!result) {
            throw new Error('No analysis result received');
        }
        // Extract text content
        let extractedText = '';
        if (result.content) {
            extractedText = result.content;
        }
        // Extract tables
        const tables = [];
        if (result.tables) {
            for (const table of result.tables) {
                const tableData = {
                    rowCount: table.rowCount,
                    columnCount: table.columnCount,
                    cells: table.cells.map(cell => ({
                        content: cell.content,
                        rowIndex: cell.rowIndex,
                        columnIndex: cell.columnIndex,
                        confidence: cell.confidence || 0
                    }))
                };
                tables.push(tableData);
            }
        }
        // Extract key-value pairs
        const keyValuePairs = [];
        if (result.keyValuePairs) {
            for (const kvp of result.keyValuePairs) {
                keyValuePairs.push({
                    key: kvp.key?.content || '',
                    value: kvp.value?.content || '',
                    confidence: kvp.confidence
                });
            }
        }
        // Calculate average confidence
        const confidenceValues = [];
        if (result.pages) {
            for (const page of result.pages) {
                if (page.lines) {
                    for (const line of page.lines) {
                        const confidence = line.confidence;
                        if (confidence !== undefined) {
                            confidenceValues.push(confidence);
                        }
                    }
                }
            }
        }
        const averageConfidence = confidenceValues.length > 0
            ? confidenceValues.reduce((sum, conf) => sum + conf, 0) / confidenceValues.length
            : 0;
        return {
            text: extractedText,
            tables,
            keyValuePairs,
            confidence: averageConfidence
        };
    }
    catch (error) {
        logger_1.logger.error('Document analysis failed', {
            error: error instanceof Error ? error.message : String(error),
            modelId
        });
        throw error;
    }
}
/**
 * Process document handler
 */
async function processDocument(request, context) {
    // Handle preflight OPTIONS request
    const preflightResponse = (0, cors_1.handlePreflight)(request);
    if (preflightResponse) {
        return preflightResponse;
    }
    const correlationId = context.invocationId;
    const startTime = Date.now();
    logger_1.logger.info("Document processing started", { correlationId });
    try {
        // Authenticate user
        const authResult = await (0, auth_1.authenticateRequest)(request);
        if (!authResult.success || !authResult.user) {
            return (0, cors_1.addCorsHeaders)({
                status: 401,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: 'Unauthorized', message: authResult.error }
            }, request);
        }
        const user = authResult.user;
        // Validate request body
        const body = await request.json();
        const { error, value } = processingSchema.validate(body);
        if (error) {
            return (0, cors_1.addCorsHeaders)({
                status: 400,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: {
                    error: 'Validation Error',
                    message: error.details.map(d => d.message).join(', ')
                }
            }, request);
        }
        const processingRequest = value;
        // Get document metadata
        const document = await database_1.db.readItem('documents', processingRequest.documentId, processingRequest.documentId);
        if (!document) {
            return (0, cors_1.addCorsHeaders)({
                status: 404,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Document not found" }
            }, request);
        }
        // Check access permissions
        const hasAccess = (document.createdBy === user.id ||
            document.organizationId === user.tenantId ||
            user.roles?.includes('admin'));
        if (!hasAccess) {
            return (0, cors_1.addCorsHeaders)({
                status: 403,
                headers: { 'Content-Type': 'application/json' },
                jsonBody: { error: "Access denied" }
            }, request);
        }
        // Update document status to processing
        const updatedDocument = {
            ...document,
            id: document.id,
            status: DocumentStatus.PROCESSING,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id
        };
        await database_1.db.updateItem('documents', updatedDocument);
        // Download document from blob storage
        const connectionString = process.env.AZURE_STORAGE_CONNECTION_STRING;
        if (!connectionString) {
            throw new Error('Azure Storage connection string not configured');
        }
        const blobServiceClient = storage_blob_1.BlobServiceClient.fromConnectionString(connectionString);
        const containerClient = blobServiceClient.getContainerClient(process.env.DOCUMENT_CONTAINER || "documents");
        const blobClient = containerClient.getBlobClient(document.blobName);
        const downloadResponse = await blobClient.download();
        if (!downloadResponse.readableStreamBody) {
            throw new Error('Failed to download document content');
        }
        // Convert stream to buffer
        const chunks = [];
        for await (const chunk of downloadResponse.readableStreamBody) {
            chunks.push(Buffer.from(chunk));
        }
        const documentBuffer = Buffer.concat(chunks);
        // Initialize Document Intelligence client
        const endpoint = process.env.AZURE_DOCUMENT_INTELLIGENCE_ENDPOINT;
        const key = process.env.AZURE_DOCUMENT_INTELLIGENCE_KEY;
        if (!endpoint || !key) {
            throw new Error('Azure Document Intelligence credentials not configured');
        }
        const client = new ai_form_recognizer_1.DocumentAnalysisClient(endpoint, new ai_form_recognizer_1.AzureKeyCredential(key));
        // Determine model to use
        let modelId = 'prebuilt-layout';
        if (processingRequest.customModelId) {
            modelId = processingRequest.customModelId;
        }
        else {
            switch (processingRequest.analysisType) {
                case 'invoice':
                    modelId = 'prebuilt-invoice';
                    break;
                case 'receipt':
                    modelId = 'prebuilt-receipt';
                    break;
                case 'identity':
                    modelId = 'prebuilt-idDocument';
                    break;
                case 'general':
                    modelId = 'prebuilt-document';
                    break;
                default:
                    modelId = 'prebuilt-layout';
            }
        }
        // Process document
        const analysisResult = await extractTextFromDocument(documentBuffer, modelId, client);
        const processingTime = Date.now() - startTime;
        // Create processing result
        const result = {
            documentId: processingRequest.documentId,
            status: 'completed',
            extractedText: analysisResult.text,
            tables: processingRequest.extractTables ? analysisResult.tables : undefined,
            keyValuePairs: processingRequest.extractKeyValuePairs ? analysisResult.keyValuePairs : undefined,
            entities: processingRequest.extractEntities ? [] : undefined, // Placeholder for entity extraction
            confidence: analysisResult.confidence,
            processingTime,
            modelUsed: modelId
        };
        // Update document status to processed
        const finalDocument = {
            ...document,
            id: document.id,
            status: DocumentStatus.PROCESSED,
            updatedAt: new Date().toISOString(),
            updatedBy: user.id,
            processingResult: result
        };
        await database_1.db.updateItem('documents', finalDocument);
        logger_1.logger.info("Document processing completed", {
            correlationId,
            documentId: processingRequest.documentId,
            processingTime,
            modelUsed: modelId,
            confidence: analysisResult.confidence
        });
        return (0, cors_1.addCorsHeaders)({
            status: 200,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: result
        }, request);
    }
    catch (error) {
        const errorMessage = error instanceof Error ? error.message : String(error);
        logger_1.logger.error("Document processing failed", {
            correlationId,
            error: errorMessage
        });
        return (0, cors_1.addCorsHeaders)({
            status: 500,
            headers: { 'Content-Type': 'application/json' },
            jsonBody: { error: "Document processing failed" }
        }, request);
    }
}
functions_1.app.http('document-processing', {
    methods: ['POST', 'OPTIONS'],
    authLevel: 'function',
    route: 'documents/process',
    handler: processDocument
});
//# sourceMappingURL=document-processing.js.map