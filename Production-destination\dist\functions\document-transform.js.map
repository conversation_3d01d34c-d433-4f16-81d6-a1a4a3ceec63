{"version": 3, "file": "document-transform.js", "sourceRoot": "", "sources": ["../../src/functions/document-transform.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AA8DA,8CAyPC;AAvTD;;;GAGG;AACH,gDAAyF;AACzF,sDAAwD;AACxD,+BAAoC;AACpC,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,4BAA4B;AAC5B,IAAK,kBAQJ;AARD,WAAK,kBAAkB;IACrB,6DAAuC,CAAA;IACvC,yDAAmC,CAAA;IACnC,uDAAiC,CAAA;IACjC,qDAA+B,CAAA;IAC/B,mDAA6B,CAAA;IAC7B,6CAAuB,CAAA;IACvB,2CAAqB,CAAA;AACvB,CAAC,EARI,kBAAkB,KAAlB,kBAAkB,QAQtB;AAED,qBAAqB;AACrB,MAAM,uBAAuB,GAAG,GAAG,CAAC,MAAM,CAAC;IACzC,kBAAkB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,GAAG,MAAM,CAAC,MAAM,CAAC,kBAAkB,CAAC,CAAC,CAAC,QAAQ,EAAE;IACvF,iBAAiB,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;IAC3E,YAAY,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,QAAQ,EAAE;IACrC,OAAO,EAAE,GAAG,CAAC,MAAM,CAAC;QAClB,KAAK,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;QAClE,UAAU,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAC3B,GAAG,CAAC,MAAM,CAAC;YACT,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;YAC/C,GAAG,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;SAC9C,CAAC,CACH,CAAC,QAAQ,EAAE;QACZ,QAAQ,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,KAAK,CAAC,EAAE,EAAE,GAAG,EAAE,GAAG,CAAC,CAAC,QAAQ,EAAE;QACrD,aAAa,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC/C,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;QAC3D,gBAAgB,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,QAAQ,EAAE;QACvD,UAAU,EAAE,GAAG,CAAC,KAAK,EAAE,CAAC,KAAK,CAAC,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,CAAC,QAAQ,EAAE;KAC9D,CAAC,CAAC,QAAQ,EAAE;IACb,cAAc,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IAC9C,SAAS,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,CAAC,QAAQ,EAAE;IACzC,UAAU,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,QAAQ,EAAE;CAC7C,CAAC,CAAC;AAaH;;GAEG;AACI,KAAK,UAAU,iBAAiB,CAAC,OAAoB,EAAE,OAA0B;IACtF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,aAAa,EAAE,CAAC,CAAC;IAElE,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,wBAAwB;QACxB,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAClC,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,uBAAuB,CAAC,QAAQ,CAAC,IAAI,CAAC,CAAC;QAEhE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,EACJ,kBAAkB,EAClB,iBAAiB,EACjB,YAAY,EACZ,OAAO,EACP,cAAc,EACd,SAAS,EACT,UAAU,EACX,GAAG,KAAK,CAAC;QACV,MAAM,SAAS,GAAG,IAAI,CAAC,GAAG,EAAE,CAAC;QAE7B,oCAAoC;QACpC,MAAM,eAAe,GAAU,EAAE,CAAC;QAClC,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAK,MAAM,UAAU,IAAI,iBAAiB,EAAE,CAAC;YAC3C,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;YACxE,IAAI,CAAC,QAAQ,EAAE,CAAC;gBACd,OAAO,IAAA,qBAAc,EAAC;oBACpB,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,UAAU,EAAE,EAAE;iBACzD,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,2BAA2B;YAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;gBACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;gBAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;YAEF,IAAI,CAAC,SAAS,EAAE,CAAC;gBACf,OAAO,IAAA,qBAAc,EAAC;oBACpB,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,8BAA8B,UAAU,EAAE,EAAE;iBAChE,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,eAAe,CAAC,IAAI,CAAC,QAAQ,CAAC,CAAC;YAC/B,iBAAiB,IAAK,QAAgB,CAAC,IAAI,IAAI,CAAC,CAAC;QACnD,CAAC;QAED,iCAAiC;QACjC,MAAM,iBAAiB,GAAG,IAAI,gCAAiB,CAC7C,OAAO,CAAC,GAAG,CAAC,+BAA+B,IAAI,EAAE,CAClD,CAAC;QAEF,MAAM,eAAe,GAAG,iBAAiB,CAAC,kBAAkB,CAC1D,OAAO,CAAC,GAAG,CAAC,kBAAkB,IAAI,WAAW,CAC9C,CAAC;QAEF,4BAA4B;QAC5B,MAAM,eAAe,GAAa,EAAE,CAAC;QACrC,KAAK,MAAM,QAAQ,IAAI,eAAe,EAAE,CAAC;YACvC,MAAM,UAAU,GAAG,eAAe,CAAC,kBAAkB,CAAE,QAAgB,CAAC,QAAQ,CAAC,CAAC;YAClF,MAAM,gBAAgB,GAAG,MAAM,UAAU,CAAC,QAAQ,CAAC,CAAC,CAAC,CAAC;YAEtD,IAAI,CAAC,gBAAgB,CAAC,kBAAkB,EAAE,CAAC;gBACzC,OAAO,IAAA,qBAAc,EAAC;oBACpB,MAAM,EAAE,GAAG;oBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;oBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,gCAAiC,QAAgB,CAAC,EAAE,EAAE,EAAE;iBAC5E,EAAE,OAAO,CAAC,CAAC;YACd,CAAC;YAED,MAAM,cAAc,GAAG,MAAM,cAAc,CAAC,gBAAgB,CAAC,kBAAkB,CAAC,CAAC;YACjF,eAAe,CAAC,IAAI,CAAC,cAAc,CAAC,CAAC;QACvC,CAAC;QAED,yBAAyB;QACzB,MAAM,oBAAoB,GAAG,MAAM,qBAAqB,CACtD,eAAe,EACf,eAAe,EACf,kBAAkB,EAClB,YAAY,EACZ,OAAO,IAAI,EAAE,CACd,CAAC;QAEF,4CAA4C;QAC5C,MAAM,qBAAqB,GAAG,IAAA,SAAM,GAAE,CAAC;QACvC,MAAM,aAAa,GAAG,gBAAgB,CAAC,oBAAoB,CAAC,WAAW,CAAC,CAAC;QACzE,MAAM,mBAAmB,GAAG,GAAG,cAAc,IAAI,SAAS,IAAI,qBAAqB,gBAAgB,aAAa,EAAE,CAAC;QACnH,MAAM,qBAAqB,GAAG,eAAe,CAAC,kBAAkB,CAAC,mBAAmB,CAAC,CAAC;QAEtF,MAAM,qBAAqB,CAAC,MAAM,CAChC,oBAAoB,CAAC,iBAAiB,EACtC,oBAAoB,CAAC,iBAAiB,CAAC,MAAM,EAC7C;YACE,eAAe,EAAE,EAAE,eAAe,EAAE,oBAAoB,CAAC,WAAW,EAAE;SACvE,CACF,CAAC;QAEF,uBAAuB;QACvB,MAAM,WAAW,GAAG,eAAe,CAAC,MAAM,KAAK,CAAC;YAC9C,CAAC,CAAC,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,IAAI,gBAAgB;YAC5C,CAAC,CAAC,oBAAoB,eAAe,CAAC,MAAM,SAAS,CAAC;QAExD,qCAAqC;QACrC,MAAM,mBAAmB,GAAG;YAC1B,EAAE,EAAE,qBAAqB;YACzB,iBAAiB;YACjB,IAAI,EAAE,UAAU,IAAI,WAAW;YAC/B,WAAW,EAAE,8BAA8B,kBAAkB,EAAE;YAC/D,QAAQ,EAAE,mBAAmB;YAC7B,WAAW,EAAE,oBAAoB,CAAC,WAAW;YAC7C,IAAI,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,MAAM;YACnD,cAAc;YACd,SAAS;YACT,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,MAAM,EAAE,aAAa;YACrB,QAAQ,EAAE;gBACR,kBAAkB;gBAClB,iBAAiB;gBACjB,iBAAiB,EAAE,iBAAiB;gBACpC,eAAe,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,MAAM;gBAC9D,SAAS,EAAE,oBAAoB,CAAC,SAAS;gBACzC,aAAa,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;gBACvC,aAAa,EAAE,IAAI,CAAC,EAAE;gBACtB,OAAO;aACR;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,mBAAmB,CAAC,CAAC;QAEtD,+BAA+B;QAC/B,MAAM,cAAc,GAAG;YACrB,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,kBAAkB;YAClB,iBAAiB;YACjB,qBAAqB;YACrB,OAAO;YACP,MAAM,EAAE;gBACN,iBAAiB,EAAE,iBAAiB;gBACpC,eAAe,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,MAAM;gBAC9D,SAAS,EAAE,oBAAoB,CAAC,SAAS;gBACzC,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACvC;YACD,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,cAAc;YACd,SAAS;YACT,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC;QAEF,MAAM,aAAE,CAAC,UAAU,CAAC,0BAA0B,EAAE,cAAc,CAAC,CAAC;QAEhE,yBAAyB;QACzB,MAAM,aAAE,CAAC,UAAU,CAAC,YAAY,EAAE;YAChC,EAAE,EAAE,IAAA,SAAM,GAAE;YACZ,IAAI,EAAE,sBAAsB;YAC5B,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc;YACd,SAAS;YACT,UAAU,EAAE,qBAAqB;YACjC,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE;gBACP,kBAAkB;gBAClB,mBAAmB,EAAE,iBAAiB,CAAC,MAAM;gBAC7C,iBAAiB,EAAE,iBAAiB;gBACpC,eAAe,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,MAAM;gBAC9D,SAAS,EAAE,oBAAoB,CAAC,SAAS;gBACzC,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;aACvC;YACD,QAAQ,EAAE,IAAI,CAAC,QAAQ;SACxB,CAAC,CAAC;QAEH,MAAM,QAAQ,GAAyB;YACrC,kBAAkB;YAClB,iBAAiB;YACjB,qBAAqB;YACrB,iBAAiB,EAAE,iBAAiB;YACpC,eAAe,EAAE,oBAAoB,CAAC,iBAAiB,CAAC,MAAM;YAC9D,SAAS,EAAE,oBAAoB,CAAC,SAAS;YACzC,cAAc,EAAE,IAAI,CAAC,GAAG,EAAE,GAAG,SAAS;YACtC,OAAO,EAAE,IAAI;SACd,CAAC;QAEF,eAAM,CAAC,IAAI,CAAC,mCAAmC,EAAE;YAC/C,aAAa;YACb,kBAAkB;YAClB,mBAAmB,EAAE,iBAAiB,CAAC,MAAM;YAC7C,qBAAqB;YACrB,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,cAAc,EAAE,QAAQ,CAAC,cAAc;SACxC,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,QAAQ;SACnB,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,qBAAqB,CAClC,eAAyB,EACzB,eAAsB,EACtB,kBAAsC,EACtC,YAAqB,EACrB,UAAe,EAAE;IAEjB,sCAAsC;IACtC,oEAAoE;IACpE,yCAAyC;IAEzC,eAAM,CAAC,IAAI,CAAC,oCAAoC,EAAE;QAChD,kBAAkB;QAClB,aAAa,EAAE,eAAe,CAAC,MAAM;QACrC,YAAY;KACb,CAAC,CAAC;IAEH,IAAI,iBAAyB,CAAC;IAC9B,IAAI,WAAmB,CAAC;IACxB,IAAI,SAAS,GAAG,CAAC,CAAC;IAElB,QAAQ,kBAAkB,EAAE,CAAC;QAC3B,KAAK,kBAAkB,CAAC,eAAe;YACrC,6BAA6B;YAC7B,iBAAiB,GAAG,MAAM,CAAC,MAAM,CAAC,eAAe,CAAC,CAAC;YACnD,WAAW,GAAG,YAAY,IAAI,iBAAiB,CAAC;YAChD,SAAS,GAAG,eAAe,CAAC,MAAM,CAAC,CAAC,aAAa;YACjD,MAAM;QAER,KAAK,kBAAkB,CAAC,iBAAiB;YACvC,6BAA6B;YAC7B,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACvC,WAAW,GAAG,YAAY,IAAI,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7D,SAAS,GAAG,CAAC,CAAC;YACd,MAAM;QAER,KAAK,kBAAkB,CAAC,cAAc;YACpC,kDAAkD;YAClD,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACvC,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7C,SAAS,GAAG,IAAI,CAAC,IAAI,CAAC,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,CAAC,GAAG,CAAC,CAAC,CAAC;YACnF,MAAM;QAER,KAAK,kBAAkB,CAAC,aAAa;YACnC,2BAA2B;YAC3B,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACvC,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7C,SAAS,GAAG,OAAO,CAAC,KAAK,EAAE,MAAM,IAAI,CAAC,CAAC;YACvC,MAAM;QAER,KAAK,kBAAkB,CAAC,QAAQ;YAC9B,mDAAmD;YACnD,MAAM,cAAc,GAAG,IAAI,CAAC,KAAK,CAAC,eAAe,CAAC,CAAC,CAAC,CAAC,MAAM,GAAG,GAAG,CAAC,CAAC;YACnE,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,EAAE,cAAc,CAAC,CAAC;YAChE,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7C,SAAS,GAAG,CAAC,CAAC;YACd,MAAM;QAER;YACE,iBAAiB,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC;YACvC,WAAW,GAAG,eAAe,CAAC,CAAC,CAAC,CAAC,WAAW,CAAC;YAC7C,SAAS,GAAG,CAAC,CAAC;YACd,MAAM;IACV,CAAC;IAED,OAAO;QACL,iBAAiB;QACjB,WAAW;QACX,SAAS;KACV,CAAC;AACJ,CAAC;AAED;;GAEG;AACH,SAAS,gBAAgB,CAAC,WAAmB;IAC3C,MAAM,UAAU,GAA8B;QAC5C,iBAAiB,EAAE,KAAK;QACxB,YAAY,EAAE,KAAK;QACnB,WAAW,EAAE,KAAK;QAClB,YAAY,EAAE,MAAM;QACpB,oBAAoB,EAAE,KAAK;QAC3B,yEAAyE,EAAE,MAAM;KAClF,CAAC;IAEF,OAAO,UAAU,CAAC,WAAW,CAAC,IAAI,KAAK,CAAC;AAC1C,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,cAAc,CAAC,cAAqC;IACjE,OAAO,IAAI,OAAO,CAAC,CAAC,OAAO,EAAE,MAAM,EAAE,EAAE;QACrC,MAAM,MAAM,GAAa,EAAE,CAAC;QAC5B,cAAc,CAAC,EAAE,CAAC,MAAM,EAAE,CAAC,IAAI,EAAE,EAAE;YACjC,MAAM,CAAC,IAAI,CAAC,IAAI,YAAY,MAAM,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC,CAAC,MAAM,CAAC,IAAI,CAAC,IAAI,CAAC,CAAC,CAAC;QACjE,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,KAAK,EAAE,GAAG,EAAE;YAC5B,OAAO,CAAC,MAAM,CAAC,MAAM,CAAC,MAAM,CAAC,CAAC,CAAC;QACjC,CAAC,CAAC,CAAC;QACH,cAAc,CAAC,EAAE,CAAC,OAAO,EAAE,MAAM,CAAC,CAAC;IACrC,CAAC,CAAC,CAAC;AACL,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,oBAAoB,EAAE;IAC7B,OAAO,EAAE,CAAC,MAAM,EAAE,SAAS,CAAC;IAC5B,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,qBAAqB;IAC5B,OAAO,EAAE,iBAAiB;CAC3B,CAAC,CAAC"}