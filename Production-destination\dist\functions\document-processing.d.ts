/**
 * Document Processing Function
 * Handles AI-powered document analysis and content extraction
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
export declare enum DocumentType {
    GENERAL = "general",
    INVOICE = "invoice",
    CONTRACT = "contract",
    RECEIPT = "receipt",
    IDENTITY = "identity",
    FINANCIAL = "financial",
    LEGAL = "legal",
    MEDICAL = "medical",
    TAX = "tax",
    INSURANCE = "insurance"
}
export declare enum DocumentStatus {
    PENDING = "pending",
    UPLOADED = "uploaded",
    PROCESSING = "processing",
    PROCESSED = "processed",
    FAILED = "failed",
    ARCHIVED = "archived"
}
/**
 * Process document handler
 */
export declare function processDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
