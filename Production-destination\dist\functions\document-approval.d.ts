/**
 * Document Approval Function
 * Handles document approval workflows, review processes, and approval tracking
 * Migrated from old-arch/src/document-service/approval/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create document approval handler
 */
export declare function createDocumentApproval(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Review document handler
 */
export declare function reviewDocument(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
