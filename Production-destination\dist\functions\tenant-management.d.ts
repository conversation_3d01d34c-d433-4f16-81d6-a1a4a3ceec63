/**
 * Tenant Management Function
 * Handles multi-tenant operations and tenant isolation
 * Migrated from old-arch/src/tenant-service/management/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Create tenant handler
 */
export declare function createTenant(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get tenant handler
 */
export declare function getTenant(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
