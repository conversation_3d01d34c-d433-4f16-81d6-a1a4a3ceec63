/**
 * Mobile API Function
 * Handles mobile-optimized endpoints and Progressive Web App features
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Register mobile device handler
 */
export declare function registerDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Sync data handler
 */
export declare function syncData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Get offline data handler
 */
export declare function getOfflineData(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
