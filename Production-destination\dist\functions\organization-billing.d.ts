/**
 * Organization Billing Function
 * Handles billing management, subscription changes, and usage tracking
 * Migrated from old-arch/src/organization-service/billing/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get billing information handler
 */
export declare function getBillingInfo(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update subscription handler
 */
export declare function updateSubscription(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
