/**
 * Push Notifications Function
 * Handles push notifications to mobile and web clients
 * Migrated from old-arch/src/notification-service/push/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Send push notification handler
 */
export declare function sendPushNotification(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Register device handler
 */
export declare function registerDevice(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
