"use strict";
/**
 * Database service for Cosmos DB operations
 * Provides centralized database access and common operations
 */
Object.defineProperty(exports, "__esModule", { value: true });
exports.db = exports.DatabaseService = void 0;
const cosmos_1 = require("@azure/cosmos");
const logger_1 = require("../utils/logger");
const env_1 = require("../../env");
class DatabaseService {
    constructor() {
        this.containers = new Map();
        // Initialize as null - will be lazily initialized when first used
        this.client = null;
        this.database = null;
    }
    /**
     * Initialize the database connection lazily
     */
    initializeDatabase() {
        if (this.client !== null) {
            return; // Already initialized
        }
        // Check if we have valid Cosmos DB configuration
        if (!env_1.config.cosmosDb.endpoint || !env_1.config.cosmosDb.key) {
            logger_1.logger.warn('Cosmos DB configuration missing. Database operations will be mocked in development.');
            return;
        }
        try {
            // Validate URL format before creating client
            const url = new URL(env_1.config.cosmosDb.endpoint);
            if (!url.protocol.startsWith('https')) {
                throw new Error('Cosmos DB endpoint must use HTTPS protocol');
            }
            this.client = new cosmos_1.CosmosClient({
                endpoint: env_1.config.cosmosDb.endpoint,
                key: env_1.config.cosmosDb.key
            });
            this.database = this.client.database(env_1.config.cosmosDb.database);
            logger_1.logger.info('Cosmos DB client initialized successfully');
        }
        catch (error) {
            logger_1.logger.error('Failed to initialize Cosmos DB client', {
                endpoint: env_1.config.cosmosDb.endpoint,
                error: error instanceof Error ? error.message : String(error)
            });
            this.client = null;
            this.database = null;
        }
    }
    static getInstance() {
        if (!DatabaseService.instance) {
            DatabaseService.instance = new DatabaseService();
        }
        return DatabaseService.instance;
    }
    /**
     * Check if database is available
     */
    isDatabaseAvailable() {
        this.initializeDatabase();
        return this.client !== null && this.database !== null;
    }
    /**
     * Get container instance
     */
    getContainer(containerName) {
        if (!this.isDatabaseAvailable()) {
            throw new Error('Database not available. Please configure Cosmos DB connection.');
        }
        if (!this.containers.has(containerName)) {
            const container = this.database.container(containerName);
            this.containers.set(containerName, container);
        }
        return this.containers.get(containerName);
    }
    /**
     * Create item in container
     */
    async createItem(containerName, item) {
        if (!this.isDatabaseAvailable()) {
            logger_1.logger.warn(`Mock: Creating item in ${containerName}`, { itemId: item?.id });
            return item; // Return the item as-is in mock mode
        }
        try {
            const container = this.getContainer(containerName);
            const { resource } = await container.items.create(item);
            logger_1.logger.info(`Created item in ${containerName}`, { itemId: resource?.id });
            return resource;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create item in ${containerName}`, { error: error instanceof Error ? error.message : String(error) });
            throw error;
        }
    }
    /**
     * Read item by id and partition key
     */
    async readItem(containerName, id, partitionKey) {
        if (!this.isDatabaseAvailable()) {
            logger_1.logger.warn(`Mock: Reading item from ${containerName}`, { id, partitionKey });
            return null; // Return null in mock mode
        }
        try {
            const container = this.getContainer(containerName);
            const { resource } = await container.item(id, partitionKey).read();
            return resource || null;
        }
        catch (error) {
            if (error.code === 404) {
                return null;
            }
            logger_1.logger.error(`Failed to read item from ${containerName}`, {
                id,
                partitionKey,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Update item
     */
    async updateItem(containerName, item) {
        try {
            const container = this.getContainer(containerName);
            const { resource } = await container.item(item.id).replace(item);
            logger_1.logger.info(`Updated item in ${containerName}`, { itemId: item.id });
            return resource;
        }
        catch (error) {
            logger_1.logger.error(`Failed to update item in ${containerName}`, {
                itemId: item.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Delete item
     */
    async deleteItem(containerName, id, partitionKey) {
        try {
            const container = this.getContainer(containerName);
            await container.item(id, partitionKey).delete();
            logger_1.logger.info(`Deleted item from ${containerName}`, { id, partitionKey });
        }
        catch (error) {
            logger_1.logger.error(`Failed to delete item from ${containerName}`, {
                id,
                partitionKey,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Query items with SQL
     */
    async queryItems(containerName, query, parameters) {
        try {
            const container = this.getContainer(containerName);
            // Build query spec with proper parameter mapping
            const querySpec = {
                query,
                parameters: parameters?.map((value, index) => ({
                    name: `@param${index}`,
                    value
                })) || []
            };
            // Replace parameter placeholders in query to match the parameter names
            let finalQuery = query;
            if (parameters && parameters.length > 0) {
                const paramNames = ['@documentId', '@sharedWith', '@status', '@tenantId', '@userId', '@pageNumber', '@resolved', '@organizationId', '@projectId', '@orgIds', '@projectIds', '@query', '@userId2', '@search', '@visibility', '@tags', '@assigneeId', '@limit'];
                parameters.forEach((param, index) => {
                    if (index < paramNames.length) {
                        finalQuery = finalQuery.replace(new RegExp(paramNames[index], 'g'), `@param${index}`);
                    }
                });
            }
            querySpec.query = finalQuery;
            const { resources } = await container.items.query(querySpec).fetchAll();
            logger_1.logger.info(`Queried items from ${containerName}`, {
                query: query.substring(0, 100),
                resultCount: resources.length
            });
            return resources;
        }
        catch (error) {
            logger_1.logger.error(`Failed to query items from ${containerName}`, {
                query: query.substring(0, 100),
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Query items with pagination
     */
    async queryItemsPaginated(containerName, query, parameters, maxItemCount = 20, continuationToken) {
        try {
            const container = this.getContainer(containerName);
            const querySpec = {
                query,
                parameters: parameters?.map((value, index) => ({
                    name: `@param${index}`,
                    value
                })) || []
            };
            const queryIterator = container.items.query(querySpec, {
                maxItemCount,
                continuationToken
            });
            const { resources, continuationToken: nextToken } = await queryIterator.fetchNext();
            return {
                items: resources,
                continuationToken: nextToken
            };
        }
        catch (error) {
            logger_1.logger.error(`Failed to query items with pagination from ${containerName}`, {
                query,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Check if container exists
     */
    async containerExists(containerName) {
        try {
            await this.database.container(containerName).read();
            return true;
        }
        catch (error) {
            if (error.code === 404) {
                return false;
            }
            throw error;
        }
    }
    /**
     * Upsert an item in a container (create or update)
     */
    async upsertItem(containerName, item) {
        try {
            const container = this.getContainer(containerName);
            const { resource } = await container.items.upsert(item);
            logger_1.logger.debug('Database item upserted', {
                container: containerName,
                id: item.id
            });
            return resource;
        }
        catch (error) {
            logger_1.logger.error('Failed to upsert database item', {
                container: containerName,
                id: item.id,
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
    /**
     * Create container if it doesn't exist
     */
    async createContainerIfNotExists(containerName, partitionKey) {
        try {
            const { container } = await this.database.containers.createIfNotExists({
                id: containerName,
                partitionKey: { paths: [partitionKey] }
            });
            this.containers.set(containerName, container);
            logger_1.logger.info(`Container ${containerName} created or already exists`);
            return container;
        }
        catch (error) {
            logger_1.logger.error(`Failed to create container ${containerName}`, {
                error: error instanceof Error ? error.message : String(error)
            });
            throw error;
        }
    }
}
exports.DatabaseService = DatabaseService;
// Export singleton instance
exports.db = DatabaseService.getInstance();
exports.default = exports.db;
//# sourceMappingURL=database.js.map