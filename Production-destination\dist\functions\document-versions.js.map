{"version": 3, "file": "document-versions.js", "sourceRoot": "", "sources": ["../../src/functions/document-versions.ts"], "names": [], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAuCA,kDA6IC;AAKD,sDA2IC;AApUD;;;GAGG;AACH,gDAAyF;AACzF,yCAA2B;AAC3B,mDAAgD;AAChD,oDAA4E;AAC5E,+CAA2D;AAC3D,0DAAiD;AAEjD,qBAAqB;AACrB,MAAM,iBAAiB,GAAG,GAAG,CAAC,MAAM,CAAC;IACnC,IAAI,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC;IAC9C,KAAK,EAAE,GAAG,CAAC,MAAM,EAAE,CAAC,OAAO,EAAE,CAAC,GAAG,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC,GAAG,CAAC,CAAC,OAAO,CAAC,EAAE,CAAC;CAC1D,CAAC,CAAC;AAqBH;;GAEG;AACI,KAAK,UAAU,mBAAmB,CAAC,OAAoB,EAAE,OAA0B;IACxF,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;IAErC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE;SAC/C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,+BAA+B,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;IAE5E,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAE7B,4BAA4B;QAC5B,MAAM,WAAW,GAAG,MAAM,CAAC,WAAW,CAAC,OAAO,CAAC,KAAK,CAAC,OAAO,EAAE,CAAC,CAAC;QAChE,MAAM,EAAE,KAAK,EAAE,KAAK,EAAE,GAAG,iBAAiB,CAAC,QAAQ,CAAC,WAAW,CAAC,CAAC;QAEjE,IAAI,KAAK,EAAE,CAAC;YACV,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE;oBACR,KAAK,EAAE,kBAAkB;oBACzB,OAAO,EAAE,KAAK,CAAC,OAAO,CAAC,GAAG,CAAC,CAAC,CAAC,EAAE,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,IAAI,CAAC,IAAI,CAAC;iBACtD;aACF,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,EAAE,IAAI,EAAE,KAAK,EAAE,GAAG,KAAK,CAAC;QAE9B,wDAAwD;QACxD,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,8BAA8B;QAC9B,MAAM,KAAK,GAAG,gFAAgF,CAAC;QAC/F,MAAM,UAAU,GAAG,CAAC,UAAU,CAAC,CAAC;QAEhC,gCAAgC;QAChC,MAAM,MAAM,GAAG,CAAC,IAAI,GAAG,CAAC,CAAC,GAAG,KAAK,CAAC;QAClC,MAAM,cAAc,GAAG,GAAG,KAAK,WAAW,MAAM,UAAU,KAAK,EAAE,CAAC;QAElE,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,cAAc,EAAE,UAAU,CAAC,CAAC;QAErF,iCAAiC;QACjC,MAAM,UAAU,GAAG,+DAA+D,CAAC;QACnF,MAAM,WAAW,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QACpF,MAAM,KAAK,GAAG,MAAM,CAAC,WAAW,CAAC,CAAC,CAAC,CAAC,IAAI,CAAC,CAAC;QAE1C,+BAA+B;QAC/B,MAAM,iBAAiB,GAAG,QAAQ,CAAC,GAAG,CAAC,CAAC,OAAY,EAAE,EAAE,CAAC,CAAC;YACxD,aAAa,EAAE,OAAO,CAAC,aAAa;YACpC,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,SAAS,EAAE,OAAO,CAAC,SAAS;YAC5B,OAAO,EAAE,OAAO,CAAC,OAAO;YACxB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,IAAI,EAAE,OAAO,CAAC,IAAI;YAClB,QAAQ,EAAE,OAAO,CAAC,QAAQ;YAC1B,OAAO,EAAE,OAAO,CAAC,OAAO,IAAI,EAAE;SAC/B,CAAC,CAAC,CAAC;QAEJ,eAAM,CAAC,IAAI,CAAC,0CAA0C,EAAE;YACtD,aAAa;YACb,UAAU;YACV,MAAM,EAAE,IAAI,CAAC,EAAE;YACf,aAAa,EAAE,QAAQ,CAAC,MAAM;YAC9B,IAAI;YACJ,KAAK;SACN,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,UAAU;gBACV,QAAQ,EAAE,iBAAiB;gBAC3B,UAAU,EAAE;oBACV,IAAI;oBACJ,KAAK;oBACL,KAAK;oBACL,OAAO,EAAE,CAAC,IAAI,GAAG,KAAK,CAAC,GAAG,KAAK;iBAChC;aACF;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,8BAA8B,EAAE;YAC3C,aAAa;YACb,UAAU;YACV,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACI,KAAK,UAAU,qBAAqB,CAAC,OAAoB,EAAE,OAA0B;IAC1F,mCAAmC;IACnC,MAAM,iBAAiB,GAAG,IAAA,sBAAe,EAAC,OAAO,CAAC,CAAC;IACnD,IAAI,iBAAiB,EAAE,CAAC;QACtB,OAAO,iBAAiB,CAAC;IAC3B,CAAC;IAED,MAAM,aAAa,GAAG,OAAO,CAAC,YAAY,CAAC;IAC3C,MAAM,UAAU,GAAG,OAAO,CAAC,MAAM,CAAC,EAAE,CAAC;IAErC,IAAI,CAAC,UAAU,EAAE,CAAC;QAChB,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,yBAAyB,EAAE;SAC/C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;IAED,eAAM,CAAC,IAAI,CAAC,iCAAiC,EAAE,EAAE,aAAa,EAAE,UAAU,EAAE,CAAC,CAAC;IAE9E,IAAI,CAAC;QACH,oBAAoB;QACpB,MAAM,UAAU,GAAG,MAAM,IAAA,0BAAmB,EAAC,OAAO,CAAC,CAAC;QACtD,IAAI,CAAC,UAAU,CAAC,OAAO,IAAI,CAAC,UAAU,CAAC,IAAI,EAAE,CAAC;YAC5C,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,cAAc,EAAE,OAAO,EAAE,UAAU,CAAC,KAAK,EAAE;aAC/D,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,MAAM,IAAI,GAAG,UAAU,CAAC,IAAI,CAAC;QAC7B,MAAM,IAAI,GAAG,MAAM,OAAO,CAAC,IAAI,EAAE,CAAC;QAElC,iDAAiD;QACjD,MAAM,QAAQ,GAAG,MAAM,aAAE,CAAC,QAAQ,CAAC,WAAW,EAAE,UAAU,EAAE,UAAU,CAAC,CAAC;QAExE,IAAI,CAAC,QAAQ,EAAE,CAAC;YACd,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,2BAA2B;QAC3B,MAAM,SAAS,GAAG,CACf,QAAgB,CAAC,SAAS,KAAK,IAAI,CAAC,EAAE;YACtC,QAAgB,CAAC,cAAc,KAAK,IAAI,CAAC,QAAQ;YAClD,IAAI,CAAC,KAAK,EAAE,QAAQ,CAAC,OAAO,CAAC,CAC9B,CAAC;QAEF,IAAI,CAAC,SAAS,EAAE,CAAC;YACf,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,eAAe,EAAE;aACrC,EAAE,OAAO,CAAC,CAAC;QACd,CAAC;QAED,yCAAyC;QACzC,MAAM,kBAAkB,GAAG,sFAAsF,CAAC;QAClH,MAAM,cAAc,GAAG,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,kBAAkB,EAAE,CAAC,UAAU,CAAC,CAAC,CAAC;QACjG,MAAM,aAAa,GAAG,cAAc,CAAC,CAAC,CAAQ,CAAC;QAC/C,MAAM,gBAAgB,GAAG,aAAa,CAAC,CAAC,CAAC,aAAa,CAAC,aAAa,GAAG,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;QAE7E,0BAA0B;QAC1B,MAAM,QAAQ,GAAG,IAAW,CAAC;QAE7B,4BAA4B;QAC5B,MAAM,UAAU,GAAoB;YAClC,EAAE,EAAE,GAAG,UAAU,KAAK,gBAAgB,EAAE;YACxC,UAAU;YACV,aAAa,EAAE,gBAAgB;YAC/B,SAAS,EAAE,IAAI,CAAC,EAAE;YAClB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,WAAW,gBAAgB,EAAE;YAC1D,IAAI,EAAG,QAAgB,CAAC,IAAI,IAAI,CAAC;YACjC,IAAI,EAAE,QAAQ,CAAC,IAAI,IAAI,EAAE;YACzB,QAAQ,EAAG,QAAgB,CAAC,QAAQ,IAAI,EAAE;YAC1C,WAAW,EAAG,QAAgB,CAAC,WAAW,IAAI,EAAE;YAChD,QAAQ,EAAE,IAAI;YACd,OAAO,EAAE,QAAQ,CAAC,OAAO,IAAI,EAAE;SAChC,CAAC;QAEF,2CAA2C;QAC3C,IAAI,aAAa,EAAE,CAAC;YAClB,MAAM,sBAAsB,GAAG;gBAC7B,GAAG,aAAa;gBAChB,EAAE,EAAE,aAAa,CAAC,EAAE;gBACpB,QAAQ,EAAE,KAAK;aAChB,CAAC;YACF,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,sBAAsB,CAAC,CAAC;QAClE,CAAC;QAED,mBAAmB;QACnB,MAAM,aAAE,CAAC,UAAU,CAAC,kBAAkB,EAAE,UAAU,CAAC,CAAC;QAEpD,wCAAwC;QACxC,MAAM,eAAe,GAAG;YACtB,GAAG,QAAQ;YACX,EAAE,EAAG,QAAgB,CAAC,EAAE;YACxB,OAAO,EAAE,gBAAgB;YACzB,SAAS,EAAE,IAAI,IAAI,EAAE,CAAC,WAAW,EAAE;YACnC,SAAS,EAAE,IAAI,CAAC,EAAE;SACnB,CAAC;QACF,MAAM,aAAE,CAAC,UAAU,CAAC,WAAW,EAAE,eAAe,CAAC,CAAC;QAElD,eAAM,CAAC,IAAI,CAAC,uCAAuC,EAAE;YACnD,aAAa;YACb,UAAU;YACV,aAAa,EAAE,gBAAgB;YAC/B,MAAM,EAAE,IAAI,CAAC,EAAE;SAChB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE;gBACR,UAAU;gBACV,aAAa,EAAE,gBAAgB;gBAC/B,OAAO,EAAE,uCAAuC;aACjD;SACF,EAAE,OAAO,CAAC,CAAC;IAEd,CAAC;IAAC,OAAO,KAAK,EAAE,CAAC;QACf,MAAM,YAAY,GAAG,KAAK,YAAY,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC,CAAC;QAC5E,eAAM,CAAC,KAAK,CAAC,gCAAgC,EAAE;YAC7C,aAAa;YACb,UAAU;YACV,KAAK,EAAE,YAAY;SACpB,CAAC,CAAC;QAEH,OAAO,IAAA,qBAAc,EAAC;YACpB,MAAM,EAAE,GAAG;YACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;YAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,uBAAuB,EAAE;SAC7C,EAAE,OAAO,CAAC,CAAC;IACd,CAAC;AACH,CAAC;AAED;;GAEG;AACH,KAAK,UAAU,sBAAsB,CAAC,OAAoB,EAAE,OAA0B;IACpF,MAAM,MAAM,GAAG,OAAO,CAAC,MAAM,CAAC,WAAW,EAAE,CAAC;IAE5C,QAAQ,MAAM,EAAE,CAAC;QACf,KAAK,KAAK;YACR,OAAO,MAAM,mBAAmB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACrD,KAAK,MAAM;YACT,OAAO,MAAM,qBAAqB,CAAC,OAAO,EAAE,OAAO,CAAC,CAAC;QACvD,KAAK,SAAS;YACZ,OAAO,IAAA,sBAAe,EAAC,OAAO,CAAC,IAAI,EAAE,MAAM,EAAE,GAAG,EAAE,CAAC;QACrD;YACE,OAAO,IAAA,qBAAc,EAAC;gBACpB,MAAM,EAAE,GAAG;gBACX,OAAO,EAAE,EAAE,cAAc,EAAE,kBAAkB,EAAE;gBAC/C,QAAQ,EAAE,EAAE,KAAK,EAAE,oBAAoB,EAAE;aAC1C,EAAE,OAAO,CAAC,CAAC;IAChB,CAAC;AACH,CAAC;AAED,qBAAqB;AACrB,eAAG,CAAC,IAAI,CAAC,mBAAmB,EAAE;IAC5B,OAAO,EAAE,CAAC,KAAK,EAAE,MAAM,EAAE,SAAS,CAAC;IACnC,SAAS,EAAE,UAAU;IACrB,KAAK,EAAE,yBAAyB;IAChC,OAAO,EAAE,sBAAsB;CAChC,CAAC,CAAC"}