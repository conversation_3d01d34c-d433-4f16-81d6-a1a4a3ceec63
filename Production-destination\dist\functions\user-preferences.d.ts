/**
 * User Preferences Function
 * Handles user preference management, notification settings, and UI customization
 * Migrated from old-arch/src/user-service/preferences/index.ts
 */
import { HttpRequest, HttpResponseInit, InvocationContext } from '@azure/functions';
/**
 * Get user preferences handler
 */
export declare function getUserPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Update user preferences handler
 */
export declare function updateUserPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
/**
 * Reset user preferences to defaults handler
 */
export declare function resetUserPreferences(request: HttpRequest, context: InvocationContext): Promise<HttpResponseInit>;
