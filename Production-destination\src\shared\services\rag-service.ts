/**
 * RAG (Retrieval Augmented Generation) Service
 * Production-ready implementation for document-based AI reasoning and content generation
 */

import { logger } from '../utils/logger';
import { db } from './database';
import { aiServices } from './ai-services';
import { redis } from './redis';

export interface DocumentChunk {
  id: string;
  documentId: string;
  content: string;
  embedding: number[];
  metadata: {
    pageNumber?: number;
    section?: string;
    chunkIndex: number;
    wordCount: number;
    documentType?: string;
    extractedAt: string;
  };
}

export interface RAGQuery {
  query: string;
  documentIds?: string[];
  organizationId: string;
  projectId?: string;
  maxResults?: number;
  similarityThreshold?: number;
  includeMetadata?: boolean;
}

export interface RAGResult {
  answer: string;
  reasoning?: string;
  sources: Array<{
    documentId: string;
    documentName: string;
    content: string;
    relevanceScore: number;
    pageNumber?: number;
    section?: string;
  }>;
  confidence: number;
  tokensUsed: number;
  processingTime: number;
}

export interface DocumentIndexRequest {
  documentId: string;
  content: string;
  metadata?: any;
  chunkSize?: number;
  overlapSize?: number;
}

export class RAGService {
  private initialized: boolean = false;
  private readonly CHUNK_SIZE = 1000; // words
  private readonly OVERLAP_SIZE = 200; // words
  private readonly SIMILARITY_THRESHOLD = 0.7;

  async initialize(): Promise<void> {
    if (this.initialized) return;

    try {
      await aiServices.initialize();
      this.initialized = true;
      logger.info('RAG Service initialized successfully');
    } catch (error) {
      logger.error('Failed to initialize RAG Service', { error });
      throw error;
    }
  }

  /**
   * Index a document for RAG retrieval
   */
  async indexDocument(request: DocumentIndexRequest): Promise<void> {
    await this.initialize();

    const startTime = Date.now();
    const chunkSize = request.chunkSize || this.CHUNK_SIZE;
    const overlapSize = request.overlapSize || this.OVERLAP_SIZE;

    try {
      logger.info('Starting document indexing for RAG', {
        documentId: request.documentId,
        contentLength: request.content.length,
        chunkSize,
        overlapSize
      });

      // Split document into chunks
      const chunks = this.splitIntoChunks(request.content, chunkSize, overlapSize);
      
      // Generate embeddings for each chunk
      const documentChunks: DocumentChunk[] = [];
      
      for (let i = 0; i < chunks.length; i++) {
        const chunk = chunks[i];
        
        try {
          const embeddingResponse = await aiServices.generateEmbeddings(chunk.content);
          
          const documentChunk: DocumentChunk = {
            id: `${request.documentId}-chunk-${i}`,
            documentId: request.documentId,
            content: chunk.content,
            embedding: embeddingResponse.embedding,
            metadata: {
              chunkIndex: i,
              wordCount: chunk.wordCount,
              extractedAt: new Date().toISOString(),
              ...request.metadata
            }
          };
          
          documentChunks.push(documentChunk);
        } catch (error) {
          logger.warn('Failed to generate embedding for chunk', {
            documentId: request.documentId,
            chunkIndex: i,
            error
          });
        }
      }

      // Store chunks in database
      for (const chunk of documentChunks) {
        await db.createItem('document-chunks', chunk);
      }

      // Cache document chunks for quick access
      const cacheKey = `rag:chunks:${request.documentId}`;
      await redis.setex(cacheKey, 86400, JSON.stringify(documentChunks)); // 24 hours

      logger.info('Document indexing completed', {
        documentId: request.documentId,
        chunksCreated: documentChunks.length,
        processingTime: Date.now() - startTime
      });

    } catch (error) {
      logger.error('Document indexing failed', {
        documentId: request.documentId,
        error
      });
      throw error;
    }
  }

  /**
   * Perform RAG query - retrieve relevant documents and generate answer
   */
  async query(ragQuery: RAGQuery): Promise<RAGResult> {
    await this.initialize();

    const startTime = Date.now();
    const maxResults = ragQuery.maxResults || 5;
    const similarityThreshold = ragQuery.similarityThreshold || this.SIMILARITY_THRESHOLD;

    try {
      logger.info('Starting RAG query', {
        query: ragQuery.query,
        organizationId: ragQuery.organizationId,
        maxResults,
        similarityThreshold
      });

      // Generate embedding for the query
      const queryEmbedding = await aiServices.generateEmbeddings(ragQuery.query);

      // Retrieve relevant document chunks
      const relevantChunks = await this.retrieveRelevantChunks(
        queryEmbedding.embedding,
        ragQuery,
        maxResults,
        similarityThreshold
      );

      if (relevantChunks.length === 0) {
        return {
          answer: "I couldn't find any relevant information in the documents to answer your question.",
          sources: [],
          confidence: 0,
          tokensUsed: queryEmbedding.tokensUsed,
          processingTime: Date.now() - startTime
        };
      }

      // Prepare context for AI generation
      const context = relevantChunks.map(chunk => chunk.content);
      const sourceInfo = relevantChunks.map(chunk => ({
        documentId: chunk.documentId,
        content: chunk.content,
        relevanceScore: chunk.relevanceScore,
        ...chunk.metadata
      }));

      // Generate answer using DeepSeek R1 for reasoning
      const aiResponse = await aiServices.reason(
        `Based on the provided context, please answer the following question: ${ragQuery.query}`,
        context,
        {
          systemPrompt: `You are an AI assistant that answers questions based on provided document context. 
          Always base your answers on the given context and cite relevant information. 
          If the context doesn't contain enough information to answer the question, say so clearly.
          Provide reasoning for your answer.`,
          maxTokens: 2000,
          temperature: 0.3
        }
      );

      // Get document names for sources
      const documentIds = [...new Set(relevantChunks.map(chunk => chunk.documentId))];
      const documents = await this.getDocumentNames(documentIds);

      const sources = sourceInfo.map(source => ({
        documentId: source.documentId,
        documentName: documents[source.documentId] || 'Unknown Document',
        content: source.content.substring(0, 200) + '...',
        relevanceScore: source.relevanceScore,
        pageNumber: source.pageNumber,
        section: source.section
      }));

      const result: RAGResult = {
        answer: aiResponse.content,
        reasoning: aiResponse.reasoning,
        sources,
        confidence: aiResponse.confidence,
        tokensUsed: queryEmbedding.tokensUsed + aiResponse.tokensUsed,
        processingTime: Date.now() - startTime
      };

      logger.info('RAG query completed', {
        query: ragQuery.query,
        sourcesFound: sources.length,
        confidence: result.confidence,
        processingTime: result.processingTime
      });

      return result;

    } catch (error) {
      logger.error('RAG query failed', {
        query: ragQuery.query,
        error
      });
      throw error;
    }
  }

  /**
   * Split content into overlapping chunks
   */
  private splitIntoChunks(content: string, chunkSize: number, overlapSize: number): Array<{content: string, wordCount: number}> {
    const words = content.split(/\s+/);
    const chunks: Array<{content: string, wordCount: number}> = [];
    
    let startIndex = 0;
    
    while (startIndex < words.length) {
      const endIndex = Math.min(startIndex + chunkSize, words.length);
      const chunkWords = words.slice(startIndex, endIndex);
      const chunkContent = chunkWords.join(' ');
      
      chunks.push({
        content: chunkContent,
        wordCount: chunkWords.length
      });
      
      // Move start index forward, accounting for overlap
      startIndex = endIndex - overlapSize;
      
      // Prevent infinite loop
      if (startIndex >= endIndex) {
        break;
      }
    }
    
    return chunks;
  }

  /**
   * Retrieve relevant chunks using vector similarity
   */
  private async retrieveRelevantChunks(
    queryEmbedding: number[],
    ragQuery: RAGQuery,
    maxResults: number,
    similarityThreshold: number
  ): Promise<Array<DocumentChunk & {relevanceScore: number}>> {
    try {
      // Build query for document chunks
      let cosmosQuery = 'SELECT * FROM c WHERE c.id != null';
      const parameters: any[] = [];

      // Filter by organization
      cosmosQuery += ' AND c.organizationId = @organizationId';
      parameters.push({ name: '@organizationId', value: ragQuery.organizationId });

      // Filter by project if specified
      if (ragQuery.projectId) {
        cosmosQuery += ' AND c.projectId = @projectId';
        parameters.push({ name: '@projectId', value: ragQuery.projectId });
      }

      // Filter by specific documents if specified
      if (ragQuery.documentIds && ragQuery.documentIds.length > 0) {
        const documentIdParams = ragQuery.documentIds.map((id, index) => {
          parameters.push({ name: `@docId${index}`, value: id });
          return `@docId${index}`;
        });
        cosmosQuery += ` AND c.documentId IN (${documentIdParams.join(', ')})`;
      }

      const chunks = await db.queryItems<DocumentChunk>('document-chunks', cosmosQuery, parameters);

      // Calculate similarity scores
      const chunksWithScores = chunks.map(chunk => ({
        ...chunk,
        relevanceScore: this.calculateCosineSimilarity(queryEmbedding, chunk.embedding)
      }));

      // Filter by similarity threshold and sort by relevance
      const relevantChunks = chunksWithScores
        .filter(chunk => chunk.relevanceScore >= similarityThreshold)
        .sort((a, b) => b.relevanceScore - a.relevanceScore)
        .slice(0, maxResults);

      return relevantChunks;

    } catch (error) {
      logger.error('Failed to retrieve relevant chunks', { error });
      return [];
    }
  }

  /**
   * Calculate cosine similarity between two vectors
   */
  private calculateCosineSimilarity(vectorA: number[], vectorB: number[]): number {
    if (vectorA.length !== vectorB.length) {
      return 0;
    }

    let dotProduct = 0;
    let normA = 0;
    let normB = 0;

    for (let i = 0; i < vectorA.length; i++) {
      dotProduct += vectorA[i] * vectorB[i];
      normA += vectorA[i] * vectorA[i];
      normB += vectorB[i] * vectorB[i];
    }

    if (normA === 0 || normB === 0) {
      return 0;
    }

    return dotProduct / (Math.sqrt(normA) * Math.sqrt(normB));
  }

  /**
   * Get document names for source attribution
   */
  private async getDocumentNames(documentIds: string[]): Promise<{[key: string]: string}> {
    try {
      const documents: {[key: string]: string} = {};
      
      for (const documentId of documentIds) {
        try {
          const doc = await db.readItem('documents', documentId, documentId);
          documents[documentId] = doc?.name || 'Unknown Document';
        } catch (error) {
          documents[documentId] = 'Unknown Document';
        }
      }
      
      return documents;
    } catch (error) {
      logger.error('Failed to get document names', { error });
      return {};
    }
  }

  /**
   * Remove document from RAG index
   */
  async removeDocumentFromIndex(documentId: string): Promise<void> {
    try {
      // Remove chunks from database
      const query = 'SELECT * FROM c WHERE c.documentId = @documentId';
      const chunks = await db.queryItems<DocumentChunk>('document-chunks', query, [
        { name: '@documentId', value: documentId }
      ]);

      for (const chunk of chunks) {
        await db.deleteItem('document-chunks', chunk.id, chunk.id);
      }

      // Remove from cache
      const cacheKey = `rag:chunks:${documentId}`;
      await redis.del(cacheKey);

      logger.info('Document removed from RAG index', {
        documentId,
        chunksRemoved: chunks.length
      });

    } catch (error) {
      logger.error('Failed to remove document from RAG index', {
        documentId,
        error
      });
      throw error;
    }
  }
}

// Export singleton instance
export const ragService = new RAGService();
